<?php
/**
 * @copyright Copyright (c) 2021 仲正堂工作室
 * @license https://opensource.org/licenses/GPL-3.0
 * @link https://www.gougucms.com
 */

namespace app\dividend\service;

use think\facade\Db;
use app\dividend\model\DividendStoreInfo;
use app\dividend\model\DividendShareholder;
use app\dividend\model\DividendStoreDetail;
use app\dividend\model\DividendStoreDetailPerson;
use app\dividend\service\DividendService;

class DividendCalculateService
{
    /**
     * 计算指定周期的门店分红数据
     * @param string $period 统计周期（如：2025-04）
     * @param int $adminId 操作人ID（默认为0表示系统自动）
     * @return array
     */
    public static function calculateDividend($period, $adminId = 0)
    {
        $result = [
            'success' => true,
            'message' => '',
            'data' => []
        ];

        try {
            // 开启事务
            Db::startTrans();

            // 1. 获取所有配置了分红的门店
            $storeInfoList = DividendStoreInfo::where('is_delete', 0)->select()->toArray();

            if (empty($storeInfoList)) {
                $result['message'] = '没有找到配置分红的门店';
                return $result;
            }

            $calculatedData = [];
            $skippedStores = []; // 记录跳过的门店

            foreach ($storeInfoList as $storeInfo) {
                $storeId = $storeInfo['store_id'];

                // 2. 从oa_store_bill表获取门店账单数据
                $billData = self::getStoreBillData($storeId, $period);

                if (empty($billData)) {
                    // 如果没有账单数据，记录并跳过该门店
                    $skippedStores[] = [
                        'store_id' => $storeId,
                        'store_name' => self::getStoreName($storeId),
                        'reason' => '无账单数据'
                    ];
                    continue;
                }

                // 3. 计算风险金计提
                $riskReserveCurrent = self::calculateRiskReserve(
                    $billData['net_profit'],
                    $storeInfo['risk_reserve'],
                    $storeId,
                    $period
                );

                // 4. 计算分红利润
                $dividendProfit = $billData['total_income'] - $billData['total_expenses'] - $riskReserveCurrent;

                // 5. 更新风险金总额
                $beforeRiskReserve = $storeInfo['risk_reserve'];
                $newRiskReserveTotal = $beforeRiskReserve + $riskReserveCurrent;

                // 6. 检查是否已存在该周期的数据
                $existingDetail = DividendStoreDetail::where([
                    'store_id' => $storeId,
                    'period' => $period,
                    'is_delete' => 0
                ])->find();

                $detailData = [
                    'store_id' => $storeId,
                    'period' => $period,
                    'income' => $billData['total_income'],
                    'expense' => $billData['total_expenses'],
                    'risk_reserve_current' => $riskReserveCurrent,
                    'other_adjustment' => 0, // 默认为0
                    'dividend_profit' => $dividendProfit,
                    'risk_reserve_total' => $newRiskReserveTotal,
                    'company_shareholding_ratio' => $storeInfo['company_shareholding_ratio'],
                    'remark' => "",
                ];

                if ($existingDetail) {
                    // 更新现有记录
                    $detailData['update_time'] = time();
                    DividendStoreDetail::where('id', $existingDetail['id'])->update($detailData);
                    $detailId = $existingDetail['id'];

                    // 软删除原有的分红人记录
                    DividendStoreDetailPerson::where('dividend_store_detail_id', $detailId)
                        ->update(['is_delete' => 1, 'delete_time' => time()]);
                } else {
                    // 创建新记录
                    $detailData['create_time'] = time();
                    $detailData['update_time'] = time();
                    $detailId = DividendStoreDetail::insertGetId($detailData);
                }

                // 7. 计算分红人数据
                $personData = self::calculatePersonDividend($storeInfo['id'], $dividendProfit, $detailId);

                // 8. 更新门店分红信息表的风险金总额
                DividendStoreInfo::where('id', $storeInfo['id'])->update([
                    'risk_reserve' => $newRiskReserveTotal,
                    'update_time' => time()
                ]);

                // 9. 记录风险金变化日志（如果有风险金计提）
                if ($riskReserveCurrent > 0) {
                    $dividendService = new DividendService();
                    $dividendService->addRiskReserveProvisionLog(
                        $storeId,
                        $storeInfo['id'],
                        $beforeRiskReserve,
                        $riskReserveCurrent,
                        $newRiskReserveTotal,
                        $detailId,
                        0, // 系统自动计算，操作人ID设为0
                        $period // 计算周期
                    );
                }

                $calculatedData[] = [
                    'store_id' => $storeId,
                    'store_name' => self::getStoreName($storeId),
                    'period' => $period,
                    'income' => $billData['total_income'],
                    'expense' => $billData['total_expenses'],
                    'net_profit' => $billData['net_profit'],
                    'risk_reserve_current' => $riskReserveCurrent,
                    'dividend_profit' => $dividendProfit,
                    'risk_reserve_total' => $newRiskReserveTotal,
                    'person_count' => count($personData)
                ];
            }

            // 检查是否有成功计算的门店
            if (empty($calculatedData)) {
                Db::rollback();
                $result['success'] = false;
                if (!empty($skippedStores)) {
                    $result['message'] = "计算失败：{$period} 期间所有门店都没有账单数据，请先录入门店账单数据后再进行分红计算";
                } else {
                    $result['message'] = "计算失败：没有找到可计算的门店数据";
                }
                return $result;
            }

            // 提交事务
            Db::commit();

            $result['data'] = $calculatedData;
            $result['skipped_stores'] = $skippedStores;

            // 构建返回消息
            $message = "成功计算 {$period} 期间 " . count($calculatedData) . " 个门店的分红数据";
            if (!empty($skippedStores)) {
                $message .= "，跳过 " . count($skippedStores) . " 个门店（无账单数据）";
            }
            $result['message'] = $message;

        } catch (\Exception $e) {
            // 回滚事务
            Db::rollback();
            $result['success'] = false;
            $result['message'] = '计算失败：' . $e->getMessage();
        }

        return $result;
    }

    /**
     * 获取门店账单数据
     * @param int $storeId 门店ID
     * @param string $period 统计周期（如：2025-04）
     * @return array|null
     */
    private static function getStoreBillData($storeId, $period)
    {
        // 直接使用周期查询，因为sdate_date字段格式为YYYY-MM
        $billData = Db::name('store_bill')
            ->where('did', $storeId)
            ->where('sdate_date', $period)
            ->field([
                'SUM(total_income) as total_income',
                'SUM(total_expenses) as total_expenses',
                'SUM(net_profit) as net_profit'
            ])
            ->find();

        if (empty($billData) || $billData['total_income'] === null) {
            return null;
        }

        return [
            'total_income' => floatval($billData['total_income'] ?? 0),
            'total_expenses' => floatval($billData['total_expenses'] ?? 0),
            'net_profit' => floatval($billData['net_profit'] ?? 0)
        ];
    }

    /**
     * 计算风险金计提
     * @param float $netProfit 净利润
     * @param float $currentRiskReserve 当前已计提风险金
     * @param int $storeId 门店ID
     * @param string $period 统计周期
     * @return float
     */
    private static function calculateRiskReserve($netProfit, $currentRiskReserve, $storeId, $period)
    {
        // 1. 检查是否为新开门店前3个月
        if (self::isNewStoreWithinThreeMonths($storeId, $period)) {
            return 0;
        }

        // 2. 净利润小于1万，不计提
        if ($netProfit < 10000) {
            return 0;
        }

        // 3. 风险金累计金额已达到10万，不再计提
        if ($currentRiskReserve >= 100000) {
            return 0;
        }

        // 4. 净利润≥1万，计提1万元，但不能超过总额10万
        $riskReserveAmount = 10000;

        // 确保计提后不超过10万总额
        if ($currentRiskReserve + $riskReserveAmount > 100000) {
            $riskReserveAmount = 100000 - $currentRiskReserve;
        }

        return $riskReserveAmount;
    }

    /**
     * 检查是否为新开门店前3个月
     * @param int $storeId 门店ID
     * @param string $period 统计周期
     * @return bool
     */
    private static function isNewStoreWithinThreeMonths($storeId, $period)
    {
        // 获取门店第一次有账单数据的时间
        $firstBillDate = Db::name('store_bill')
            ->where('did', $storeId)
            ->where('total_income', '>', 0)
            ->order('sdate_date asc')
            ->value('sdate_date');

        if (empty($firstBillDate)) {
            return true; // 没有账单数据，视为新门店
        }

        // 计算第一次有账单数据到当前周期的月份差
        $firstDate = new \DateTime($firstBillDate);
        $currentDate = new \DateTime($period . '-01');

        $interval = $firstDate->diff($currentDate);
        $monthsDiff = $interval->y * 12 + $interval->m;

        return $monthsDiff < 3;
    }

    /**
     * 计算分红人数据
     * @param int $storeInfoId 门店分红信息ID
     * @param float $dividendProfit 分红利润
     * @param int $detailId 分红汇总表ID
     * @return array
     */
    private static function calculatePersonDividend($storeInfoId, $dividendProfit, $detailId)
    {
        $personData = [];

        // 获取门店分红信息，用于获取公司持股比例
        $storeInfo = DividendStoreInfo::where('id', $storeInfoId)->find();
        if (empty($storeInfo)) {
            return $personData;
        }

        // 1. 添加公司作为独立分红人（如果公司持股比例大于0）
        if ($storeInfo['company_shareholding_ratio'] > 0) {
            $companyPayableAmount = $dividendProfit * ($storeInfo['company_shareholding_ratio'] / 100);

            $companyPersonRecord = [
                'dividend_store_detail_id' => $detailId,
                'shareholder_id' => 0, // 公司分红人ID设为0
                'shareholder_name' => '公司',
                'store_shareholding_ratio' => $storeInfo['company_shareholding_ratio'],
                'shareholder_type' => 1, // 公司股东类型
                'payable_amount' => $companyPayableAmount,
                'other_adjustment' => 0, // 默认为0
                'actual_payable_amount' => $companyPayableAmount, // 默认等于应付金额
                'remark' => '',
                'create_time' => time(),
                'update_time' => time()
            ];

            DividendStoreDetailPerson::insert($companyPersonRecord);
            $personData[] = $companyPersonRecord;
        }

        // 2. 获取个人股东并单独计算
        $personalShareholders = DividendShareholder::where([
            'dividend_store_info_id' => $storeInfoId,
            'shareholder_type' => 2, // 只获取个人股东
            'is_delete' => 0
        ])->select()->toArray();

        foreach ($personalShareholders as $shareholder) {
            // 计算个人股东应付金额
            $payableAmount = $dividendProfit * ($shareholder['store_shareholding_ratio'] / 100);

            $personRecord = [
                'dividend_store_detail_id' => $detailId,
                'shareholder_id' => $shareholder['id'],
                'shareholder_name' => $shareholder['shareholder_name'],
                'store_shareholding_ratio' => $shareholder['store_shareholding_ratio'],
                'shareholder_type' => $shareholder['shareholder_type'],
                'payable_amount' => $payableAmount,
                'other_adjustment' => 0, // 默认为0
                'actual_payable_amount' => $payableAmount, // 默认等于应付金额
                'remark' => '',
                'create_time' => time(),
                'update_time' => time()
            ];

            DividendStoreDetailPerson::insert($personRecord);
            $personData[] = $personRecord;
        }

        return $personData;
    }

    /**
     * 获取门店名称
     * @param int $storeId 门店ID
     * @return string
     */
    private static function getStoreName($storeId)
    {
        $storeName = Db::name('department')
            ->where('id', $storeId)
            ->value('title');

        return $storeName ?: '未知门店';
    }

    /**
     * 获取计算统计信息
     * @param string $period 统计周期
     * @return array
     */
    public static function getCalculateStatistics($period)
    {
        // 统计该周期的分红数据
        $statistics = DividendStoreDetail::alias('dsd')
            ->join('oa_department d', 'd.id = dsd.store_id', 'LEFT')
            ->where('dsd.period', $period)
            ->where('dsd.is_delete', 0)
            ->field([
                'COUNT(dsd.id) as store_count',
                'SUM(dsd.income) as total_income',
                'SUM(dsd.expense) as total_expense',
                'SUM(dsd.risk_reserve_current) as total_risk_reserve_current',
                'SUM(dsd.dividend_profit) as total_dividend_profit'
            ])
            ->find();

        return [
            'period' => $period,
            'store_count' => intval($statistics['store_count'] ?? 0),
            'total_income' => floatval($statistics['total_income'] ?? 0),
            'total_expense' => floatval($statistics['total_expense'] ?? 0),
            'total_risk_reserve_current' => floatval($statistics['total_risk_reserve_current'] ?? 0),
            'total_dividend_profit' => floatval($statistics['total_dividend_profit'] ?? 0)
        ];
    }
}