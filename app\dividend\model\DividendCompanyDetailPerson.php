<?php
/**
 * @copyright Copyright (c) 2021 仲正堂工作室
 * @license https://opensource.org/licenses/GPL-3.0
 * @link https://www.gougucms.com
 */

namespace app\dividend\model;

use think\Model;

class DividendCompanyDetailPerson extends Model
{
    protected $name = 'dividend_company_detail_person';

    // 设置字段信息
    protected $schema = [
        'id'                    => 'int',
        'company_detail_id'     => 'int',
        'shareholder_id'        => 'int',
        'shareholder_name'      => 'string',
        'shareholding_ratio'    => 'decimal',
        'actual_shareholding'   => 'decimal',
        'amount'                => 'decimal',
        'adjustment_amount'     => 'decimal',
        'payable_amount'        => 'decimal',
        'remark'                => 'string',
        'is_delete'             => 'int',
        'delete_time'           => 'int',
        'create_time'           => 'int',
        'update_time'           => 'int',
    ];

    // 自动时间戳
    protected $autoWriteTimestamp = true;
    protected $createTime = 'create_time';
    protected $updateTime = 'update_time';

    /**
     * 关联公司分红明细
     */
    public function companyDetail()
    {
        return $this->belongsTo('app\dividend\model\DividendCompanyDetail', 'company_detail_id', 'id');
    }

    /**
     * 关联股东信息
     */
    public function shareholder()
    {
        return $this->belongsTo('app\dividend\model\DividendShareholder', 'shareholder_id', 'id');
    }

    /**
     * 批量创建分红人数据
     * @param int $companyDetailId 公司分红明细ID
     * @param array $persons 分红人数据
     * @return bool
     */
    public static function batchCreate($companyDetailId, $persons)
    {
        $data = [];
        foreach ($persons as $person) {
            $data[] = [
                'company_detail_id' => $companyDetailId,
                'shareholder_id' => $person['shareholder_id'],
                'shareholder_name' => $person['shareholder_name'],
                'shareholding_ratio' => $person['shareholding_ratio'],
                'actual_shareholding' => $person['actual_shareholding'],
                'amount' => $person['amount'],
                'adjustment_amount' => $person['adjustment_amount'] ?? 0,
                'payable_amount' => $person['payable_amount'],
                'remark' => $person['remark'] ?? '',
                'create_time' => time(),
                'update_time' => time(),
                'is_delete' => 0
            ];
        }

        if (!empty($data)) {
            return self::insertAll($data);
        }

        return true;
    }

    /**
     * 软删除指定公司分红明细的分红人数据
     * @param int $companyDetailId 公司分红明细ID
     * @return bool
     */
    public static function softDeleteByCompanyDetailId($companyDetailId)
    {
        return self::where('company_detail_id', $companyDetailId)
            ->where('is_delete', 0)
            ->update([
                'is_delete' => 1,
                'delete_time' => time(),
                'update_time' => time()
            ]);
    }

    /**
     * 计算分红人应付金额
     * @param float $dividendProfit 分红利润
     * @param float $shareholdingRatio 持股比例
     * @return float
     */
    public static function calculateAmount($dividendProfit, $shareholdingRatio)
    {
        return $dividendProfit * $shareholdingRatio / 100;
    }

    /**
     * 计算应付金额
     * @param float $amount 金额
     * @param float $adjustmentAmount 调整金额
     * @return float
     */
    public static function calculatePayableAmount($amount, $adjustmentAmount = 0)
    {
        return $amount - $adjustmentAmount;
    }

    /**
     * 获取指定公司分红明细的分红人统计数据
     * @param array $companyDetailIds 公司分红明细ID数组
     * @return array
     */
    public static function getPersonStatistics($companyDetailIds)
    {
        if (empty($companyDetailIds)) {
            return [
                'total_amount' => 0,
                'total_adjustment_amount' => 0,
                'total_payable_amount' => 0
            ];
        }

        $statistics = self::where('company_detail_id', 'in', $companyDetailIds)
            ->where('is_delete', 0)
            ->field([
                'SUM(amount) as total_amount',
                'SUM(adjustment_amount) as total_adjustment_amount',
                'SUM(payable_amount) as total_payable_amount'
            ])
            ->find();

        return [
            'total_amount' => floatval($statistics['total_amount'] ?? 0),
            'total_adjustment_amount' => floatval($statistics['total_adjustment_amount'] ?? 0),
            'total_payable_amount' => floatval($statistics['total_payable_amount'] ?? 0)
        ];
    }

    /**
     * 更新分红人数据
     * @param int $companyDetailId 公司分红明细ID
     * @param array $persons 分红人数据
     * @return bool
     */
    public static function updatePersons($companyDetailId, $persons)
    {
        // 先软删除原有数据
        self::softDeleteByCompanyDetailId($companyDetailId);

        // 再批量创建新数据
        return self::batchCreate($companyDetailId, $persons);
    }
}
