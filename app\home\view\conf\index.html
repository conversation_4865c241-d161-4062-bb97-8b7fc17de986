{extend name="../../base/view/common/base" /}
<!-- 主体 -->
{block name="body"}
<div class="p-page">
	<table class="layui-hide" id="conf" lay-filter="conf"></table>
</div>

<script type="text/html" id="status">
	<i class="layui-icon {{#  if(d.status == 1){ }}green layui-icon-ok{{#  } else { }}red layui-icon-close{{#  } }}"></i>
</script>
<script type="text/html" id="toolbarDemo">
  <div class="layui-btn-container">
	<button class="layui-btn layui-btn-sm" lay-event="add">+ 添加配置项</button>
  </div>
</script>
{/block}
<!-- /主体 -->

<!-- 脚本 -->
{block name="script"}
<script>
	const moduleInit = ['tool'];
	function gouguInit() {
		var table = layui.table, tool = layui.tool, form = layui.form;
		layui.pageTable = table.render({
			elem: '#conf',
			title: '配置列表',
			toolbar: '#toolbarDemo',
			defaultToolbar: false,
			url: "/home/<USER>/index",
			cellMinWidth: 80,
			page: false, //开启分页				
			limit: 20,
			cols: [
				[{
					field: 'id',
					width: 80,
					title: 'ID编号',
					align: 'center'
				}, {
					field: 'title',
					width: 200,
					title: '配置名称'
				}, {
					field: 'name',
					title: '配置标识<span class="red">（新增的模板文件名称需与标识名称一致）</span>'
				}, {
					field: 'status',
					width: 80,
					title: '状态',
					templet: '#status',
					align: 'center'
				}, {
					width: 160,
					title: '操作',
					align: 'center',
					templet: function (d) {
						var html = '<div class="layui-btn-group"><button class="layui-btn layui-btn-xs" lay-event="add">修改</button><button class="layui-btn layui-btn-normal layui-btn-xs" lay-event="edit" >编辑配置</button><button class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">删除</button></div>';
						return html;
					}
				}]
			]
		});

		//表头工具栏事件
		table.on('toolbar(conf)', function (obj) {
			if (obj.event === 'add') {
				tool.side("/home/<USER>/add");
				return;
			}
		});

		//监听行工具事件
		table.on('tool(conf)', function (obj) {
			var data = obj.data;
			if (obj.event === 'add') {
				tool.side('/home/<USER>/add?id=' + data.id);
				return;
			}
			if (obj.event === 'edit') {
				tool.side('/home/<USER>/edit?id=' + data.id);
				return;
			}
			if (obj.event === 'del') {
				layer.confirm('确定要删除吗?', {
					icon: 3,
					title: '提示'
				}, function (index) {
					let callback = function (e) {
						layer.msg(e.msg);
						if (e.code == 0) {
							obj.del();
							layer.close(index);
						}
					}
					tool.delete("/home/<USER>/delete", { id: obj.data.id }, callback);
				});
			}
		});
	}
</script>
{/block}
<!-- /脚本 -->