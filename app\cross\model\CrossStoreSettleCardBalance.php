<?php
/**
 * @copyright Copyright (c) 2021 仲正堂工作室
 * @license https://opensource.org/licenses/GPL-3.0
 * @link https://www.gougucms.com
 */

namespace app\cross\model;

use think\Model;
use think\facade\Db;

class CrossStoreSettleCardBalance extends Model
{
    protected $name = 'cross_store_settle_card_balance';

    // 设置字段信息
    protected $schema = [
        'id'                           => 'int',
        'period'                       => 'string',
        'store_id'                     => 'int',
        'card_balance'                 => 'decimal',
        'logical_store_type'           => 'string',
        'previous_month_logical_type'  => 'string',
        'settlement_store_type'        => 'string',
        'create_time'                  => 'int',
        'update_time'                  => 'int',
    ];

    // 自动时间戳
    protected $autoWriteTimestamp = true;
    protected $createTime = 'create_time';
    protected $updateTime = 'update_time';

    /**
     * 关联门店信息
     */
    public function store()
    {
        return $this->belongsTo('app\user\model\Department', 'store_id', 'id');
    }

    /**
     * 获取卡余额列表
     * @param array $where 查询条件
     * @param int $page 页码
     * @param int $limit 每页数量
     * @return array
     */
    public static function getList($where = [], $page = 1, $limit = 20)
    {
        $query = self::alias('cb')
            ->join('oa_department d', 'd.id = cb.store_id', 'LEFT')
            ->field('cb.*, d.title as store_name');

        if (!empty($where)) {
            $query->where($where);
        }

        $list = $query->order('cb.period desc, cb.id desc')
            ->paginate($limit, false, ['page' => $page])
            ->each(function ($item) {
                // 格式化余额显示
                $item['card_balance_formatted'] = number_format(floatval($item['card_balance']), 2);
                
                // 格式化时间
                $item['create_time_formatted'] = date('Y-m-d H:i:s', $item['create_time']);
                $item['update_time_formatted'] = date('Y-m-d H:i:s', $item['update_time']);
                
                return $item;
            });

        return $list;
    }

    /**
     * 获取卡余额详情
     * @param int $id 记录ID
     * @return array
     */
    public static function getDetail($id)
    {
        $detail = self::alias('cb')
            ->join('oa_department d', 'd.id = cb.store_id', 'LEFT')
            ->field('cb.*, d.title as store_name')
            ->where('cb.id', $id)
            ->find();

        if (empty($detail)) {
            return [];
        }

        return $detail;
    }

    /**
     * 检查门店和月份是否已存在
     * @param string $period 月份
     * @param int $storeId 门店ID
     * @param int $excludeId 排除的记录ID
     * @return bool
     */
    public static function checkExists($period, $storeId, $excludeId = 0)
    {
        $where = [
            ['period', '=', $period],
            ['store_id', '=', $storeId]
        ];

        if ($excludeId > 0) {
            $where[] = ['id', '<>', $excludeId];
        }

        $exists = self::where($where)->find();
        return !empty($exists);
    }

    /**
     * 获取门店类型选项
     * @return array
     */
    public static function getStoreTypeOptions()
    {
        return [
            '新店' => '新店',
            '老店' => '老店'
        ];
    }

    /**
     * 根据余额计算逻辑门店类型
     * @param float $cardBalance 卡余额
     * @return string
     */
    public static function calculateLogicalStoreType($cardBalance)
    {
        return floatval($cardBalance) >= 600000 ? '老店' : '新店';
    }

    /**
     * 获取上月记录
     * @param int $storeId 门店ID
     * @param string $currentPeriod 当前月份
     * @return array|null
     */
    public static function getPreviousMonthRecord($storeId, $currentPeriod)
    {
        // 计算上月期间
        $currentDate = \DateTime::createFromFormat('Y-m', $currentPeriod);
        if (!$currentDate) {
            return null;
        }
        
        $currentDate->modify('-1 month');
        $previousPeriod = $currentDate->format('Y-m');
        
        return self::where([
            ['store_id', '=', $storeId],
            ['period', '=', $previousPeriod]
        ])->find();
    }

    /**
     * 计算结算门店类型
     * @param array $previousMonthRecord 上月记录
     * @param string $previousMonthLogicalType 上月逻辑类型
     * @return string
     */
    public static function calculateSettlementStoreType($previousMonthRecord, $previousMonthLogicalType)
    {
        // 规则1：如果上月结算类型是'老店'，则本月继承为'老店'
        if (!empty($previousMonthRecord) && $previousMonthRecord['settlement_store_type'] === '老店') {
            return '老店';
        }
        
        // 规则2：如果上月结算类型是'新店'（或无记录），则本月结算类型等于上月的逻辑类型
        return $previousMonthLogicalType;
    }

    /**
     * 根据门店名称查找门店信息（支持OA门店名称和有赞门店名称）
     * @param string $storeName 门店名称
     * @return array|null 门店信息
     */
    public static function findStoreByName($storeName)
    {
        // 首先尝试直接匹配OA系统中的门店名称
        $store = Db::name('Department')
            ->where('title', $storeName)
            ->where('status', 1)
            ->where('remark', '门店')
            ->find();

        if (!empty($store)) {
            return $store;
        }

        // 如果直接匹配失败，则通过门店名称映射表查找
        $mapping = Db::name('store_name_mapping')
            ->alias('snm')
            ->join('oa_department d', 'd.id = snm.department_id', 'LEFT')
            ->where('snm.status', 1)
            ->where('d.status', 1)
            ->where('d.remark', '门店')
            ->where(function ($query) use ($storeName) {
                $query->whereOr([
                    ['snm.oa_name', '=', $storeName],
                    ['snm.youzan_name', '=', $storeName],
                    ['snm.reconciliation_table', '=', $storeName],
                    ['snm.terminal_match', '=', $storeName],
                    ['snm.boss_account', '=', $storeName],
                    ['snm.douyin_verification', '=', $storeName],
                    ['snm.cross_store_settlement', '=', $storeName],
                    ['snm.reconciliation_table2', '=', $storeName],
                    ['snm.store_dividend', '=', $storeName],
                    ['snm.data_framework', '=', $storeName],
                    ['snm.meituan_name', '=', $storeName],
                    ['snm.purchase_system', '=', $storeName],
                    ['snm.dianping_name', '=', $storeName],
                    ['snm.douyin_ad', '=', $storeName],
                    ['snm.visitor_collection', '=', $storeName]
                ]);
            })
            ->field('d.id, d.title, d.status, d.remark')
            ->find();

        if (!empty($mapping)) {
            return $mapping;
        }

        return null;
    }

    /**
     * 批量导入卡余额数据
     * @param string $period 月份
     * @param array $data 数据数组，格式：[['store_name' => '门店名称', 'card_balance' => 余额], ...]
     * @return array 导入结果
     */
    public static function batchImport($period, $data)
    {
        $successCount = 0;
        $failCount = 0;
        $errors = [];

        Db::startTrans();
        try {
            foreach ($data as $index => $row) {
                $storeName = trim($row['store_name'] ?? '');
                $cardBalance = floatval($row['card_balance'] ?? 0);

                if (empty($storeName)) {
                    $errors[] = "第" . ($index + 1) . "行：门店名称不能为空";
                    $failCount++;
                    continue;
                }

                // 根据门店名称查找门店ID（支持OA门店名称和有赞门店名称）
                $store = self::findStoreByName($storeName);

                if (empty($store)) {
                    $errors[] = "第" . ($index + 1) . "行：门店「{$storeName}」不存在或已停用（请使用OA门店名称或有赞门店名称）";
                    $failCount++;
                    continue;
                }

                $storeId = $store['id'];

                // 检查是否已存在
                if (self::checkExists($period, $storeId)) {
                    $errors[] = "第" . ($index + 1) . "行：门店「{$storeName}」在{$period}月份已存在记录";
                    $failCount++;
                    continue;
                }

                // 计算逻辑门店类型
                $logicalStoreType = self::calculateLogicalStoreType($cardBalance);

                // 获取上月记录
                $previousMonthRecord = self::getPreviousMonthRecord($storeId, $period);
                $previousMonthLogicalType = !empty($previousMonthRecord) 
                    ? $previousMonthRecord['logical_store_type'] 
                    : '新店';

                // 计算结算门店类型
                $settlementStoreType = self::calculateSettlementStoreType($previousMonthRecord, $previousMonthLogicalType);

                // 插入数据
                $insertData = [
                    'period' => $period,
                    'store_id' => $storeId,
                    'card_balance' => $cardBalance,
                    'logical_store_type' => $logicalStoreType,
                    'previous_month_logical_type' => $previousMonthLogicalType,
                    'settlement_store_type' => $settlementStoreType,
                    'create_time' => time(),
                    'update_time' => time()
                ];

                $result = self::create($insertData);
                if ($result) {
                    $successCount++;
                } else {
                    $errors[] = "第" . ($index + 1) . "行：门店「{$storeName}」数据保存失败";
                    $failCount++;
                }
            }

            Db::commit();
        } catch (\Exception $e) {
            Db::rollback();
            $errors[] = "导入过程中发生错误：" . $e->getMessage();
            return [
                'success' => false,
                'message' => '导入失败',
                'success_count' => 0,
                'fail_count' => count($data),
                'errors' => $errors
            ];
        }

        return [
            'success' => $failCount === 0,
            'message' => $failCount === 0 ? '导入成功' : '部分导入失败',
            'success_count' => $successCount,
            'fail_count' => $failCount,
            'errors' => $errors
        ];
    }

    /**
     * 获取统计汇总数据
     * @param array $where 查询条件
     * @return array
     */
    public static function getSummaryData($where = [])
    {
        $query = self::where($where);

        $summaryData = $query->field([
            'SUM(card_balance) as total_balance',
            'COUNT(*) as total_count',
            'COUNT(CASE WHEN logical_store_type = "老店" THEN 1 END) as logical_old_store_count',
            'COUNT(CASE WHEN logical_store_type = "新店" THEN 1 END) as logical_new_store_count',
            'COUNT(CASE WHEN settlement_store_type = "老店" THEN 1 END) as settlement_old_store_count',
            'COUNT(CASE WHEN settlement_store_type = "新店" THEN 1 END) as settlement_new_store_count'
        ])->find();

        return [
            'total_balance' => number_format(floatval($summaryData['total_balance'] ?? 0), 2),
            'total_count' => intval($summaryData['total_count'] ?? 0),
            'logical_old_store_count' => intval($summaryData['logical_old_store_count'] ?? 0),
            'logical_new_store_count' => intval($summaryData['logical_new_store_count'] ?? 0),
            'settlement_old_store_count' => intval($summaryData['settlement_old_store_count'] ?? 0),
            'settlement_new_store_count' => intval($summaryData['settlement_new_store_count'] ?? 0)
        ];
    }
}