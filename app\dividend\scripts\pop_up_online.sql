CREATE TABLE `oa_dividend_company_detail` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `store_id` int unsigned NOT NULL DEFAULT '0' COMMENT '门店ID（关联oa_department表）',
  `period` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '统计周期（如：2024-05）',
  `dividend_profit` decimal(15,2) NOT NULL DEFAULT '0.00' COMMENT '分红利润（元）',
  `company_shareholding_ratio` decimal(6,3) NOT NULL DEFAULT '0.000' COMMENT '公司持股门店比例（%，保留三位小数）',
  `remark` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '备注',
  `is_delete` tinyint(1) NOT NULL DEFAULT '0' COMMENT '删除标记(0:正常,1:已删除)',
  `delete_time` int NOT NULL DEFAULT '0' COMMENT '删除时间',
  `create_time` int NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` int NOT NULL DEFAULT '0' COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uk_store_period` (`store_id`,`period`) USING BTREE COMMENT '门店周期唯一索引',
  KEY `idx_store_id` (`store_id`) USING BTREE COMMENT '门店ID索引',
  KEY `idx_period` (`period`) USING BTREE COMMENT '统计周期索引',
  KEY `idx_is_delete` (`is_delete`) USING BTREE COMMENT '删除标记索引',
  KEY `idx_create_time` (`create_time`) USING BTREE COMMENT '创建时间索引',
  KEY `idx_store_delete` (`store_id`,`is_delete`) USING BTREE COMMENT '门店删除状态组合索引'
) ENGINE=InnoDB AUTO_INCREMENT=134 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='公司股东分红明细主表';


CREATE TABLE `oa_dividend_company_detail_person` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `company_detail_id` int unsigned NOT NULL DEFAULT '0' COMMENT '公司股东分红明细主表ID（关联oa_dividend_company_detail表）',
  `shareholder_id` int unsigned NOT NULL DEFAULT '0' COMMENT '分红人ID（关联oa_dividend_shareholder表）',
  `shareholder_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '分红人名称',
  `shareholding_ratio` decimal(6,3) NOT NULL DEFAULT '0.000' COMMENT '持股比例（%，保留三位小数）',
  `actual_shareholding` decimal(6,3) NOT NULL DEFAULT '0.000' COMMENT '实际持股（%，保留三位小数）',
  `amount` decimal(15,2) NOT NULL DEFAULT '0.00' COMMENT '金额（元）',
  `adjustment_amount` decimal(15,2) NOT NULL DEFAULT '0.00' COMMENT '调整金额（元）',
  `payable_amount` decimal(15,2) NOT NULL DEFAULT '0.00' COMMENT '应付金额（元）',
  `remark` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '备注',
  `is_delete` tinyint(1) NOT NULL DEFAULT '0' COMMENT '删除标记(0:正常,1:已删除)',
  `delete_time` int NOT NULL DEFAULT '0' COMMENT '删除时间',
  `create_time` int NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` int NOT NULL DEFAULT '0' COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_company_detail_id` (`company_detail_id`) USING BTREE COMMENT '公司分红明细表ID索引',
  KEY `idx_shareholder_id` (`shareholder_id`) USING BTREE COMMENT '分红人ID索引',
  KEY `idx_is_delete` (`is_delete`) USING BTREE COMMENT '删除标记索引',
  KEY `idx_company_delete` (`company_detail_id`,`is_delete`) USING BTREE COMMENT '公司分红明细删除状态组合索引'
) ENGINE=InnoDB AUTO_INCREMENT=320 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='公司股东分红人子表';


CREATE TABLE `oa_dividend_payment` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `shareholder_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '分红人姓名',
  `shareholder_ids` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '分红人ID列表（多个ID用逗号分隔）',
  `period` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '月份（如：2024-05）',
  `payable_amount` decimal(15,2) NOT NULL DEFAULT '0.00' COMMENT '应付金额（元）',
  `adjustment_amount` decimal(15,2) NOT NULL DEFAULT '0.00' COMMENT '调整金额（元）',
  `actual_payable_amount` decimal(15,2) NOT NULL DEFAULT '0.00' COMMENT '实际应付金额（元）',
  `paid_amount` decimal(15,2) NOT NULL DEFAULT '0.00' COMMENT '实付金额（元）',
  `unpaid_amount` decimal(15,2) NOT NULL DEFAULT '0.00' COMMENT '未付金额（元）',
  `remark` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '备注信息',
  `is_delete` tinyint(1) NOT NULL DEFAULT '0' COMMENT '删除标记(0:正常,1:已删除)',
  `delete_time` int NOT NULL DEFAULT '0' COMMENT '删除时间',
  `create_time` int NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` int NOT NULL DEFAULT '0' COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uk_shareholder_period` (`shareholder_name`,`period`) USING BTREE COMMENT '分红人月份唯一索引',
  KEY `idx_period` (`period`) USING BTREE COMMENT '月份索引',
  KEY `idx_is_delete` (`is_delete`) USING BTREE COMMENT '删除标记索引',
  KEY `idx_create_time` (`create_time`) USING BTREE COMMENT '创建时间索引'
) ENGINE=InnoDB AUTO_INCREMENT=198 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='股东分红清单表';


CREATE TABLE `oa_dividend_risk_reserve_log` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `store_id` int NOT NULL COMMENT '门店ID（关联oa_department表）',
  `dividend_store_info_id` int NOT NULL COMMENT '关联分红配置ID（oa_dividend_store_info表）',
  `change_type` tinyint(1) NOT NULL COMMENT '变化类型：1=计提,2=调整,3=初始化',
  `before_amount` decimal(15,2) NOT NULL DEFAULT '0.00' COMMENT '变化前金额',
  `change_amount` decimal(15,2) NOT NULL DEFAULT '0.00' COMMENT '变化金额（正数为增加，负数为减少）',
  `after_amount` decimal(15,2) NOT NULL DEFAULT '0.00' COMMENT '变化后金额',
  `dividend_detail_id` int DEFAULT NULL COMMENT '关联分红明细ID（oa_dividend_store_detail表，仅计提时有值）',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注说明',
  `admin_id` int NOT NULL COMMENT '操作人ID',
  `create_time` int NOT NULL COMMENT '创建时间',
  `update_time` int NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_dividend_store_info` (`dividend_store_info_id`),
  KEY `idx_store_id` (`store_id`),
  KEY `idx_change_type` (`change_type`),
  KEY `idx_dividend_detail` (`dividend_detail_id`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB AUTO_INCREMENT=75 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='门店风险金变化记录表';


CREATE TABLE `oa_dividend_shareholder` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `dividend_store_info_id` int unsigned NOT NULL DEFAULT '0' COMMENT '关联门店分红数据主表ID',
  `admin_id` int unsigned DEFAULT NULL COMMENT '关联人员ID（关联oa_admin表，非必填）',
  `shareholder_name` varchar(50) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '股东姓名',
  `shareholder_type` tinyint(1) NOT NULL DEFAULT '1' COMMENT '股东类型(1:公司股东,2:个人股东)',
  `company_shareholding_ratio` decimal(6,3) DEFAULT NULL COMMENT '公司内部持股比例（%，仅公司股东有值，保留三位小数）',
  `store_shareholding_ratio` decimal(6,3) NOT NULL COMMENT '门店持股比例（%，保留三位小数）',
  `sort_order` int NOT NULL DEFAULT '0' COMMENT '排序字段',
  `remark` varchar(500) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '备注信息',
  `is_delete` tinyint(1) NOT NULL DEFAULT '0' COMMENT '删除标记(0:正常,1:已删除)',
  `delete_time` int NOT NULL DEFAULT '0' COMMENT '删除时间',
  `create_time` int NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` int NOT NULL DEFAULT '0' COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_dividend_store_info_id` (`dividend_store_info_id`) USING BTREE COMMENT '门店分红数据主表ID索引',
  KEY `idx_admin_id` (`admin_id`) USING BTREE COMMENT '关联人员ID索引',
  KEY `idx_shareholder_type` (`shareholder_type`) USING BTREE COMMENT '股东类型索引',
  KEY `idx_is_delete` (`is_delete`) USING BTREE COMMENT '删除标记索引',
  KEY `idx_dividend_type_delete` (`dividend_store_info_id`,`shareholder_type`,`is_delete`),
  KEY `idx_dividend_delete` (`dividend_store_info_id`,`is_delete`)
) ENGINE=InnoDB AUTO_INCREMENT=241 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='分红股东持股子表';


CREATE TABLE `oa_dividend_store_detail` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `store_id` int unsigned NOT NULL DEFAULT '0' COMMENT '门店ID（关联oa_department表）',
  `period` varchar(20) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '统计周期（如：2024-05）',
  `income` decimal(15,2) NOT NULL DEFAULT '0.00' COMMENT '收入（元）',
  `expense` decimal(15,2) NOT NULL DEFAULT '0.00' COMMENT '支出（元）',
  `risk_reserve_current` decimal(15,2) NOT NULL DEFAULT '0.00' COMMENT '风险金计提（元）',
  `other_adjustment` decimal(15,2) NOT NULL DEFAULT '0.00' COMMENT '其他调整（元）',
  `dividend_profit` decimal(15,2) NOT NULL DEFAULT '0.00' COMMENT '分红利润（元）',
  `risk_reserve_total` decimal(15,2) NOT NULL DEFAULT '0.00' COMMENT '风险金总金额已计提（元）',
  `company_shareholding_ratio` decimal(5,2) NOT NULL DEFAULT '0.00' COMMENT '公司持股门店比例（%，保留两位小数）',
  `remark` text COLLATE utf8mb4_general_ci COMMENT '备注信息',
  `is_delete` tinyint(1) NOT NULL DEFAULT '0' COMMENT '删除标记(0:正常,1:已删除)',
  `delete_time` int NOT NULL DEFAULT '0' COMMENT '删除时间',
  `create_time` int NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` int NOT NULL DEFAULT '0' COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uk_store_period` (`store_id`,`period`) USING BTREE COMMENT '门店周期唯一索引',
  KEY `idx_store_id` (`store_id`) USING BTREE COMMENT '门店ID索引',
  KEY `idx_period` (`period`) USING BTREE COMMENT '统计周期索引',
  KEY `idx_is_delete` (`is_delete`) USING BTREE COMMENT '删除标记索引',
  KEY `idx_create_time` (`create_time`) USING BTREE COMMENT '创建时间索引',
  KEY `idx_store_delete` (`store_id`,`is_delete`) USING BTREE COMMENT '门店删除状态组合索引'
) ENGINE=InnoDB AUTO_INCREMENT=184 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='门店分红明细主表';


CREATE TABLE `oa_dividend_store_detail_person` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `dividend_store_detail_id` int unsigned NOT NULL DEFAULT '0' COMMENT '门店分红明细表ID（关联oa_dividend_store_detail表）',
  `shareholder_id` int unsigned NOT NULL DEFAULT '0' COMMENT '分红人ID（关联oa_dividend_shareholder表）',
  `shareholder_name` varchar(50) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '分红人名称',
  `store_shareholding_ratio` decimal(6,3) NOT NULL COMMENT '持股比例（%，保留三位小数）',
  `shareholder_type` tinyint(1) NOT NULL DEFAULT '1' COMMENT '股东类型(1:公司股东,2:个人股东)',
  `payable_amount` decimal(15,2) NOT NULL DEFAULT '0.00' COMMENT '应付金额（元）',
  `other_adjustment` decimal(15,2) NOT NULL DEFAULT '0.00' COMMENT '其他调整',
  `actual_payable_amount` decimal(15,2) NOT NULL DEFAULT '0.00' COMMENT '实际应付金额',
  `is_delete` tinyint(1) NOT NULL DEFAULT '0' COMMENT '删除标记(0:正常,1:已删除)',
  `delete_time` int NOT NULL DEFAULT '0' COMMENT '删除时间',
  `create_time` int NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` int NOT NULL DEFAULT '0' COMMENT '更新时间',
  `remark` text COLLATE utf8mb4_general_ci COMMENT '备注信息',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_dividend_store_id` (`dividend_store_detail_id`) USING BTREE COMMENT '门店分红汇总表ID索引',
  KEY `idx_shareholder_id` (`shareholder_id`) USING BTREE COMMENT '分红人ID索引',
  KEY `idx_shareholder_type` (`shareholder_type`) USING BTREE COMMENT '股东类型索引',
  KEY `idx_is_delete` (`is_delete`) USING BTREE COMMENT '删除标记索引',
  KEY `idx_store_delete` (`dividend_store_detail_id`,`is_delete`) USING BTREE COMMENT '汇总表删除状态组合索引',
  KEY `idx_store_type_delete` (`dividend_store_detail_id`,`shareholder_type`,`is_delete`) USING BTREE COMMENT '汇总表股东类型删除状态组合索引'
) ENGINE=InnoDB AUTO_INCREMENT=655 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='门店分红人子表';


CREATE TABLE `oa_dividend_store_info` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `store_id` int unsigned NOT NULL DEFAULT '0' COMMENT '关联的门店ID（关联oa_department表）',
  `risk_reserve` decimal(15,2) NOT NULL DEFAULT '0.00' COMMENT '已计提风险金（元）',
  `company_shareholding_ratio` decimal(6,3) NOT NULL DEFAULT '0.000' COMMENT '公司股东持股比例（%，保留三位小数）',
  `remark` text COLLATE utf8mb4_general_ci COMMENT '备注信息',
  `is_delete` tinyint(1) NOT NULL DEFAULT '0' COMMENT '删除标记(0:正常,1:已删除)',
  `delete_time` int NOT NULL DEFAULT '0' COMMENT '删除时间',
  `create_time` int NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` int NOT NULL DEFAULT '0' COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uk_store_id` (`store_id`) USING BTREE COMMENT '门店ID唯一索引',
  KEY `idx_is_delete` (`is_delete`) USING BTREE COMMENT '删除标记索引',
  KEY `idx_create_time` (`create_time`) USING BTREE COMMENT '创建时间索引',
  KEY `idx_store_id_delete` (`store_id`,`is_delete`),
  KEY `idx_delete_id` (`is_delete`,`id`)
) ENGINE=InnoDB AUTO_INCREMENT=14 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='门店分红信息主表';


-- 下面是2025年5月份数据-财务提供


-- =================================================================================================
-- 重要提示:
-- 1. 请务必在执行前备份相关表格！
-- 2. 建议在事务中执行所有插入语句。
-- 3. 以下脚本假设 `oa_dividend_store_info` 表的主键 `id` 会从 1 开始连续自增。
--    如果你的表不是空的，或自增ID不连续，执行此脚本可能会因主键冲突而失败。
--    在这种情况下，请先清空目标表，或手动调整脚本中的主键ID。
-- =================================================================================================

-- [1] 门店分红信息主表 (oa_dividend_store_info)
INSERT INTO `oa_dividend_store_info` (`id`, `store_id`, `risk_reserve`, `company_shareholding_ratio`, `remark`, `is_delete`, `delete_time`, `create_time`, `update_time`) VALUES (1, 74, 100000.00, 40.000, NULL, 0, 0, 0, 0);
INSERT INTO `oa_dividend_store_info` (`id`, `store_id`, `risk_reserve`, `company_shareholding_ratio`, `remark`, `is_delete`, `delete_time`, `create_time`, `update_time`) VALUES (2, 65, 10000.00, 50.000, NULL, 0, 0, 0, 0);
INSERT INTO `oa_dividend_store_info` (`id`, `store_id`, `risk_reserve`, `company_shareholding_ratio`, `remark`, `is_delete`, `delete_time`, `create_time`, `update_time`) VALUES (3, 96, 20000.00, 65.000, NULL, 0, 0, 0, 0);
INSERT INTO `oa_dividend_store_info` (`id`, `store_id`, `risk_reserve`, `company_shareholding_ratio`, `remark`, `is_delete`, `delete_time`, `create_time`, `update_time`) VALUES (4, 54, 100000.00, 55.000, NULL, 0, 0, 0, 0);
INSERT INTO `oa_dividend_store_info` (`id`, `store_id`, `risk_reserve`, `company_shareholding_ratio`, `remark`, `is_delete`, `delete_time`, `create_time`, `update_time`) VALUES (5, 64, 100000.00, 55.000, NULL, 0, 0, 0, 0);
INSERT INTO `oa_dividend_store_info` (`id`, `store_id`, `risk_reserve`, `company_shareholding_ratio`, `remark`, `is_delete`, `delete_time`, `create_time`, `update_time`) VALUES (6, 58, 100000.00, 60.000, NULL, 0, 0, 0, 0);
INSERT INTO `oa_dividend_store_info` (`id`, `store_id`, `risk_reserve`, `company_shareholding_ratio`, `remark`, `is_delete`, `delete_time`, `create_time`, `update_time`) VALUES (7, 84, 100000.00, 55.000, NULL, 0, 0, 0, 0);
INSERT INTO `oa_dividend_store_info` (`id`, `store_id`, `risk_reserve`, `company_shareholding_ratio`, `remark`, `is_delete`, `delete_time`, `create_time`, `update_time`) VALUES (8, 67, 100000.00, 55.000, NULL, 0, 0, 0, 0);
INSERT INTO `oa_dividend_store_info` (`id`, `store_id`, `risk_reserve`, `company_shareholding_ratio`, `remark`, `is_delete`, `delete_time`, `create_time`, `update_time`) VALUES (9, 47, 100000.00, 45.000, NULL, 0, 0, 0, 0);
INSERT INTO `oa_dividend_store_info` (`id`, `store_id`, `risk_reserve`, `company_shareholding_ratio`, `remark`, `is_delete`, `delete_time`, `create_time`, `update_time`) VALUES (10, 70, 100000.00, 70.000, NULL, 0, 0, 0, 0);
INSERT INTO `oa_dividend_store_info` (`id`, `store_id`, `risk_reserve`, `company_shareholding_ratio`, `remark`, `is_delete`, `delete_time`, `create_time`, `update_time`) VALUES (11, 76, 100000.00, 55.000, NULL, 0, 0, 0, 0);
INSERT INTO `oa_dividend_store_info` (`id`, `store_id`, `risk_reserve`, `company_shareholding_ratio`, `remark`, `is_delete`, `delete_time`, `create_time`, `update_time`) VALUES (12, 63, 50000.00, 75.000, NULL, 0, 0, 0, 0);
INSERT INTO `oa_dividend_store_info` (`id`, `store_id`, `risk_reserve`, `company_shareholding_ratio`, `remark`, `is_delete`, `delete_time`, `create_time`, `update_time`) VALUES (13, 53, 100000.00, 45.000, NULL, 0, 0, 0, 0);
INSERT INTO `oa_dividend_store_info` (`id`, `store_id`, `risk_reserve`, `company_shareholding_ratio`, `remark`, `is_delete`, `delete_time`, `create_time`, `update_time`) VALUES (14, 46, 80000.00, 55.000, NULL, 0, 0, 0, 0);
INSERT INTO `oa_dividend_store_info` (`id`, `store_id`, `risk_reserve`, `company_shareholding_ratio`, `remark`, `is_delete`, `delete_time`, `create_time`, `update_time`) VALUES (15, 59, 80000.00, 65.000, NULL, 0, 0, 0, 0);
INSERT INTO `oa_dividend_store_info` (`id`, `store_id`, `risk_reserve`, `company_shareholding_ratio`, `remark`, `is_delete`, `delete_time`, `create_time`, `update_time`) VALUES (16, 44, 80000.00, 70.000, NULL, 0, 0, 0, 0);
INSERT INTO `oa_dividend_store_info` (`id`, `store_id`, `risk_reserve`, `company_shareholding_ratio`, `remark`, `is_delete`, `delete_time`, `create_time`, `update_time`) VALUES (17, 104, 0.00, 60.000, NULL, 0, 0, 0, 0);
INSERT INTO `oa_dividend_store_info` (`id`, `store_id`, `risk_reserve`, `company_shareholding_ratio`, `remark`, `is_delete`, `delete_time`, `create_time`, `update_time`) VALUES (18, 80, 100000.00, 50.000, NULL, 0, 0, 0, 0);
INSERT INTO `oa_dividend_store_info` (`id`, `store_id`, `risk_reserve`, `company_shareholding_ratio`, `remark`, `is_delete`, `delete_time`, `create_time`, `update_time`) VALUES (19, 73, 100000.00, 55.000, NULL, 0, 0, 0, 0);
INSERT INTO `oa_dividend_store_info` (`id`, `store_id`, `risk_reserve`, `company_shareholding_ratio`, `remark`, `is_delete`, `delete_time`, `create_time`, `update_time`) VALUES (20, 69, 80000.00, 60.000, NULL, 0, 0, 0, 0);
INSERT INTO `oa_dividend_store_info` (`id`, `store_id`, `risk_reserve`, `company_shareholding_ratio`, `remark`, `is_delete`, `delete_time`, `create_time`, `update_time`) VALUES (21, 55, 100000.00, 55.000, NULL, 0, 0, 0, 0);
INSERT INTO `oa_dividend_store_info` (`id`, `store_id`, `risk_reserve`, `company_shareholding_ratio`, `remark`, `is_delete`, `delete_time`, `create_time`, `update_time`) VALUES (22, 105, 0.00, 50.000, NULL, 0, 0, 0, 0);
INSERT INTO `oa_dividend_store_info` (`id`, `store_id`, `risk_reserve`, `company_shareholding_ratio`, `remark`, `is_delete`, `delete_time`, `create_time`, `update_time`) VALUES (23, 60, 100000.00, 55.000, NULL, 0, 0, 0, 0);
INSERT INTO `oa_dividend_store_info` (`id`, `store_id`, `risk_reserve`, `company_shareholding_ratio`, `remark`, `is_delete`, `delete_time`, `create_time`, `update_time`) VALUES (24, 43, 100000.00, 60.000, NULL, 0, 0, 0, 0);
INSERT INTO `oa_dividend_store_info` (`id`, `store_id`, `risk_reserve`, `company_shareholding_ratio`, `remark`, `is_delete`, `delete_time`, `create_time`, `update_time`) VALUES (25, 98, 30000.00, 55.000, NULL, 0, 0, 0, 0);
INSERT INTO `oa_dividend_store_info` (`id`, `store_id`, `risk_reserve`, `company_shareholding_ratio`, `remark`, `is_delete`, `delete_time`, `create_time`, `update_time`) VALUES (26, 52, 80000.00, 45.000, NULL, 0, 0, 0, 0);
INSERT INTO `oa_dividend_store_info` (`id`, `store_id`, `risk_reserve`, `company_shareholding_ratio`, `remark`, `is_delete`, `delete_time`, `create_time`, `update_time`) VALUES (27, 71, 100000.00, 60.000, NULL, 0, 0, 0, 0);
INSERT INTO `oa_dividend_store_info` (`id`, `store_id`, `risk_reserve`, `company_shareholding_ratio`, `remark`, `is_delete`, `delete_time`, `create_time`, `update_time`) VALUES (28, 85, 100000.00, 60.000, NULL, 0, 0, 0, 0);
INSERT INTO `oa_dividend_store_info` (`id`, `store_id`, `risk_reserve`, `company_shareholding_ratio`, `remark`, `is_delete`, `delete_time`, `create_time`, `update_time`) VALUES (29, 77, 80000.00, 70.000, NULL, 0, 0, 0, 0);
INSERT INTO `oa_dividend_store_info` (`id`, `store_id`, `risk_reserve`, `company_shareholding_ratio`, `remark`, `is_delete`, `delete_time`, `create_time`, `update_time`) VALUES (30, 62, 100000.00, 55.000, NULL, 0, 0, 0, 0);
INSERT INTO `oa_dividend_store_info` (`id`, `store_id`, `risk_reserve`, `company_shareholding_ratio`, `remark`, `is_delete`, `delete_time`, `create_time`, `update_time`) VALUES (31, 68, 80000.00, 45.000, NULL, 0, 0, 0, 0);
INSERT INTO `oa_dividend_store_info` (`id`, `store_id`, `risk_reserve`, `company_shareholding_ratio`, `remark`, `is_delete`, `delete_time`, `create_time`, `update_time`) VALUES (32, 40, 100000.00, 60.000, NULL, 0, 0, 0, 0);
INSERT INTO `oa_dividend_store_info` (`id`, `store_id`, `risk_reserve`, `company_shareholding_ratio`, `remark`, `is_delete`, `delete_time`, `create_time`, `update_time`) VALUES (33, 79, 100000.00, 60.000, NULL, 0, 0, 0, 0);
INSERT INTO `oa_dividend_store_info` (`id`, `store_id`, `risk_reserve`, `company_shareholding_ratio`, `remark`, `is_delete`, `delete_time`, `create_time`, `update_time`) VALUES (34, 97, 30000.00, 55.000, NULL, 0, 0, 0, 0);
INSERT INTO `oa_dividend_store_info` (`id`, `store_id`, `risk_reserve`, `company_shareholding_ratio`, `remark`, `is_delete`, `delete_time`, `create_time`, `update_time`) VALUES (35, 57, 100000.00, 55.000, NULL, 0, 0, 0, 0);
INSERT INTO `oa_dividend_store_info` (`id`, `store_id`, `risk_reserve`, `company_shareholding_ratio`, `remark`, `is_delete`, `delete_time`, `create_time`, `update_time`) VALUES (36, 51, 100000.00, 55.000, NULL, 0, 0, 0, 0);
INSERT INTO `oa_dividend_store_info` (`id`, `store_id`, `risk_reserve`, `company_shareholding_ratio`, `remark`, `is_delete`, `delete_time`, `create_time`, `update_time`) VALUES (37, 41, 50000.00, 50.000, NULL, 0, 0, 0, 0);
INSERT INTO `oa_dividend_store_info` (`id`, `store_id`, `risk_reserve`, `company_shareholding_ratio`, `remark`, `is_delete`, `delete_time`, `create_time`, `update_time`) VALUES (38, 49, 100000.00, 65.000, NULL, 0, 0, 0, 0);
INSERT INTO `oa_dividend_store_info` (`id`, `store_id`, `risk_reserve`, `company_shareholding_ratio`, `remark`, `is_delete`, `delete_time`, `create_time`, `update_time`) VALUES (39, 61, 100000.00, 60.000, NULL, 0, 0, 0, 0);
INSERT INTO `oa_dividend_store_info` (`id`, `store_id`, `risk_reserve`, `company_shareholding_ratio`, `remark`, `is_delete`, `delete_time`, `create_time`, `update_time`) VALUES (40, 42, 100000.00, 60.000, NULL, 0, 0, 0, 0);
INSERT INTO `oa_dividend_store_info` (`id`, `store_id`, `risk_reserve`, `company_shareholding_ratio`, `remark`, `is_delete`, `delete_time`, `create_time`, `update_time`) VALUES (41, 78, 100000.00, 35.000, NULL, 0, 0, 0, 0);
INSERT INTO `oa_dividend_store_info` (`id`, `store_id`, `risk_reserve`, `company_shareholding_ratio`, `remark`, `is_delete`, `delete_time`, `create_time`, `update_time`) VALUES (42, 56, 100000.00, 55.000, NULL, 0, 0, 0, 0);
INSERT INTO `oa_dividend_store_info` (`id`, `store_id`, `risk_reserve`, `company_shareholding_ratio`, `remark`, `is_delete`, `delete_time`, `create_time`, `update_time`) VALUES (43, 50, 100000.00, 65.000, NULL, 0, 0, 0, 0);
INSERT INTO `oa_dividend_store_info` (`id`, `store_id`, `risk_reserve`, `company_shareholding_ratio`, `remark`, `is_delete`, `delete_time`, `create_time`, `update_time`) VALUES (44, 86, 50000.00, 65.000, NULL, 0, 0, 0, 0);
INSERT INTO `oa_dividend_store_info` (`id`, `store_id`, `risk_reserve`, `company_shareholding_ratio`, `remark`, `is_delete`, `delete_time`, `create_time`, `update_time`) VALUES (45, 45, 100000.00, 55.000, NULL, 0, 0, 0, 0);
INSERT INTO `oa_dividend_store_info` (`id`, `store_id`, `risk_reserve`, `company_shareholding_ratio`, `remark`, `is_delete`, `delete_time`, `create_time`, `update_time`) VALUES (46, 75, 100000.00, 60.000, NULL, 0, 0, 0, 0);
INSERT INTO `oa_dividend_store_info` (`id`, `store_id`, `risk_reserve`, `company_shareholding_ratio`, `remark`, `is_delete`, `delete_time`, `create_time`, `update_time`) VALUES (47, 95, 0.00, 60.000, NULL, 0, 0, 0, 0);
INSERT INTO `oa_dividend_store_info` (`id`, `store_id`, `risk_reserve`, `company_shareholding_ratio`, `remark`, `is_delete`, `delete_time`, `create_time`, `update_time`) VALUES (48, 81, 100000.00, 60.000, NULL, 0, 0, 0, 0);
INSERT INTO `oa_dividend_store_info` (`id`, `store_id`, `risk_reserve`, `company_shareholding_ratio`, `remark`, `is_delete`, `delete_time`, `create_time`, `update_time`) VALUES (49, 66, 100000.00, 65.000, NULL, 0, 0, 0, 0);
INSERT INTO `oa_dividend_store_info` (`id`, `store_id`, `risk_reserve`, `company_shareholding_ratio`, `remark`, `is_delete`, `delete_time`, `create_time`, `update_time`) VALUES (50, 72, 70000.00, 70.000, NULL, 0, 0, 0, 0);
INSERT INTO `oa_dividend_store_info` (`id`, `store_id`, `risk_reserve`, `company_shareholding_ratio`, `remark`, `is_delete`, `delete_time`, `create_time`, `update_time`) VALUES (51, 48, 100000.00, 55.000, NULL, 0, 0, 0, 0);
INSERT INTO `oa_dividend_store_info` (`id`, `store_id`, `risk_reserve`, `company_shareholding_ratio`, `remark`, `is_delete`, `delete_time`, `create_time`, `update_time`) VALUES (52, 91, 70000.00, 55.000, NULL, 0, 0, 0, 0);
INSERT INTO `oa_dividend_store_info` (`id`, `store_id`, `risk_reserve`, `company_shareholding_ratio`, `remark`, `is_delete`, `delete_time`, `create_time`, `update_time`) VALUES (53, 92, 30000.00, 60.000, NULL, 0, 0, 0, 0);

-- [2] 分红股东持股子表 (oa_dividend_shareholder)
-- 七宝店 的股东信息 (dividend_store_info_id = 1)
INSERT INTO `oa_dividend_shareholder` (`dividend_store_info_id`, `admin_id`, `shareholder_name`, `shareholder_type`, `company_shareholding_ratio`, `store_shareholding_ratio`, `sort_order`, `remark`, `is_delete`, `delete_time`, `create_time`, `update_time`) VALUES
(1, NULL, '孙艳东', 1, 52.500, 21.000, 1, NULL, 0, 0, 0, 0),
(1, NULL, '胡鹏飞', 1, 20.000, 8.000, 2, NULL, 0, 0, 0, 0),
(1, NULL, '李朝彬', 1, 10.000, 4.000, 3, NULL, 0, 0, 0, 0),
(1, NULL, '李飞', 1, 17.500, 7.000, 4, NULL, 0, 0, 0, 0),
(1, NULL, '孙克义', 2, NULL, 30.000, 5, NULL, 0, 0, 0, 0),
(1, NULL, '刘政', 2, NULL, 5.000, 6, NULL, 0, 0, 0, 0),
(1, NULL, '罗庆红', 2, NULL, 5.000, 7, NULL, 0, 0, 0, 0),
(1, NULL, '吴侠慧', 2, NULL, 10.000, 8, NULL, 0, 0, 0, 0),
(1, NULL, '郑广林', 2, NULL, 5.000, 9, NULL, 0, 0, 0, 0),
(1, NULL, '胡弦', 2, NULL, 5.000, 10, NULL, 0, 0, 0, 0);

-- 万泰广场店 的股东信息 (dividend_store_info_id = 2)
INSERT INTO `oa_dividend_shareholder` (`dividend_store_info_id`, `admin_id`, `shareholder_name`, `shareholder_type`, `company_shareholding_ratio`, `store_shareholding_ratio`, `sort_order`, `remark`, `is_delete`, `delete_time`, `create_time`, `update_time`) VALUES
(2, NULL, '孙艳东', 1, 50.000, 25.000, 1, NULL, 0, 0, 0, 0),
(2, NULL, '胡鹏飞', 1, 20.000, 10.000, 2, NULL, 0, 0, 0, 0),
(2, NULL, '李朝彬', 1, 10.000, 5.000, 3, NULL, 0, 0, 0, 0),
(2, NULL, '李飞', 1, 20.000, 10.000, 4, NULL, 0, 0, 0, 0),
(2, NULL, '皮书历', 2, NULL, 30.000, 5, NULL, 0, 0, 0, 0),
(2, NULL, '姜玉东', 2, NULL, 15.000, 6, NULL, 0, 0, 0, 0),
(2, NULL, '周薇', 2, NULL, 5.000, 7, NULL, 0, 0, 0, 0);

-- 万象汇店 的股东信息 (dividend_store_info_id = 3)
INSERT INTO `oa_dividend_shareholder` (`dividend_store_info_id`, `admin_id`, `shareholder_name`, `shareholder_type`, `company_shareholding_ratio`, `store_shareholding_ratio`, `sort_order`, `remark`, `is_delete`, `delete_time`, `create_time`, `update_time`) VALUES
(3, NULL, '孙艳东', 1, 51.540, 33.500, 1, NULL, 0, 0, 0, 0),
(3, NULL, '胡鹏飞', 1, 20.000, 13.000, 2, NULL, 0, 0, 0, 0),
(3, NULL, '李朝彬', 1, 10.000, 6.500, 3, NULL, 0, 0, 0, 0),
(3, NULL, '李飞', 1, 18.460, 12.000, 4, NULL, 0, 0, 0, 0),
(3, NULL, '龚轩', 2, NULL, 30.000, 5, NULL, 0, 0, 0, 0),
(3, NULL, '涂胜', 2, NULL, 5.000, 6, NULL, 0, 0, 0, 0);

-- 三林店 的股东信息 (dividend_store_info_id = 4)
INSERT INTO `oa_dividend_shareholder` (`dividend_store_info_id`, `admin_id`, `shareholder_name`, `shareholder_type`, `company_shareholding_ratio`, `store_shareholding_ratio`, `sort_order`, `remark`, `is_delete`, `delete_time`, `create_time`, `update_time`) VALUES
(4, NULL, '孙艳东', 1, 50.000, 27.500, 1, NULL, 0, 0, 0, 0),
(4, NULL, '胡鹏飞', 1, 20.000, 11.000, 2, NULL, 0, 0, 0, 0),
(4, NULL, '李朝彬', 1, 10.000, 5.500, 3, NULL, 0, 0, 0, 0),
(4, NULL, '李飞', 1, 20.000, 11.000, 4, NULL, 0, 0, 0, 0),
(4, NULL, '王广涛', 2, NULL, 30.000, 5, NULL, 0, 0, 0, 0),
(4, NULL, '郑冠羽', 2, NULL, 5.000, 6, NULL, 0, 0, 0, 0),
(4, NULL, '殷涛', 2, NULL, 5.000, 7, NULL, 0, 0, 0, 0),
(4, NULL, '刘怀玲', 2, NULL, 5.000, 8, NULL, 0, 0, 0, 0);

-- 上海西站店 的股东信息 (dividend_store_info_id = 5)
INSERT INTO `oa_dividend_shareholder` (`dividend_store_info_id`, `admin_id`, `shareholder_name`, `shareholder_type`, `company_shareholding_ratio`, `store_shareholding_ratio`, `sort_order`, `remark`, `is_delete`, `delete_time`, `create_time`, `update_time`) VALUES
(5, NULL, '孙艳东', 1, 50.000, 27.500, 1, NULL, 0, 0, 0, 0),
(5, NULL, '胡鹏飞', 1, 20.000, 11.000, 2, NULL, 0, 0, 0, 0),
(5, NULL, '李朝彬', 1, 10.000, 5.500, 3, NULL, 0, 0, 0, 0),
(5, NULL, '李飞', 1, 20.000, 11.000, 4, NULL, 0, 0, 0, 0),
(5, NULL, '皮书历', 2, NULL, 30.000, 5, NULL, 0, 0, 0, 0),
(5, NULL, '周薇', 2, NULL, 5.000, 6, NULL, 0, 0, 0, 0),
(5, NULL, '屈磊', 2, NULL, 10.000, 7, NULL, 0, 0, 0, 0);

-- 世纪大道店 的股东信息 (dividend_store_info_id = 6)
INSERT INTO `oa_dividend_shareholder` (`dividend_store_info_id`, `admin_id`, `shareholder_name`, `shareholder_type`, `company_shareholding_ratio`, `store_shareholding_ratio`, `sort_order`, `remark`, `is_delete`, `delete_time`, `create_time`, `update_time`) VALUES
(6, NULL, '孙艳东', 1, 51.820, 31.090, 1, NULL, 0, 0, 0, 0),
(6, NULL, '胡鹏飞', 1, 20.000, 12.000, 2, NULL, 0, 0, 0, 0),
(6, NULL, '李朝彬', 1, 10.000, 6.000, 3, NULL, 0, 0, 0, 0),
(6, NULL, '李飞', 1, 18.180, 10.910, 4, NULL, 0, 0, 0, 0),
(6, NULL, '涂胜', 2, NULL, 30.000, 5, NULL, 0, 0, 0, 0),
(6, NULL, '刘行', 2, NULL, 10.000, 6, NULL, 0, 0, 0, 0);

-- 中山南一店 的股东信息 (dividend_store_info_id = 7)
INSERT INTO `oa_dividend_shareholder` (`dividend_store_info_id`, `admin_id`, `shareholder_name`, `shareholder_type`, `company_shareholding_ratio`, `store_shareholding_ratio`, `sort_order`, `remark`, `is_delete`, `delete_time`, `create_time`, `update_time`) VALUES
(7, NULL, '孙艳东', 1, 60.000, 33.000, 1, NULL, 0, 0, 0, 0),
(7, NULL, '胡鹏飞', 1, 20.000, 11.000, 2, NULL, 0, 0, 0, 0),
(7, NULL, '李飞', 1, 20.000, 11.000, 3, NULL, 0, 0, 0, 0),
(7, NULL, '蔡永干', 2, NULL, 20.000, 4, NULL, 0, 0, 0, 0),
(7, NULL, '孙艳东', 2, NULL, 20.000, 5, NULL, 0, 0, 0, 0),
(7, NULL, '孔德仁', 2, NULL, 5.000, 6, NULL, 0, 0, 0, 0);

-- 九亭店 的股东信息 (dividend_store_info_id = 8)
INSERT INTO `oa_dividend_shareholder` (`dividend_store_info_id`, `admin_id`, `shareholder_name`, `shareholder_type`, `company_shareholding_ratio`, `store_shareholding_ratio`, `sort_order`, `remark`, `is_delete`, `delete_time`, `create_time`, `update_time`) VALUES
(8, NULL, '孙艳东', 1, 50.000, 27.500, 1, NULL, 0, 0, 0, 0),
(8, NULL, '胡鹏飞', 1, 20.000, 11.000, 2, NULL, 0, 0, 0, 0),
(8, NULL, '李朝彬', 1, 10.000, 5.500, 3, NULL, 0, 0, 0, 0),
(8, NULL, '李飞', 1, 20.000, 11.000, 4, NULL, 0, 0, 0, 0),
(8, NULL, '徐红群', 2, NULL, 30.000, 5, NULL, 0, 0, 0, 0),
(8, NULL, '许金富', 2, NULL, 10.000, 6, NULL, 0, 0, 0, 0),
(8, NULL, '吕学明', 2, NULL, 5.000, 7, NULL, 0, 0, 0, 0);

-- 交通大学店 的股东信息 (dividend_store_info_id = 9)
INSERT INTO `oa_dividend_shareholder` (`dividend_store_info_id`, `admin_id`, `shareholder_name`, `shareholder_type`, `company_shareholding_ratio`, `store_shareholding_ratio`, `sort_order`, `remark`, `is_delete`, `delete_time`, `create_time`, `update_time`) VALUES
(9, NULL, '孙艳东', 1, 50.000, 22.500, 1, NULL, 0, 0, 0, 0),
(9, NULL, '胡鹏飞', 1, 20.000, 9.000, 2, NULL, 0, 0, 0, 0),
(9, NULL, '李朝彬', 1, 10.000, 4.500, 3, NULL, 0, 0, 0, 0),
(9, NULL, '李飞', 1, 20.000, 9.000, 4, NULL, 0, 0, 0, 0),
(9, NULL, '石超锋', 2, NULL, 10.000, 5, NULL, 0, 0, 0, 0),
(9, NULL, '张市委', 2, NULL, 30.000, 6, NULL, 0, 0, 0, 0),
(9, NULL, '江英', 2, NULL, 5.000, 7, NULL, 0, 0, 0, 0),
(9, NULL, '孙', 2, NULL, 10.000, 8, NULL, 0, 0, 0, 0);

-- 人民广场店 的股东信息 (dividend_store_info_id = 10)
INSERT INTO `oa_dividend_shareholder` (`dividend_store_info_id`, `admin_id`, `shareholder_name`, `shareholder_type`, `company_shareholding_ratio`, `store_shareholding_ratio`, `sort_order`, `remark`, `is_delete`, `delete_time`, `create_time`, `update_time`) VALUES
(10, NULL, '孙艳东', 1, 50.000, 35.000, 1, NULL, 0, 0, 0, 0),
(10, NULL, '胡鹏飞', 1, 20.000, 14.000, 2, NULL, 0, 0, 0, 0),
(10, NULL, '李朝彬', 1, 10.000, 7.000, 3, NULL, 0, 0, 0, 0),
(10, NULL, '李飞', 1, 20.000, 14.000, 4, NULL, 0, 0, 0, 0),
(10, NULL, '吕学明', 2, NULL, 30.000, 5, NULL, 0, 0, 0, 0);

-- 八佰伴店 的股东信息 (dividend_store_info_id = 11)
INSERT INTO `oa_dividend_shareholder` (`dividend_store_info_id`, `admin_id`, `shareholder_name`, `shareholder_type`, `company_shareholding_ratio`, `store_shareholding_ratio`, `sort_order`, `remark`, `is_delete`, `delete_time`, `create_time`, `update_time`) VALUES
(11, NULL, '孙艳东', 1, 48.180, 26.500, 1, NULL, 0, 0, 0, 0),
(11, NULL, '胡鹏飞', 1, 20.000, 11.000, 2, NULL, 0, 0, 0, 0),
(11, NULL, '李朝彬', 1, 10.000, 5.500, 3, NULL, 0, 0, 0, 0),
(11, NULL, '李飞', 1, 21.820, 12.000, 4, NULL, 0, 0, 0, 0),
(11, NULL, '侯逸伦', 2, NULL, 30.000, 5, NULL, 0, 0, 0, 0),
(11, NULL, '孙', 2, NULL, 5.000, 6, NULL, 0, 0, 0, 0),
(11, NULL, '梁为红', 2, NULL, 10.000, 7, NULL, 0, 0, 0, 0);

-- 关山店 的股东信息 (dividend_store_info_id = 12)
INSERT INTO `oa_dividend_shareholder` (`dividend_store_info_id`, `admin_id`, `shareholder_name`, `shareholder_type`, `company_shareholding_ratio`, `store_shareholding_ratio`, `sort_order`, `remark`, `is_delete`, `delete_time`, `create_time`, `update_time`) VALUES
(12, NULL, '孙艳东', 1, 50.000, 37.500, 1, NULL, 0, 0, 0, 0),
(12, NULL, '胡鹏飞', 1, 20.000, 15.000, 2, NULL, 0, 0, 0, 0),
(12, NULL, '李飞', 1, 20.000, 15.000, 3, NULL, 0, 0, 0, 0),
(12, NULL, '李朝彬', 1, 10.000, 7.500, 4, NULL, 0, 0, 0, 0),
(12, NULL, '皮书历', 2, NULL, 25.000, 5, NULL, 0, 0, 0, 0);

-- 华鹏店 的股东信息 (dividend_store_info_id = 13)
INSERT INTO `oa_dividend_shareholder` (`dividend_store_info_id`, `admin_id`, `shareholder_name`, `shareholder_type`, `company_shareholding_ratio`, `store_shareholding_ratio`, `sort_order`, `remark`, `is_delete`, `delete_time`, `create_time`, `update_time`) VALUES
(13, NULL, '孙艳东', 1, 52.220, 23.500, 1, NULL, 0, 0, 0, 0),
(13, NULL, '胡鹏飞', 1, 20.000, 9.000, 2, NULL, 0, 0, 0, 0),
(13, NULL, '李飞', 1, 17.780, 8.000, 3, NULL, 0, 0, 0, 0),
(13, NULL, '李朝彬', 1, 10.000, 4.500, 4, NULL, 0, 0, 0, 0),
(13, NULL, '郑冠羽', 2, NULL, 30.000, 5, NULL, 0, 0, 0, 0),
(13, NULL, '王海军', 2, NULL, 10.000, 6, NULL, 0, 0, 0, 0),
(13, NULL, '张南陵', 2, NULL, 5.000, 7, NULL, 0, 0, 0, 0),
(13, NULL, '宁静', 2, NULL, 10.000, 8, NULL, 0, 0, 0, 0);

-- 古美店 的股东信息 (dividend_store_info_id = 14)
INSERT INTO `oa_dividend_shareholder` (`dividend_store_info_id`, `admin_id`, `shareholder_name`, `shareholder_type`, `company_shareholding_ratio`, `store_shareholding_ratio`, `sort_order`, `remark`, `is_delete`, `delete_time`, `create_time`, `update_time`) VALUES
(14, NULL, '孙艳东', 1, 54.550, 30.000, 1, NULL, 0, 0, 0, 0),
(14, NULL, '胡鹏飞', 1, 18.180, 10.000, 2, NULL, 0, 0, 0, 0),
(14, NULL, '李飞', 1, 18.180, 10.000, 3, NULL, 0, 0, 0, 0),
(14, NULL, '李朝彬', 1, 9.090, 5.000, 4, NULL, 0, 0, 0, 0),
(14, NULL, '侯逸伦', 2, NULL, 30.000, 5, NULL, 0, 0, 0, 0),
(14, NULL, '余萌', 2, NULL, 10.000, 6, NULL, 0, 0, 0, 0),
(14, NULL, '陈林辉', 2, NULL, 5.000, 7, NULL, 0, 0, 0, 0);

-- 呼玛店 的股东信息 (dividend_store_info_id = 15)
INSERT INTO `oa_dividend_shareholder` (`dividend_store_info_id`, `admin_id`, `shareholder_name`, `shareholder_type`, `company_shareholding_ratio`, `store_shareholding_ratio`, `sort_order`, `remark`, `is_delete`, `delete_time`, `create_time`, `update_time`) VALUES
(15, NULL, '孙艳东', 1, 51.540, 33.500, 1, NULL, 0, 0, 0, 0),
(15, NULL, '胡鹏飞', 1, 20.000, 13.000, 2, NULL, 0, 0, 0, 0),
(15, NULL, '李飞', 1, 18.460, 12.000, 3, NULL, 0, 0, 0, 0),
(15, NULL, '李朝彬', 1, 10.000, 6.500, 4, NULL, 0, 0, 0, 0),
(15, NULL, '胡旭阳', 2, NULL, 30.000, 5, NULL, 0, 0, 0, 0),
(15, NULL, '涂胜', 2, NULL, 5.000, 6, NULL, 0, 0, 0, 0);

-- 城西银泰店 的股东信息 (dividend_store_info_id = 16)
INSERT INTO `oa_dividend_shareholder` (`dividend_store_info_id`, `admin_id`, `shareholder_name`, `shareholder_type`, `company_shareholding_ratio`, `store_shareholding_ratio`, `sort_order`, `remark`, `is_delete`, `delete_time`, `create_time`, `update_time`) VALUES
(16, NULL, '孙艳东', 1, 50.000, 35.000, 1, NULL, 0, 0, 0, 0),
(16, NULL, '李朝彬', 1, 10.000, 7.000, 2, NULL, 0, 0, 0, 0),
(16, NULL, '胡鹏飞', 1, 20.000, 14.000, 3, NULL, 0, 0, 0, 0),
(16, NULL, '李飞', 1, 20.000, 14.000, 4, NULL, 0, 0, 0, 0),
(16, NULL, '陈林辉', 2, NULL, 30.000, 5, NULL, 0, 0, 0, 0);

-- 夏阳湖店 的股东信息 (dividend_store_info_id = 17)
INSERT INTO `oa_dividend_shareholder` (`dividend_store_info_id`, `admin_id`, `shareholder_name`, `shareholder_type`, `company_shareholding_ratio`, `store_shareholding_ratio`, `sort_order`, `remark`, `is_delete`, `delete_time`, `create_time`, `update_time`) VALUES
(17, NULL, '孙艳东', 1, 50.000, 30.000, 1, NULL, 0, 0, 0, 0),
(17, NULL, '胡鹏飞', 1, 30.000, 18.000, 2, NULL, 0, 0, 0, 0),
(17, NULL, '李朝彬', 1, 20.000, 12.000, 3, NULL, 0, 0, 0, 0),
(17, NULL, '樊小飞', 2, NULL, 30.000, 4, NULL, 0, 0, 0, 0),
(17, NULL, '周杜学', 2, NULL, 10.000, 5, NULL, 0, 0, 0, 0);

-- 大渡河店 的股东信息 (dividend_store_info_id = 18)
INSERT INTO `oa_dividend_shareholder` (`dividend_store_info_id`, `admin_id`, `shareholder_name`, `shareholder_type`, `company_shareholding_ratio`, `store_shareholding_ratio`, `sort_order`, `remark`, `is_delete`, `delete_time`, `create_time`, `update_time`) VALUES
(18, NULL, '孙艳东', 1, 55.000, 27.500, 1, NULL, 0, 0, 0, 0),
(18, NULL, '胡鹏飞', 1, 20.000, 10.000, 2, NULL, 0, 0, 0, 0),
(18, NULL, '李飞', 1, 15.000, 7.500, 3, NULL, 0, 0, 0, 0),
(18, NULL, '李朝彬', 1, 10.000, 5.000, 4, NULL, 0, 0, 0, 0),
(18, NULL, '倪佳伟', 2, NULL, 30.000, 5, NULL, 0, 0, 0, 0),
(18, NULL, '张南陵', 2, NULL, 5.000, 6, NULL, 0, 0, 0, 0),
(18, NULL, '李明明', 2, NULL, 10.000, 7, NULL, 0, 0, 0, 0),
(18, NULL, '王青梅', 2, NULL, 5.000, 8, NULL, 0, 0, 0, 0);

-- 大连店 的股东信息 (dividend_store_info_id = 19)
INSERT INTO `oa_dividend_shareholder` (`dividend_store_info_id`, `admin_id`, `shareholder_name`, `shareholder_type`, `company_shareholding_ratio`, `store_shareholding_ratio`, `sort_order`, `remark`, `is_delete`, `delete_time`, `create_time`, `update_time`) VALUES
(19, NULL, '孙艳东', 1, 47.780, 26.280, 1, NULL, 0, 0, 0, 0),
(19, NULL, '胡鹏飞', 1, 20.000, 11.000, 2, NULL, 0, 0, 0, 0),
(19, NULL, '李朝彬', 1, 10.000, 5.500, 3, NULL, 0, 0, 0, 0),
(19, NULL, '李飞', 1, 22.220, 12.220, 4, NULL, 0, 0, 0, 0),
(19, NULL, '郑广林', 2, NULL, 30.000, 5, NULL, 0, 0, 0, 0),
(19, NULL, '孙', 2, NULL, 15.000, 6, NULL, 0, 0, 0, 0);

-- 宜川店 的股东信息 (dividend_store_info_id = 20)
INSERT INTO `oa_dividend_shareholder` (`dividend_store_info_id`, `admin_id`, `shareholder_name`, `shareholder_type`, `company_shareholding_ratio`, `store_shareholding_ratio`, `sort_order`, `remark`, `is_delete`, `delete_time`, `create_time`, `update_time`) VALUES
(20, NULL, '孙艳东', 1, 51.670, 31.000, 1, NULL, 0, 0, 0, 0),
(20, NULL, '胡鹏飞', 1, 20.000, 12.000, 2, NULL, 0, 0, 0, 0),
(20, NULL, '李飞', 1, 18.330, 11.000, 3, NULL, 0, 0, 0, 0),
(20, NULL, '李朝彬', 1, 10.000, 6.000, 4, NULL, 0, 0, 0, 0),
(20, NULL, '吕学明', 2, NULL, 30.000, 5, NULL, 0, 0, 0, 0),
(20, NULL, '杨保霞', 2, NULL, 10.000, 6, NULL, 0, 0, 0, 0);

-- 宝山万达店 的股东信息 (dividend_store_info_id = 21)
INSERT INTO `oa_dividend_shareholder` (`dividend_store_info_id`, `admin_id`, `shareholder_name`, `shareholder_type`, `company_shareholding_ratio`, `store_shareholding_ratio`, `sort_order`, `remark`, `is_delete`, `delete_time`, `create_time`, `update_time`) VALUES
(21, NULL, '孙艳东', 1, 51.820, 28.500, 1, NULL, 0, 0, 0, 0),
(21, NULL, '胡鹏飞', 1, 20.000, 11.000, 2, NULL, 0, 0, 0, 0),
(21, NULL, '李朝彬', 1, 10.000, 5.500, 3, NULL, 0, 0, 0, 0),
(21, NULL, '李飞', 1, 18.180, 10.000, 4, NULL, 0, 0, 0, 0),
(21, NULL, '郑冠羽', 2, NULL, 30.000, 5, NULL, 0, 0, 0, 0),
(21, NULL, '刘俊', 2, NULL, 10.000, 6, NULL, 0, 0, 0, 0),
(21, NULL, '周薇', 2, NULL, 5.000, 7, NULL, 0, 0, 0, 0);

-- 富都广场店 的股东信息 (dividend_store_info_id = 22)
INSERT INTO `oa_dividend_shareholder` (`dividend_store_info_id`, `admin_id`, `shareholder_name`, `shareholder_type`, `company_shareholding_ratio`, `store_shareholding_ratio`, `sort_order`, `remark`, `is_delete`, `delete_time`, `create_time`, `update_time`) VALUES
(22, NULL, '孙艳东', 1, 50.000, 25.000, 1, NULL, 0, 0, 0, 0),
(22, NULL, '胡鹏飞', 1, 30.000, 15.000, 2, NULL, 0, 0, 0, 0),
(22, NULL, '李朝彬', 1, 20.000, 10.000, 3, NULL, 0, 0, 0, 0),
(22, NULL, '涂胜', 2, NULL, 35.000, 4, NULL, 0, 0, 0, 0),
(22, NULL, '孟爽', 2, NULL, 10.000, 5, NULL, 0, 0, 0, 0),
(22, NULL, '王彪', 2, NULL, 5.000, 6, NULL, 0, 0, 0, 0);

-- 张杨店 的股东信息 (dividend_store_info_id = 23)
INSERT INTO `oa_dividend_shareholder` (`dividend_store_info_id`, `admin_id`, `shareholder_name`, `shareholder_type`, `company_shareholding_ratio`, `store_shareholding_ratio`, `sort_order`, `remark`, `is_delete`, `delete_time`, `create_time`, `update_time`) VALUES
(23, NULL, '孙艳东', 1, 45.000, 24.750, 1, NULL, 0, 0, 0, 0),
(23, NULL, '胡鹏飞', 1, 20.000, 11.000, 2, NULL, 0, 0, 0, 0),
(23, NULL, '李朝彬', 1, 10.000, 5.500, 3, NULL, 0, 0, 0, 0),
(23, NULL, '李飞', 1, 25.000, 13.750, 4, NULL, 0, 0, 0, 0),
(23, NULL, '涂胜', 2, NULL, 30.000, 5, NULL, 0, 0, 0, 0),
(23, NULL, '余洋', 2, NULL, 5.000, 6, NULL, 0, 0, 0, 0),
(23, NULL, '公司代持', 2, NULL, 5.000, 7, NULL, 0, 0, 0, 0),
(23, NULL, '严雪勤', 2, NULL, 5.000, 8, NULL, 0, 0, 0, 0);

-- 徐家汇店 的股东信息 (dividend_store_info_id = 24)
INSERT INTO `oa_dividend_shareholder` (`dividend_store_info_id`, `admin_id`, `shareholder_name`, `shareholder_type`, `company_shareholding_ratio`, `store_shareholding_ratio`, `sort_order`, `remark`, `is_delete`, `delete_time`, `create_time`, `update_time`) VALUES
(24, NULL, '孙艳东', 1, 50.000, 30.000, 1, NULL, 0, 0, 0, 0),
(24, NULL, '胡鹏飞', 1, 20.000, 12.000, 2, NULL, 0, 0, 0, 0),
(24, NULL, '李朝彬', 1, 10.000, 6.000, 3, NULL, 0, 0, 0, 0),
(24, NULL, '李飞', 1, 20.000, 12.000, 4, NULL, 0, 0, 0, 0),
(24, NULL, '陈林辉', 2, NULL, 30.000, 5, NULL, 0, 0, 0, 0),
(24, NULL, '王彪', 2, NULL, 10.000, 6, NULL, 0, 0, 0, 0);

-- 成山店 的股东信息 (dividend_store_info_id = 25)
INSERT INTO `oa_dividend_shareholder` (`dividend_store_info_id`, `admin_id`, `shareholder_name`, `shareholder_type`, `company_shareholding_ratio`, `store_shareholding_ratio`, `sort_order`, `remark`, `is_delete`, `delete_time`, `create_time`, `update_time`) VALUES
(25, NULL, '孙艳东', 1, 51.820, 28.500, 1, NULL, 0, 0, 0, 0),
(25, NULL, '胡鹏飞', 1, 20.000, 11.000, 2, NULL, 0, 0, 0, 0),
(25, NULL, '李飞', 1, 18.180, 10.000, 3, NULL, 0, 0, 0, 0),
(25, NULL, '李朝彬', 1, 10.000, 5.500, 4, NULL, 0, 0, 0, 0),
(25, NULL, '郝佳伟', 2, NULL, 30.000, 5, NULL, 0, 0, 0, 0),
(25, NULL, '侯逸伦', 2, NULL, 5.000, 6, NULL, 0, 0, 0, 0),
(25, NULL, '晏海坤', 2, NULL, 10.000, 7, NULL, 0, 0, 0, 0);

-- 打浦桥店 的股东信息 (dividend_store_info_id = 26)
INSERT INTO `oa_dividend_shareholder` (`dividend_store_info_id`, `admin_id`, `shareholder_name`, `shareholder_type`, `company_shareholding_ratio`, `store_shareholding_ratio`, `sort_order`, `remark`, `is_delete`, `delete_time`, `create_time`, `update_time`) VALUES
(26, NULL, '孙艳东', 1, 50.000, 22.500, 1, NULL, 0, 0, 0, 0),
(26, NULL, '李飞', 1, 20.000, 9.000, 2, NULL, 0, 0, 0, 0),
(26, NULL, '李朝彬', 1, 10.000, 4.500, 3, NULL, 0, 0, 0, 0),
(26, NULL, '胡鹏飞', 1, 20.000, 9.000, 4, NULL, 0, 0, 0, 0),
(26, NULL, '马坤', 2, NULL, 10.000, 5, NULL, 0, 0, 0, 0),
(26, NULL, '郑冠羽', 2, NULL, 30.000, 6, NULL, 0, 0, 0, 0),
(26, NULL, '白玲玲', 2, NULL, 15.000, 7, NULL, 0, 0, 0, 0);

-- 春城店 的股东信息 (dividend_store_info_id = 27)
INSERT INTO `oa_dividend_shareholder` (`dividend_store_info_id`, `admin_id`, `shareholder_name`, `shareholder_type`, `company_shareholding_ratio`, `store_shareholding_ratio`, `sort_order`, `remark`, `is_delete`, `delete_time`, `create_time`, `update_time`) VALUES
(27, NULL, '孙艳东', 1, 50.000, 30.000, 1, NULL, 0, 0, 0, 0),
(27, NULL, '胡鹏飞', 1, 20.000, 12.000, 2, NULL, 0, 0, 0, 0),
(27, NULL, '李朝彬', 1, 10.000, 6.000, 3, NULL, 0, 0, 0, 0),
(27, NULL, '李飞', 1, 20.000, 12.000, 4, NULL, 0, 0, 0, 0),
(27, NULL, '樊小飞', 2, NULL, 30.000, 5, NULL, 0, 0, 0, 0),
(27, NULL, '吴侠慧', 2, NULL, 10.000, 6, NULL, 0, 0, 0, 0);

-- 梅川店 的股东信息 (dividend_store_info_id = 28)
INSERT INTO `oa_dividend_shareholder` (`dividend_store_info_id`, `admin_id`, `shareholder_name`, `shareholder_type`, `company_shareholding_ratio`, `store_shareholding_ratio`, `sort_order`, `remark`, `is_delete`, `delete_time`, `create_time`, `update_time`) VALUES
(28, NULL, '孙艳东', 1, 51.820, 31.100, 1, NULL, 0, 0, 0, 0),
(28, NULL, '李朝彬', 1, 10.000, 6.000, 2, NULL, 0, 0, 0, 0),
(28, NULL, '胡鹏飞', 1, 20.000, 12.000, 3, NULL, 0, 0, 0, 0),
(28, NULL, '李飞', 1, 18.180, 10.900, 4, NULL, 0, 0, 0, 0),
(28, NULL, '蔡永干', 2, NULL, 30.000, 5, NULL, 0, 0, 0, 0),
(28, NULL, '孙兰', 2, NULL, 5.000, 6, NULL, 0, 0, 0, 0),
(28, NULL, '聂珂', 2, NULL, 5.000, 7, NULL, 0, 0, 0, 0);

-- 欧阳店 的股东信息 (dividend_store_info_id = 29)
INSERT INTO `oa_dividend_shareholder` (`dividend_store_info_id`, `admin_id`, `shareholder_name`, `shareholder_type`, `company_shareholding_ratio`, `store_shareholding_ratio`, `sort_order`, `remark`, `is_delete`, `delete_time`, `create_time`, `update_time`) VALUES
(29, NULL, '孙艳东', 1, 50.000, 35.000, 1, NULL, 0, 0, 0, 0),
(29, NULL, '胡鹏飞', 1, 20.000, 14.000, 2, NULL, 0, 0, 0, 0),
(29, NULL, '李朝彬', 1, 10.000, 7.000, 3, NULL, 0, 0, 0, 0),
(29, NULL, '李飞', 1, 20.000, 14.000, 4, NULL, 0, 0, 0, 0),
(29, NULL, '张志建', 2, NULL, 30.000, 5, NULL, 0, 0, 0, 0);

-- 武夷店 的股东信息 (dividend_store_info_id = 30)
INSERT INTO `oa_dividend_shareholder` (`dividend_store_info_id`, `admin_id`, `shareholder_name`, `shareholder_type`, `company_shareholding_ratio`, `store_shareholding_ratio`, `sort_order`, `remark`, `is_delete`, `delete_time`, `create_time`, `update_time`) VALUES
(30, NULL, '孙艳东', 1, 45.560, 25.060, 1, NULL, 0, 0, 0, 0),
(30, NULL, '李飞', 1, 24.440, 13.440, 2, NULL, 0, 0, 0, 0),
(30, NULL, '李朝彬', 1, 10.000, 5.500, 3, NULL, 0, 0, 0, 0),
(30, NULL, '胡鹏飞', 1, 20.000, 11.000, 4, NULL, 0, 0, 0, 0),
(30, NULL, '皮书历', 2, NULL, 30.000, 5, NULL, 0, 0, 0, 0),
(30, NULL, '郝东辉', 2, NULL, 5.000, 6, NULL, 0, 0, 0, 0),
(30, NULL, '周界祥', 2, NULL, 10.000, 7, NULL, 0, 0, 0, 0);

-- 武川店 的股东信息 (dividend_store_info_id = 31)
INSERT INTO `oa_dividend_shareholder` (`dividend_store_info_id`, `admin_id`, `shareholder_name`, `shareholder_type`, `company_shareholding_ratio`, `store_shareholding_ratio`, `sort_order`, `remark`, `is_delete`, `delete_time`, `create_time`, `update_time`) VALUES
(31, NULL, '孙艳东', 1, 52.500, 23.625, 1, NULL, 0, 0, 0, 0),
(31, NULL, '胡鹏飞', 1, 20.000, 9.000, 2, NULL, 0, 0, 0, 0),
(31, NULL, '李飞', 1, 17.500, 7.875, 3, NULL, 0, 0, 0, 0),
(31, NULL, '李朝彬', 1, 10.000, 4.500, 4, NULL, 0, 0, 0, 0),
(31, NULL, '胡旭阳', 2, NULL, 30.000, 5, NULL, 0, 0, 0, 0),
(31, NULL, '赵佳欣', 2, NULL, 10.000, 6, NULL, 0, 0, 0, 0),
(31, NULL, '吴侠慧', 2, NULL, 5.000, 7, NULL, 0, 0, 0, 0),
(31, NULL, '王彪', 2, NULL, 5.000, 8, NULL, 0, 0, 0, 0),
(31, NULL, '吕学明', 2, NULL, 5.000, 9, NULL, 0, 0, 0, 0);

-- 永德店 的股东信息 (dividend_store_info_id = 32)
INSERT INTO `oa_dividend_shareholder` (`dividend_store_info_id`, `admin_id`, `shareholder_name`, `shareholder_type`, `company_shareholding_ratio`, `store_shareholding_ratio`, `sort_order`, `remark`, `is_delete`, `delete_time`, `create_time`, `update_time`) VALUES
(32, NULL, '孙艳东', 1, 60.000, 36.000, 1, NULL, 0, 0, 0, 0),
(32, NULL, '胡鹏飞', 1, 20.000, 12.000, 2, NULL, 0, 0, 0, 0),
(32, NULL, '李飞', 1, 20.000, 12.000, 3, NULL, 0, 0, 0, 0),
(32, NULL, '李朝彬', 2, NULL, 30.000, 4, NULL, 0, 0, 0, 0),
(32, NULL, '蔡永干', 2, NULL, 5.000, 5, NULL, 0, 0, 0, 0),
(32, NULL, '姜玉东', 2, NULL, 5.000, 6, NULL, 0, 0, 0, 0);

-- 江阴街店 的股东信息 (dividend_store_info_id = 33)
INSERT INTO `oa_dividend_shareholder` (`dividend_store_info_id`, `admin_id`, `shareholder_name`, `shareholder_type`, `company_shareholding_ratio`, `store_shareholding_ratio`, `sort_order`, `remark`, `is_delete`, `delete_time`, `create_time`, `update_time`) VALUES
(33, NULL, '孙艳东', 1, 50.000, 30.000, 1, NULL, 0, 0, 0, 0),
(33, NULL, '胡鹏飞', 1, 20.000, 12.000, 2, NULL, 0, 0, 0, 0),
(33, NULL, '李朝彬', 1, 10.000, 6.000, 3, NULL, 0, 0, 0, 0),
(33, NULL, '李飞', 1, 20.000, 12.000, 4, NULL, 0, 0, 0, 0),
(33, NULL, '宋世超', 2, NULL, 30.000, 5, NULL, 0, 0, 0, 0),
(33, NULL, '王小政', 2, NULL, 5.000, 6, NULL, 0, 0, 0, 0),
(33, NULL, '周薇', 2, NULL, 5.000, 7, NULL, 0, 0, 0, 0);

-- 源深体育店 的股东信息 (dividend_store_info_id = 34)
INSERT INTO `oa_dividend_shareholder` (`dividend_store_info_id`, `admin_id`, `shareholder_name`, `shareholder_type`, `company_shareholding_ratio`, `store_shareholding_ratio`, `sort_order`, `remark`, `is_delete`, `delete_time`, `create_time`, `update_time`) VALUES
(34, NULL, '孙艳东', 1, 51.820, 28.500, 1, NULL, 0, 0, 0, 0),
(34, NULL, '胡鹏飞', 1, 20.000, 11.000, 2, NULL, 0, 0, 0, 0),
(34, NULL, '李飞', 1, 18.180, 10.000, 3, NULL, 0, 0, 0, 0),
(34, NULL, '李朝彬', 1, 10.000, 5.500, 4, NULL, 0, 0, 0, 0),
(34, NULL, '徐继香', 2, NULL, 30.000, 5, NULL, 0, 0, 0, 0),
(34, NULL, '皮书历', 2, NULL, 5.000, 6, NULL, 0, 0, 0, 0),
(34, NULL, '李连顺', 2, NULL, 10.000, 7, NULL, 0, 0, 0, 0);

-- 滨江天街店 的股东信息 (dividend_store_info_id = 35)
INSERT INTO `oa_dividend_shareholder` (`dividend_store_info_id`, `admin_id`, `shareholder_name`, `shareholder_type`, `company_shareholding_ratio`, `store_shareholding_ratio`, `sort_order`, `remark`, `is_delete`, `delete_time`, `create_time`, `update_time`) VALUES
(35, NULL, '孙艳东', 1, 53.080, 29.190, 1, NULL, 0, 0, 0, 0),
(35, NULL, '胡鹏飞', 1, 20.000, 11.000, 2, NULL, 0, 0, 0, 0),
(35, NULL, '李飞', 1, 16.920, 9.310, 3, NULL, 0, 0, 0, 0),
(35, NULL, '李朝彬', 1, 10.000, 5.500, 4, NULL, 0, 0, 0, 0),
(35, NULL, '涂胜', 2, NULL, 5.000, 5, NULL, 0, 0, 0, 0),
(35, NULL, '陈习祥', 2, NULL, 10.000, 6, NULL, 0, 0, 0, 0),
(35, NULL, '龚轩', 2, NULL, 30.000, 7, NULL, 0, 0, 0, 0);

-- 漕宝店 的股东信息 (dividend_store_info_id = 36)
INSERT INTO `oa_dividend_shareholder` (`dividend_store_info_id`, `admin_id`, `shareholder_name`, `shareholder_type`, `company_shareholding_ratio`, `store_shareholding_ratio`, `sort_order`, `remark`, `is_delete`, `delete_time`, `create_time`, `update_time`) VALUES
(36, NULL, '孙艳东', 1, 47.780, 26.280, 1, NULL, 0, 0, 0, 0),
(36, NULL, '胡鹏飞', 1, 20.000, 11.000, 2, NULL, 0, 0, 0, 0),
(36, NULL, '李朝彬', 1, 10.000, 5.500, 3, NULL, 0, 0, 0, 0),
(36, NULL, '李飞', 1, 22.220, 12.220, 4, NULL, 0, 0, 0, 0),
(36, NULL, '李忠护', 2, NULL, 30.000, 5, NULL, 0, 0, 0, 0),
(36, NULL, '张市委', 2, NULL, 5.000, 6, NULL, 0, 0, 0, 0),
(36, NULL, '牛素侠', 2, NULL, 5.000, 7, NULL, 0, 0, 0, 0),
(36, NULL, '周薇', 2, NULL, 5.000, 8, NULL, 0, 0, 0, 0);

-- 田林东路店 的股东信息 (dividend_store_info_id = 37)
INSERT INTO `oa_dividend_shareholder` (`dividend_store_info_id`, `admin_id`, `shareholder_name`, `shareholder_type`, `company_shareholding_ratio`, `store_shareholding_ratio`, `sort_order`, `remark`, `is_delete`, `delete_time`, `create_time`, `update_time`) VALUES
(37, NULL, '孙艳东', 1, 50.000, 25.000, 1, NULL, 0, 0, 0, 0),
(37, NULL, '胡鹏飞', 1, 20.000, 10.000, 2, NULL, 0, 0, 0, 0),
(37, NULL, '李飞', 1, 20.000, 10.000, 3, NULL, 0, 0, 0, 0),
(37, NULL, '李朝彬', 1, 10.000, 5.000, 4, NULL, 0, 0, 0, 0),
(37, NULL, '陶卫林', 2, NULL, 5.000, 5, NULL, 0, 0, 0, 0),
(37, NULL, '王凡', 2, NULL, 30.000, 6, NULL, 0, 0, 0, 0),
(37, NULL, '陈林辉', 2, NULL, 5.000, 7, NULL, 0, 0, 0, 0),
(37, NULL, '王小政', 2, NULL, 10.000, 8, NULL, 0, 0, 0, 0);

-- 肇嘉浜店 的股东信息 (dividend_store_info_id = 38)
INSERT INTO `oa_dividend_shareholder` (`dividend_store_info_id`, `admin_id`, `shareholder_name`, `shareholder_type`, `company_shareholding_ratio`, `store_shareholding_ratio`, `sort_order`, `remark`, `is_delete`, `delete_time`, `create_time`, `update_time`) VALUES
(38, NULL, '孙艳东', 1, 53.330, 34.670, 1, NULL, 0, 0, 0, 0),
(38, NULL, '胡鹏飞', 1, 20.000, 13.000, 2, NULL, 0, 0, 0, 0),
(38, NULL, '李飞', 1, 16.670, 10.830, 3, NULL, 0, 0, 0, 0),
(38, NULL, '李朝彬', 1, 10.000, 6.500, 4, NULL, 0, 0, 0, 0),
(38, NULL, '封前', 2, NULL, 30.000, 5, NULL, 0, 0, 0, 0),
(38, NULL, '吕学明', 2, NULL, 5.000, 6, NULL, 0, 0, 0, 0);

-- 花木店 的股东信息 (dividend_store_info_id = 39)
INSERT INTO `oa_dividend_shareholder` (`dividend_store_info_id`, `admin_id`, `shareholder_name`, `shareholder_type`, `company_shareholding_ratio`, `store_shareholding_ratio`, `sort_order`, `remark`, `is_delete`, `delete_time`, `create_time`, `update_time`) VALUES
(39, NULL, '孙艳东', 1, 53.330, 32.000, 1, NULL, 0, 0, 0, 0),
(39, NULL, '胡鹏飞', 1, 20.000, 12.000, 2, NULL, 0, 0, 0, 0),
(39, NULL, '李飞', 1, 16.670, 10.000, 3, NULL, 0, 0, 0, 0),
(39, NULL, '李朝彬', 1, 10.000, 6.000, 4, NULL, 0, 0, 0, 0),
(39, NULL, '涂胜', 2, NULL, 30.000, 5, NULL, 0, 0, 0, 0),
(39, NULL, '余洋', 2, NULL, 5.000, 6, NULL, 0, 0, 0, 0),
(39, NULL, '张瑞平', 2, NULL, 5.000, 7, NULL, 0, 0, 0, 0);

-- 莘朱店 的股东信息 (dividend_store_info_id = 40)
INSERT INTO `oa_dividend_shareholder` (`dividend_store_info_id`, `admin_id`, `shareholder_name`, `shareholder_type`, `company_shareholding_ratio`, `store_shareholding_ratio`, `sort_order`, `remark`, `is_delete`, `delete_time`, `create_time`, `update_time`) VALUES
(40, NULL, '孙艳东', 1, 28.330, 17.000, 1, NULL, 0, 0, 0, 0),
(40, NULL, '李飞', 1, 41.670, 25.000, 2, NULL, 0, 0, 0, 0),
(40, NULL, '胡鹏飞', 1, 20.000, 12.000, 3, NULL, 0, 0, 0, 0),
(40, NULL, '李朝彬', 1, 10.000, 6.000, 4, NULL, 0, 0, 0, 0),
(40, NULL, '任金梅', 2, NULL, 5.000, 5, NULL, 0, 0, 0, 0),
(40, NULL, '刘继承', 2, NULL, 5.000, 6, NULL, 0, 0, 0, 0),
(40, NULL, '陈林辉', 2, NULL, 30.000, 7, NULL, 0, 0, 0, 0);

-- 莲花店 的股东信息 (dividend_store_info_id = 41)
INSERT INTO `oa_dividend_shareholder` (`dividend_store_info_id`, `admin_id`, `shareholder_name`, `shareholder_type`, `company_shareholding_ratio`, `store_shareholding_ratio`, `sort_order`, `remark`, `is_delete`, `delete_time`, `create_time`, `update_time`) VALUES
(41, NULL, '孙艳东', 1, 50.000, 17.500, 1, NULL, 0, 0, 0, 0),
(41, NULL, '胡鹏飞', 1, 30.000, 10.500, 2, NULL, 0, 0, 0, 0),
(41, NULL, '李朝彬', 1, 20.000, 7.000, 3, NULL, 0, 0, 0, 0),
(41, NULL, '黄于忠', 2, NULL, 10.000, 4, NULL, 0, 0, 0, 0),
(41, NULL, '张市委', 2, NULL, 30.000, 5, NULL, 0, 0, 0, 0),
(41, NULL, '吴侠慧', 2, NULL, 25.000, 6, NULL, 0, 0, 0, 0);

-- 虬江店 的股东信息 (dividend_store_info_id = 42)
INSERT INTO `oa_dividend_shareholder` (`dividend_store_info_id`, `admin_id`, `shareholder_name`, `shareholder_type`, `company_shareholding_ratio`, `store_shareholding_ratio`, `sort_order`, `remark`, `is_delete`, `delete_time`, `create_time`, `update_time`) VALUES
(42, NULL, '孙艳东', 1, 50.000, 27.500, 1, NULL, 0, 0, 0, 0),
(42, NULL, '胡鹏飞', 1, 30.000, 16.500, 2, NULL, 0, 0, 0, 0),
(42, NULL, '李朝彬', 1, 20.000, 11.000, 3, NULL, 0, 0, 0, 0),
(42, NULL, '郑冠羽', 2, NULL, 30.000, 4, NULL, 0, 0, 0, 0),
(42, NULL, '姜玉东', 2, NULL, 15.000, 5, NULL, 0, 0, 0, 0);

-- 虹梅店 的股东信息 (dividend_store_info_id = 43)
INSERT INTO `oa_dividend_shareholder` (`dividend_store_info_id`, `admin_id`, `shareholder_name`, `shareholder_type`, `company_shareholding_ratio`, `store_shareholding_ratio`, `sort_order`, `remark`, `is_delete`, `delete_time`, `create_time`, `update_time`) VALUES
(43, NULL, '孙艳东', 1, 53.750, 34.940, 1, NULL, 0, 0, 0, 0),
(43, NULL, '胡鹏飞', 1, 20.000, 13.000, 2, NULL, 0, 0, 0, 0),
(43, NULL, '李朝彬', 1, 10.000, 6.500, 3, NULL, 0, 0, 0, 0),
(43, NULL, '李飞', 1, 16.250, 10.560, 4, NULL, 0, 0, 0, 0),
(43, NULL, '张市委', 2, NULL, 30.000, 5, NULL, 0, 0, 0, 0),
(43, NULL, '姚景鑫', 2, NULL, 5.000, 6, NULL, 0, 0, 0, 0);

-- 融创精彩天地店 的股东信息 (dividend_store_info_id = 44)
INSERT INTO `oa_dividend_shareholder` (`dividend_store_info_id`, `admin_id`, `shareholder_name`, `shareholder_type`, `company_shareholding_ratio`, `store_shareholding_ratio`, `sort_order`, `remark`, `is_delete`, `delete_time`, `create_time`, `update_time`) VALUES
(44, NULL, '孙艳东', 1, 51.670, 33.580, 1, NULL, 0, 0, 0, 0),
(44, NULL, '胡鹏飞', 1, 20.000, 13.000, 2, NULL, 0, 0, 0, 0),
(44, NULL, '李飞', 1, 18.330, 11.920, 3, NULL, 0, 0, 0, 0),
(44, NULL, '李朝彬', 1, 10.000, 6.500, 4, NULL, 0, 0, 0, 0),
(44, NULL, '赵长城', 2, NULL, 30.000, 5, NULL, 0, 0, 0, 0),
(44, NULL, '蔡永干', 2, NULL, 5.000, 6, NULL, 0, 0, 0, 0);

-- 西藏北路店 的股东信息 (dividend_store_info_id = 45)
INSERT INTO `oa_dividend_shareholder` (`dividend_store_info_id`, `admin_id`, `shareholder_name`, `shareholder_type`, `company_shareholding_ratio`, `store_shareholding_ratio`, `sort_order`, `remark`, `is_delete`, `delete_time`, `create_time`, `update_time`) VALUES
(45, NULL, '孙艳东', 1, 50.000, 27.500, 1, NULL, 0, 0, 0, 0),
(45, NULL, '胡鹏飞', 1, 20.000, 11.000, 2, NULL, 0, 0, 0, 0),
(45, NULL, '李朝彬', 1, 10.000, 5.500, 3, NULL, 0, 0, 0, 0),
(45, NULL, '李飞', 1, 20.000, 11.000, 4, NULL, 0, 0, 0, 0),
(45, NULL, '吴侠慧', 2, NULL, 10.000, 5, NULL, 0, 0, 0, 0),
(45, NULL, '刘庆旺', 2, NULL, 5.000, 6, NULL, 0, 0, 0, 0),
(45, NULL, '陈林辉', 2, NULL, 30.000, 7, NULL, 0, 0, 0, 0);

-- 金桥国际店 的股东信息 (dividend_store_info_id = 46)
INSERT INTO `oa_dividend_shareholder` (`dividend_store_info_id`, `admin_id`, `shareholder_name`, `shareholder_type`, `company_shareholding_ratio`, `store_shareholding_ratio`, `sort_order`, `remark`, `is_delete`, `delete_time`, `create_time`, `update_time`) VALUES
(46, NULL, '孙艳东', 1, 50.000, 30.000, 1, NULL, 0, 0, 0, 0),
(46, NULL, '李飞', 1, 20.000, 12.000, 2, NULL, 0, 0, 0, 0),
(46, NULL, '李朝彬', 1, 10.000, 6.000, 3, NULL, 0, 0, 0, 0),
(46, NULL, '胡鹏飞', 1, 20.000, 12.000, 4, NULL, 0, 0, 0, 0),
(46, NULL, '侯逸伦', 2, NULL, 30.000, 5, NULL, 0, 0, 0, 0),
(46, NULL, '屈士圆', 2, NULL, 5.000, 6, NULL, 0, 0, 0, 0),
(46, NULL, '陶卫林', 2, NULL, 5.000, 7, NULL, 0, 0, 0, 0);

-- 锦安店 的股东信息 (dividend_store_info_id = 47)
INSERT INTO `oa_dividend_shareholder` (`dividend_store_info_id`, `admin_id`, `shareholder_name`, `shareholder_type`, `company_shareholding_ratio`, `store_shareholding_ratio`, `sort_order`, `remark`, `is_delete`, `delete_time`, `create_time`, `update_time`) VALUES
(47, NULL, '孙艳东', 1, 51.820, 31.090, 1, NULL, 0, 0, 0, 0),
(47, NULL, '胡鹏飞', 1, 20.000, 12.000, 2, NULL, 0, 0, 0, 0),
(47, NULL, '李飞', 1, 18.180, 10.910, 3, NULL, 0, 0, 0, 0),
(47, NULL, '李朝彬', 1, 10.000, 6.000, 4, NULL, 0, 0, 0, 0),
(47, NULL, '徐红群', 2, NULL, 30.000, 5, NULL, 0, 0, 0, 0),
(47, NULL, '吕学明', 2, NULL, 5.000, 6, NULL, 0, 0, 0, 0),
(47, NULL, '许金富', 2, NULL, 5.000, 7, NULL, 0, 0, 0, 0);

-- 长寿店 的股东信息 (dividend_store_info_id = 48)
INSERT INTO `oa_dividend_shareholder` (`dividend_store_info_id`, `admin_id`, `shareholder_name`, `shareholder_type`, `company_shareholding_ratio`, `store_shareholding_ratio`, `sort_order`, `remark`, `is_delete`, `delete_time`, `create_time`, `update_time`) VALUES
(48, NULL, '孙艳东', 1, 50.000, 30.000, 1, NULL, 0, 0, 0, 0),
(48, NULL, '胡鹏飞', 1, 20.000, 12.000, 2, NULL, 0, 0, 0, 0),
(48, NULL, '李朝彬', 1, 10.000, 6.000, 3, NULL, 0, 0, 0, 0),
(48, NULL, '李飞', 1, 20.000, 12.000, 4, NULL, 0, 0, 0, 0),
(48, NULL, '倪佳伟', 2, NULL, 20.000, 5, NULL, 0, 0, 0, 0),
(48, NULL, '刘俊', 2, NULL, 15.000, 6, NULL, 0, 0, 0, 0),
(48, NULL, '王青梅', 2, NULL, 5.000, 7, NULL, 0, 0, 0, 0);

-- 阳城店 的股东信息 (dividend_store_info_id = 49)
INSERT INTO `oa_dividend_shareholder` (`dividend_store_info_id`, `admin_id`, `shareholder_name`, `shareholder_type`, `company_shareholding_ratio`, `store_shareholding_ratio`, `sort_order`, `remark`, `is_delete`, `delete_time`, `create_time`, `update_time`) VALUES
(49, NULL, '孙艳东', 1, 50.000, 32.500, 1, NULL, 0, 0, 0, 0),
(49, NULL, '胡鹏飞', 1, 20.000, 13.000, 2, NULL, 0, 0, 0, 0),
(49, NULL, '李朝彬', 1, 10.000, 6.500, 3, NULL, 0, 0, 0, 0),
(49, NULL, '李飞', 1, 20.000, 13.000, 4, NULL, 0, 0, 0, 0),
(49, NULL, '王青梅', 2, NULL, 5.000, 5, NULL, 0, 0, 0, 0),
(49, NULL, '吕学明', 2, NULL, 30.000, 6, NULL, 0, 0, 0, 0);

-- 黄兴店 的股东信息 (dividend_store_info_id = 50)
INSERT INTO `oa_dividend_shareholder` (`dividend_store_info_id`, `admin_id`, `shareholder_name`, `shareholder_type`, `company_shareholding_ratio`, `store_shareholding_ratio`, `sort_order`, `remark`, `is_delete`, `delete_time`, `create_time`, `update_time`) VALUES
(50, NULL, '孙艳东', 1, 51.540, 36.080, 1, NULL, 0, 0, 0, 0),
(50, NULL, '李飞', 1, 18.460, 12.920, 2, NULL, 0, 0, 0, 0),
(50, NULL, '李朝彬', 1, 10.000, 7.000, 3, NULL, 0, 0, 0, 0),
(50, NULL, '胡鹏飞', 1, 20.000, 14.000, 4, NULL, 0, 0, 0, 0),
(50, NULL, '郑广林', 2, NULL, 30.000, 5, NULL, 0, 0, 0, 0);

-- 龙吴店 的股东信息 (dividend_store_info_id = 51)
INSERT INTO `oa_dividend_shareholder` (`dividend_store_info_id`, `admin_id`, `shareholder_name`, `shareholder_type`, `company_shareholding_ratio`, `store_shareholding_ratio`, `sort_order`, `remark`, `is_delete`, `delete_time`, `create_time`, `update_time`) VALUES
(51, NULL, '孙艳东', 1, 50.000, 27.500, 1, NULL, 0, 0, 0, 0),
(51, NULL, '胡鹏飞', 1, 20.000, 11.000, 2, NULL, 0, 0, 0, 0),
(51, NULL, '李朝彬', 1, 10.000, 5.500, 3, NULL, 0, 0, 0, 0),
(51, NULL, '李飞', 1, 20.000, 11.000, 4, NULL, 0, 0, 0, 0),
(51, NULL, '张市委', 2, NULL, 30.000, 5, NULL, 0, 0, 0, 0),
(51, NULL, '王彪', 2, NULL, 10.000, 6, NULL, 0, 0, 0, 0),
(51, NULL, '薛飞', 2, NULL, 5.000, 7, NULL, 0, 0, 0, 0);

-- 龙盛店 的股东信息 (dividend_store_info_id = 52)
INSERT INTO `oa_dividend_shareholder` (`dividend_store_info_id`, `admin_id`, `shareholder_name`, `shareholder_type`, `company_shareholding_ratio`, `store_shareholding_ratio`, `sort_order`, `remark`, `is_delete`, `delete_time`, `create_time`, `update_time`) VALUES
(52, NULL, '孙艳东', 1, 51.820, 28.500, 1, NULL, 0, 0, 0, 0),
(52, NULL, '胡鹏飞', 1, 20.000, 11.000, 2, NULL, 0, 0, 0, 0),
(52, NULL, '李飞', 1, 18.180, 10.000, 3, NULL, 0, 0, 0, 0),
(52, NULL, '李朝彬', 1, 10.000, 5.500, 4, NULL, 0, 0, 0, 0),
(52, NULL, '徐华', 2, NULL, 5.000, 5, NULL, 0, 0, 0, 0),
(52, NULL, '樊小飞', 2, NULL, 30.000, 6, NULL, 0, 0, 0, 0),
(52, NULL, '张旭凡', 2, NULL, 10.000, 7, NULL, 0, 0, 0, 0);

-- 龙茗店 的股东信息 (dividend_store_info_id = 53)
INSERT INTO `oa_dividend_shareholder` (`dividend_store_info_id`, `admin_id`, `shareholder_name`, `shareholder_type`, `company_shareholding_ratio`, `store_shareholding_ratio`, `sort_order`, `remark`, `is_delete`, `delete_time`, `create_time`, `update_time`) VALUES
(53, NULL, '孙艳东', 1, 50.000, 30.000, 1, NULL, 0, 0, 0, 0),
(53, NULL, '胡鹏飞', 1, 20.000, 12.000, 2, NULL, 0, 0, 0, 0),
(53, NULL, '李飞', 1, 20.000, 12.000, 3, NULL, 0, 0, 0, 0),
(53, NULL, '李朝彬', 1, 10.000, 6.000, 4, NULL, 0, 0, 0, 0),
(53, NULL, '殷耘萌', 2, NULL, 30.000, 5, NULL, 0, 0, 0, 0),
(53, NULL, '武银珠', 2, NULL, 10.000, 6, NULL, 0, 0, 0, 0);