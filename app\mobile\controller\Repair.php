<?php
declare (strict_types = 1);

namespace app\mobile\controller;

use app\base\BaseController;
use think\facade\View;
use think\facade\Db;
use think\facade\Log;
use think\facade\Config;
use app\repair\service\ApproveMapService;
use systematic\ConfigManager;

class Repair extends BaseController
{
    protected $approval_admin_id; // 审批人ID
    protected $approval_admin_name; // 审批人姓名
    protected $temp_user_id; // 临时接收消息的用户ID
    protected $type_id; // 流程类型ID

    /**
     * 构造函数，初始化通用变量
     */
    public function initialize()
    {

        // 设置空的login_admin变量，解决模板中变量未定义的问题
        View::assign('login_admin', ['id' => 0]);

        // 从数据库中读取配置信息（JSON格式）
        $repair_config = ConfigManager::get('flow_repair', []);
        
        // 从配置中获取审批人ID和流程类型ID
        $this->approval_admin_id = isset($repair_config['users']['approval_admin_id']) ? $repair_config['users']['approval_admin_id'] : 0;
        $this->approval_admin_name = isset($repair_config['users']['approval_admin_name']) ? $repair_config['users']['approval_admin_name'] : '';
        $this->temp_user_id = isset($repair_config['users']['temp_user_id']) ? $repair_config['users']['temp_user_id'] : 0;
        $this->type_id = isset($repair_config['type_id']) ? $repair_config['type_id'] : 0;
        
        // 如果数据库中没有配置，则尝试从配置文件中读取（兼容旧版本）
        if (empty($this->approval_admin_id)) {
            $this->approval_admin_id = Config::get('flow-config.repair.users.approval_admin_id');
            $this->approval_admin_name = Config::get('flow-config.repair.users.approval_admin_name');
            $this->temp_user_id = Config::get('flow-config.repair.users.temp_user_id');
            $this->type_id = Config::get('flow-config.repair.type_id');
        }
    }

    /**
     * 维修人员验证页面
     */
    public function verify()
    {
        return View::fetch();
    }

    /**
     * 流程列表页面
     */
    public function process_list()
    {
        return View::fetch();
    }

    /**
     * 验证维修人员手机号后四位
     */
    public function check_code()
    {
        if (!request()->isPost()) {
            return json(['code' => 1, 'msg' => '请求方式错误']);
        }

        // 获取POST数据，兼容不同格式
        $postData = request()->post();

        // 尝试获取JSON格式数据
        if (empty($postData)) {
            $inputData = file_get_contents('php://input');
            if (!empty($inputData)) {
                $postData = json_decode($inputData, true) ?: [];
            }
        }

        $phoneLastFour = isset($postData['phone_last_four']) ? trim($postData['phone_last_four']) : '';

        if (empty($phoneLastFour)) {
            return json(['code' => 1, 'msg' => '请输入手机号后四位']);
        }

        // 验证是否为4位数字
        if (!preg_match('/^\d{4}$/', $phoneLastFour)) {
            return json(['code' => 1, 'msg' => '请输入正确的手机号后四位（4位数字）']);
        }

        // 查找对应的维修人员
        $repairer = Db::name('repair_personnel')
            ->where('phone', 'like', '%' . $phoneLastFour)
            ->where('status', 1) // 启用状态
            ->find();

        if (!$repairer) {
            return json(['code' => 1, 'msg' => '未找到匹配的维修人员，请确认手机号后四位是否正确']);
        }

        // 查找该维修人员需要处理的维修流程
        $processList = Db::name('repair_process')
            ->where('repairer_id', $repairer['id'])
            ->whereIn('status', [0, 1]) // 包括进行中(0)和已完成(1)的状态
            ->whereIn('current_node', [3, 4, 5]) // 3=维修节点, 4=评价节点（等待验收）, 5=已完成
            ->field('id, process_no, store_id, store_name, manager_name, manager_phone, repair_date,
                    store_address, repairer_id, repairer_name, repairer_phone, expected_date,
                    current_node, status, create_time, update_time')
            ->select()
            ->toArray();

        // 格式化日期字段
        foreach ($processList as &$process) {
            if (!empty($process['repair_date'])) {
                $process['repair_date_format'] = date('Y-m-d', strtotime($process['repair_date']));
            }
            if (!empty($process['expected_date'])) {
                $process['expected_date_format'] = date('Y-m-d', strtotime($process['expected_date']));
            }
        }

        // 返回维修人员信息和流程列表
        return json([
            'code' => 0,
            'msg' => '验证成功',
            'data' => [
                'repairer_info' => [
                    'id' => $repairer['id'],
                    'name' => $repairer['name'],
                    'phone' => $repairer['phone']
                ],
                'process_list' => $processList
            ]
        ]);
    }

    /**
     * 维修详情页面
     */
    public function detail($id = 0)
    {
        if ($id <= 0) {
            return '<div style="text-align:center;padding:50px 0;color:#999;">参数错误</div>';
        }

        // 获取维修流程详情，允许查看已完成的流程
        $process = Db::name('repair_process')
            ->where('id', $id)
            ->whereIn('status', [0, 1]) // 包括进行中(0)和已完成(1)的状态
            ->find();

        if (!$process) {
            return '<div style="text-align:center;padding:50px 0;color:#999;">维修流程不存在或已结束</div>';
        }

        // 判断维修流程是否已超过维修节点或者已完成
        $canEdit = true;
        if ($process['current_node'] > 3 || $process['status'] == 1) {
            // 超过维修节点或已完成，标记为不可编辑
            $canEdit = false;
        }

        // 获取流程节点信息
        $nodes = Db::name('repair_process_nodes')
            ->where('process_id', $id)
            ->order('id asc')
            ->select()
            ->toArray();

        // 获取申请节点信息
        $apply_node = [];
        foreach ($nodes as $node) {
            if ($node['node_type'] == 1) { // 申请节点
                $apply_node = $node;
                break;
            }
        }

        // 获取审批节点信息
        $approve_node = [];
        foreach ($nodes as $node) {
            if ($node['node_type'] == 2) { // 审批节点
                $approve_node = $node;
                break;
            }
        }

        // 获取维修节点信息，用于回显维修备注
        $repair_node = [];
        foreach ($nodes as $node) {
            if ($node['node_type'] == 3) { // 维修节点
                $repair_node = $node;
                break;
            }
        }

        // 处理附件
        if (!empty($process['before_photos'])) {
            $process['before_photos'] = explode(',', $process['before_photos']);
        } else {
            $process['before_photos'] = [];
        }

        // 格式化日期
        if (!empty($process['repair_date'])) {
            $process['repair_date_format'] = date('Y-m-d', strtotime($process['repair_date']));
        }

        if (!empty($process['expected_date'])) {
            $process['expected_date_format'] = date('Y-m-d', strtotime($process['expected_date']));
        }

        View::assign('detail', $process);
        View::assign('apply_node', $apply_node);
        View::assign('approve_node', $approve_node);
        View::assign('repair_node', $repair_node);
        View::assign('can_edit', $canEdit); // 传递是否可编辑的标志

        return View::fetch();
    }

    /**
     * 更新维修信息
     */
    public function update()
    {
        if (!request()->isPost()) {
            return json(['code' => 1, 'msg' => '请求方式错误']);
        }

        // 获取POST数据，兼容不同格式
        $postData = request()->post();

        // 尝试获取JSON格式数据
        if (empty($postData)) {
            $inputData = file_get_contents('php://input');
            if (!empty($inputData)) {
                $postData = json_decode($inputData, true) ?: [];
            }
        }

        // 验证必填项
        if (empty($postData['process_id'])) {
            return json(['code' => 1, 'msg' => '参数错误']);
        }
        if (empty($postData['expected_date'])) {
            return json(['code' => 1, 'msg' => '请选择预计维修日期']);
        }

        // 查找维修流程
        $process = Db::name('repair_process')
            ->where('id', $postData['process_id'])
            ->where('status', 0) // 只验证是否进行中
            ->find();

        if (!$process) {
            return json(['code' => 1, 'msg' => '维修流程不存在或已结束']);
        }

        // 更新维修信息，只保留表中存在的字段
        $update = [
            'expected_date' => $postData['expected_date'],
            'update_time' => time()
        ];

        // 只有当流程还在维修节点(3)时，才推进到下一节点
        if ($process['current_node'] == 3) {
            $update['current_node'] = 4; // 推进到下一节点

            // 发送消息通知申请人（店长）
            try {
                // 构建消息数据，参考RepairService.php的实现方式
                $message_data = [
                    'process_no' => $process['process_no'] ?? '',
                    'store_name' => $process['store_name'] ?? '',
                    'manager_name' => $process['manager_name'] ?? '',
                    'action_id' => $process['id'],
                    'from_uid' => $this->approval_admin_id // 使用配置中的审批人ID
                ];

                $receiver_id = $this->temp_user_id == -1 ? $process['created_by'] : $this->temp_user_id;
                // 发消息日志
                Log::info('发送维修预计时间消息通知, 流程ID: ' . $process['id'] . ', 接收人ID: ' . $receiver_id . ', 消息数据: ' . json_encode($message_data, JSON_UNESCAPED_UNICODE));
                // 发送消息
                sendMessage($receiver_id, 63, $message_data, '您提交的『物品维修』申请，维修人员已安排维修时间');
                // 记录成功发送日志
                Log::info('已发送维修预计时间消息通知, 流程ID: ' . $process['id'] . ', 接收人ID: ' . $receiver_id);
            } catch (\Exception $e) {
                // 记录消息发送错误，但不影响流程继续
                Log::error('发送消息失败: ' . $e->getMessage());
            }
        }

        // 更新节点信息，将备注放在节点表中
        $nodeUpdate = [
            'handler_comments' => $postData['repairer_remark'] ?? '',
            'node_status' => 1, // 设置为已完成状态
            'finished_time' => time()
        ];

        Db::startTrans();
        try {
            // 更新流程表
            Db::name('repair_process')->where('id', $postData['process_id'])->update($update);

            // 更新节点表，将维修人员备注存储在handler_comments字段中
            $repairNode = Db::name('repair_process_nodes')
                ->where('process_id', $postData['process_id'])
                ->where('node_type', 3) // 维修节点
                ->find();

            if ($repairNode) {
                Db::name('repair_process_nodes')
                    ->where('id', $repairNode['id'])
                    ->update($nodeUpdate);
            } else {
                // 如果没有找到维修节点，创建一个新的节点
                Db::name('repair_process_nodes')->insert([
                    'process_id' => $postData['process_id'],
                    'node_type' => 3,
                    'node_status' => 1,
                    'handler_id' => 0,
                    'handler_name' => $process['repairer_name'] ?? '',
                    'handler_phone' => $process['repairer_phone'] ?? '',
                    'action_result' => 1,
                    'handler_comments' => $postData['repairer_remark'] ?? '',
                    'created_time' => time(),
                    'finished_time' => time()
                ]);
            }

            // 只有在当前是维修节点且将要推进到评价节点时，才创建评价节点记录
            if ($process['current_node'] == 3) {
                // 检查是否已存在评价节点
                $evaluationNode = Db::name('repair_process_nodes')
                    ->where('process_id', $postData['process_id'])
                    ->where('node_type', 4) // 评价节点
                    ->find();

                if (!$evaluationNode) {
                    // 创建评价节点
                    Db::name('repair_process_nodes')->insert([
                        'process_id' => $postData['process_id'],
                        'node_type' => 4, // 评价节点
                        'node_status' => 0, // 待处理状态
                        'handler_id' => $process['created_by'] ?? 0, // 处理人应为发起申请的店长ID
                        'handler_name' => $process['manager_name'] ?? '', // 评价由店长完成
                        'handler_phone' => $process['manager_phone'] ?? '',
                        'created_time' => time(),
                        'finished_time' => 0
                    ]);
                }
            }

            Db::commit();

            // 调用同步服务，将维修流程同步到审批流程表
            try {
                $approveMapService = new ApproveMapService();
                $syncResult = $approveMapService->syncToApprove($postData['process_id']);
                if (!$syncResult) {
                    Log::warning('维修流程同步失败，流程ID：' . $postData['process_id']);
                }
            } catch (\Exception $e) {
                Log::error('维修流程同步异常：' . $e->getMessage());
            }

            return json(['code' => 0, 'msg' => '更新成功']);
        } catch (\Exception $e) {
            Db::rollback();
            return json(['code' => 1, 'msg' => '更新失败: ' . $e->getMessage()]);
        }
    }

    /**
     * 维修信息提交成功页面
     */
    public function success()
    {
        $id = request()->param('id/d', 0);
        if ($id <= 0) {
            return '<div style="text-align:center;padding:50px 0;color:#999;">参数错误</div>';
        }

        // 获取维修流程详情
        $process = Db::name('repair_process')
            ->where('id', $id)
            ->find();

        if (!$process) {
            return '<div style="text-align:center;padding:50px 0;color:#999;">维修流程不存在</div>';
        }

        // 获取流程节点信息
        $nodes = Db::name('repair_process_nodes')
            ->where('process_id', $id)
            ->order('id asc')
            ->select()
            ->toArray();

        // 处理附件
        if (!empty($process['before_photos'])) {
            $process['before_photos_array'] = explode(',', $process['before_photos']);
        } else {
            $process['before_photos_array'] = [];
        }

        // 格式化日期
        if (!empty($process['repair_date'])) {
            $process['repair_date_format'] = date('Y-m-d', strtotime($process['repair_date']));
        }

        if (!empty($process['expected_date'])) {
            $process['expected_date_format'] = date('Y-m-d', strtotime($process['expected_date']));
        }

        View::assign('detail', $process);
        View::assign('nodes', $nodes);
        View::assign('success', true); // 标记为成功页面

        return View::fetch('detail');
    }
}
