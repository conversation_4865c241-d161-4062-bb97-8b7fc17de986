{extend name="../../base/view/common/base" /}
<!-- 主体 -->
{block name="body"}
<form class="layui-form p-4">
	<h3 class="pb-3">TOKEN配置</h3>
	<table class="layui-table layui-table-form">
		<tr>
			<td class="layui-td-gray-2">Token签发组织</td>
			<td>
				<input type="hidden" value="{$id}" name="id">
				<input type="text" name="iss" autocomplete="off" placeholder="请输入签发组织" lay-reqText="请输入签发组织" class="layui-input" value="{$config.iss|default=''}">
			</td>
			<td class="layui-td-gray-2">Token签发作者</td>
			<td>
				<input type="text" name="aud" autocomplete="off" placeholder="请输入签发作者" lay-reqText="请输入签发作者" class="layui-input" value="{$config.aud|default=''}">
			</td>
		</tr>
		<tr>
			<td class="layui-td-gray">Token Secrect</td>
			<td>
				<input type="text" name="secrect" autocomplete="off" placeholder="请输入secrect" lay-reqText="请输入secrect" class="layui-input" value="{$config.secrect|default=''}">
			</td>
			<td class="layui-td-gray">Token过期时间</td>
			<td>
				<input type="text" name="exptime" autocomplete="off" placeholder="请输入过期时间" lay-reqText="请输入过期时间" class="layui-input" value="{$config.exptime|default=''}">
			</td>
		</tr>
	</table>
	<div class="py-3">
		<button class="layui-btn layui-btn-normal" lay-submit="" lay-filter="webform">立即提交</button>
		<button type="reset" class="layui-btn layui-btn-primary">重置</button>
	</div>

	<div style="padding:30px 0;">
		<span class="layui-btn layui-btn-sm" onclick="testLogin();">Api测试登录</span>
		<span class="layui-btn layui-btn-sm" onclick="testToken();">Token测试</span>
	</div>
	<div style="padding:12px 0;word-wrap:break-word">
		<p class="red">先点击登录，在点击测试，会有好的结果哦！</p>
		测试结果：
		<div id="res"></div>
	</div>
</form>
{/block}
<!-- /主体 -->

<!-- 脚本 -->
{block name="script"}
<script>
	const moduleInit = ['tool'];
	function gouguInit() {
		var form = layui.form, tool = layui.tool;
		//监听提交
		form.on('submit(webform)', function (data) {
			let callback = function (e) {
				layer.msg(e.msg);
				if (e.code == 0) {
					tool.sideClose(1000);
				}
			}
			tool.post("/home/<USER>/edit", data.field, callback);
			return false;
		});
	}

	var token = 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJhcGkuZ291Z3VjbXMuY29tIiwiYXVkIjoiZ291Z3VjbXMiLCJpYXQiOjE2MjczMTY1MTgsImV4cCI6MTYyNzMyMDExOCwidWlkIjoxfQ.3soLDbwrEqn4EZtpD4h05FmvmMtJEh1LtE1vZ_ANcnI';
	function testToken() {
		$.ajax({
			headers: {
				Token: token
			},
			url: "/api/demo/test",
			type: "get",
			success: function (res) {
				$('#res').html(JSON.stringify(res));
				if (res.code == 111) {
					layer.msg(res.msg);
				}
			}
		});
	}

	function testLogin() {
		var content = '<form class="layui-form" style="width:400px"><div style="padding:10px 15px">\
							<p style="padding:10px 0">用户名：</p>\
							<p><input name="username" type="text" class="layui-input" value="admin"/></p>\
							<p style="padding:10px 0">密 码：</p>\
							<p><input name="password" type="password" class="layui-input" value="123456"/></p>\
							</div>\
						</form>';
		layer.open({
			type: 1,
			title: 'API测试用户登录',
			area: ['432px', '300px'],
			content: content,
			btnAlign: 'c',
			btn: ['登录'],
			yes: function (idx) {
				var username = $('[name="username"]').val();
				var password = $('[name="password"]').val();
				if (username == '') {
					layer.msg('请填写用户名');
					return;
				}
				if (password == '') {
					layer.msg('请填写密码');
					return;
				}
				$.ajax({
					url: "/api/demo/login",
					type: 'post',
					data: { username: username, password: password },
					success: function (res) {
						$('#res').html(JSON.stringify(res));
						layer.msg(res.msg);
						if (res.code == 0) {
							token = res.data.token;
							layer.close(idx);
						}
					}
				})
			}
		})
	}
</script>
{/block}
<!-- /脚本 -->