{extend name="../../base/view/common/base" /}
{block name="style"}
  <style>
	.layui-tree-entry{font-size:15px; line-height:24px}
	.layui-tree-set{padding:2px 0}
	.layui-tree-iconClick .layui-icon{color:#1E9FFF}
	.layui-icon layui-icon-file{font-size:16px;}
	.layui-tree-icon {height: 15px;width: 15px; text-align: center;border: 1px solid #1E9FFF; color:#1E9FFF}
	.layui-tree-line .layui-tree-set .layui-tree-set:after{top:18px;}
	.tree-left{width:200px; float:left; height:calc(100% - 30px); overflow: scroll; border:1px solid #eeeeee; background-color:#FAFAFA; padding:12px 12px 12px 5px;}
	.tree-left h3{font-size:16px; height:30px; padding-left:10px; font-weight:800}
	
	.gougu-upload-files{background-color: #ffffff; border:1px solid #e4e7ed;color: #c0c4cc;cursor: not-allowed; padding:0 12px; width:180px; box-sizing: border-box; display: inline-block; font-size: inherit; height: 38px; line-height: 35px; margin-right:8px; border-radius:2px;}
	.gougu-upload-tips{color:#969696}
	.layui-form-item{margin-bottom:8px;}
	.layui-input-block{min-height:24px;}
  </style>
{/block}
<!-- 主体 -->
{block name="body"}
<div class="p-3" style="height:100%; box-sizing: border-box;">
<!--	<div class="tree-left">-->
<!--		<h3>企业组织架构</h3>-->
<!--		<div id="depament"></div>-->
<!--	</div>-->
	<div class="body-table" style="overflow:hidden;">
		<form class="layui-form gg-form-bar border-t border-x" lay-filter="barsearchform">
			<div class="layui-input-inline" style="width:250px">
				<input name="dname" type="text" readonly id="tree2" lay-filter="tree2" class="layui-input" placeholder="选择部门" />
				<input type="hidden" name="did" class="layui-input" />
			</div>
			<div class="layui-input-inline" style="width:136px">
				<select name="status">
					<option value="">选择员工状态</option>
					<option value="1" selected>正常状态</option>
					<option value="2">离职状态</option>
					<option value="0">禁止登录</option>
				</select>
			</div>
			<div class="layui-input-inline" style="width:136px">
				<select name="type">
					<option value="">选择员工类型</option>
					<option value="1">正式员工</option>
					<option value="2">试用员工</option>
					<option value="3">实习员工</option>
				</select>
			</div>
			<div class="layui-input-inline" style="width:136px">
				<select name="position_id" lay-search="">
					<option value="">选择岗位职称</option>
					{volist name="position" id="vo"}
					<option value="{$vo.id}">{$vo.title}</option>
					{/volist}
				</select>
			</div>
			<div class="layui-input-inline" style="width:136px">
				<select name="rank_id" lay-search="">
					<option value="">选择职级</option>
					{volist name="rank" id="vo"}
					<option value="{$vo.id}">{$vo.rank}</option>
					{/volist}
				</select>
			</div>
			<div class="layui-input-inline" style="width:136px">
				<select name="label_id" lay-search="">
					<option value="">选择标签</option>
					{volist name="label" id="vo"}
					<option value="{$vo.id}">{$vo.name}</option>
					{/volist}
				</select>
			</div>
			<div class="layui-input-inline" style="width:136px">
				<select name="is_black" lay-search="">
					<option value="">--黑名单--</option>
					<option value="1">是</option>
					<option value="2">否</option>
				</select>
			</div>
			<div class="layui-input-inline" style="width:136px">
				<select name="pay_type" lay-search="">
					<option value="" >--代缴社保--</option>
					<option value="1" >公积金</option>
					<option value="2" >社保</option>
					<option value="3" >公积金&社保</option>
				</select>
			</div>
			<div class="layui-input-inline" style="width:240px">
				<input type="text" name="keywords" placeholder="输入关键字，如：ID/姓名/手机号码" class="layui-input" autocomplete="off" />
			</div>
			<div class="layui-input-inline" style="width:150px;">
				<button class="layui-btn layui-btn-normal" lay-submit="" lay-filter="webform"><i class="layui-icon layui-icon-search mr-1"></i>搜索</button>
				<button type="reset" class="layui-btn layui-btn-reset" lay-filter="reset">清空</button>
			</div>
		</form>
		<table class="layui-hide" id="test" lay-filter="test"></table>
	</div>
</div>

<script type="text/html" id="thumb">
	<img src="{{d.thumb}}" width="30" height="30" />
</script>
<script type="text/html" id="toolbara">
	<div class="layui-btn-group">
		<button class="layui-btn layui-btn-sm" lay-event="add"><i class="layui-icon">&#xe61f;</i>添加员工</button>
		<button class="layui-btn layui-btn-danger layui-btn-sm" lay-event="disable"><i class="layui-icon">&#x1006;</i>禁止登录</button>
		<button class="layui-btn layui-btn-warm layui-btn-sm" lay-event="recovery"><i class="layui-icon">&#xe605;</i>恢复正常</button>
		<button class="layui-btn layui-btn-normal layui-btn-sm" lay-event="import"><i class="layui-icon">&#xe66f;</i>批量导入</button>
		<button class="layui-btn layui-btn-normal layui-btn-sm" lay-event="import_annual_leave"><i class="layui-icon">&#xe66f;</i>年假导入</button>
	</div>
</script>

{/block}
<!-- /主体 -->

<!-- 脚本 -->
{block name="script"}
<script>
	const moduleInit = ['tool','tablePlus','customSelect'];
	function gouguInit() {
		var table = layui.tablePlus, tool = layui.tool,upload = layui.upload;
		var custom_select = layui.customSelect
		var form = layui.form;
		var tree = layui.tree;
		// $.ajax({
		// 	url: "/api/index/get_department_tree",
		// 	type:'get',
		// 	success:function(res){
		// 		//仅节点左侧图标控制收缩
		// 		tree.render({
		// 			elem: '#depament',
		// 			data: res.trees,
		// 			onlyIconControl: true,  //是否仅允许节点左侧图标控制展开收缩
		// 			click: function(obj){
		// 				//layer.msg(JSON.stringify(obj.data));
		// 				layui.pageTable.reload({
		// 				   where: {did: obj.data.id}
		// 				   ,page:{curr:1}
		// 				});
		// 				$('[name="keywords"]').val('');
		// 				$('[name="status"]').val('');
		// 				$('[name="type"]').val('');
		// 				layui.form.render('select');
		// 			}
		// 		});
		// 	}
		// })

		$.ajax({
			url: "/api/index/get_department_tree",
			type:'get',
			success:function(res){
				//仅节点左侧图标控制收缩
				custom_select.render({
					el: 'tree2',
					data: res.trees,
					type:'radio',
					checkbox: true,
					checked: function (obj) {
						let value = obj.combData.idents
						let did = $('input[name="did"]').val();
						console.log(obj.combData)
						console.log(did)
						if (did == value){
							$('input[name="dname"]').val('');
							$('input[name="did"]').val('');
						}else{
							$('input[name="did"]').val(obj.combData.idents);
						}

					}
				})

			}
		})

		let uploadFiles;
		function clearFile() {
			for (let x in uploadFiles) {
				delete uploadFiles[x];
			}
			$('#gougu-upload-choosed').html('');
		}
		function uploadImport(){
			layer.open({
				'title':'批量导入员工',
				'type':1,
				'area': ['640px', '360px'],
				'content':'<div class="layui-form p-3">\
						<div id="uploadType1">\
							<div class="layui-form-item">\
								<label class="layui-form-label">文件：</label>\
								<div class="layui-input-block">\
									<span class="gougu-upload-files">.xls,.xlsx</span><button type="button" class="layui-btn layui-btn-normal" id="uploadSelect">选择文件</button><a href="/static/home/<USER>/仲正堂OA员工导入模板.xlsx" class="layui-btn ml-4">Execl表格模板下载</a>\
								</div>\
							</div>\
							<div class="layui-form-item">\
								<label class="layui-form-label"></label>\
								<div class="layui-input-block">\
									<span class="gougu-upload-tips">1、只有超级管理员才能进行批量导入操作；<br>2、只能上传 .xls、.xlsx文件；<br>3、数据请勿放在合并的单元格中；<br>4、文件大小请勿超过2MB，导入数据不能超过3000条<br>5、如果导入失败，请注意检查表格数据，格式按照样本填写，部门、职位需要系统中存在，不存在的话可能会导入失败。</span>\
								</div>\
							</div>\
							<div class="layui-form-item">\
								<label class="layui-form-label"></label>\
								<div class="layui-input-block green" id="gougu-upload-choosed"></div>\
							</div>\
							<div class="layui-form-item">\
								<label class="layui-form-label"></label>\
								<div class="layui-input-block red" id="gougu-upload-note"></div>\
							</div>\
							<div class="layui-form-item layui-form-item-sm">\
								<label class="layui-form-label"></label>\
								<div class="layui-input-block">\
									<button type="button" class="layui-btn" id="uploadAjax">上传并导入</button>\
								</div>\
							</div>\
						</div> \
				</div>',
				success: function(layero, idx){
					form.render();
					//选文件
					let uploadImport = upload.render({
						elem: '#uploadSelect'
						,url: '/api/import/import_admin'
						,auto: false
						,accept: 'file' //普通文件
						,acceptMime: 'application/vnd.ms-excel,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' // 此处设置上传的文件格式
						,exts: 'xls|xlsx' //只允许上传文件格式
						,bindAction: '#uploadAjax'
						,choose: function(obj){
							uploadFiles = obj.pushFile();
							// 清空,防止多次上传
							clearFile();
							obj.preview(function(index, file, result){
								obj.pushFile();// 再添加
								$('#gougu-upload-choosed').html('已选择：'+file.name);
							});
						}
						,before: function(obj){
						}
						,progress: function(n, elem, e){
							$('#gougu-upload-note').html('文件上转中...');
							if(n==100){
								$('#gougu-upload-note').html('数据导入中...');
							}
						}
						,error: function(index, upload){
							clearFile();
							$('#gougu-upload-note').html('数据导入失败，请关闭重试');
						}
						,done: function(res, index, upload){
							clearFile();
							layer.msg(res.msg);
							$('#gougu-upload-note').html(res.msg);
							if(res.code==0){
								layer.close(idx);
								layui.pageTable.reload();
							}						
						}
					});
				}
			});	
		}
		function uploadImportAnnualLeave(){
			layer.open({
				'title':'批量导入员工',
				'type':1,
				'area': ['640px', '360px'],
				'content':'<div class="layui-form p-3">\
						<div id="uploadType1">\
							<div class="layui-form-item">\
								<label class="layui-form-label">文件：</label>\
								<div class="layui-input-block">\
									<span class="gougu-upload-files">.xls,.xlsx</span><button type="button" class="layui-btn layui-btn-normal" id="uploadSelect">选择文件</button><a href="/api/export/exportAnnualLeave" class="layui-btn ml-4">Execl表格模板下载</a>\
								</div>\
							</div>\
							<div class="layui-form-item">\
								<label class="layui-form-label"></label>\
								<div class="layui-input-block">\
									<span class="gougu-upload-tips">1、只有超级管理员才能进行批量导入操作；<br>2、只能上传 .xls、.xlsx文件；<br>3、数据请勿放在合并的单元格中；<br>4、文件大小请勿超过2MB，导入数据不能超过3000条<br>5、如果导入失败，请注意检查表格数据，格式按照样本填写，部门、职位需要系统中存在，不存在的话可能会导入失败。</span>\
								</div>\
							</div>\
							<div class="layui-form-item">\
								<label class="layui-form-label"></label>\
								<div class="layui-input-block green" id="gougu-upload-choosed"></div>\
							</div>\
							<div class="layui-form-item">\
								<label class="layui-form-label"></label>\
								<div class="layui-input-block red" id="gougu-upload-note"></div>\
							</div>\
							<div class="layui-form-item layui-form-item-sm">\
								<label class="layui-form-label"></label>\
								<div class="layui-input-block">\
									<button type="button" class="layui-btn" id="uploadAjax">上传并导入</button>\
								</div>\
							</div>\
						</div> \
				</div>',
				success: function(layero, idx){
					form.render();
					//选文件
					let uploadImport = upload.render({
						elem: '#uploadSelect'
						,url: '/api/import/import_annual_leave'
						,auto: false
						,accept: 'file' //普通文件
						,acceptMime: 'application/vnd.ms-excel,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' // 此处设置上传的文件格式
						,exts: 'xls|xlsx' //只允许上传文件格式
						,bindAction: '#uploadAjax'
						,choose: function(obj){
							uploadFiles = obj.pushFile();
							// 清空,防止多次上传
							clearFile();
							obj.preview(function(index, file, result){
								obj.pushFile();// 再添加
								$('#gougu-upload-choosed').html('已选择：'+file.name);
							});
						}
						,before: function(obj){
						}
						,progress: function(n, elem, e){
							$('#gougu-upload-note').html('文件上转中...');
							if(n==100){
								$('#gougu-upload-note').html('数据导入中...');
							}
						}
						,error: function(index, upload){
							clearFile();
							$('#gougu-upload-note').html('数据导入失败，请关闭重试');
						}
						,done: function(res, index, upload){
							clearFile();
							layer.msg(res.msg);
							$('#gougu-upload-note').html(res.msg);
							if(res.code==0){
								layer.close(idx);
								layui.pageTable.reload();
							}
						}
					});
				}
			});
		}
		
		layui.pageTable = table.render({
			elem: '#test',
			title: '员工列表',
			toolbar: '#toolbara',
			defaultToolbar: [
				'filter',
				{
					title: '导出',
					layEvent: 'LAYTABLE_export',
					icon: 'layui-icon-export'
				}
			],
			url: "/user/user/index?status=1", //数据接口
			page: true, //开启分页
			limit: 20,
			height: 'full-85',
			cols: [
				[
					{type:'checkbox',fixed:'left'},
					{
						field: 'workno',
						title: '工号',
						align: 'center',
						width: 80
					}, {
						field: 'status',
						title: '状态',
						align: 'center',
						width: 80,
						templet: function (d) {
							var html = '<span class="layui-badge layui-bg-green">正常</span>';
							if(d.status == 2){
								html = '<span class="layui-badge layui-bg-orange">已离职</span>'
							}
							else if(d.status == 0){
								html = '<span class="layui-badge">被禁用</span>'
							}
							return html;
						}
					},
					{
					field: 'is_black', title: '黑名单', align: 'center', width: 80, templet: function (d) {
							let html = ``;

							if (d.is_black == 1){
								html = `<span class="layui-btn layui-btn-xs layui-btn-normal" style="background-color: #000">黑名单</span>`;
							}
							return html;
						}
					},{
						field: 'type',
						title: '员工类型',
						align: 'center',
						width: 80,
						templet: function (d) {
							var html = '<span style="color:#393D49">正式员工</span>';
							if(d.type == 2){
								html = '<span style="color:#01AAED">试用员工</span>'
							}
							else if(d.type == 3){
								html = '<span style="color:#5FB878">实 习 生</span>'
							}
							return html;
						}
					},
					// {
					// 	field: 'username',
					// 	title: '登录账号',
					// 	width: 132
					// },
					{
						field: 'name',
						title: '用户姓名',
						align: 'center',
						width: 80
					},{
						field: 'mobile',
						title: '手机号码',
						align: 'center',
						width: 120
					},{
						field: 'sex',
						title: '性别',
						align: 'center',
						width: 60,
						templet: function (d) {
							var html = '未知';
							if(d.sex == 1){
								html = '男'
							}
							else if(d.sex == 2){
								html = '女'
							}
							return html;
						}
					},
					{
						field: 'd0',
						title: '一级部门',
						align: 'center',
						width: 110
					},
					{
						field: 'd1',
						title: '二级部门',
						align: 'center',
						width: 110
					},
					{
						field: 'd2',
						title: '三级部门',
						align: 'center',
						width: 110
					},
					{
						field: 'd3',
						title: '四级部门',
						align: 'center',
						width: 110
					},
					// {
					// 	field: 'department',
					// 	title: '所在部门',
					// 	align: 'center',
					// 	width: 110
					// },
					{
						field: 'position',
						title: '岗位职称',
						align: 'center',
						width: 110
					},
					{
						field: 'o_admin_dep',
						title: '兼岗',
						align: 'center',
						width: 150
					},
					{
						field: 'label_name',
						title: '标签',
						align: 'center',
					}, {
						field: 'entry_time',
						title: '入职日期',
						align: 'center',
					}, {
						field: 'res_date',
						title: '离职日期',
						align: 'center',
					},{
						field: 'right',
						fixed:'right',
						title: '操作',
						width: 160,
						align: 'center',
						templet: function (d) {
							var html = '';
							var btn1 = '<span class="layui-btn layui-btn-xs layui-btn-normal" lay-event="view">详情</span>';
							var btn2 = '<span class="layui-btn layui-btn-xs" lay-event="edit">编辑</span>';
							var btn3 = '<span class="layui-btn layui-btn-xs layui-btn-warm" lay-event="copy">复制密码</span>';
							var btn4 = '<span class="layui-btn layui-btn-xs layui-btn-danger" lay-event="psw">重置密码</span>';
							if(d.reg_pwd == ''){
								html = '<div class="layui-btn-group">'+btn1+btn2+btn4+'</div>';
							}
							else{
								html = '<div class="layui-btn-group">'+btn1+btn2+btn3+'</div>';
							}
							return html;
						}						
					}
				]
			]
		});

		//表头工具栏事件
		table.on('toolbar(test)', function(obj){
			var checkStatus = table.checkStatus(obj.config.id); //获取选中行状态
			var data = checkStatus.data;
			if (obj.event === 'add') {
				tool.side("/user/user/add");
				return;
			}
			if (obj.event === 'import') {
				uploadImport();	
				return;				
			}
			if (obj.event === 'import_annual_leave') {
				uploadImportAnnualLeave();
				return;
			}
			if (obj.event === 'LAYTABLE_export') {
				var formValues = $('form').serializeArray();

				var param = '';
				for (let i = 0; i < formValues.length; i++) {
					if (param){
						param = `${param}&${formValues[i].name}=${formValues[i].value}`
					}else{
						param = `${formValues[i].name}=${formValues[i].value}`
					}
				}
				var loadIndex = layer.load(0);
				window.location = `/api/export/export_user?${param}`;
				setTimeout(function(){
					layer.close(loadIndex)
				}, 10000);
				// $.ajax({
				// 	url: `/api/export/export_user?${param}`,
				// 	type:'post',
				// 	data:formValues,
				// 	success:function(e){
				// 		layer.close(loadIndex)
				// 	},error:function (e){
				// 		layer.close(loadIndex)
				// 	}
				// })
				return;
			}
			// if(obj.config.where.status == 2 && obj.event !== 'recovery'){
			// 	layer.msg('离职员工不支持操作');
			// 	return false;
			// }

			var uidArray=[],msg='是否执行该操作？',type=0;
			for(var i=0;i<data.length;i++){
				uidArray.push(data[i].id);
			}
			if (obj.event === 'disable') {
				if(data.length==0){
					layer.msg('请选择要操作的员工');
					return false;
				}
				layer.confirm('您确定要把选中的员工设为禁止登录?', {
					icon: 3,
					title: '提示'
				}, function (index) {
					let callback = function (e) {
						layer.msg(e.msg);
						if (e.code == 0) {
							layui.pageTable.reload();
						}
					}
					tool.post("/user/user/set", {ids: uidArray.join(','),type:0}, callback);
					layer.close(index);
				});
			}
			
			if (obj.event === 'recovery') {
				if(data.length==0){
					layer.msg('请选择要操作的员工');
					return false;
				}
				layer.confirm('您确定要把选中的员工恢复正常?', {
					icon: 3,
					title: '提示'
				}, function (index) {
					let callback = function (e) {
						layer.msg(e.msg);
						if (e.code == 0) {
							layui.pageTable.reload();
						}
					}
					tool.post("/user/user/set", {ids: uidArray.join(','),type:1}, callback);
					layer.close(index);
				});
			}

		});

		//监听行工具事件
		table.on('tool(test)', function (obj) {
			var data = obj.data;			
			if (obj.event === 'view') {
				tool.side('/user/user/view?id='+data.id);
				return;
			}
			// if(obj.data.status == 2){
			// 	layer.msg('离职员工不支持操作');
			// 	return false;
			// }
			if(obj.event === 'edit'){
				tool.side('/user/user/add?id='+data.id);
			}
			else if (obj.event === 'copy') {
				copyToClip(data.reg_pwd);
				return;
			}
			else if (obj.event === 'psw') {
				layer.confirm('确定要重设该用户的密码？', {
					icon: 3,
					title: '提示'
				}, function (index) {
					let callback = function (e) {
						layer.msg(e.msg);
						if (e.code == 0) {
							layui.pageTable.reload();
						}
					}
					tool.post("/user/user/reset_psw", {id: data.id}, callback);
					layer.close(index);
				});
				return;
			}
		});

		//监听搜索提交
		form.on('submit(webform)', function (data) {

			layui.pageTable.reload({
				where: {
					keywords: data.field.keywords,
					status: data.field.status,
					type: data.field.type,
					position_id: data.field.position_id,
					did: data.field.did,
					is_black: data.field.is_black,
					pay_type: data.field.pay_type,
					rank_id: data.field.rank_id,
					label_id: data.field.label_id,
				},
				page: {
					curr: 1
				}
			});
			return false;
		});
	}	
	
	//拷贝密码
	function copyToClip(content) {
		var save = function(e){
			e.clipboardData.setData('text/plain', content);
			e.preventDefault();
		}
		document.addEventListener('copy', save);
		document.execCommand('copy');
		document.removeEventListener('copy',save);
		if (content != '') {
			layer.msg('复制成功');
		}
	}
</script>
{/block}
<!-- /脚本 -->