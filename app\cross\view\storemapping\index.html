{extend name="../../base/view/common/base" /}

{block name="style"}
<style>
    .layui-form-item {
        margin-bottom: 0;
        display: inline-block;
        vertical-align: top;
    }

    .layui-form-item .layui-inline {
        margin-bottom: 0;
        margin-right: 5px;
    }

    .layui-input-block {
        min-height: 24px;
    }

    .gg-form-bar .layui-input-inline {
        margin-bottom: 0;
        margin-right: 5px;
        vertical-align: top;
    }

    /* xmselect组件样式 */
    .label-content {
        max-height: 34px !important;
        width: 125px !important;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }

    .xm-select {
        height: 34px !important;
        line-height: 34px !important;
    }

    /* 按钮组样式 */
    .filter-button-group .layui-btn {
        display: inline-block !important;
        margin-right: 5px !important;
        margin-bottom: 0 !important;
    }

    /* 状态标签样式 */
    .status-enabled {
        color: #5FB878;
        font-weight: bold;
    }

    .status-disabled {
        color: #FF5722;
        font-weight: bold;
    }
</style>
{/block}

{block name="body"}
<div class="p-3" style="height:100%; box-sizing: border-box;">
    <div class="body-table" style="overflow:hidden;">
        <!-- 搜索表单 -->
        <form class="layui-form gg-form-bar border-t border-x" lay-filter="webform" style="padding-bottom:10px;">
            <div class="layui-form-item">
                <input type="text" name="oa_name" placeholder="请输入门店名称" autocomplete="off" class="layui-input"
                    style="width:150px">
            </div>
            <div id="store-select-container" class="layui-input-inline" style="width:200px; height: 38px;">
            </div>
            <!-- 按钮组 -->
            <div class="layui-input-inline filter-button-group" style="width: auto; white-space: nowrap;">
                <button class="layui-btn layui-btn-normal" lay-submit="" lay-filter="webform"><i
                        class="layui-icon layui-icon-search mr-1"></i>搜索
                </button>
                <button type="button" class="layui-btn layui-btn-primary" id="resetBtn"><i
                        class="layui-icon layui-icon-refresh mr-1"></i>重置
                </button>
            </div>
        </form>

        <!-- 标题区域 -->
        <div style="margin: 10px 0;">
            <span style="font-weight:600">门店名称映射表</span>
        </div>

        <!-- 数据表格 -->
        <table class="layui-hide" id="dataTable" lay-filter="dataTable"></table>
    </div>
</div>

<!-- 表格工具栏模板 -->
<script type="text/html" id="toolbarDemo">
    <div class="layui-btn-container">
        <button class="layui-btn layui-btn-normal layui-btn-sm" lay-event="add">
            <i class="layui-icon layui-icon-add-1"></i> 新增
        </button>
    </div>
</script>

<!-- 操作列模板 -->
<script type="text/html" id="operationTpl">
    <div class="layui-btn-group">
        <a class="layui-btn layui-btn-xs" lay-event="view">查看</a>
        <a class="layui-btn layui-btn-xs layui-btn-normal" lay-event="edit">编辑</a>
        <a class="layui-btn layui-btn-xs layui-btn-danger" lay-event="delete">删除</a>
    </div>
</script>

<!-- 状态列模板 -->
<script type="text/html" id="statusTpl">
    {{# if(d.status == 1) { }}
        <span class="status-enabled">启用</span>
    {{# } else { }}
        <span class="status-disabled">禁用</span>
    {{# } }}
</script>
{/block}

{block name="script"}
<script>
    const moduleInit = ['tool', 'tablePlus'];
    function gouguInit() {
        var table = layui.tablePlus, form = layui.form, tool = layui.tool;

        // 门店选择组件
        var storeMultiSelect;

        // 等待DOM完全加载后再初始化
        $(document).ready(function() {
            // 检查元素是否存在
            if ($('#store-select-container').length === 0) {
                console.error('门店选择容器元素不存在');
                return;
            }

            // 初始化门店选择组件
            storeMultiSelect = xmSelect.render({
                el: '#store-select-container',
                name: 'department_ids',
                language: 'zn',
                filterable: true,
                tips: '请选择门店',
                data: [],
                model: { label: { type: 'xm-select-count', max: 0 } },
                prop: {
                    name: 'title',
                    value: 'id'
                },
                placeholder: '全部门店',
                radio: true, // 单选模式
                clickClose: true,
                on: function (data) {
                    // 门店选择变化时不立即刷新
                }
            });

            // 初始化完成后加载数据
            loadStoreList();
        });

        // 加载门店列表
        function loadStoreList() {
            // 确保组件已经初始化
            if (!storeMultiSelect) {
                console.error('门店选择组件未初始化');
                return;
            }

            $.ajax({
                url: '/api/cross/getStoreList',
                type: 'post',
                dataType: 'json',
                success: function (res) {
                    if (res.code == 0) {
                        var storesForXmSelect = [];
                        if (res.data && res.data.length > 0) {
                            storesForXmSelect = res.data.map(function (item) {
                                return { id: item.id, title: item.title };
                            });
                        }
                        storeMultiSelect.update({
                            data: storesForXmSelect,
                            autoRow: true
                        });
                    } else {
                        layer.msg(res.msg || '获取门店列表失败', { icon: 2 });
                    }
                },
                error: function () {
                    layer.msg('获取门店列表失败', { icon: 2 });
                }
            });
        }

        // 渲染表格
        try {
            layui.pageTable = table.render({
                elem: '#dataTable',
                toolbar: '#toolbarDemo',
                title: '门店名称映射表',
                page: false, // 禁用分页
                height: 'full-150',
                url: "/cross/storemapping/index",
                loading: true,
                even: true,
                cols: [[
                    { field: 'department_name', title: '门店', minWidth: 120, align: 'center' },
                    { field: 'oa_name', title: 'OA门店名称', minWidth: 120, align: 'center' },
                    { field: 'reconciliation_table', title: '对账表名称', minWidth: 150, align: 'center' },
                    { field: 'meituan_name', title: '美团门店名称', minWidth: 180, align: 'center' },
                    { field: 'youzan_name', title: '有赞门店名称', minWidth: 180, align: 'center' },
                    { field: 'dianping_name', title: '大众点评名称', minWidth: 180, align: 'center' },
                    { field: 'status', title: '状态', width: 80, align: 'center', templet: '#statusTpl' },
                    { title: '操作', width: 180, align: 'center', toolbar: '#operationTpl', fixed: 'right' }
                ]],
                text: {
                    none: '暂无相关数据'
                },
                done: function (res, curr, count) {
                    if (res.code !== 0) {
                        console.error('表格数据加载失败:', res.msg);
                    }
                }
            });
        } catch (e) {
            console.error('表格渲染失败:', e);
            layer.msg('表格初始化失败，请刷新页面重试', { icon: 2 });
        }

        // 监听搜索按钮
        form.on('submit(webform)', function (data) {
            var selectedStoreIds = [];
            if (storeMultiSelect && storeMultiSelect.getValue) {
                selectedStoreIds = storeMultiSelect.getValue().map(function (item) {
                    return item.id;
                });
            }

            var searchData = data.field;
            searchData.department_ids = selectedStoreIds;

            layui.pageTable.reload({
                where: searchData
            });
            return false;
        });

        // 监听重置按钮
        $('#resetBtn').on('click', function () {
            // 重置表单
            form.val('webform', {
                oa_name: ''
            });

            // 重置门店选择
            if (storeMultiSelect && storeMultiSelect.setValue) {
                storeMultiSelect.setValue([]);
            }
            form.render();

            // 重置后自动执行搜索
            layui.pageTable.reload({
                where: {
                    oa_name: '',
                    department_ids: []
                }
            });
        });

        // 监听头工具栏事件
        table.on('toolbar(dataTable)', function (obj) {
            switch (obj.event) {
                case 'add':
                    tool.side('/cross/storemapping/edit');
                    break;
            }
        });

        // 监听行工具事件
        table.on('tool(dataTable)', function (obj) {
            var data = obj.data;

            switch (obj.event) {
                case 'view':
                    tool.side('/cross/storemapping/view?id=' + data.id);
                    break;
                case 'edit':
                    tool.side('/cross/storemapping/edit?id=' + data.id);
                    break;
                case 'delete':
                    layer.confirm('确定要删除这条记录吗？', function (index) {
                        $.post('/cross/storemapping/delete', { id: data.id }, function (res) {
                            if (res.code == 0) {
                                layer.msg(res.msg, { icon: 1 });
                                layui.pageTable.reload();
                            } else {
                                layer.msg(res.msg, { icon: 2 });
                            }
                        }, 'json');
                        layer.close(index);
                    });
                    break;
            }
        });

        // 监听抽屉关闭事件，刷新表格
        $(document).on('sideClose', function () {
            if (layui.pageTable && layui.pageTable.reload) {
                layui.pageTable.reload();
            }
        });
    }
</script>
{/block}