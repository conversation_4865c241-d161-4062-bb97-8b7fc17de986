<?php
/**
 * @copyright Copyright (c) 2021 仲正堂工作室
 * @license https://opensource.org/licenses/GPL-3.0
 * @link https://www.gougucms.com
 */

namespace app\cross\model;

use think\Model;
use think\facade\Db;

class CrossStoreSettleCardDetail extends Model
{
    protected $name = 'cross_store_settle_card_detail';

    // 设置字段信息
    protected $schema = [
        'id'                           => 'int',
        'settlement_type'              => 'string',
        'consume_store_id'             => 'int',
        'product_name'                 => 'string',
        'product_quantity'             => 'int',
        'card_name'                    => 'string',
        'settlement_amount'            => 'decimal',
        'settlement_time'              => 'datetime',
        'card_open_store_id'           => 'int',
        'order_number'                 => 'string',
        'related_order_number'         => 'string',
        'customer_name'                => 'string',
        'customer_mobile'              => 'string',
        'customer_belong_store_id'     => 'int',
        'period'                       => 'string',
        'create_time'                  => 'int',
        'update_time'                  => 'int',
    ];

    // 自动时间戳
    protected $autoWriteTimestamp = true;
    protected $createTime = 'create_time';
    protected $updateTime = 'update_time';

    /**
     * 关联消费门店信息
     */
    public function consumeStore()
    {
        return $this->belongsTo('app\user\model\Department', 'consume_store_id', 'id');
    }

    /**
     * 关联开卡门店信息
     */
    public function cardOpenStore()
    {
        return $this->belongsTo('app\user\model\Department', 'card_open_store_id', 'id');
    }

    /**
     * 关联客户归属门店信息
     */
    public function customerBelongStore()
    {
        return $this->belongsTo('app\user\model\Department', 'customer_belong_store_id', 'id');
    }

    /**
     * 获取结算明细列表
     * @param array $where 查询条件
     * @param int $page 页码
     * @param int $limit 每页数量
     * @return array
     */
    public static function getList($where = [], $page = 1, $limit = 20)
    {
        $query = self::alias('cscd')
            ->join('oa_department cs', 'cs.id = cscd.consume_store_id', 'LEFT')
            ->join('oa_department cos', 'cos.id = cscd.card_open_store_id', 'LEFT')
            ->join('oa_department cbs', 'cbs.id = cscd.customer_belong_store_id', 'LEFT')
            ->field('cscd.*, cs.title as consume_store_name, 
                     cos.title as card_open_store_name, cbs.title as customer_belong_store_name');

        if (!empty($where)) {
            $query->where($where);
        }

        $list = $query->order('cscd.settlement_time desc, cscd.id desc')
            ->paginate($limit, false, ['page' => $page])
            ->each(function ($item) {
                // 格式化金额显示
                $item['settlement_amount_formatted'] = number_format(floatval($item['settlement_amount']), 2);
                
                // 格式化时间
                $item['settlement_time_formatted'] = !empty($item['settlement_time']) ? date('Y-m-d H:i:s', strtotime($item['settlement_time'])) : '';
                $item['create_time_formatted'] = date('Y-m-d H:i:s', $item['create_time']);
                $item['update_time_formatted'] = date('Y-m-d H:i:s', $item['update_time']);
                
                return $item;
            });

        return $list;
    }

    /**
     * 获取结算明细详情
     * @param int $id 记录ID
     * @return array
     */
    public static function getDetail($id)
    {
        $detail = self::alias('cscd')
            ->join('oa_department cs', 'cs.id = cscd.consume_store_id', 'LEFT')
            ->join('oa_department cos', 'cos.id = cscd.card_open_store_id', 'LEFT')
            ->join('oa_department cbs', 'cbs.id = cscd.customer_belong_store_id', 'LEFT')
            ->field('cscd.*, cs.title as consume_store_name, 
                     cos.title as card_open_store_name, cbs.title as customer_belong_store_name')
            ->where('cscd.id', $id)
            ->find();

        if (empty($detail)) {
            return [];
        }

        return $detail;
    }

    /**
     * 获取结算类型选项
     * @return array
     */
    public static function getSettlementTypeOptions()
    {
        return [
            '次卡跨店消费' => '次卡跨店消费',
            '次卡跨店消费退款' => '次卡跨店消费退款',
        ];
    }

    /**
     * 批量导入结算明细数据
     * @param array $data 数据数组
     * @return array 导入结果
     */
    public static function batchImport($data)
    {
        $successCount = 0;
        $failCount = 0;
        $errors = [];

        Db::startTrans();
        try {
            foreach ($data as $index => $row) {
                // 数据验证和处理
                $insertData = self::processImportData($row, $index + 1, $errors, $failCount);
                
                if ($insertData === false) {
                    continue;
                }

                // 插入数据
                $result = self::create($insertData);
                if ($result) {
                    $successCount++;
                } else {
                    $errors[] = "第" . ($index + 1) . "行：数据保存失败";
                    $failCount++;
                }
            }

            Db::commit();
        } catch (\Exception $e) {
            Db::rollback();
            $errors[] = "导入过程中发生错误：" . $e->getMessage();
            return [
                'success' => false,
                'message' => '导入失败',
                'success_count' => 0,
                'fail_count' => count($data),
                'errors' => $errors
            ];
        }

        return [
            'success' => $failCount === 0,
            'message' => $failCount === 0 ? '导入成功' : '部分导入失败',
            'success_count' => $successCount,
            'fail_count' => $failCount,
            'errors' => $errors
        ];
    }

    /**
     * 处理导入数据
     * @param array $row 单行数据
     * @param int $rowNumber 行号
     * @param array &$errors 错误信息数组
     * @param int &$failCount 失败计数
     * @return array|false
     */
    private static function processImportData($row, $rowNumber, &$errors, &$failCount)
    {
        // 基础字段验证
        $settlementType = trim($row['settlement_type'] ?? '');
        $consumeStoreName = trim($row['consume_store_name'] ?? '');
        $cardOpenStoreName = trim($row['card_open_store_name'] ?? '');
        $customerBelongStoreName = trim($row['customer_belong_store_name'] ?? '');

        if (empty($settlementType)) {
            $errors[] = "第{$rowNumber}行：结算类型不能为空";
            $failCount++;
            return false;
        }

        // 门店名称转换为ID
        $storeIds = self::getStoreIdsByNames([
            'consume_store' => $consumeStoreName,
            'card_open_store' => $cardOpenStoreName,
            'customer_belong_store' => $customerBelongStoreName
        ]);

        if (!$storeIds['consume_store_id']) {
            $errors[] = "第{$rowNumber}行：消费门店「{$consumeStoreName}」不存在或已停用";
            $failCount++;
            return false;
        }

        // 处理时间字段
        $settlementTime = null;
        if (!empty($row['settlement_time'])) {
            $settlementTime = date('Y-m-d H:i:s', strtotime($row['settlement_time']));
        }

        return [
            'settlement_type' => $settlementType,
            'consume_store_id' => $storeIds['consume_store_id'],
            'product_name' => trim($row['product_name'] ?? ''),
            'product_quantity' => intval($row['product_quantity'] ?? 0),
            'card_name' => trim($row['card_name'] ?? ''),
            'settlement_amount' => floatval($row['settlement_amount'] ?? 0),
            'settlement_time' => $settlementTime,
            'card_open_store_id' => $storeIds['card_open_store_id'] ?: 0,
            'order_number' => trim($row['order_number'] ?? ''),
            'related_order_number' => trim($row['related_order_number'] ?? ''),
            'customer_name' => trim($row['customer_name'] ?? ''),
            'customer_mobile' => trim($row['customer_mobile'] ?? ''),
            'customer_belong_store_id' => $storeIds['customer_belong_store_id'] ?: 0,
            'period' => trim($row['period'] ?? ''),
            'create_time' => time(),
            'update_time' => time()
        ];
    }

    /**
     * 根据门店名称获取门店ID
     * @param array $storeNames 门店名称数组
     * @return array
     */
    private static function getStoreIdsByNames($storeNames)
    {
        $result = [
            'consume_store_id' => 0,
            'card_open_store_id' => 0,
            'customer_belong_store_id' => 0
        ];

        $allStoreNames = array_filter(array_values($storeNames));
        if (empty($allStoreNames)) {
            return $result;
        }

        $stores = Db::name('Department')
            ->where('title', 'in', $allStoreNames)
            ->where('status', 1)
            ->select()
            ->toArray();

        $storeMap = [];
        foreach ($stores as $store) {
            $storeMap[$store['title']] = $store['id'];
        }

        foreach ($storeNames as $key => $storeName) {
            if (!empty($storeName) && isset($storeMap[$storeName])) {
                $result[$key . '_id'] = $storeMap[$storeName];
            }
        }

        return $result;
    }

    /**
     * 获取统计汇总数据
     * @param array $where 查询条件
     * @return array
     */
    public static function getSummaryData($where = [])
    {
        $query = self::where($where);

        $summaryData = $query->field([
            'SUM(settlement_amount) as total_settlement_amount',
            'SUM(product_quantity) as total_quantity',
            'COUNT(*) as total_count'
        ])->find();

        return [
            'total_settlement_amount' => number_format(floatval($summaryData['total_settlement_amount'] ?? 0), 2),
            'total_quantity' => intval($summaryData['total_quantity'] ?? 0),
            'total_count' => intval($summaryData['total_count'] ?? 0)
        ];
    }
}
