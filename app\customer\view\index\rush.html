{extend name="../../base/view/common/base" /}
<!-- 主体 -->
{block name="body"}
<div class="p-3">
	<div class="layui-row gg-form-bar border-x border-t">
		<div class="layui-input-inline">
			<button class="layui-btn layui-btn-danger" lay-submit="" lay-filter="webform">去抢客</button>
		</div>
    </div>
	<table class="layui-hide" id="test" lay-filter="test"></table>
</div>
<script type="text/html" id="toolbarDemo">
  <div class="red">每点击一次随机获取10个公海客户的信息，每天最多可抢<strong class="mx-1">{$max_num}</strong>个客户，你今天已抢了<strong class="mx-1" id="count">{$count}</strong>个客户</div>
</script>
{/block}
<!-- /主体 -->

<!-- 脚本 -->
{block name="script"}
<script>
	const moduleInit = ['tool'];
	function gouguInit() {
		var table = layui.table, tool = layui.tool ,form = layui.form;
		layui.pageTable = table.render({
			elem: '#test',
			title: '抢客宝列表',
			toolbar: '#toolbarDemo',
			defaultToolbar: false,
			url: "/customer/index/rush", //数据接口
			cellMinWidth: 80,
			page: false, //开启分页
			limit: 10,
			cols: [
				[ //表头
					{
						field: 'id',title: '编号',align: 'center',width: 80,templet: function (d) {
							return'C' + d.id;
						}
					},{
						field: 'name',
						title: '客户名称',
						templet: '<div><a data-href="/customer/index/view/id/{{d.id}}.html" class="side-a">{{d.name}}</a></div>'
					},{
						field: 'user',
						title: '联系人',
						align: 'center',
						width: 80
					},{
						field: 'mobile',
						title: '手机号码',
						align: 'center',
						width: 100
					},{
						field: 'qq',
						title: 'QQ号码',
						align: 'center',
						width: 100
					},{
						field: 'wechat',
						title: '微信号',
						align: 'center',
						width: 90
					},{
						field: 'source',
						title: '来源渠道',
						align: 'center',
						width: 100
					}, {
						field: 'industry',
						title: '客户所属行业',
						align: 'center',
						width: 120
					},{
						field: 'right',
						fixed:'right',
						title: '操作',
						width: 80,
						align: 'center',
						templet: function (d) {
							var btn='<span class="layui-btn layui-btn-xs" lay-event="get">领取客户</span>';
							return btn;
						}						
					}
				]
			]
		});
		
		//监听行工具事件
		table.on('tool(test)', function(obj) {
			var data = obj.data;
			if (obj.event === 'get') {
				layer.confirm('确定要获取该客户?', {
					icon: 3,
					title: '提示'
				}, function(index) {
					let callback = function (e) {
						layer.msg(e.msg);
						if (e.code == 0) {
							layui.pageTable.reload();
							let count =$('#count').text();
							$('#count').html(++count);
						}
					}
					tool.post("/customer/index/get", {id: data.id}, callback);
					layer.close(index);
				});
			}
		});

		//监听搜索提交
		form.on('submit(webform)', function(data) {
			layui.pageTable.reload();
			return false;
		});
	}
</script>
{/block}
<!-- /脚本 -->
