git <?php

    class youzanClassAction extends ActionNot
    {


        // 每天运行一次
        public function onedayAction()
        {
            m('fenghong')->save_list();
        }

        //OA系统地址http://127.0.0.1/app/xinhu/?m=test&d=public
        public function defaultAction()
        {
            $this->display = false;

            echo m('kaoqin')->getworktime(1);

            $this->aa = '111';
            //print_r(m('weixinqy:daka')->getrecord(1));
            //return m('wxgzh:index')->sendtpl('images/logo.png');
            //echo  m('weixinqy:index')->getagentid('adds,办公助手e,OA2主页,办公助手');
            //return m('weixin:media')->downmedia('3MhSL1jKzVjnDOI3GHBU-Zf5xXJuVs48ciMMWiP0xv4Afp9ijTTalyhYTpNG2o8mEr-O5tGcNGeRBp-6_N5Y_CQ');
        }

        /***
         * 每天运行脚本同步前一天的数据
         */
        public function tongbuAction()
        {
            $this->display = false;
            ini_set("max_execution_time", 0);
            //        $today = date('Y-m-d', strtotime('-1 day', strtotime(date('Y-m-d')))); //输入日期后一天
            //        $url = "http://" . $_SERVER['HTTP_HOST'] . "/?m=youzan&d=public&a=getorder&dtime=" . $today;
            //        $response = $this->ajax($url, [], 'GET'); //同步订单
            $_GET['is_llq'] = 1;
            $this->getorderAction();
        }

        /**
         * 汇总员工业绩提成 五点执行
         */
        public function tongbu1Action()
        {
            $this->display = false;
            ini_set("max_execution_time", 0);
            $today = $_GET['date'] ? $_GET['date'] : date('Y-m-d', strtotime('-1 day', strtotime(date('Y-m-d')))); //输入日期后一天
            $url = "http://" . $_SERVER['HTTP_HOST'] . "/?m=youzan&d=public&a=staff_hz&dtime=" . $today;
            $response = $this->ajax($url, [], 'GET'); //同步员工业绩
            echo $response;die;
        }
        // /**
        //  * 汇总员工业绩提成往前刷30天 五点执行
        //  */
        // public function tongbu1allAction()
        // {
        //     $this->display = false;
        //     ini_set("max_execution_time", 0);
        //     // $date_s = date()
        //     // foreach()
        //     $today = $_GET['date'] ? $_GET['date'] : date('Y-m-d', strtotime('-1 day', strtotime(date('Y-m-d')))); //输入日期后一天

        //     $url = "http://" . $_SERVER['HTTP_HOST'] . "/?m=youzan&d=public&a=staff_hz&dtime=" . $today;
        //     $response = $this->ajax($url, [], 'GET'); //同步员工业绩
        // }


        /**
         * 门店汇总同步 三点
         */
        public function tongbu2Action()
        {
            //        $this->display = false;
            //        ini_set("max_execution_time", 0);
            //        $today = date('Y-m-d', strtotime('-1 day', strtotime(date('Y-m-d')))); //输入日期后一天
            //        $url = "http://" . $_SERVER['HTTP_HOST'] . "/?m=youzan&d=public&a=getorder&page_no=15&dtime=" . $today;
            //        $response = $this->ajax($url, [], 'GET'); //同步订单
            $this->display = false;
            ini_set("max_execution_time", 0);
            $today = date('Y-m-d', strtotime('-1 day', strtotime(date('Y-m-d')))); //输入日期后一天
            //汇总门店业绩
            $url = "http://" . $_SERVER['HTTP_HOST'] . "/?m=youzan&d=public&a=md_hz&dtime=" . $today;
            $response = $this->ajax($url, [], 'GET'); //同步门店业绩
            //跨店结算
            $url = "http://" . $_SERVER['HTTP_HOST'] . "/?m=youzan&d=public&a=md_hzcross&dtime=" . $today;
            $response = $this->ajax($url, [], 'GET'); //同步门店跨店数据业绩
        }


        /**
         * 对账数据汇总
         * 四点 执行
         */
        public function tongbu3Action()
        {
            $this->display = false;
            ini_set("max_execution_time", 0);

            $url = "http://{$_SERVER['HTTP_HOST']}/?m=youzan&d=public&a=initduizhangxq";
            $response = $this->ajax($url, [], 'GET');
        }

        // // 预约详情
        // public function tongbu4Action()
        // {
        //     $id = $_GET['id'];
        //     m('flow:order_item')->upate_yuyue($id);
        // }



        /***
         * @return void
         * 实时获取订单修改的情况
         */
        public function hztbAction()
        {
            $this->display = false;
            ini_set("max_execution_time", 0);

            $ten_after = date('Y-m-d H:i:s', strtotime('-5 minute', strtotime(date('Y-m-d H:i:s'))));
            $rows = m("order_update")->getone(" created_at <  '$ten_after' order by id asc");
            if ($rows) {
                $order = m("order")->getone("tid = '{$rows['tid']}'");
                if ($order) {
                    $day = explode(" ", $order['finish_time'])[0];
                    $url = "http://{$_SERVER['HTTP_HOST']}/?m=youzan&d=public&a=md_hz&dtime=$day&kdt_id={$order['kdt_id']}";
                    $response = $this->ajax($url, [], 'GET');
                    $url = "http://{$_SERVER['HTTP_HOST']}/?m=youzan&d=public&a=initduizhangxq&tongji_date=$day&md_name={$order['kdt_id']}";
                    $response = $this->ajax($url, [], 'GET');
                }
                $rows = m("order_update")->delete("id = {$rows['id']}");
            }
        }

        public function reserveAction(){

            $token = '';
            if (!$token) {
                $token = $this->gettoken();
            }

            $reserve_no = 'R2023093019214302515189';
            // $url = "https://open.youzanyun.com/api/youzan.mei.reserve.get/4.0.0?access_token=$token&reserve_no=$reserve_no";
            $url = "https://open.youzanyun.com/api/youzan.mei.reservation.list/4.0.0?access_token=$token&page_size=20&page=1&dept_id=1";
            $response = $this->ajax($url, [], 'GET');
            $data = json_decode($response);
            var_dump($data);die;
        }

        /***
         * @param $kdt_id
         * @param $tongji_year
         * @return void
         * 自动生成当前门店统计月份的数据
         */
        public function initduizhangxqAction()
        {
            $this->display = false;
            ini_set("max_execution_time", 0);
            $tongji_year = $_REQUEST['tongji_date'] ? $_REQUEST['tongji_date'] : date('Y-m-d', strtotime('-1 day', strtotime(date('Y-m-d'))));
            $baobiao = $_REQUEST['baobiao'] ? $_REQUEST['baobiao'] : 'day';
            $md_name = $_REQUEST['md_name'] ? $_REQUEST['md_name'] : '';

            $this->display = false;
            ini_set("max_execution_time", 0);

            $this->pay_channel = [
                'income_wx' => [2, 1004, 202],
                'income_zfb' => [4, 1005],
                'income_cash' => [201],
                'income_saoma' => [1000],
                'income_tuangou' => [1001],
                'income_dzyd' => [1003],
                'income_shuaka' => [1006],
                'income_daijinquan' => [1011],
                'income_douyin' => [1012, 1014, 1015],
                'income_koubei' => [1002],
            ];

            $this->cuntfina = [
                '洗衣费' => 'expend_xiyi', '员工餐' => 'expend_yuangongcan', '聚餐费' => 'expend_jucan', '生姜' => 'expend_shengjiang',
                '维修' => 'expend_weixiu', '点评年费' => 'expend_dianping', '网络通讯费' => 'expend_wltxf',
                '水电' => 'expend_shuidian', '其他' => 'expend_qita', '公司采购' => 'expend_caigou', '退款/退卡' => 'expend_tuika',
                '零星开支' => 'expend_linxin', '奖金' => 'expend_jiangjin', '推广费' => 'expend_tuiguang',
                '物业' => 'expend_wuye', '店铺租金' => 'expend_dianpu', '宿舍租金' => 'expend_sushe', '固话' => 'expend_guhua',
                '瓶子袋子' => 'expend_pingdaizi',

                '办公用品' => 'expend_bgyp', '低值易耗品' => 'expend_dzyhp', '物流快递费' => 'expend_wlkdf', '差旅费用' => 'expend_cyf', '维修费' => 'expend_weixiu', '固定资产' => 'expend_gdzc', '打样费用' => 'expend_dyfy', '软件服务费' => 'expend_rjfwf',
                '业务招待费' => 'expend_ywzdf', '会务费用' => 'expend_hwfy', '其他开支' => 'expend_qita', '员工福利' => 'expend_ygfl', '员工餐费' => 'expend_ygjc', '员工团险' => 'expend_ygtx', '团建费' => 'expend_tjf', '员工培训费' => 'expend_ygpxf',
                'BOSS直聘' => 'expend_boss', '其他招聘费用' => 'expend_qtzpfy', '劳务外包费' => 'expend_lwwbf', '金桥租金' => 'expend_jqzj', '申通B1租金' => 'expend_stzj', '物业费' => 'expend_wuye', '水电能源费' => 'expend_sdnyf',
                '大众点评推广费' => 'expend_dzdp', '抖音推广费' => 'expend_dytgf', '其他渠道推广费' => 'expend_qttgf', '财务费用' => 'expend_cwfy', '税金及附加' => 'expend_sjjfj', '所得税费用' => 'expend_sdsfy'
            ];

            $now_year = date("Y");
            $now_month = date("m");
            $now_day = date("d");
            $year = explode("-", $tongji_year)[0];
            $month = explode("-", $tongji_year)[1];
            if ($now_year < $year && $now_month < $month) {
                return;
            }


            if ($baobiao == 'day') {
                $day = explode("-", $tongji_year)[2];
            } else if ($baobiao == 'month' && empty($md_name)) {
                $day = 0;
            } else if ($baobiao == 'month' && !empty($md_name)) {
                $days = date("t", strtotime("$month-01"));
                $md_rows = m("md")->getone("kdt_id = $md_name");
                for ($i = 1; $i <= $days; $i++) {
                    $iday = $i;
                    if ($i < 10) {
                        $iday = "0$i";
                    }
                    $date_start = "$tongji_year-$iday 00:00:00";
                    $today = date('Y-m-d 00:00:00', strtotime('+1 day', strtotime($date_start))); //输入日期后一天
                    $this->v($md_rows, $year, $month, $i, $date_start, $today, $tongji_year);
                }
                echo 'success';
                return;
            }

            //        if (!$type){
            //            $duizhangxiangxq = m("duizhangxiangxq")->getone("tongji_year = $year and tongji_month = $month");
            //            if ($duizhangxiangxq && $duizhangxiangxq['update_time'] == date('Y-m-d')){
            //                return;
            //            }
            //        }

            if ($md_name) {
                $md_rows = m("md")->getall("kdt_id = $md_name");
            } else {
                $md_rows = m("md")->getall("kdt_id is not null");
            }
            //$md_rows = m("md")->getall("kdt_id = 120807511");

            if ($baobiao == 'day') {
                $date_start = "$tongji_year 00:00:00";
                $date_end = "$tongji_year 23:59:59";
            } else if ($baobiao == 'month') {
                $date_start = "$tongji_year-01 00:00:00";
                if ($month == date("m")) {
                    $date_end = date('Y-m-d 23:59:59', strtotime('-1 day', strtotime(date('Y-m-d')))); //输入日期后一天
                } else {
                    $date_end = date("Y-m-t 23:59:59", strtotime($date_start));
                }
            }
            foreach ($md_rows as $k => $v) {
                $this->v($v, $year, $month, $day, $date_start, $date_end, $tongji_year);
            }
            //m("duizhang")->delete("kdt_id is null");

            echo 'success';
        }

        public function v($v, $year, $month, $day, $date_start, $date_end, $tongji_year)
        {
            $idata['kdt_id'] = $v['kdt_id'];
            $idata['name'] = $v['name'];
            $idata['tongji_year'] = $year;
            $idata['tongji_month'] = $month;
            $idata['tongji_date'] = $day;

            $idata['income_pos'] = 0;
            $idata['income_tg'] = 0;
            $idata['income_cash'] = 0;
            $idata['income_tuangou'] = 0;
            $idata['income_douyin'] = 0;
            $idata['income_daijinquan'] = 0;
            $idata['income_zfb'] = 0;
            $idata['income_wx'] = 0;
            $idata['income_dzyd'] = 0;
            $idata['income_saoma'] = 0;
            $idata['income_shuaka'] = 0;
            $idata['income_koubei'] = 0;
            $idata['income_total'] = 0;

            $idata['expend_guanli'] = 0;
            $idata['expend_gongzi'] = 0;
            $idata['expend_shebao'] = 0;
            $idata['expend_dianpu'] = 0;
            $idata['expend_sushe'] = 0;

            $idata['koudian_price'] = 0;
            $idata['lirun_price'] = 0;
            $idata['tiaozhen_price'] = 0;
            $idata['jinlirun_price'] = 0;
            $idata['jiangjin_price'] = 0;

            $idata['expend_total'] = 0;

            $inwhere = "xorder.`kdt_id` = '{$v['kdt_id']}' and xorder.order_state = 40 
                and xorder.order_type != 3
                and xorder.tid = order_pay.tid
                and xorder.finish_time between '$date_start' and '$date_end' ";

            $outwhere = "oreverse.`kdt_id` = '{$v['kdt_id']}' and oreverse.reserve_no = xorc.reserve_no and xorder.order_state = 40 and xorder.tid = oreverse.tid
                    and oreverse.reverse_finish_time between '$date_start' and '$date_end' ";

            //收入来源
            $pay_channel = $this->pay_channel;
            foreach ($pay_channel as $key => $value) {
                $tiaojian = " and order_pay.pay_channel in (" . implode(",", $value) . ")  ";
                $in_rows = m("order_pay as order_pay,xinhu_order as xorder")->getone($inwhere . $tiaojian, 'sum(order_pay.real_pay) as item_real_pay');
                $idata[$key] += $in_rows['item_real_pay'];
                //var_dump(m("order_pay")->getLastSql());
                $tiaojian = " and xorc.channel in (" . implode(",", $value) . ")  ";
                $out_rows = m("order_reverse as oreverse,xinhu_order_refund_channels as xorc,xinhu_order as xorder")->getone($outwhere . $tiaojian, 'sum(xorc.amount) as amount ');
                $idata[$key] -= $out_rows['amount'];
            }
            //其他退款
            $tiaojian = " and xorc.channel in (14)  ";
            $out_rows = m("order_reverse as oreverse,xinhu_order_refund_channels as xorc,xinhu_order as xorder")->getone($outwhere . $tiaojian, 'sum(xorc.amount) as amount ');
            $idata['income_total'] -= $out_rows['amount'];

            /**
             * 微信、支付宝的手续费是0.0038；
             * 银行卡手续费0.0055；
             * 团购手续费0.06；
             * 18会员手续费0.006；
             * 预定手续费0.06
             */

            $koudian = 0;

            //        $koudian = $idata['income_zfb'] * 0.0038;
            $idata['income_zfb'] = $idata['income_zfb'] - $koudian;
            //        $idata['koudian_price'] += $koudian;

            //        $koudian = $idata['income_wx'] * 0.0038;
            $idata['income_wx'] = $idata['income_wx'] - $koudian;
            //        $idata['koudian_price'] += $koudian;

            //        $koudian = $idata['income_saoma'] * 0.0038;
            $idata['income_saoma'] = $idata['income_saoma'] - $koudian;
            //        $idata['koudian_price'] += $koudian;

            //        $koudian = $idata['income_dzyd'] * 0.06;
            $idata['income_dzyd'] = $idata['income_dzyd'] - $koudian;
            //        $idata['koudian_price'] += $koudian;

            //        $koudian = $idata['income_tuangou'] * 0.06;
            $idata['income_tuangou'] = $idata['income_tuangou'] - $koudian;
            //        $idata['koudian_price'] += $koudian;

            //        $koudian = $idata['income_shuaka'] * 0.0055;
            $idata['income_shuaka'] = $idata['income_shuaka'] - $koudian;
            //        $idata['koudian_price'] += $koudian;

            //        $koudian = $idata['income_koubei'] * 0.006;
            $idata['income_koubei'] = $idata['income_koubei'] - $koudian;
            //        $idata['koudian_price'] += $koudian;

            //pos机 = 微信、支付宝、扫码支付、刷卡支付
            $idata['income_pos'] = $idata['income_wx'] + $idata['income_zfb'] + $idata['income_saoma'] + $idata['income_shuaka'];
            //团购 = 大众团购、代金券、口碑
            $idata['income_tg'] = $idata['income_tuangou'] + $idata['income_daijinquan'] + $idata['income_koubei'];

            //总收入
            foreach ($pay_channel as $key => $value) {
                $idata['income_total'] += $idata[$key];
            }

            //付款申请费用
            $cuntfina = $this->cuntfina;
            foreach ($cuntfina as $ck => $cv) {
                if (!isset($idata[$cv])) {
                    $idata[$cv] = 0;
                }
                if ($ck == '店铺租金' || $ck == '宿舍租金') {
                    continue;
                }

                //付款申请费用
                $custfina = m('fininfom')
                    ->getone("`md_name` = '{$v['name']}' and shengq_item = '{$ck}' and type = 4
                            and audit_date between '$date_start' and '$date_end'", "sum(money) as money");
                $money = !empty($custfina) && !empty($custfina['money']) ? round($custfina['money'], 2) : 0;
                $idata[$cv] = $idata[$cv] + $money;
                $idata['expend_total'] += $money;
            }

            //费用报销
            $baoxiao_rows = m('fininfom')
                ->getall("`md_name` = '{$v['name']}' and type = 0
                            and audit_date between '$date_start' and '$date_end'");
            foreach ($baoxiao_rows as $baoxiao_k => $baoxiao_v) {
                $fininfos = m('fininfos')->getall("mid = {$baoxiao_v['id']}");
                foreach ($fininfos as $fininfos_k => $fininfos_v) {
                    if (isset($cuntfina[$fininfos_v['name']])) {
                        $c_key = $cuntfina[$fininfos_v['name']];
                        $idata[$c_key] += $fininfos_v['money'];
                        $idata['expend_total'] += $fininfos_v['money'];
                    }
                }
            }

            //管理费
            $idata['expend_guanli'] = $idata['income_total'] * 0.05;
            $idata['expend_total'] += $idata['expend_guanli'];

            //店铺租金
            //            $dianpu_zuj = m('fininfom')
            //                ->getone("`md_name` = '{$v['name']}' and shengq_item = '店铺租金' and type = 4
            //                and zuj_start_date <= '" . explode(" ", $date_start)[0] . "' AND '" . explode(" ", $date_start)[0] . "' <= zuj_end_date ");
            //            if ($dianpu_zuj){
            //                //判断是否审核
            //                $dianpu_audit_date = $dianpu_zuj['audit_date'];
            //                if (empty($dianpu_audit_date)){
            //                    continue;
            //                }
            //                $month_data = $this->dateMonths($dianpu_zuj['zuj_start_date'],$dianpu_zuj['zuj_end_date']);
            //                $idata['expend_dianpu'] = round($dianpu_zuj['money'] / count($month_data),2);
            //                $idata['expend_total'] += $idata['expend_dianpu'];
            //            }

            //宿舍租金
            //            $sushe_zuj = m('fininfom')
            //                ->getone("`md_name` = '{$v['name']}' and shengq_item = '宿舍租金' and type = 4
            //                and zuj_start_date <= '" . explode(" ", $date_start)[0] . "' AND '" . explode(" ", $date_start)[0] . "' <= zuj_end_date ");
            //            $idata['expend_sushe'] = !empty($sushe_zuj) ? $sushe_zuj['money'] : 0;
            //            if ($sushe_zuj){
            //                //判断是否审核
            //                $sushe_audit_date = $sushe_zuj['audit_date'];
            //                if (empty($sushe_audit_date)){
            //                    continue;
            //                }
            //                $month_data = $this->dateMonths($sushe_zuj['zuj_start_date'],$sushe_zuj['zuj_end_date']);
            //                $idata['expend_sushe'] = round($sushe_zuj['money'] / count($month_data),2);
            //                $idata['expend_total'] += $idata['expend_sushe'];
            //            }

            //备用金 推广费 公司采购 其他
            $beiyongjin = m('fininfom as fininfom,xinhu_fininfos as fininfos')
                ->getall(
                    "fininfos.`md_id` = {$v['kdt_id']} and fininfom.type = 3
                    and fininfom.audit_date between '$date_start' and '$date_end' and fininfom.id = fininfos.mid",
                    "fininfom.`shengq_item` as shengq_item,fininfos.`money` as money"
                );

            foreach ($beiyongjin as $bei_k => $bei_v) {
                if ($bei_v['shengq_item'] == '推广费') {
                    continue;
                } elseif ($bei_v['shengq_item'] == '公司采购') {
                    $idata['expend_caigou'] += $bei_v['money'];
                } elseif ($bei_v['shengq_item'] == '其他') {
                    $idata['expend_qita'] += $bei_v['money'];
                }
                $idata['expend_total'] += $bei_v['money'];
            }


            //员工社保 工资
            $yuangong = [];
            $staff = m("staff")->getall("node_kdt_id = '{$v['kdt_id']}'");
            foreach ($staff as $staffkey => $staffvalue) {
                $yuangong[] = $staffvalue['id'];
            }
            $yuangong = implode(",", $yuangong);
            $hrsalary = 0;
            if (!empty($yuangong)) {
                $hrsalary = m("hrsalary")->getone(
                    " 
                xuid in ($yuangong) and month = '$tongji_year'",
                    "sum(money) as money,sum(socials) as socials"
                );
            }

            if ($hrsalary && !empty($hrsalary['money']) && !empty($hrsalary['socials'])) {
                $idata['expend_gongzi'] = $hrsalary['money'];
                $idata['expend_shebao'] = $hrsalary['socials'];
            }


            $idata['lirun_price'] = $idata['income_total'] - $idata['expend_total'];
            $idata['jinlirun_price'] = $idata['lirun_price'] - $idata['tiaozhen_price'];

            $idata['update_time'] = date('Y-m-d');

            $where = "kdt_id = '{$v['kdt_id']}' and `tongji_year` = $year and `tongji_month` = $month and `tongji_date` = $day";
            $row = m('duizhang')->getone($where);
            if (!$row) {
                m('duizhang')->insert($idata);
            } else {
                m('duizhang')->update($idata, "id = {$row['id']}");
            }
        }

        //http://test-zxtoa.qipaisoft.com:8888/?m=youzan&d=public&a=getcustomer
        //获取顾客
        public function getcustomerAction($page_no = 1, $page_size = 200)
        {
            $this->display = false;
            ini_set("max_execution_time", 0);
            //        if (!$page_no){
            //            $last_row = m('customer')->getone('1=1 order by id desc');
            //            if ($last_row){
            //                $page_no = $last_row['page'];
            //            }
            //        }

            $start_create_at = strtotime('2023-07-01 00:00:00') * 1000;

            $token = '';
            if (!$token) {
                $token = $this->gettoken();
            }

            $url = 'https://open.youzanyun.com/api/youzan.mei.customer.search/4.0.0?access_token=' . $token .
                '&page_no=' . $page_no .
                '&page_size=' . $page_size .
                '&start_create_at=' . $start_create_at;
            $response = $this->ajax($url, [], 'GET');
            $data = json_decode($response);
            $items = $data->items;

            if (empty($items)) {
                return;
            }

            foreach ($items as $k => $v) {
                $yz_open_id = $v->yz_open_id;

                //存储到数据库中
                $rows = m('customer')->getone("yz_open_id = '{$yz_open_id}'");

                if (!$rows) {
                    $idata = [
                        'yz_open_id' => $yz_open_id,
                        'mobile' => $v->customer_mobile,
                        'customer_no' => $v->customer_no,
                        'is_member' => $v->is_member,
                        'created_at' => date('Y-m-d H:i:s', ($v->created_at / 1000)),
                        'member_created_at' => date('Y-m-d H:i:s', ($v->member_created_at / 1000)),
                        'member_level' => $v->member_level,
                        'belong_shop_name' => $v->belong_shop_name,
                        'belong_kdt_id' => $v->belong_kdt_id,
                        'customer_gender' => $v->customer_gender,
                        'name' => $v->customer_name,
                        'customer_growth' => $v->customer_growth,
                        'source_name' => $v->source_name,
                        'member_level_name' => $v->member_level_name
                    ];

                    if ($v->customer_birth_day) {
                        $idata['customer_birth_day'] = $v->customer_birth_day;
                    }

                    $re = m('customer')->insert($idata);
                }
                $this->getvaluecardAction($yz_open_id);
            }

            $page_no = $page_no + 1;
            $this->getcustomerAction($page_no, $page_size);
        }

        //更新顾客信息
        public function updatecunstomerAction($page_no = 1, $page_size = 1000)
        {
            $this->display = false;
            ini_set("max_execution_time", 0);

            $token = '';
            if (!$token) {
                $token = $this->gettoken();
            }

            $customer_rows = m('customer')->getall("1=1 Limit " . ($page_no - 1) * $page_size . " , {$page_size}");
            var_dump($page_no);

            if (empty($customer_rows)) return;

            foreach ($customer_rows as $key => $vaule) {
                $url = 'https://open.youzanyun.com/api/youzan.mei.customer.search/4.0.0?access_token=' . $token .
                    '&keyword=' . $vaule['mobile'];
                $response = $this->ajax($url, [], 'GET');
                $data = json_decode($response);
                $items = $data->items;
                if (empty($items)) {
                    continue;
                }

                foreach ($items as $k => $v) {
                    $yz_open_id = $v->yz_open_id;

                    //存储到数据库中
                    $rows = m('customer')->getone("yz_open_id = '{$yz_open_id}'");

                    if (!$rows) {
                        $idata = [
                            'yz_open_id' => $yz_open_id,
                            'mobile' => $v->customer_mobile,
                            'customer_no' => $v->customer_no,
                            'is_member' => $v->is_member,
                            'created_at' => date('Y-m-d H:i:s', ($v->created_at / 1000)),
                            'member_created_at' => date('Y-m-d H:i:s', ($v->member_created_at / 1000)),
                            'member_level' => $v->member_level,
                            'belong_shop_name' => $v->belong_shop_name,
                            'belong_kdt_id' => $v->belong_kdt_id,
                            'customer_gender' => $v->customer_gender,
                            'name' => $v->customer_name,
                            'customer_growth' => $v->customer_growth,
                            'source_name' => $v->source_name,
                            'member_level_name' => $v->member_level_name
                        ];

                        if ($v->customer_birth_day) {
                            $idata['customer_birth_day'] = $v->customer_birth_day;
                        }

                        $re = m('customer')->update($idata, "id = {$vaule['id']}");
                    }
                    $this->getvaluecardAction($yz_open_id);
                }
            }

            //$this->updatecunstomerAction($page_no + 1, $page_size);
        }

        //查询单个会员信息
        public function customersearch($yz_open_id)
        {
            $token = $this->gettoken();
            $url = 'https://open.youzanyun.com/api/youzan.mei.customer.query/4.0.0?access_token=' . $token .
                '&yz_open_id=' . $yz_open_id;
            $response = $this->ajax($url, [], 'GET');
            return json_decode($response)->data;
        }


        public function ceshiAction()
        {
            $this->display = false;
            ini_set("max_execution_time", 0);
            //        $order_rows = m("order")->getall("1=1");
            //        foreach ($order_rows as $k => $v){
            //            $created = $this->tidtodate($v['tid']);
            //            m("order")->update(['create_time'=>$created],"id = {$v['id']}");
            //        }


            //        return json_decode($response)->data;
            //
            //        $msg = "%7B%22kdtId%22%3A43984049%2C%22open_id%22%3A%22CpeyKBkU860172178434301952%22%2C%22deptId%22%3A97828904%2C%22yz_open_id%22%3A15280031271%2C%22eventType%22%3A2%2C%22yzUid%22%3A15280031271%7D";
            //
            //
            //        $msg = json_decode(urldecode($msg), true);
            //        var_dump($msg);


            //
            //        'SELECT xoi.tid from xinhu_order_item as xoi,xinhu_order as xo
            //where xoi.tech_yz_open_id is null and xoi.sales_yz_open_id is null and xoi.item_id != 887836855 and xo.order_type != 1 and xo.order_type != 2
            //    and xoi.tid = xo.tid and xo.order_state != 99 and xo.is_reverse != 1
            //GROUP BY xoi.tid'

            //        $data = [
            //            'msg' => '%7B%22positionId%22%3A5%2C%22kdtId%22%3A43984049%2C%22open_id%22%3A%22rcz1ZVGu1093850200981647360%22%2C%22deptId%22%3A132189550%2C%22roleIdSet%22%3A%5B5%5D%2C%22yz_open_id%22%3A16332685463%2C%22eventType%22%3A1%2C%22yzUid%22%3A16332685463%7D',
            //            'status' => 1
            //        ];
            //
            //        $this->open_staff($data);

            //$this->md_hzfenAction("2023-08-10");

        }

        //同步空白订单
        public function emptyorderAction()
        {
            $this->display = false;
            ini_set("max_execution_time", 0);
            $rows = m("order_item as xoi,xinhu_order as xo ")->getall("
        xoi.tech_yz_open_id is null and xoi.sales_yz_open_id is null and xoi.item_id != 887836855 and xo.order_type != 1 and xo.order_type != 2
            and xoi.tid = xo.tid and xo.order_state != 99 and xo.is_reverse != 1
            GROUP BY xoi.tid
        ", "xoi.tid");

            foreach ($rows as $v) {
                $tid = $v['tid'];
                $this->getorderinfo($tid);
            }
        }

        //修改订单日期 将订单号作为订单创建日期
        public function ordercreateatAction()
        {
            $this->display = false;
            ini_set("max_execution_time", 0);
            $orders = m("order")->getall("");
            foreach ($orders as $k => $v) {
                $tid = $v['tid'];
                $idata['create_time'] = $this->tidtodate($tid);
                m("order")->update($idata, "id = {$v['id']}");
            }
        }

        public function ceshi2($rows, $count)
        {
            if (empty($rows)) {
                return $count;
            }
            $row = $rows[0];
            $count = $count + $row['real_pay'];
            foreach ($rows as $k => $v) {
                if ($v['tid'] == $row['tid']) {
                    unset($rows[$k]);
                }
            }
            return $this->ceshi2($rows, $count);
        }

        public function foreachGetorderAction()
        {
            $this->display = false;
            ini_set("max_execution_time", 0);

            $url = "http://" . $_SERVER['HTTP_HOST'] . "/?m=youzan&d=public&a=getorder&dtime=";
            $day = date('d');
            $year_month = date('Y-m');
            for ($i = 1; $i < $day; $i++) {
                if ($i < 10) {
                    $dtime = "$year_month-0$i";
                } else {
                    $dtime = "$year_month-$i";
                }
                $response = $this->ajax($url . $dtime, [], 'GET');
            }
        }

        public function staff_hzmonthAction()
        {
            $this->display = false;
            ini_set("max_execution_time", 0);
            $dtime = $_REQUEST['dtime'];


            $days = date("t", strtotime("$dtime-01 00:00:00"));
            if (!$dtime || !$days) {
                return 0;
            }

            for ($i = 1; $i <= $days; $i++) {
                if ($i < 10) {
                    $day = "0$i";
                } else {
                    $day = $i;
                }
                $url = "http://{$_SERVER['HTTP_HOST']}/?m=youzan&d=public&a=staff_hz&dtime=$dtime-$day";
                $response = $this->ajax($url, [], 'GET');
            }
        }

        public function md_hzmonthAction()
        {
            $this->display = false;
            ini_set("max_execution_time", 0);
            $dtime = $_REQUEST['dtime'];


            $days = date("t", strtotime("$dtime-01 00:00:00"));
            if (!$dtime || !$days) {
                return 0;
            }

            for ($i = 1; $i <= $days; $i++) {
                if ($i < 10) {
                    $day = "0$i";
                } else {
                    $day = $i;
                }
                $url = "http://{$_SERVER['HTTP_HOST']}/?m=youzan&d=public&a=md_hz&dtime=$dtime-$day";
                //$url = "http://{$_SERVER['HTTP_HOST']}/?a=staff_hz&m=youzan&d=public&dtime=2023-$dtime-$day&type=day&month=&md_id=";
                $response = $this->ajax($url, [], 'GET');
            }
        }
        //    //获取订单
        //
        //    /***
        //     * @param int $page_no
        //     * @param int $page_size
        //     * @param date $dtime 指定日期
        //     */
        //    public function getorderAction($page_no = 1, $page_size = 50, $tid = '')
        //    {
        //        $this->display = false;
        //        ini_set("max_execution_time", 0);
        //
        //        $token = '';
        //        if (!$token) {
        //            $token = $this->gettoken();
        //        }
        //
        //        $dtime = isset($_REQUEST['dtime']) ? $_REQUEST['dtime'] : date('Y-m-d'); //输入的日期
        //        $today = date('Y-m-d', strtotime('+1 day', strtotime($dtime))); //输入日期后一天
        //
        //        $begin_time = strtotime($dtime) * 1000;
        //        $end_time = strtotime($today) * 1000;
        //
        //        $url = 'https://open.youzanyun.com/api/youzan.mei.order.list/4.0.0?access_token=' . $token .
        //            '&page_no=' . $page_no .
        //            '&page_size=' . $page_size .
        //            //            '&kdt_id=96176002' .
        ////            '&keyword=M2023071820093593556594'.
        //            '&begin_time=' . $begin_time .
        //            '&end_time=' . $end_time; //订单列表
        //
        //        if ($tid) {
        //            $url .= '&keyword=' . $tid;
        //        }
        //
        //        $response = $this->ajax($url, [], 'GET');
        //        $data = json_decode($response);
        //        $items = $data->items;
        //        if (empty($items)) {
        //            return;
        //        }
        //
        //        foreach ($items as $k => $v) {
        //            $tid = $v->tid; //订单号
        //            //$tid = "M2023071110524243319654";
        //            $idata['tid'] = $tid;
        //
        //            $url = "https://open.youzanyun.com/api/youzan.mei.order.get/4.4.1?access_token={$token}&tid={$tid}"; //订单详情
        //            $response = json_decode($this->ajax($url, [], 'GET'));
        //            $data = $response->data;
        //
        //            $idata['create_time'] = date('Y-m-d H:i:s', ($v->create_time / 1000)); //创建时间
        //            $idata['finish_time'] = date('Y-m-d H:i:s', ($v->finish_time / 1000)); //完成时间
        //            $idata['update_time'] = date('Y-m-d H:i:s', ($v->update_time / 1000)); //更新时间
        //            $idata['total_pay'] = doubleval($v->total_pay / 100);
        //            $idata['real_pay'] = doubleval($v->real_pay / 100);
        //            $idata['order_type'] = $v->order_type;
        //            $idata['order_state'] = $v->order_state;
        //
        //            $order_sale_info = $data->order_sale_info;
        //            $idata['kdt_id'] = $order_sale_info->kdt_id;
        //            $idata['shop_name'] = $order_sale_info->shop_name;
        //
        //            $order_pay_list = $data->order_pay_list; //支付方式
        //
        //            $order_buyer_info = $data->order_buyer_info;
        //            $idata['yz_open_id'] = $order_buyer_info->yz_open_id;
        //            $idata['belong_kdt_id'] = $order_buyer_info->belong_kdt_id;
        //
        //            $order_reverse_infos = $data->order_reverse_infos; //退款记录
        //
        //            $order_item_infos = $data->order_item_infos;
        //            foreach ($order_item_infos as $kk => $vv) {
        //                $item_no = $vv->item_no;
        //
        //                $idata['item_id'] = $vv->item_id;
        //                $idata['item_type'] = $vv->item_type;
        //                $idata['item_no'] = $vv->item_no;
        //
        //                $re = m('order')->getone(" `item_no` = '{$item_no}' ");
        //
        //                $idata['item_name'] = $vv->item_name;
        //                $idata['num'] = $vv->num;
        //                $idata['item_origin_price'] = doubleval($vv->item_origin_price / 100);
        //
        //                $idata['promotion_name'] = $vv->promotion_name;
        //                $idata['promotion_type'] = $vv->promotion_type;
        //                $idata['promotion_card_no'] = $vv->promotion_card_no;
        //                $idata['promotion_price'] = doubleval($vv->promotion_price / 100);
        //                $idata['worth'] = doubleval($vv->worth / 100);
        //
        //                $tech_name = '';
        //                $tech_yz_open_id = '';
        //                $tech_info_list = $vv->tech_info_list;
        //                if (!empty($tech_info_list)) {
        //                    foreach ($tech_info_list as $tk => $tv) {
        //                        $tech_name .= $tv->name . ',';
        //                        $tech_yz_open_id .= $tv->tech_yz_open_id . ',';
        //                    }
        //                }
        //                $sales_name = '';
        //                $sales_yz_open_id = '';
        //                $sales_info_list = $vv->sales_info_list;
        //                if (!empty($sales_info_list)) {
        //                    foreach ($sales_info_list as $sk => $sv) {
        //                        $sales_name .= $sv->name . ',';
        //                        $sales_yz_open_id .= $sv->sales_yz_open_id . ',';
        //                    }
        //                }
        //
        //                $idata['tech_name'] = $tech_name;
        //                $idata['tech_yz_open_id'] = $tech_yz_open_id;
        //                $idata['sales_name'] = $sales_name;
        //                $idata['sales_yz_open_id'] = $sales_yz_open_id;
        //
        //                if (count($order_pay_list) > 1) {
        //                    $idata['pay_channel_name'] = $order_pay_list[$kk]->pay_channel_name;
        //                    $idata['pay_channel'] = $order_pay_list[$kk]->pay_channel;
        //                    $idata['item_real_pay'] = doubleval($order_pay_list[$kk]->real_pay / 100);
        //                } else {
        //                    $idata['pay_channel_name'] = $order_pay_list[0]->pay_channel_name;
        //                    $idata['pay_channel'] = $order_pay_list[0]->pay_channel;
        //                    $idata['item_real_pay'] = doubleval($vv->item_real_pay / 100);
        //                }
        //
        //                if ($re) {
        //                    $re = m('order')->update($idata, "id = {$re['id']}");
        //                } else {
        //                    $re = m('order')->insert($idata);
        //                }
        //
        //                if ($re) {
        //                    $this->tuikuan($idata, $order_reverse_infos, $kk);
        //                }
        //            }
        //
        //
        //        }
        //        $page_no = $page_no + 1;
        //        $this->getorderAction($page_no, $page_size);
        //    }
        //获取订单

        /***
         * @param int $page_no
         * @param int $page_size
         * @param date $dtime 指定日期
         */
        public function getorderAction($page_no = 1, $page_size = 50, $tid = '')
        {
            set_time_limit(300);
            if (!isset($_GET['is_llq']) || !$_GET['is_llq']) {
                $_GET['is_llq'] = 1;
            }
            $this->display = false;
            ini_set("max_execution_time", 0);

            $str1 = "开始执行时间：" . date("Y-m-d H:i:s") . "<hr>";
            $token = '';
            if (!$token) {
                $token = $this->gettoken();
            }

            if (isset($_REQUEST['page_no'])) {
                $page_no = $_REQUEST['page_no'];
            }
            if (isset($_REQUEST['page_size'])) {
                $page_size = $_REQUEST['page_size'];
            }
            if (isset($_REQUEST['tid'])) {
                $tid = $_REQUEST['tid'];
            }

            $dtime = isset($_REQUEST['dtime']) ? $_REQUEST['dtime'] : date('Y-m-d', strtotime('-1 day', strtotime(date('Y-m-d')))); //输入的日期
            if (isset($_REQUEST['detime']) && !empty($_REQUEST['detime'])) {
                $detime = $_REQUEST['detime'];
                $today = date('Y-m-d', strtotime('+1 day', strtotime($detime))); //输入日期后一天
            } else {
                $today = date('Y-m-d', strtotime('+1 day', strtotime($dtime))); //输入日期后一天
            }

            if ($_GET['is_cron'] == 1) {
                $youzan_page_file_path = __DIR__ . '/' . "youzan_api_page.txt";
                $page_info = file_get_contents($youzan_page_file_path);
                $page_info = json_decode($page_info, 1);
                $page_no = max(intval($page_info[$dtime . '~' . $today]), 1);
            }

            $begin_time = strtotime($dtime) * 1000;
            $end_time = strtotime($today) * 1000;
            $url = 'https://open.youzanyun.com/api/youzan.mei.order.list/4.0.0?access_token=' . $token .
                '&page_no=' . $page_no .
                '&page_size=' . $page_size .
                //                        '&kdt_id=97828904' .
                //            '&keyword=M2023080414511832325406'.
                '&begin_time=' . $begin_time .
                '&end_time=' . $end_time; //订单列表

            if ($tid) {
                $url .= '&keyword=' . $tid;
            }

            $response = $this->ajax($url, [], 'GET');
            $data = json_decode($response);
            $items = $data->items;

            if (empty($items)) {
                var_dump($data);
                if ($_GET['is_cron'] == 1) {

                    $str2 = '日期：' . $dtime . '~' . $today . ' 本次执行第' . $page_no . '页,起始第' . (($page_no - 1) * $page_size) . '条,返回条数：' . count($items) . '条,执行完毕……';

                    $page_no++;
                    echo $str1 . $str2;
                    file_put_contents($youzan_page_file_path, json_encode([$dtime . '~' . $today => 1]));
                    exit;
                } else if ($_GET['is_llq'] == 1) {
                    $page_no++;

                    $str2 = '本次执行第<span style="color:red;">' . $page_no . '</span>页,起始第<span style="color:red;">' . (($page_no - 1) * $page_size) . '</span>条,返回条数：<span style="color:red;">' . count($items) . '</span>条<hr>执行完毕……<hr>';
                    $html = $this->pri_get_html($str2, $str1);

                    echo $html;
                    exit;
                } else {
                    var_dump($data);
                    return;
                }
            }

            foreach ($items as $k => $v) {
                $this->getorderinfo($v->tid);
            }
            if ($_GET['is_cron'] == 1) {

                $str2 = '日期：' . $dtime . '~' . $today . ' 本次执行第' . $page_no . '页,起始第' . (($page_no - 1) * $page_size) . '条,返回条数：' . count($items) . '条,继续执行中……';
                $page_no++;

                echo $str1 . $str2;
                file_put_contents($youzan_page_file_path, json_encode([$dtime . '~' . $today => $page_no]));
                exit;
            } else if ($_GET['is_llq'] == 1) {
                $page_no++;

                // echo '<pre>';var_dump($v);
                foreach ($v as $kk => $vv) {
                    echo $kk . "：" . print_r($vv) . '，';
                }

                $str2 = '本次执行第<span style="color:red;">' . $page_no . '</span>页,起始第<span style="color:red;">' . (($page_no - 1) * $page_size) . '</span>条,返回条数：<span style="color:red;">' . count($items) . '</span>条<hr>继续执行中……<hr>';
                $html = $this->pri_get_html($str2, $str1);

                echo $html;

                echo "<meta http-equiv=\"Refresh\" content=\"0.5; url=/?m=youzan&d=public&a=getorder&dtime={$dtime}&detime={$detime}&page_no={$page_no}&page_size={$page_size}&tid={$tid}&is_llq={$_GET['is_llq']}\">";
                exit;
            } else {
                if (!$tid) {
                    var_dump(count($items));
                    var_dump("------");
                    var_dump($page_no);
                    $page_no = $page_no + 1;
                    $this->getorderAction($page_no, $page_size);
                }
            }
        }

        private function pri_get_html($str2, $str1 = '')
        {
            $html = '<!DOCTYPE html>
        <html>
        
        <head>
          <title>Loading Page</title>
          <style>
            body {
              text-align: center;
              font-size: 24px;
              padding: 50px;
            }
          </style>
          <script>
            window.onload = function() {
              var countdown = 1; // 设置倒计时秒数
        
              var countdownElement = document.getElementById(\'countdown\');
              countdownElement.innerText = countdown;
        
              var interval = setInterval(function() {
                countdown++;
                countdownElement.innerText = countdown;
                
                if (countdown === 0) {
                  clearInterval(interval);
                  countdownElement.innerText = \'Loading...\';
                  // 在这里可以添加其他加载操作或重定向到其他页面
                }
              }, 1000);
            };
          </script>
        </head>
        
        <body>
        <div style="font-size:22px;">用时 <span id="countdown" style="color:red;"></span> 秒</div><hr>
            <div>' . $str2 . '</div>
            <div>' . $str1 . '</div>
        </body>
        
        </html>';
            return $html;
        }

        public function getorderinfoAjax()
        {
            $this->getorderinfo($_GET['order_id']);
        }
        
        //获取订单详情
        public function getorderinfo($tid)
        {
            $token = '';
            if (!$token) {
                $token = $this->gettoken();
            }
            $idata['tid'] = $tid;
            //订单详情
            $url = "https://open.youzanyun.com/api/youzan.mei.order.get/4.4.1?access_token={$token}&tid={$tid}"; //订单详情
            // /
            $response = json_decode($this->ajax($url, [], 'GET'));

            $data = $response->data;
            //公共信息
            $idata['create_time'] = $this->tidtodate($tid); //创建时间 这里创建时间有问题 接口数据不准确需要根据订单号来获取
            $idata['finish_time'] = date('Y-m-d H:i:s', ($data->finish_time / 1000)); //完成时间
            $idata['update_time'] = date('Y-m-d H:i:s', ($data->update_time / 1000)); //更新时间
            $order_sale_info = $data->order_sale_info;
            $idata['kdt_id'] = $order_sale_info->kdt_id;
            $idata['shop_name'] = $order_sale_info->shop_name;

            //订单信息
            $order_idata = $idata;
            $order_idata['cashier_id'] = isset($data->cashier_id) ? $data->cashier_id : '';
            $order_idata['total_pay'] = doubleval($data->total_pay / 100);
            $order_idata['real_pay'] = doubleval($data->real_pay / 100);
            $order_idata['order_type'] = $data->order_type;
            $order_idata['order_state'] = $data->order_state;

            $order_buyer_info = $data->order_buyer_info;
            $order_idata['buyer_gender'] = $order_buyer_info->gender;
            $order_idata['buyer_mobile'] = $order_buyer_info->mobile;
            $order_idata['buyer_yz_open_id'] = $order_buyer_info->yz_open_id;
            $order_idata['buyer_belong_kdt_id'] = $order_buyer_info->belong_kdt_id;

            $order_pay_info = $data->order_pay_info;
            $order_idata['pay_channel_name'] = $order_pay_info->pay_channel_name;
            $order_idata['pay_real_pay'] = doubleval($order_pay_info->real_pay / 100);
            $order_idata['pay_channel'] = $order_pay_info->pay_channel;
            $order_idata['pay_time'] = date('Y-m-d H:i:s', ($order_pay_info->pay_time / 1000));


            //订单商品信息
            $order_item_idata = $idata;
            $order_item_infos = $data->order_item_infos;
            foreach ($order_item_infos as $order_key => $order_item) {
                $order_item_idata['promotion_type'] = $order_item->promotion_type;
                $order_item_idata['item_id'] = $order_item->item_id;
                $order_item_idata['promotion_value'] = $order_item->promotion_value;
                $order_item_idata['item_type'] = $order_item->item_type;
                $order_item_idata['num'] = $order_item->num;
                $order_item_idata['index'] = $order_item->index;
                $order_item_idata['promotion_id'] = $order_item->promotion_id;
                $order_item_idata['item_name'] = $order_item->item_name;
                $order_item_idata['sku_id'] = $order_item->sku_id;
                $order_item_idata['item_no'] = $order_item->item_no;
                $order_item_idata['promotion_name'] = $order_item->promotion_name;
                $order_item_idata['promotion_card_no'] = $order_item->promotion_card_no;

                $order_item_idata['promotion_price'] = doubleval($order_item->promotion_price / 100);
                $order_item_idata['worth'] = doubleval($order_item->worth / 100);
                $order_item_idata['operate_price'] = doubleval($order_item->operate_price / 100);
                $order_item_idata['item_real_pay'] = doubleval($order_item->item_real_pay / 100);
                $order_item_idata['item_origin_price'] = doubleval($order_item->item_origin_price / 100);

                $tech_name = '';
                $tech_yz_open_id = $is_diank = '';
                $tech_info_list = $order_item->tech_info_list;
                if (!empty($tech_info_list)) {
                    foreach ($tech_info_list as $tk => $tv) {
                        $tech_name .= $tv->name . ',';
                        $tech_yz_open_id .= $tv->tech_yz_open_id . ',';
                        $is_diank .= $tv->assigned . ',';
                        // var_dump($is_diank);die;
                    }
                }
                $sales_name = '';
                $sales_yz_open_id = '';
                $sales_info_list = $order_item->sales_info_list;
                if (!empty($sales_info_list)) {
                    foreach ($sales_info_list as $sk => $sv) {
                        $sales_name .= $sv->name . ',';
                        $sales_yz_open_id .= $sv->sales_yz_open_id . ',';
                    }
                }
                $order_item_idata['tech_name'] = $tech_name;
                $order_item_idata['tech_yz_open_id'] = $tech_yz_open_id;
                $order_item_idata['sales_name'] = $sales_name;
                $order_item_idata['sales_yz_open_id'] = $sales_yz_open_id;
                $order_item_idata['is_diank'] = $is_diank;

                $order_item_re = m('order_item')->getone("tid = '{$tid}' and item_no = '{$order_item_idata['item_no']}' ");
                if ($order_item_re) {
                    m('order_item')->update($order_item_idata, "id = {$order_item_re['id']}");
                } else {
                    m('order_item')->insert($order_item_idata);
                }
            }

            //支付信息
            $order_pay_idata = $idata;
            $order_pay_list = $data->order_pay_list;
            foreach ($order_pay_list as $pay_key => $pay_item) {
                $order_pay_idata['pay_channel_name'] = $pay_item->pay_channel_name;
                $order_pay_idata['real_pay'] = doubleval($pay_item->real_pay / 100);
                $order_pay_idata['pay_channel'] = $pay_item->pay_channel;
                $order_pay_idata['remark'] = $pay_item->remark;
                $order_pay_idata['source'] = $pay_item->source;
                $order_pay_idata['transaction_no'] = $pay_item->transaction_no;
                $order_pay_re = m('order_pay')->getone("tid = '{$tid}' and transaction_no = '{$order_pay_idata['transaction_no']}' ");
                if ($order_pay_re) {
                    m('order_pay')->update($order_pay_idata, "id = {$order_pay_re['id']}");
                } else {
                    m('order_pay')->insert($order_pay_idata);
                }
            }

            //支付信息
            $reverse_idata = $idata;
            $order_reverse_infos = $data->order_reverse_infos;
            foreach ($order_reverse_infos as $reverse_key => $reverse_item) {
                $reverse_idata['reserve_no'] = $reverse_item->reserve_no;

                $order_refund_channels = $reverse_item->refund_channels;
                foreach ($order_refund_channels as $refund_key => $refund_item) {
                    $refund_idata = $reverse_idata;
                    $refund_idata['channel_name'] = $refund_item->channel_name;
                    $refund_idata['amount'] = doubleval($refund_item->amount / 100);
                    $refund_idata['channel'] = $refund_item->channel;
                    $refund_idata['refund_state'] = $refund_item->refund_state;
                    $refund_idata['channel_reserve_no'] = $refund_item->channel_reserve_no;
                    $refund_channels_re = m('order_refund_channels')->getone(" `tid` = '{$tid}' and channel_reserve_no = '{$refund_idata['channel_reserve_no']}' ");
                    if ($refund_channels_re) {
                        m('order_refund_channels')->update($refund_idata, "id = {$refund_channels_re['id']}");
                    } else {
                        m('order_refund_channels')->insert($refund_idata);
                    }
                }

                $reverse_idata['refund_type'] = $reverse_item->refund_type;
                $reverse_idata['operator_yz_open_id'] = $reverse_item->operator_yz_open_id;
                $reverse_idata['reserve_amount'] = doubleval($reverse_item->reserve_amount / 100);
                $reverse_idata['reverse_finish_time'] = date('Y-m-d H:i:s', ($reverse_item->reverse_finish_time / 1000));
                $reverse_idata['reverse_state'] = $reverse_item->reverse_state;
                $reverse_idata['reverse_time'] = date('Y-m-d H:i:s', ($reverse_item->reverse_time / 1000));
                $reverse_idata['refund_demand'] = $reverse_item->refund_demand;

                $reverse_re = m('order_reverse')->getone(" `tid` = '{$tid}' and reserve_no = '{$reverse_idata['reserve_no']}' ");

                if ($reverse_re) {
                    m('order_reverse')->update($reverse_idata, "id = {$reverse_re['id']}");
                } else {
                    m('order_reverse')->insert($reverse_idata);
                }
            }

            $re = m('order')->getone(" `tid` = '{$tid}' ");

            //获取一下是否有退款信息
            if (count($order_reverse_infos) > 0) {
                $order_idata['is_reverse'] = 1;
            }
            if ($re) {
                m('order')->update($order_idata, "id = {$re['id']}");
            } else {
                m('order')->insert($order_idata);
            }
        }

        public function tuikuan($re, $order_reverse_infos, $key)
        {
            //是否存在退款单
            foreach ($order_reverse_infos as $info) {
                $refund_channels = $info->refund_channels;
                $rc = $refund_channels[$key];

                $re_tuikuan = m('order')->getone("`reserve_no` = '{$rc->channel_reserve_no}' and `item_no` = '{$re['item_no']}' ");

                $re['reserve_amount'] = doubleval($rc->amount / 100);
                $re['reverse_state'] = $rc->refund_state;
                $re['reserve_no'] = $rc->channel_reserve_no;
                $re['reverse_finish_time'] = date('Y-m-d H:i:s', ($info->reverse_finish_time / 1000));
                unset($re['id']);

                if (!$re_tuikuan) {
                    m('order')->insert($re);
                } else {
                    m('order')->update($re, "id = {$re_tuikuan['id']}");
                }
            }
        }

        //获取员工
        public function getstaffAction($page_no = 1, $page_size = 200)
        {
            $this->display = false;
            ini_set("max_execution_time", 0);

            $token = '';
            if (!$token) {
                $token = $this->gettoken();
            }

            $md_rows = m('md')->getall('');
            foreach ($md_rows as $md_row) {
                $url = 'https://open.youzanyun.com/api/youzan.mei.staff.list/4.0.0?access_token=' . $token .
                    '&node_kdt_id=' . $md_row['kdt_id'] .
                    '&page_no=' . $page_no .
                    '&page_size=' . $page_size;

                $response = $this->ajax($url, [], 'GET');
                $data = json_decode($response);
                $items = $data->items;

                if (empty($items)) {
                    continue;
                }

                foreach ($items as $k => $v) {

                    $yz_open_id = $v->yz_open_id;
                    $roles = $v->roles;

                    $roles_id = '';
                    $role_name = '';
                    foreach ($roles as $r) {
                        $roles_id .= $r->role_id . ',';
                        $role_name .= $r->role_name . ',';
                    }

                    $staff_query = $this->getstaffquery($yz_open_id, $md_row['kdt_id']);

                    $status = $staff_query->status;
                    if ($status) { //1删除 0正常
                        $this->deletestaff($yz_open_id, $v->node_kdt_id);
                        continue;
                    }

                    $node_kdt_id = $v->node_kdt_id;

                    if ($node_kdt_id == 1) {
                        $md_rows = m('md')->getone("`kdt_type_name` = '总部'");
                        $node_kdt_id = $md_rows['kdt_id'];
                    }

                    $idata = [
                        'role_id' => $roles_id,
                        'role_name' => $role_name,
                        'node_kdt_id' => $node_kdt_id,
                        'name' => $v->name,
                        'mobile' => $v->mobile,
                        'status' => $v->status,
                        'yz_open_id' => $yz_open_id,
                        'position_id' => $v->position_id,
                        'position_rank_id' => isset($staff_query->position_rank_id) ? $staff_query->position_rank_id : '',
                        'position_rank_name' => isset($staff_query->position_rank_name) ? $staff_query->position_rank_name : '',
                        'position_name' => isset($staff_query->position_name) ? $staff_query->position_name : '',
                        'created_at' => date('Y-m-d H:i:s', ($v->created_at / 1000)),
                        'updated_at' => date('Y-m-d H:i:s', ($v->updated_at / 1000)),
                        'salary' => 1500,
                        'annual_leave' => 0,
                        'res_annual_leave' => 0
                    ];

                    if (empty($idata['position_name']) && !empty($role_name)) {
                        $idata['position_name'] = substr($role_name, 0, strlen($role_name) - 1);;
                    }

                    //数据是否存在
                    $rows = m('staff')->getone("yz_open_id = '{$yz_open_id}' and node_kdt_id = '{$node_kdt_id}' ");
                    if (!$rows) {
                        $re = m('staff')->insert($idata);
                    } else {
                        $re = m('staff')->update($idata, "id = {$rows['id']}");
                    }
                }
            }

            //        $page_no = $page_no + 1;
            //        $this->getstaffAction($page_no, $page_size);
        }

        //员工详情
        public function getstaffquery($yz_open_id, $node_kdt_id)
        {
            $token = $this->gettoken();
            $url = 'https://open.youzanyun.com/api/youzan.mei.staff.query/4.0.0?access_token=' . $token .
                '&yz_open_id=' . $yz_open_id .
                '&node_kdt_id=' . $node_kdt_id;
            $response = $this->ajax($url, [], 'GET');
            return json_decode($response)->data;
        }

        //获取门店
        public function getmdAction($page_no = 1, $page_size = 50)
        {
            $this->display = false;
            ini_set("max_execution_time", 0);

            $token = '';
            if (!$token) {
                $token = $this->gettoken();
            }

            $url = 'https://open.youzanyun.com/api/youzan.mei.dept.org.get/4.0.0?access_token=' . $token .
                '&page_no=' . $page_no .
                '&page_size=' . $page_size;
            $response = $this->ajax($url, [], 'GET');
            $data = json_decode($response);
            $items = $data->items;

            if (empty($items)) {
                return;
            }

            $md_rows = m('md')->getall("");

            foreach ($items as $k => $v) {
                $kdt_id = $v->kdt_id;
                $name = $v->name;
                //存储到数据库中
                //$rows = m('md')->getone("name = '{$name}'");
                $idata = [
                    'kdt_type_name' => $v->kdt_type_name,
                    'kdt_id' => $v->kdt_id,
                    'name' => $v->name,
                    'logo' => $v->logo,
                    'kdt_type' => $v->kdt_type,
                    'status' => 1,
                    'xufeilv' => 0,
                ];

                $flag = true;
                foreach ($md_rows as $md_k => $md_v) {
                    $name_1 = explode(" ", $this->quchu($name))[1];
                    $name_2 = explode(" ", $this->quchu($md_v['name']))[1];
                    var_dump($name_1);
                    var_dump($name_2);
                    var_dump("</br>");
                    if ($name_1 == $name_2) {
                        $flag = false;
                        $idata['name'] = $md_v['name'];
                        $re = m('md')->update($idata, "id = {$md_v['id']}");
                        break;
                    }
                }

                if ($flag) {
                    $re = m('md')->insert($idata);
                }
            }
        }

        //去除中英文标点符号
        public function quchu($str)
        {
            $char = "。、！？：；﹑•＂…‘’“”〝〞∕¦‖—　〈〉﹞﹝「」‹›〖〗】【»«』『〕〔》《﹐¸﹕︰﹔！¡？¿﹖﹌﹏﹋＇´ˊˋ―﹫︳︴¯＿￣﹢﹦﹤‐­˜﹟﹩﹠﹪﹡﹨﹍﹉﹎﹊ˇ︵︶︷︸︹︿﹀︺︽︾ˉ﹁﹂﹃﹄︻︼（）";

            $pattern = array(
                "/[[:punct:]]/i", //英文标点符号
                '/[' . $char . ']/u', //中文标点符号
                '/[ ]{2,}/'
            );
            $str = preg_replace($pattern, ' ', $str);
            return $str;
        }

        //获取服务
        public function getservingAction($page_no = 1, $page_size = 200)
        {
            $this->display = false;
            ini_set("max_execution_time", 0);

            $token = '';
            if (!$token) {
                $token = $this->gettoken();
            }

            $md_rows = m('md')->getall('', 'kdt_id');

            if (empty($md_rows)) {
                return;
            }

            foreach ($md_rows as $md) {
                $kdt_id = $md['kdt_id'];

                $url = 'https://open.youzanyun.com/api/youzan.mei.serving.list/4.0.0?access_token=' . $token .
                    '&kdt_id=' . $kdt_id .
                    '&page_no=' . $page_no .
                    '&page_size=' . $page_size;
                $response = $this->ajax($url, [], 'GET');
                // var_dump(($response));die;
                $data = json_decode($response)->data;
                $items = $data->items;

                if (empty($items)) {
                    continue;
                }

                foreach ($items as $k => $v) {
                    $item_id = $v->item_id;
                    //存储到数据库中
                    // $rows = m('md')->getone("item_id = '{$item_id}'");
                    $rows = m('serving')->getone("item_id = '{$item_id}' and kdt_id='$kdt_id'");

                    $data = [
                            'kdt_id' => $kdt_id,
                            'service_time' => $v->service_time,
                            'category_name' => $v->category_name,
                            'item_tags' => $v->item_tags,
                            'item_id' => $v->item_id,
                            'title' => $v->title,
                            'item_status' => $v->item_status,
                            'is_wx_show' => $v->is_wx_show,
                            'item_no' => $v->item_no,
                            'create_time' => date('Y-m-d H:i:s', $v->create_time / 1000),
                            'update_time' => date('Y-m-d H:i:s', $v->update_time / 1000),
                            'alias' => $v->alias,
                            'price' => doubleval($v->price / 100),
                            'price_min' => doubleval($v->price_min / 100),
                            'price_max' => doubleval($v->price_max / 100)
                        ];
                    if (!$rows) {
                        $re = m('serving')->insert($data);
                    }else {
                         m('serving')->update($data,"item_id = '{$item_id}' and kdt_id='$kdt_id'");
                    }
                }
            }
        }

        //获取卡项
        public function getcardAction($page_no = 1, $page_size = 200)
        {
            $this->display = false;
            ini_set("max_execution_time", 0);

            $token = '';
            if (!$token) {
                $token = $this->gettoken();
            }

            $md_rows = m('md')->getall('', 'kdt_id');

            if (empty($md_rows)) {
                return;
            }

            foreach ($md_rows as $md) {
                $kdt_id = $md['kdt_id'];
                $url = 'https://open.youzanyun.com/api/youzan.mei.card.list/4.0.0?access_token=' . $token .
                    '&kdt_id=' . $kdt_id .
                    '&page_no=' . $page_no .
                    '&page_size=' . $page_size;
                $response = $this->ajax($url, [], 'GET');
                $data = json_decode($response)->data;
                $items = $data->items;

                if (empty($items)) {
                    continue;
                }

                foreach ($items as $k => $v) {
                    $card_id = $v->card_id;
                    //存储到数据库中
                    $rows = m('md')->getone("card_id = '{$card_id}'");

                    if (!$rows) {
                        $re = m('card')->insert([
                            'card_type' => $v->card_type,
                            'is_wap_show' => $v->is_wap_show,
                            'card_id' => $v->card_id,
                            'card_alias' => $v->card_alias,
                            'is_shelve' => $v->is_shelve,
                            'kdt_id' => $kdt_id,
                            'is_deleted' => $v->is_deleted,
                            'card_name' => $v->card_name,
                            'created_time' => date('Y-m-d H:i:s', $v->created_time),
                            'updated_time' => date('Y-m-d H:i:s', $v->updated_time),
                            'prepaid_gift_price' => doubleval($v->prepaid_gift_price / 100),
                            'card_price' => doubleval($v->card_price / 100)
                        ]);
                    }
                }
            }
        }

        //更新非普通会员的信息
        public function updatevipcustomerAction()
        {
            $this->display = false;
            $rows = m('customer')->getall("1=1", 'yz_open_id');
            foreach ($rows as $k) {
                $this->getvaluecardAction($k['yz_open_id']);
                $this->stat_xf_numerAction($k['yz_open_id']);
            }
        }

        //获取储值卡余额
        public function getvaluecardAction($yz_open_id = "", $page_no = 1, $page_size = 200)
        {
            $this->display = false;
            ini_set("max_execution_time", 0);

            $token = '';
            if (!$token) {
                $token = $this->gettoken();
            }

            $url = 'https://open.youzanyun.com/api/youzan.mei.valuecard.list/4.0.0?access_token=' . $token .
                '&yz_open_id=' . $yz_open_id .
                //            '&node_kdt_id=' . $customer['belong_kdt_id'] .
                '&page_no=' . $page_no .
                '&page_size=' . $page_size;
            $response = $this->ajax($url, [], 'GET');
            $data = json_decode($response);
            $items = $data->items;

            if (empty($items)) {
                return;
            }


            $url = 'https://open.youzanyun.com/api/youzan.mei.valuecard.list/4.0.0?access_token=' . $token .
                '&yz_open_id=' . $yz_open_id .
                //            '&node_kdt_id=' . $customer['belong_kdt_id'] .
                '&page_no=' . $page_no .
                '&page_size=' . $page_size;


            foreach ($items as $v) {
                $idata = [
                    'yz_open_id' => $yz_open_id,
                    'account_name' => $v->account_name,
                    'active_node_name' => $v->active_node_name,
                    'publish_node_kdt_id' => $v->publish_node_kdt_id,
                    'distinguish_account' => $v->distinguish_account,
                    'account_status' => $v->account_status,
                    'total_balance' => doubleval($v->total_balance / 100),
                    'capital_balance' => doubleval($v->capital_balance / 100),
                    'present_balance' => doubleval($v->present_balance / 100),
                    'is_default_account' => $v->is_default_account,
                    'account_no' => $v->account_no,
                    'active_node_id' => $v->active_node_id
                ];

                $rows = m('customer_card')->getone("account_no = '{$v->account_no}'");

                if ($rows) {
                    m('customer_card')->delete("id != {$rows['id']} and account_no = '{$v->account_no}'");
                    m('customer_card')->update($idata, "id = {$rows['id']}");
                } else {
                    m('customer_card')->insert($idata);
                }
            }
        }

        //统计消费频次
        public function stat_xf_numerAction($yz_open_id = "")
        {
            $this->display = false;
            //$yz_open_id = 'mAoCroSz617670810940792832';
            $order_to_yzopenid = m('order')
                ->getall("buyer_yz_open_id = '{$yz_open_id}' 
                    and `order_state` = 40 and is_reverse = -1 
                    order by `create_time` desc");
            if (!empty($order_to_yzopenid)) {
                foreach ($order_to_yzopenid as $value) {
                    $idata['xf_number'] = count($order_to_yzopenid);
                    $idata['last_xf_date'] = $order_to_yzopenid[0]['create_time'];
                    $idata['last_xf_price'] = $order_to_yzopenid[0]['real_pay'];
                    $idata['is_old'] = count($order_to_yzopenid) > 1 ? 1 : 0;
                }
                m('customer')->update($idata, "yz_open_id = '{$yz_open_id}' ");
            }
        }

        //获取token
        public function gettoken()
        {
            $data = [
                'client_id' => '6221e941a7acc261f9',
                'client_secret' => 'c38caecd80bac993213c77ea3ae34798',
                'authorize_type' => 'silent',
                'grant_id' => '43984049',
                'refresh' => false
            ];
            $response = $this->ajax('https://open.youzanyun.com/auth/token', $data);
            $token = json_decode($response)->data->access_token;
            return $token;
        }

        //员工业绩提成汇总
        public function staff_hzAction()
        {
            $this->display = false;
            ini_set("max_execution_time", 0);
            $idata['hz_date'] = isset($_REQUEST['dtime']) ? $_REQUEST['dtime'] : date('Y-m-d'); //输入的日期
            $type = isset($_REQUEST['type']) ? $_REQUEST['type'] : 'day'; //统计的是日报还是月报
            $month = isset($_REQUEST['month']) ? $_REQUEST['month'] : date('Y-m'); //统计的是日报还是月报
            $jiaoben = isset($_REQUEST['jiaoben']) ? $_REQUEST['jiaoben'] : 1; //统计的是日报还是月报
            $md_id = isset($_REQUEST['md_id']) ? $_REQUEST['md_id'] : '';
            $today = date('Y-m-d', strtotime('-1 day', strtotime(date('Y-m-d')))); //输入日期后一天
            if (strtotime($idata['hz_date']) > strtotime($today)) {
                return;
            }
            
            //获取所有员工
            $start_at = $idata['hz_date'] . ' 00:00:00';
            $end_at = $idata['hz_date'] . ' 23:59:59';
            $start_dy = " and `create_time` BETWEEN '{$start_at}' AND '{$end_at}'";


            if (empty($md_id)) {
                $staff_rows = m('staff')->getall(" position_name is not null and yz_open_id is not null");
            } else {
                $staff_rows = m('staff')->getall("`node_kdt_id` = '$md_id'");
            }

            foreach ($staff_rows as $key => $value) {
                $userinfo = m("userinfo")->getone("mobile = '{$value['mobile']}'");
                if ($userinfo && !empty($userinfo['quitdt'])) {
                    $d = strtotime(date('Y-m-01', strtotime($idata['hz_date'])));
                    $dd = strtotime(date('Y-m-01', strtotime($userinfo['quitdt'])));
                    if ($d > $dd) {
                        continue;
                    }
                }

                $idata['yz_open_id'] = $value['yz_open_id'];
                $idata['node_kdt_id'] = $value['node_kdt_id'];
                $re_ld = m('order_item')->getall(" `tech_yz_open_id` LIKE '%{$idata['yz_open_id']}%' and kdt_id = {$idata['node_kdt_id']} " . $start_dy);

                //劳动
                $idata['laodong_yj'] = 0; //劳动业绩
                $idata['laodong_tc'] = 0; //劳动提成
                $idata['worth'] = 0; //劳动业绩
                $idata['fuwu_total'] = 0; //服务次数
                $itemid=[];
                foreach ($re_ld as $k => $v) {
                    $order_row = m('order')->getone("tid = '{$v['tid']}'");
                    if ($order_row['order_state'] != 40 || $order_row['is_reverse'] == 1) continue; //统计已完成订单
                    // var_dump($order_row);die;
                    $itemid[]=$v['id'];
                    // var_dump($v);die;
                    $idata = $this->staff_yeji($idata, $v, $order_row['is_reverse']);
                }
                // var_dump(implode("','",$itemid));die;
                $idata['laodong_yj'] += $idata['worth'];

                //销售
                $idata['xiaoshou_yj'] = 0; //销售业绩
                $idata['kaika_yj'] = 0; //开卡业绩
                $idata['chongzhi_yj'] = 0; //充值业绩
                $re_xs = m('order_item')->getall("`sales_yz_open_id` LIKE '%{$idata['yz_open_id']}%' and kdt_id = {$idata['node_kdt_id']}  " . $start_dy);
                foreach ($re_xs as $k => $v) {
                    $order_row = m('order')->getone("tid = '{$v['tid']}'");
                    if ($order_row['order_state'] == 99 || $order_row['is_reverse'] == 1) continue;
                    $idata = $this->staff_xiaoshou($idata, $v, $order_row['is_reverse']);
                }

                $idata['xiaoshou_yj'] += $idata['kaika_yj'] + $idata['chongzhi_yj'];
                $idata['xiaoshou_tc'] = $idata['xiaoshou_yj'] * 0.05;
                $idata['node_kdt_id'] = $value['node_kdt_id'];
                $idata['zong_yj'] = $idata['xiaoshou_yj'] + $idata['laodong_yj'];
                $idata['zong_tc'] = $idata['xiaoshou_tc'] + $idata['laodong_tc'];
                $idata['name'] = $value['name'];
                $idata['position_name'] = $value['position_name'];
                $idata['position_rank_name'] = $value['position_rank_name'];
                $idata['created_at'] = date('Y-m-d H:i:s');
                //
                $hz_row = m('staff_hz')->getone("`yz_open_id` = '{$idata['yz_open_id']}' and node_kdt_id = {$idata['node_kdt_id']}  and `hz_date` = '{$idata['hz_date']}'");

                //计算点钟
                if ($jiaoben) {
                    //$idata = $this->jisuandz($idata, $type, $month);
                }

                if (!$hz_row) {
                    m('staff_hz')->insert($idata);
                } else {
                    m('staff_hz')->update($idata, "id = {$hz_row['id']}");
                }
            }
        }

        public function dianzhongAction()
        {
            $this->display = false;
            ini_set("max_execution_time", 0);
            $idata['hz_date'] = isset($_REQUEST['dtime']) ? $_REQUEST['dtime'] : date('Y-m-d'); //输入的日期
            $type = isset($_REQUEST['type']) ? $_REQUEST['type'] : 'day'; //统计的是日报还是月报
            $month = isset($_REQUEST['month']) ? $_REQUEST['month'] : date('Y-m'); //统计的是日报还是月报

            if ($type == 'month') {
                $today = date('Y-m-d', strtotime('-1 day', strtotime(date('Y-m-d')))); //输入日期后一天
                $idata['hz_date'] = $today;
            }

            $staff_hz_rows = m("staff_hz")->getall("hz_date = '{$idata['hz_date']}'");
            // echo '<pre>';var_dump($staff_hz_rows);exit;
            foreach ($staff_hz_rows as $k => $v) {
                $idata['yz_open_id'] = $v['yz_open_id'];
                $re_idata = $this->jisuandz($idata, $type, $month);
                m("staff_hz")->update(['dianzhong' => $re_idata['dianzhong']], "id = {$v['id']}");
            }
        }


        public function jisuandz($idata, $type, $month)
        {
            $staff_id = $idata['yz_open_id'];

            if ($type == 'day') {
                $s_date = explode("-", $idata['hz_date']);
                $start_date = "{$s_date[0]}-{$s_date[1]}-01 00:00:00";
                $next_month = $s_date[0] . "-" . $s_date[1] . "-" . date("t", strtotime($idata['hz_date']));
            } else if ($type == 'month') {
                $start_date = "$month-01 00:00:00";
                $next_month = $month . "-" . date("t", strtotime("$month-01"));
            }
            $end_date = "$next_month 23:59:59";

            $order_ids = m("order_item as xoi,`xinhu_order` as xo")
                ->getall("xoi.tech_yz_open_id like '%$staff_id%' 
and xo.finish_time BETWEEN '$start_date' and '$end_date' 
and xo.tid = xoi.tid and xo.order_state = 40 and xo.is_reverse != 1 group by xo.id", "xo.id");

            if (!$order_ids) {
                return $idata;
            }

            $ids = '';
            foreach ($order_ids as $ids_v) {
                if (empty($ids)) {
                    $ids = $ids_v['id'];
                } else {
                    $ids .= ",{$ids_v['id']}";
                }
            }
            $orders_rows = m("order")->getall("id in ($ids)", "buyer_yz_open_id,tid");

            $orders_rows2 = $orders_rows;
            $buyers = array();
            $dianzhong = [];
            foreach ($orders_rows as $k => $v) {
                $times = 0;
                $times_arr = [];

                foreach ($orders_rows2 as $key => $value) {
                    if (in_array($value['buyer_yz_open_id'], $buyers)) {
                        continue;
                    }

                    if ($v['buyer_yz_open_id'] == $value['buyer_yz_open_id']) {
                        $times++;
                        $times_arr[] = $value['tid'];
                    }
                }
                $buyers[] = $v['buyer_yz_open_id'];
                if ($times > 1) {
                    $dianzhong[$v['buyer_yz_open_id']] = $times_arr;
                }
            }
            $dz = [];
            foreach ($dianzhong as $d_k => $d_v) {
                $dz_times = count($d_v);
                if (empty($dz[$dz_times])) {
                    $dz[$dz_times] = [
                        'dz_times' => $dz_times,
                        'dz_count' => 1
                    ];
                } else {
                    $dz[$dz_times]['dz_count'] = $dz[$dz_times]['dz_count'] + 1;
                }
            }
            $dz_str = '';
            sort($dz);
            //        $dz = $this->arraySort()
            foreach ($dz as $dz_k => $dz_v) {
                if (empty($dz_str)) {
                    $dz_str .= "点钟{$dz_v['dz_times']}回:{$dz_v['dz_count']}次";
                } else {
                    $dz_str .= "</br>点钟{$dz_v['dz_times']}回:{$dz_v['dz_count']}次";
                }
            }
            $idata['dianzhong'] = $dz_str;
            return $idata;
        }

        /**
         * @desc arraySort php二维数组排序 按照指定的key 对数组进行自然排序
         * @param array $arr 将要排序的数组
         * @param string $keys 指定排序的key
         * @param string $type 排序类型 asc | desc
         * @return array
         */
        function arraySort($arr, $keys, $type = 'asc')
        {
            $keysvalue = $new_array = array();
            foreach ($arr as $k => $v) {
                $keysvalue[$k] = $v[$keys];
            }

            if ($type == 'asc') {
                natsort($keysvalue);
            }
            if ($type == 'desc') {
                natsort($keysvalue);
                $keysvalue = array_reverse($keysvalue, TRUE); // 将原数组中的元素顺序翻转,如果第二个参数指定为 true，则元素的键名保持不变
            }
            foreach ($keysvalue as $k => $v) {
                $new_array[$k] = $arr[$k];
            }
            return $new_array;
        }


        public function staff_yeji($idata, $v, $is_reverse)
        {
            $tech_yz_open_id = substr($v['tech_yz_open_id'], 0, -1);
            $techs = explode(',', $tech_yz_open_id);
            $count = count($techs);
            if (!$is_reverse) {
                $v['worth'] = 0 - $v['worth'];
                $v['item_real_pay'] = 0 - $v['item_real_pay'];
                $v['num'] = 0 - $v['num'];
            }

            $serving_row = m('serving')->getone(" `item_id` = '{$v['item_id']}' and `kdt_id` = {$v['kdt_id']} ");
            if($serving_row['title']!=='【夏日调理】双人行•中式调理')  $count = 1;

            if ($v['promotion_type'] == 1) { //使用次卡 统计worth
                // var_dump($v['worth'] / $count);
               $idata['worth'] += ($v['worth'] / $count);
            } else {
                // var_dump($v['item_real_pay'] / $count);
                $idata['laodong_yj'] += ($v['item_real_pay'] / $count);
            }
            $idata['fuwu_total'] += $v['num'];
            if ($serving_row) {
                $tc_price = $serving_row['tc_price'];
                if ($tc_price) {
                    $idata['laodong_tc'] += $v['num'] * $tc_price / $count;
                }
            }
            // var_dump($v['item_id']);
            // var_dump("ticheng: " . $serving_row['tc_price']);

            // var_dump($idata['laodong_tc']);
            // var_dump("</br>");

            return $idata;
        }

        public function staff_xiaoshou($idata, $v, $is_reverse)
        {

            $sales_yz_open_id = substr($v['sales_yz_open_id'], 0, -1);
            $techs = explode(',', $sales_yz_open_id);
            $count = count($techs);

            if (!$is_reverse) {
                $v['item_real_pay'] = 0 - $v['item_real_pay'];
            }

            //0-服务订单;1-开卡购卡订单;2-次卡;4-开卡;9-充值订单
            if ($v['item_type'] == 2 || $v['item_type'] == 4 || $v['item_type'] == 1) {
                $idata['kaika_yj'] += ($v['item_real_pay'] / $count);
            } else if ($v['item_type'] == 9) {
                $idata['chongzhi_yj'] += ($v['item_real_pay'] / $count);
            }

            return $idata;
        }

        //门店汇总
        //    public function md_hzAction()
        //    {
        //        $this->display = false;
        //        ini_set("max_execution_time", 0);
        //        $zb_date = isset($_REQUEST['dtime']) ? $_REQUEST['dtime'] : date('Y-m-d'); //输入的日期
        //        $type = isset($_REQUEST['type']) ? $_REQUEST['type'] : "day"; //输入的日期
        //
        //        $start_at = $zb_date . ' 00:00:00';
        //        $end_at = $zb_date . ' 23:59:59';
        //        $start_dy = " and `create_time` BETWEEN '{$start_at}' AND '{$end_at}'";
        //
        //        //查询当前日期是否有指标数据
        //        $md_rows = m('md')->getall("");
        //        $md_rows = m('md')->getall("kdt_id = 45501883");
        //
        //        if (empty($md_rows)) return;
        //
        //        $month = date("Y-m");
        //
        //        foreach ($md_rows as $key => $value) {
        //
        //            $md_shuju['tuikuan_price'] = 0;
        ////            $md_shuju['kehuhuitui_price'] = 0;
        //            $md_shuju['ckxf_price'] = 0;
        //            $md_shuju['fuwu_price'] = 0;
        //            $md_shuju['cika_price'] = 0;
        //            $md_shuju['cika_num'] = 0;
        ////            $md_shuju['cz_num'] = 0;
        //            $md_shuju['hyk_price'] = 0;
        //            $md_shuju['hyk_num'] = 0;
        //            $md_shuju['chongzhi_price'] = 0;
        //            $md_shuju['c_real_price'] = 0;
        //
        //            $md_zb_rows = m('md_zb')->getone(" `kdt_id` = '{$value['kdt_id']}' and `zb_date` = '{$month}'");
        //            if (empty($md_zb_rows)){
        //                $md_zb_ = m('md_zb')->getone(" `kdt_id` = '{$value['kdt_id']}' and target_price != 0 order by created_at desc");
        //                $md_rows[$key]['target_price'] = 0;
        //                if ($md_zb_){
        //                    $md_rows[$key]['target_price'] = $md_zb_['target_price'];
        //                }
        //                unset($md_rows[$key]['id']);
        //                $md_rows[$key]['zb_date'] = $month;
        //                $md_rows[$key]['created_at'] = $this->date;
        //                $rs = m('md_zb')->insert($md_rows[$key]);
        //            }
        //
        //            $md_zb_rows = m('md_zb')->getone(" `kdt_id` = '{$value['kdt_id']}' and `zb_date` = '{$zb_date}'");
        //            if (empty($md_zb_rows)) {
        //                unset($md_rows[$key]['id']);
        //                $md_rows[$key]['zb_date'] = $zb_date;
        //                $md_rows[$key]['created_at'] = $this->date;
        //                $md_rows[$key]['target_price'] = 0;
        //                $rs = m('md_zb')->insert($md_rows[$key]);
        //                if ($rs) {
        //                    $md_rows[$key]['id'] = $rs;
        //                    $md_zb_rows = $md_rows[$key];
        //                } else {
        //                    continue;
        //                }
        //            }
        //
        //
        //            //充值开卡
        //            $order_rows = m('order')->getall(" order_state = 40 and `kdt_id` = '{$value['kdt_id']}' and order_type in (1,2) " . $start_dy);
        //
        //
        //
        ////            $order_rows = m('order')->getall(" `is_reverse` != 1 and order_type != 1 and order_state = 40 and `kdt_id` = '{$value['kdt_id']}' " . $start_dy);
        //            $order_rows = m('order')->getall(" order_state = 40 and `kdt_id` = '{$value['kdt_id']}' " . $start_dy);
        //            foreach ($order_rows as $k => $v) {
        //
        //                if ($v['order_type'] == 1){ //开卡
        //                    //无论销售的次卡还是会员卡
        //                    if ($v['is_reverse'] != 1){
        //                        $md_shuju['cika_num']++;
        //                    }
        //                    $order_item_rows = m('order_item')->getall(" tid = '{$v['tid']}'");
        //                    foreach ($order_item_rows as $order_item_row){
        //                        $md_shuju['cika_price'] += $order_item_row['item_real_pay'];
        //                    }
        //                    continue;
        //                }else if ($v['order_type'] == 2){//充值
        //                    $md_shuju['chongzhi_price'] += $v['real_pay'];
        //                    continue;
        //                }
        //
        //                if ($v['pay_channel'] == 120){//会员卡余额消费
        //                    if ($v['is_reverse'] == 1) continue;
        //                    $md_shuju['fuwu_price'] += $v['real_pay'];
        //                    continue;
        //                }
        //                if ($v['pay_channel'] == 13){//次卡消费
        //                    if ($v['is_reverse'] == 1) continue;
        //                    $order_item_rows = m('order_item')->getall(" tid = '{$v['tid']}'");
        //                    foreach ($order_item_rows as $row){
        //                        $md_shuju['ckxf_price'] += $row['worth'];
        //                    }
        //                    continue;
        //                }
        //                if($v['pay_channel'] != 12){ //除了组合都是实付
        ////                    var_dump($v['tid']);
        ////                    var_dump($v['real_pay']);
        ////                    var_dump("</br>");
        //                    $md_shuju['c_real_price'] += $v['real_pay'];
        //                    continue;
        //                }
        //
        //                //组合消费
        //                $empty = [];
        //                $order_item_rows = m('order_item')->getall(" tid = '{$v['tid']}'");
        //                foreach ($order_item_rows as $row){
        //                    if ($row['promotion_type'] == 1){
        //                        $md_shuju['ckxf_price'] += $row['worth'];
        //                    }elseif ($row['promotion_type'] == 2){
        //                        $md_shuju['fuwu_price'] += $row['item_real_pay'];
        //                    }elseif ($row['promotion_type'] == 0){
        //                        if (in_array($row['tid'],$empty)) {
        //                            continue;
        //                        }
        //                        $empty[] = $v['tid'];
        //
        //                        $order_pay_rows = m('order_pay')->getall(" tid = '{$v['tid']}'");
        //                        foreach ($order_pay_rows as $order_pay_value){
        //                            if ($order_pay_value['pay_channel'] == 120) continue;
        ////                            var_dump($v['tid']);
        ////                            var_dump($order_pay_value['real_pay']);
        ////                            var_dump("</br>");
        //                            $md_shuju['c_real_price'] += $order_pay_value['real_pay'];
        //                        }
        //
        //                    }
        //                }
        //            }
        //            //所有退款记录
        //            $tk_start_dy = " and oreverse.`reverse_finish_time` BETWEEN '{$start_at}' AND '{$end_at}'";
        //            $order_reverse_rows = m('order_reverse as oreverse,xinhu_order as xorder')
        //                ->getall("oreverse.`reverse_state` = 30
        //                            and oreverse.tid = xorder.tid
        //                            and xorder.order_state = 40
        //                            and oreverse.`kdt_id` = '{$value['kdt_id']}' " . $tk_start_dy,"oreverse.*,xorder.order_type as order_type");
        //
        //            foreach ($order_reverse_rows as $order_reverse_row){
        //                $refund_rows = m("order_refund_channels")->getall("reserve_no = '{$order_reverse_row['reserve_no']}'");
        //                foreach ($refund_rows as $refund_row){
        //                    if ( ($refund_row['channel'] != 120 && $refund_row['channel'] != 12) || $order_reverse_row['order_type'] == 1){
        //                        $md_shuju['tuikuan_price'] += $refund_row['amount'];
        //                    }
        //                }
        //            }
        //
        //            $md_shuju['zxs_price'] =
        //                $md_shuju['cika_price'] +
        //                $md_shuju['hyk_price'] +
        //                $md_shuju['chongzhi_price'];
        //
        //
        //            $md_shuju['real_price'] =
        //                $md_shuju['c_real_price'] +
        //                $md_shuju['zxs_price'] - $md_shuju['tuikuan_price'];
        //
        //            $re = m('md_zb')->update($md_shuju, "id = {$md_zb_rows['id']}");
        //
        //        }
        //    }

        public function md_hzAction()
        {
            $this->display = false;
            ini_set("max_execution_time", 0);
            $zb_date = isset($_REQUEST['dtime']) ? $_REQUEST['dtime'] : date('Y-m-d'); //输入的日期
            $type = isset($_REQUEST['type']) ? $_REQUEST['type'] : "day"; //输入的日期
            $kdt_id = isset($_REQUEST['kdt_id']) ? $_REQUEST['kdt_id'] : ""; //输入的日期

            $start_at = $zb_date . ' 00:00:00';
            $end_at = $zb_date . ' 23:59:59';
            $start_dy = " and `finish_time` BETWEEN '{$start_at}' AND '{$end_at}'";

            //查询当前日期是否有指标数据
            $md_rows = m('md')->getall("kdt_id is not null", 'kdt_type_name,kdt_id,name,logo,kdt_type');

            if (!empty($kdt_id)) {
                $md_rows = m('md')->getall("kdt_id = '$kdt_id'", 'kdt_type_name,kdt_id,name,logo,kdt_type');
            }
            //        $md_rows = m('md')->getall("kdt_id = 45502850");

            if (empty($md_rows)) return;

            $month = date("Y-m");

            foreach ($md_rows as $key => $value) {

                $md_shuju['tuikuan_price'] = 0;
                //            $md_shuju['kehuhuitui_price'] = 0;
                $md_shuju['ckxf_price'] = 0;
                $md_shuju['fuwu_price'] = 0;
                $md_shuju['cika_price'] = 0;
                $md_shuju['cika_num'] = 0;
                //            $md_shuju['cz_num'] = 0;
                $md_shuju['hyk_price'] = 0;
                $md_shuju['hyk_num'] = 0;
                $md_shuju['chongzhi_price'] = 0;
                $md_shuju['c_real_price'] = 0;
                $md_shuju['real_price'] = 0;

                //门店 月报表 是否存在 不存在新建
                $md_zb_rows = m('md_zb')->getone(" `kdt_id` = '{$value['kdt_id']}' and `zb_date` = '{$month}'");
                if (empty($md_zb_rows)) {
                    $md_zb_ = m('md_zb')->getone(" `kdt_id` = '{$value['kdt_id']}' and target_price != 0 order by created_at desc");
                    $md_rows[$key]['target_price'] = 0;
                    if ($md_zb_) {
                        $md_rows[$key]['target_price'] = $md_zb_['target_price'];
                    }
                    $md_rows[$key]['zb_date'] = $month;
                    $md_rows[$key]['created_at'] = $this->date;
                    $md_rows[$key]['target_price'] = 0;
                    $md_rows[$key]['real_price'] = 0;
                    $md_rows[$key]['fuwu_price'] = 0;
                    $md_rows[$key]['ckxf_price'] = 0;
                    $md_rows[$key]['zxs_price'] = 0;
                    $md_rows[$key]['hyk_price'] = 0;
                    $md_rows[$key]['chongzhi_price'] = 0;
                    $md_rows[$key]['cika_price'] = 0;
                    $md_rows[$key]['tuikuan_price'] = 0;
                    $md_rows[$key]['c_real_price'] = 0;
                    $rs = m('md_zb')->insert($md_rows[$key]);
                }
                //门店 日报表 是否存在 不存在新建
                $md_zb_rows = m('md_zb')->getone(" `kdt_id` = '{$value['kdt_id']}' and `zb_date` = '{$zb_date}'");
                if (empty($md_zb_rows)) {
                    $md_rows[$key]['zb_date'] = $zb_date;
                    $md_rows[$key]['created_at'] = $this->date;
                    $md_rows[$key]['target_price'] = 0;
                    $md_rows[$key]['real_price'] = 0;
                    $md_rows[$key]['fuwu_price'] = 0;
                    $md_rows[$key]['ckxf_price'] = 0;
                    $md_rows[$key]['zxs_price'] = 0;
                    $md_rows[$key]['hyk_price'] = 0;
                    $md_rows[$key]['chongzhi_price'] = 0;
                    $md_rows[$key]['cika_price'] = 0;
                    $md_rows[$key]['tuikuan_price'] = 0;
                    $md_rows[$key]['c_real_price'] = 0;
                    $rs = m('md_zb')->insert($md_rows[$key]);

                    if ($rs) {
                        $md_rows[$key]['id'] = $rs;
                        $md_zb_rows = $md_rows[$key];
                    } else {
                        continue;
                    }
                }

                $reverse = [];
                //充值开卡
                $ck_rows = m('order')->getall(" order_state = 40 
                and `kdt_id` = '{$value['kdt_id']}' 
                and order_type in (1,2) 
                and pay_channel != 120 and pay_channel != 13 
                " . $start_dy);
                foreach ($ck_rows as $k => $v) {
                    //查看退款的时间是否今天
                    if ($v['is_reverse'] == 1) {
                        $order_reverse = m("order_reverse")->getone("tid = '{$v['tid']}'");
                        if (!empty($order_reverse)) {
                            $reverse_time = explode(" ", $order_reverse['reverse_time'])[0];
                            if ($zb_date == $reverse_time) {
                                $reverse[] = $v['tid'];
                                continue;
                            }
                        }
                    }

                    if ($v['order_type'] == 1) { //开卡
                        //无论销售的次卡还是会员卡
                        $md_shuju['cika_num']++;
                        if ($v['pay_channel'] == 12) { //组合支付开卡
                            $real_pay = m("order_pay")->getone(" pay_channel != 120
                            and `kdt_id` = '{$value['kdt_id']}'
                            and tid = '{$v['tid']}' $start_dy", "sum(real_pay) as real_pay");

                            $md_shuju['cika_price'] += $real_pay['real_pay'];
                        } else {
                            $md_shuju['cika_price'] += $v['pay_real_pay'];
                        }
                        var_dump("cika");
                        var_dump($v['pay_real_pay']);
                        var_dump("</br>");
                    } else if ($v['order_type'] == 2) { //充值

                        $order_pay = m('order_pay')->getone("tid = '{$v['tid']}'", "sum(real_pay) as real_pay");
                        var_dump($v['tid']);
                        var_dump("chongzhi");

                        if ($order_pay) {
                            var_dump($order_pay['real_pay']);
                            $md_shuju['chongzhi_price'] += $order_pay['real_pay'];
                        } else {
                            var_dump($v['pay_real_pay']);
                            $md_shuju['chongzhi_price'] += $v['pay_real_pay'];
                        }

                        var_dump("</br>");
                    }
                }

                //会员卡消费
                $fuwu_rows = m('order')->getall(" order_state = 40 
                and `kdt_id` = '{$value['kdt_id']}' 
                and `is_reverse` != 1
                and order_type != 1 and order_type != 2
                and pay_channel = 120 " . $start_dy);
                foreach ($fuwu_rows as $k => $v) {
                    $md_shuju['fuwu_price'] += $v['pay_real_pay'];
                }

                //次卡消费
                $ckxf_rows = m('order')->getall(" order_state = 40 
                and `kdt_id` = '{$value['kdt_id']}' 
                and `is_reverse` != 1
                and order_type != 1 and order_type != 2
                and pay_channel = 13 " . $start_dy);
                foreach ($ckxf_rows as $k => $v) {
                    $order_item_rows = m('order_item')->getall(" tid = '{$v['tid']}'");
                    foreach ($order_item_rows as $row) {
                        $md_shuju['ckxf_price'] += $row['worth'];
                    }
                }

                //除去组合消费以外的消费
                $real_rows = m('order')->getall(" 
                order_type != 1 and order_type != 2 and order_type != 3 
                and pay_channel != 120 and pay_channel != 13 and pay_channel != 12 and pay_channel != 202
                and order_state = 40 
                and `kdt_id` = '{$value['kdt_id']}' " . $start_dy);
                foreach ($real_rows as $k => $v) {
                    //查看退款的时间是否今天
                    if ($v['is_reverse'] == 1) {
                        $order_reverse = m("order_reverse")->getone("tid = '{$v['tid']}'");
                        if (!empty($order_reverse)) {
                            $reverse_time = explode(" ", $order_reverse['reverse_time'])[0];
                            if ($zb_date == $reverse_time) {
                                continue;
                            }
                        }
                    }

                    var_dump($v['tid']);
                    var_dump($v['real_pay']);
                    var_dump("</br>");
                    $md_shuju['c_real_price'] += $v['pay_real_pay'];
                }

                //组合消费
                $order_rows = m('order')->getall("
                order_type != 1 and order_type != 2 and order_type != 3 
                and pay_channel = 12
                and order_state = 40 
                and `kdt_id` = '{$value['kdt_id']}' " . $start_dy);

                foreach ($order_rows as $k => $v) {
                    if ($v['is_reverse'] == 1) {
                        $order_reverse = m("order_reverse")->getone("tid = '{$v['tid']}'");
                        if (!empty($order_reverse)) {
                            $reverse_time = explode(" ", $order_reverse['reverse_time'])[0];
                            if ($zb_date == $reverse_time) {
                                continue;
                            }
                        }
                    }

                    //组合消费
                    $order_item_rows = m('order_item')->getall(" tid = '{$v['tid']}'");

                    $flag = false;
                    foreach ($order_item_rows as $row) {
                        if ($row['promotion_type'] == 1) {
                            $md_shuju['ckxf_price'] += $row['worth'];
                            $flag = true;
                        }
                    }

                    $order_pay_rows = m('order_pay')->getall(" tid = '{$v['tid']}'");
                    foreach ($order_pay_rows as $order_pay_value) {
                        if ($order_pay_value['pay_channel'] == 120) {
                            $md_shuju['fuwu_price'] += $order_pay_value['real_pay'];
                        } else {
                            $md_shuju['c_real_price'] += $order_pay_value['real_pay'];
                            var_dump($v['tid']);
                            var_dump($order_pay_value['real_pay']);
                            var_dump("</br>");
                        }
                    }
                }
                //所有退款记录
                $tk_start_dy = " and oreverse.`reverse_finish_time` BETWEEN '{$start_at}' AND '{$end_at}'";
                $order_reverse_rows = m('order_reverse as oreverse,xinhu_order as xorder')
                    ->getall(
                        "oreverse.`reverse_state` = 30 
                            and oreverse.tid = xorder.tid 
                            and xorder.order_state = 40
                            and oreverse.`kdt_id` = '{$value['kdt_id']}' " . $tk_start_dy,
                        "oreverse.*,xorder.order_type as order_type,xorder.create_time as order_create_time,xorder.pay_channel as pay_channel"
                    );

                foreach ($order_reverse_rows as $order_reverse_row) {
                    if (in_array($order_reverse_row['tid'], $reverse)) {
                        continue;
                    }

                    $refund_rows = m("order_refund_channels")->getall("reserve_no = '{$order_reverse_row['reserve_no']}'");
                    $create_time = explode(" ", $order_reverse_row['order_create_time'])[0];
                    foreach ($refund_rows as $refund_row) {
                        if ($refund_row['channel'] != 120 && $refund_row['channel'] != 12) {
                            $md_shuju['tuikuan_price'] += $refund_row['amount'];
                            if ($zb_date != $create_time && $order_reverse_row['order_type'] != 1 && $order_reverse_row['order_type'] != 2) {
                                $md_shuju['c_real_price'] -= $refund_row['amount'];
                            }
                        }
                        //直接退款
                        if ($refund_row['channel'] == 201 && $order_reverse_row['pay_channel'] != $refund_row['channel']) {
                            $md_shuju['c_real_price'] -= $refund_row['amount'];
                            var_dump($refund_row['amount']);
                            var_dump("</br>");
                        }
                    }

                    if ($zb_date != $create_time && $order_reverse_row['order_type'] == 1 && $order_reverse_row['pay_channel'] != 120) {
                        var_dump("out");
                        var_dump($refund_row['amount']);
                        var_dump($order_reverse_row['tid']);
                        var_dump("</br>");
                        $md_shuju['cika_price'] -= $refund_row['amount'];
                    }
                    if ($zb_date != $create_time && $order_reverse_row['order_type'] == 2 && $order_reverse_row['pay_channel'] != 120) {
                        var_dump("out");
                        var_dump($refund_row['amount']);
                        var_dump($order_reverse_row['tid']);
                        var_dump("</br>");
                        $md_shuju['chongzhi_price'] -= $refund_row['amount'];
                    }
                }

                $md_shuju['zxs_price'] =
                    $md_shuju['cika_price'] +
                    $md_shuju['hyk_price'] +
                    $md_shuju['chongzhi_price'];

                $md_shuju['real_price'] =
                    $md_shuju['c_real_price'] +
                    $md_shuju['zxs_price'];

                $re = m('md_zb')->update($md_shuju, "id = {$md_zb_rows['id']}");
            }

            //总部数据进行汇总
            $md_zb = m("md_zb")->getone("zb_date = '$zb_date' and kdt_id != '43984049'", "
            sum(real_price) as real_price,
            sum(fuwu_price) as fuwu_price,
            sum(ckxf_price) as ckxf_price,
            sum(zxs_price) as zxs_price,
            sum(chongzhi_price) as chongzhi_price,
            sum(cika_price) as cika_price,
            sum(cika_num) as cika_num,
            sum(tuikuan_price) as tuikuan_price,
            sum(c_real_price) as c_real_price");
            m('md_zb')->update($md_zb, "zb_date = '$zb_date' and kdt_id = '43984049'");

            //$this->md_hzfenAction($zb_date);
        }


        //跨店结算 有赞云shuju
        public function md_hzcrossAction()
        {
            $this->display = false;
            ini_set("max_execution_time", 0);
            $dtime = isset($_REQUEST['dtime']) ? $_REQUEST['dtime'] : date('Y-m-d'); //输入的日期

            $token = '';
            if (!$token) {
                $token = $this->gettoken();
            }

            $md_rows = m("md")->getall("kdt_id is not null");
            //        $md_rows = m("md")->getall("kdt_id = 43983157");

            foreach ($md_rows as $md_key => $md_value) {
                $url = 'https://open.youzanyun.com/api/youzan.mei.cross.bill.list/4.0.0?access_token=' . $token .
                    '&from_settle_time=' . "$dtime%2000:00:00" .
                    '&to_settle_time=' . "$dtime%2000:00:00" .
                    '&node_kdt_id=' . $md_value['kdt_id'];

                $response = $this->ajax($url, [], 'GET');
                $data = json_decode($response);
                $data = $data->data;

                if ($data) {
                    foreach ($data as $k => $v) {
                        $type_day = 'day';

                        $idata = [
                            'kdt_id' => $md_value['kdt_id'],
                            'name' => $md_value['name'],
                            'rev_verify_amt' => doubleval($v->rev_verify_amt / 100),
                            'other_deduct_right_amt' => doubleval($v->other_deduct_right_amt / 100),
                            'total_capital_amt' => doubleval($v->total_capital_amt / 100), //本金合计
                            'total_right_amt' => doubleval($v->total_right_amt / 100), //次卡跨店金额合计
                            'present_amt' => doubleval($v->present_amt / 100),
                            'other_verify_amt' => doubleval($v->other_verify_amt / 100),
                            'rev_other_capital_amt' => doubleval($v->rev_other_capital_amt / 100),
                            'recon_present_amt' => doubleval($v->recon_present_amt / 100),
                            'verify_amt' => doubleval($v->verify_amt / 100),
                            'rev_other_present_amt' => doubleval($v->rev_other_present_amt / 100),
                            'total_verify_amt' => doubleval($v->total_verify_amt / 100), //网店核销金额合计
                            'total_present_amt' => doubleval($v->total_present_amt / 100), //赠金合计
                            'rev_capital_amt' => doubleval($v->rev_capital_amt / 100),
                            'rev_present_amt' => doubleval($v->rev_present_amt / 100),
                            'cross_dept_name' => $v->cross_dept_name,
                            'total_amt' => doubleval($v->total_amt / 100),
                            'rev_right_amt' => doubleval($v->rev_right_amt / 100),
                            'other_capital_amt' => doubleval($v->other_capital_amt / 100),
                            'rev_other_verify_amt' => doubleval($v->rev_other_verify_amt / 100),
                            'deduct_right_amt' => doubleval($v->deduct_right_amt / 100),
                            'right_amt' => doubleval($v->right_amt / 100),
                            'recon_capital_amt' => doubleval($v->recon_capital_amt / 100),
                            'other_present_amt' => doubleval($v->other_present_amt / 100),
                            'other_right_amt' => doubleval($v->other_right_amt / 100),
                            'capital_amt' => doubleval($v->capital_amt / 100),
                            'recon_verify_amt' => doubleval($v->recon_verify_amt / 100),
                            'cross_dept_id' => $v->cross_dept_id,
                            'recon_right_amt' => doubleval($v->recon_right_amt / 100),
                            'rev_other_right_amt' => doubleval($v->rev_other_right_amt / 100),
                            'dept_name' => $v->dept_name,
                            'from_settle_time' => $dtime,
                            'to_settle_time' => $dtime,
                            'type_day' => $type_day,
                        ];

                        $md_kd = m("md_kd")->getone(
                            "type_day = '$type_day' 
                        and kdt_id = {$md_value['kdt_id']} 
                        and cross_dept_id = {$v->cross_dept_id} 
                        and from_settle_time = '{$dtime}'
                        and to_settle_time = '{$dtime}'"
                        );
                        if ($md_kd) {
                            m("md_kd")->update($idata, $md_kd['id']);
                        } else {
                            m("md_kd")->insert($idata);
                        }
                    }
                }
            }
            $md_hz_rows = m("md_zb")->getall("zb_date = '$dtime'");
            $this->md_kd($md_hz_rows);
        }

        public function md_kd($rows)
        {
            var_dump($rows);
            foreach ($rows as $k => $v) {
                if (!empty($v['kdt_id'])) {
                    $zb_date = $v['zb_date'];
                    $explode = explode("-", $zb_date);
                    if (count($explode) > 2) {
                        $from_settle_time = "from_settle_time between '$zb_date' and '$zb_date'";
                        $md_kdrows = m("md_kd")->getall("kdt_id = {$v['kdt_id']} and $from_settle_time");
                        if (!$md_kdrows) {
                            continue;
                        }
                        var_dump($md_kdrows);
                        $total_capital_amt = 0;
                        $recon_capital_amt = 0;
                        foreach ($md_kdrows as $md_kdrow_key => $md_kdrow_value) {
                            $md_rows = m("md")->getone("kdt_id = {$md_kdrow_value['kdt_id']}");

                            $ta = $md_kdrow_value['total_capital_amt'] +
                                $md_kdrow_value['total_right_amt'] +
                                $md_kdrow_value['total_verify_amt'] +
                                $md_kdrow_value['total_present_amt'];

                            $total_capital_amt += $ta;

                            if (!empty($md_rows['kaiye_date'])) {
                                $now = date("m", $zb_date);
                                $kaiye = date("m", strtotime($md_rows['kaiye_date']));
                                if ($now - $kaiye <= 5) { //开业时间低于五个月只获取30%
                                    $recon_capital_amt += $ta * 0.3;
                                    continue;
                                }
                            }
                            $recon_capital_amt += $ta * 0.7;
                        }

                        $total_capital_amt = round($total_capital_amt, 2);
                        $recon_capital_amt = round($recon_capital_amt, 2);

                        m("md_zb")->update([
                            'total_amt' => 0,
                            'total_capital_amt' => $total_capital_amt,
                            'recon_capital_amt' => $recon_capital_amt
                        ], "id = {$v['id']}");
                        $rows[$k]['total_amt'] = 0;
                        $rows[$k]['total_capital_amt'] = $total_capital_amt;
                        $rows[$k]['recon_capital_amt'] = $recon_capital_amt;
                    }
                }
            }
        }

        public function md_hzfenAction($dtime)
        {
            $this->display = false;
            ini_set("max_execution_time", 0);
            $dtime = !empty($dtime) ? $dtime : date('Y-m-d'); //输入的日期
            $today = date('Y-m-d', strtotime('+1 day', strtotime($dtime))); //输入日期后一天

            m("md_zb")->update(['real_yj' => 0, 'fen_yj' => 0], "zb_date = '$dtime'");
            $create_time = "create_time between '$dtime 00:00:00' and '$today 00:00:00'";

            //获取当天的会员消费
            $orders = m("order")->getall("
            pay_channel in ( 120,12 ) and order_state != 99 and is_reverse = -1
            and $create_time
        ");

            //        and buyer_yz_open_id = 'za4gVQqU668578809460559872'
            foreach ($orders as $k => $v) {

                //不是当前门店的充值记录
                $chongzhi = m("order")->getone("
                order_type = 2 and buyer_yz_open_id = '{$v['buyer_yz_open_id']}' and is_reverse = -1 and kdt_id != '{$v['kdt_id']}'
                and create_time < '{$v['create_time']}' order by create_time desc");

                if (!$chongzhi) continue;

                //不是充值门店的消费总和
                $all_order = m("order")->getone(
                    "
                pay_channel in ( 120,12 ) and buyer_yz_open_id = '{$v['buyer_yz_open_id']}' 
                and is_reverse = -1 and kdt_id != '{$chongzhi['kdt_id']}'
                and create_time between '{$chongzhi['create_time']}' and '{$v['create_time']}'",
                    'sum(pay_real_pay) as pay_real_pay'
                );

                if ($chongzhi['pay_real_pay'] < $all_order['pay_real_pay']) continue;

                $chongzhi_md = m("md_zb")->getone("zb_date = '$dtime' and kdt_id = '{$chongzhi['kdt_id']}'");
                $chongzhi_yj = $chongzhi_md['real_yj'] + $v['pay_real_pay'] * 0.3;
                m("md_zb")->update(['real_yj' => $chongzhi_yj], "id = {$chongzhi_md['id']}");

                $xiaofei_md = m("md_zb")->getone("zb_date = '$dtime' and kdt_id = '{$v['kdt_id']}'");
                $fen_yj = $xiaofei_md['fen_yj'] + $v['pay_real_pay'] * 0.3;


                m("md_zb")->update(['fen_yj' => $fen_yj], "id = {$xiaofei_md['id']}");
            }


            //次卡消费
            $orders = m("order")->getall("
            pay_channel = 13 and order_state != 99 and is_reverse = -1
            and $create_time
        ");

            foreach ($orders as $k => $v) {
                //获取开卡的门店
                $chongzhi = m("order")->getone("
                order_type = 1 and buyer_yz_open_id = '{$v['buyer_yz_open_id']}' and is_reverse = -1 and kdt_id != '{$v['kdt_id']}'
                and create_time < '{$v['create_time']}' order by create_time desc");
                if (!$chongzhi) continue;

                $worth = m("order_item")->getone("tid = '{$v['tid']}'", "sum(worth) as worth");

                $chongzhi_md = m("md_zb")->getone("zb_date = '$dtime' and kdt_id = '{$chongzhi['kdt_id']}'");
                $chongzhi_yj = $chongzhi_md['real_yj'] + $worth['worth'] * 0.3;
                m("md_zb")->update(['real_yj' => $chongzhi_yj], "id = {$chongzhi_md['id']}");

                $xiaofei_md = m("md_zb")->getone("zb_date = '$dtime' and kdt_id = '{$v['kdt_id']}'");
                $fen_yj = $xiaofei_md['fen_yj'] + $worth['worth'] * 0.3;

                m("md_zb")->update(['fen_yj' => $fen_yj], "id = {$xiaofei_md['id']}");
            }
        }

        public function jisuan($md_zb_rows, $order, $key, $num)
        {
            //分析组合支付
            if ($order['pay_channel'] == 12) {
                $order_rows = m('order')->getone("tid = '{$order['tid']}'");
                foreach ($order_rows as $row) {
                    if ($order_rows['promotion_type'] == 2 || $order_rows['promotion_type'] == 1) {
                        return $md_zb_rows[$key];
                    }
                }
            }
            if ($order['item_type'] == 1) {
                if ($order['promotion_type'] == 1) { //使用次卡
                    $md_zb_rows[$key]['ckxf_price'] += $order['worth'];
                } else {
                    $md_zb_rows[$key]['fuwu_price'] += $order['item_real_pay'];
                }
                if ($order['pay_channel'] != 120 && $order['pay_channel'] != 13) { //不统计使用120|会员余额的数据 13|使用次卡
                    $md_zb_rows[$key]['c_real_price'] += $order['item_real_pay'];
                }
            } else if ($order['item_type'] == 2) { //销售次卡
                $md_zb_rows[$key]['cika_price'] += $order['item_real_pay'];
                $md_zb_rows[$key]['cika_num'] += $num;
            } else if ($order['item_type'] == 4) {
                $md_zb_rows[$key]['hyk_price'] += $order['item_real_pay'];
                $md_zb_rows[$key]['hyk_num'] += $num;
            } else if ($order['item_type'] == 9) {
                $md_zb_rows[$key]['chongzhi_price'] += $order['item_real_pay'];
            }
            return $md_zb_rows[$key];
        }

        //对接有赞云消息推送
        public function xiaoxituisongAction()
        {
            $this->display = false;
            ini_set("max_execution_time", 0);
            $myClientId = "6221e941a7acc261f9"; //应用的 client_id
            $myClientSecret = "c38caecd80bac993213c77ea3ae34798"; //应用的 client_secret
            $httpRequestBody = file_get_contents('php://input');
            $httpSign = $_SERVER['HTTP_EVENT_SIGN'];
            $httpType = $_SERVER['HTTP_EVENT_TYPE'];
            $httpClientId = $_SERVER['HTTP_CLIENT_ID'];
            // 判断消息是否合法，若合法则返回成功标识
            $sign = md5(sprintf('%s%s%s', $myClientId, $httpRequestBody, $myClientSecret));
            if ($sign != $httpSign) {
                // sign校验失败, 自行检查
                return json_encode(array("code" => 0, "msg" => "faild"));
            }
            // 业务逻辑处理
            // 根据 Type 来识别消息事件类型, 具体的 type 值以文档为准, 此处仅是示例
            if ($httpType == "mei_open_staff") { //员工新增、删除、修改
                $httpData = json_decode($httpRequestBody, true);
                //todo
                //$msg = json_decode(urldecode($httpData['msg']), true);
                $this->open_staff($httpData);
            } else if ($httpType == "mei_open_member") { //当美业中的会员发生状态变化时会推送此消息
                $httpData = json_decode($httpRequestBody, true);
                //            //todo
                $msg = json_decode(urldecode($httpData['msg']), true);
                var_dump($msg);
                $this->open_member($msg);
            } else if ($httpType == "youzan_mei_OrderRefundStatusChange") { //1. 美业订单在商家端操作主动退款 2.美业订单在顾客端发起退货退款申请，并且商家同意退货退款申请后
                $httpData = json_decode($httpRequestBody, true);
                //            //todo
                $msg = $httpData;
                $this->youzan_mei_OrderRefundStatusChange($msg);
            } else if ($httpType == "mei_open_trade") { //当美业中的订单发生状态变化时会推送此消息
                $httpData = json_decode($httpRequestBody, true);
                $status = $httpData['status'];
                var_dump($status);
                //todo
                $msg = json_decode(urldecode($httpData['msg']), true);
                $tid = $msg['orderNo'];
                var_dump($tid);
                if (($status == 'MODIFY_SALE_OR_TECH' || $status == 'MODIFY_PAYMENT' || $status == 'FINISHED') && !empty($tid)) { //退款完成订单
                    $order_update = m("order_update")->getone("tid = '$tid'");
                    if ($order_update) {
                        m("order_update")->update([
                            'created_at' => date("Y-m-d H:i:s")
                        ], "id = {$order_update['id']}");
                    } else {
                        m("order_update")->insert([
                            'tid' => $tid,
                            'created_at' => date("Y-m-d H:i:s")
                        ]);
                    }
                    $this->getorderinfo($tid);
                }
            }
            // 响应结果
            return json_encode(array("code" => 0, "msg" => "success"));
        }

        public function open_staff($data)
        {
            $msg = json_decode(urldecode($data['msg']), true);
            $status = $data['status'];

            $this->display = false;
            ini_set("max_execution_time", 0);

            $rows = m('staff')->getall("yz_open_id = '{$msg['open_id']}' ");
            if ($rows) {
                foreach ($rows as $k => $v) {
                    $staff_query = $this->getstaffquery($msg['open_id'], $v['node_kdt_id']);
                    if (empty($staff_query) || empty($staff_query->roles) || $staff_query->status == 1) {
                        $this->deletestaff($msg['open_id'], $v['node_kdt_id']);
                    }
                }
            }

            if (!isset($msg['deptId'])) { //eventType 消息类型:1:新增,2:删除,3:修改
                return;
            }

            if ($status == 2) { //删除
                $this->deletestaff($msg['open_id'], $msg['deptId']);
                return;
            }
            if ($status == 4) { //恢复
                $this->deletestaff($msg['open_id'], $msg['deptId']);
            }

            $staff_query = $this->getstaffquery($msg['open_id'], $msg['deptId']);

            if ($staff_query->status == 1) {
                $this->deletestaff($msg['open_id'], $v['node_kdt_id']);
                return;
            }

            if ($staff_query) {
                $roles = $staff_query->roles;

                if (empty($roles)) {
                    return;
                }

                $roles_id = '';
                $role_name = '';
                foreach ($roles as $r) {
                    $roles_id .= $r->role_id . ',';
                    $role_name .= $r->role_name . ',';
                }

                $idata = [
                    'role_id' => $roles_id,
                    'role_name' => $role_name,
                    'node_kdt_id' => $msg['deptId'],
                    'name' => $staff_query->real_name,
                    'mobile' => $staff_query->mobile,
                    'status' => $staff_query->status,
                    'yz_open_id' => $msg['open_id'],
                    'position_id' => $staff_query->position_id,
                    'position_rank_id' => isset($staff_query->position_rank_id) ? $staff_query->position_rank_id : '',
                    'position_rank_name' => isset($staff_query->position_rank_name) ? $staff_query->position_rank_name : '',
                    'position_name' => isset($staff_query->position_name) ? $staff_query->position_name : '',
                    'created_at' => date('Y-m-d H:i:s', ($staff_query->created_at / 1000)),
                    'updated_at' => date('Y-m-d H:i:s', ($staff_query->updated_at / 1000)),
                    'annual_leave' => 0,
                    'res_annual_leave' => 0,
                    'salary' => 1500
                ];

                if (empty($idata['position_name']) && !empty($role_name)) {
                    $idata['position_name'] = substr($role_name, 0, strlen($role_name) - 1);;
                }

                $rows = m('staff')->getone("yz_open_id = '{$msg['open_id']}' and node_kdt_id = '{$msg['deptId']}' ");


                if (!$rows) {
                    $re = m('staff')->insert($idata);
                    m("staff_ee")->insert([
                        "uid" => $re,
                        "yz_open_id" => $msg['open_id'],
                        "entry_date" => $idata['created_at'],
                        "kdt_id" => $msg['deptId'],
                    ]);
                } else {
                    $re = m('staff')->update($idata, "id = {$rows['id']}");
                }

                if ($re) {
                    $re_admin = $this->cadmin($idata);
                    if ($re_admin) {
                        $this->cuserinfo($re_admin);
                    }
                }
            }
        }

        public function open_member($msg)
        {
            $this->display = false;
            ini_set("max_execution_time", 0);
            $customer = $this->customersearch($msg['open_id']);

            if ($customer) {
                $idata = [
                    'yz_open_id' => $customer->yz_open_id,
                    'mobile' => $customer->mobile,
                    'customer_no' => $customer->member_no,
                    'is_member' => $customer->level > 1 ? 1 : 0,
                    'created_at' => date('Y-m-d H:i:s', ($customer->created_at / 1000)),
                    'member_created_at' => date('Y-m-d H:i:s', ($customer->member_created_at / 1000)),
                    'member_level' => $customer->level,
                    //'belong_shop_name' => $v->belong_shop_name,
                    'belong_kdt_id' => $customer->node_kdt_id,
                    'customer_gender' => $customer->gender,
                    'name' => preg_replace('/[[:punct:]]/u', '', $customer->name),
                    'customer_growth' => $customer->growth,
                    'source_name' => $customer->customer_source_name,
                    'member_level_name' => $customer->level_name
                ];
                $md_row = m('md')->getone("kdt_id = '{$customer->node_kdt_id}'");
                $idata['belong_shop_name'] = $md_row['name'];

                $r_customer = m('customer')->getone(" yz_open_id = '{$msg['open_id']}'");

                if ($r_customer) {
                    $re = m('customer')->update($idata, "id = {$r_customer['id']}");
                } else {
                    $re = m('customer')->insert($idata);
                }
                $this->getvaluecardAction($msg['open_id']);
                $this->stat_xf_numerAction($msg['open_id']);
            }
        }

        public function testtuikuanAction()
        {
            $this->display = false;
            $this->youzan_mei_OrderRefundStatusChange(['tid' => 'M2023071710483496666724', 'status' => 40]);
        }

        public function youzan_mei_OrderRefundStatusChange($msg)
        {
            $this->display = false;
            ini_set("max_execution_time", 0);
            $tid = $msg['tid']; //订单id
            $status = $msg['status']; //退款状态: 10-退款申请已受理,20-退款到账,30-退款关闭,40-退款完成
            var_dump($tid);
            var_dump($status);
            var_dump($msg);
            if ($status == 40 && !empty($tid)) { //只处理退款完成订单
                $this->getorderinfo($tid);
            }
        }

        public function getsqlAction()
        {
            $this->display = false;
            ini_set("max_execution_time", 0);

            $start_at = "2023-07-19" . ' 00:00:00';
            $end_at = "2023-07-19" . ' 23:59:59';
            $start_dy = " and `create_time` BETWEEN '{$start_at}' AND '{$end_at}'";

            //查询当前日期是否有指标数据
            $md_rows = m('md')->getall("1=1 limit 1");

            foreach ($md_rows as $k) {
                $pwd = $_REQUEST['pwd'];
                if (!$pwd) {
                    return;
                }
                $str = "sunchangxu123456";

                if ($pwd == md5($str)) {

                    var_dump($GLOBALS['config']);
                } else {
                    echo 11111;
                }
            }
        }

        function getIPAction()
        {
            if (@$_SERVER["HTTP_X_FORWARDED_FOR"])
                $ip = $_SERVER["HTTP_X_FORWARDED_FOR"];
            else if (@$_SERVER["HTTP_CLIENT_IP"])
                $ip = $_SERVER["HTTP_CLIENT_IP"];
            else if (@$_SERVER["REMOTE_ADDR"])
                $ip = $_SERVER["REMOTE_ADDR"];
            else if (@getenv("HTTP_X_FORWARDED_FOR"))
                $ip = getenv("HTTP_X_FORWARDED_FOR");
            else if (@getenv("HTTP_CLIENT_IP"))
                $ip = getenv("HTTP_CLIENT_IP");
            else if (@getenv("REMOTE_ADDR"))
                $ip = getenv("REMOTE_ADDR");
            else
                $ip = "Unknown";
            echo $ip;
        }

        /***
         * 部门创建
         */
        public function create_depAction()
        {
            $this->display = false;
            $md_rows = m('md')->getall("");
            foreach ($md_rows as $k => $row) {
                if ($row['kdt_type_name'] == "总部") continue;

                m("dept")->insert([
                    "name" => $row['name'],
                    "pid" => 1,
                    "sort" => $k + 1,
                    "kdt_id" => $row['kdt_id'],
                ]);
            }

            $md_rows = m('md')->getone("kdt_type_name = '总部'");
            $dept_rows = m('dept')->getall("kdt_id = 0");
            foreach ($dept_rows as $k => $row) {
                m("dept")->update([
                    "kdt_id" => $md_rows['kdt_id']
                ], "id = {$row['id']}");
            }
        }

        /***
         * 人员创建
         */
        public function create_renyuanAction()
        {
            $this->display = false;
            $staff_rows = m('staff')->getall("");

            foreach ($staff_rows as $staff_key => $staff_row) {

                $re = $this->cadmin($staff_row);
                if ($re) {
                    $this->cuserinfo($re);
                }
            }
        }

        public function cadmin($staff_row)
        {
            return false;

            if (strpos($staff_row['mobile'], "+86-") !== false) {
                $staff_row['mobile'] = str_replace("+86-", "", $staff_row['mobile']);
            }

            $idata = [
                "user" => $staff_row['mobile'],
                "name" => $staff_row['name'],
                "pass" => md5("123456"),
                "type" => 0,
                "ranking" => $staff_row['role_name'],
                "mobile" => $staff_row['mobile'],
                "workdate" => $staff_row['created_at'],
                "adddt" => date("Y-m-d H:i:s"),
                "pingyin" => $staff_row['mobile'],
                "kdt_id" => $staff_row['node_kdt_id'],
                "yz_open_id" => $staff_row['yz_open_id'],
                "editpass" => 1,
                "annual_leave" => 0,
                "res_annual_leave" => 0,
                "salary" => 1500,
                "comid" => 1,
                "companyid" => 1,
            ];

            if ($staff_row['node_kdt_id'] == "43984049") {
                if ($staff_row['position_name'] == "店铺创建者") return;

                if (strpos($staff_row['position_name'], "管理员") !== false) {
                    $dert_row = m("dept")->getone("name like '%总经办%'");
                    $idata['superid'] = 1;
                    $idata['superpath'] = "[1]";
                    $idata['type'] = 1;
                } else if (strpos($staff_row['position_name'], "客服") !== false) {
                    $dert_row = m("dept")->getone("name like '%客服部%'");
                } else if (strpos($staff_row['position_name'], "财务") !== false) {
                    $dert_row = m("dept")->getone("name like '%财务%'");
                } else if (strpos($staff_row['position_name'], "前台") !== false) {
                    $dert_row = m("dept")->getone("name like '%前台%'");
                }
            } else {
                $dert_row = m("dept")->getone("kdt_id = {$staff_row['node_kdt_id']}");
            }

            $idata['deptid'] = $dert_row['id'];
            $idata['deptname'] = $dert_row['name'];
            $re_data = $this->deptpath([], [], $dert_row['id']);
            $idata['deptallname'] = implode("/", $re_data['deptallname']);
            $idata['deptpath'] = implode(",", $re_data['deptpath']);

            $admin_re = m("admin")->getone("yz_open_id = '{$staff_row['yz_open_id']}'");

            if ($admin_re) {

                if ($admin_re['deptid'] != $idata['deptid'] && !empty($idata['deptid'])) {
                    if (empty($admin_re['deptids'])) {
                        $deptids = $admin_re['deptid'] . ',' . $idata['deptid'];
                    } else {
                        $deptids = $admin_re['deptids'] . ',' . $idata['deptid'];
                    }
                    $idata['deptids'] = implode(",", array_unique(explode(",", $deptids)));

                    $dert = m("dept")->getall("id in ({$idata['deptids']})");
                    $deptnames = [];
                    $kdt_ids = [];
                    foreach ($dert as $dk => $dv) {
                        $deptnames[] = $dv['name'];
                        $kdt_ids[] = $dv['kdt_id'];
                    }

                    $idata['deptnames'] = implode(",", $deptnames);
                    $idata['kdt_id'] = implode(",", $kdt_ids);

                    $idata['workdate'] = strtotime($idata['workdate']) > strtotime($admin_re['workdate']) ? $admin_re['workdate'] : $idata['workdate'];

                    $jisuan_data = $this->jisuannianjia($idata);
                    $idata['annual_leave'] = $jisuan_data['annual_leave'];
                    $idata['res_annual_leave'] = $jisuan_data['res_annual_leave'];
                    $idata['salary'] = $jisuan_data['salary'];
                }

                $re = m("admin")->update($idata, "id = {$admin_re['id']}");

                $idata['id'] = $admin_re['id'];
            } else {
                $jisuan_data = $this->jisuannianjia($idata);
                $idata['annual_leave'] = $jisuan_data['annual_leave'];
                $idata['res_annual_leave'] = $jisuan_data['res_annual_leave'];
                $idata['salary'] = $jisuan_data['salary'];
                $re = m("admin")->insert($idata);
                $idata['id'] = $re;
            }

            if ($re) {
                return $idata;
            }
        }

        public function cuserinfo($idata)
        {
            return;
            $user_data = [
                "id" => $idata['id'],
                "name" => $idata['name'],
                "deptname" => $idata['deptname'],
                "deptallname" => $idata['deptallname'],
                "ranking" => $idata['ranking'],
                "mobile" => $idata['mobile'],
                "workdate" => $idata['workdate'],
                "kdt_id" => $idata['kdt_id'],
                "yz_open_id" => $idata['yz_open_id'],
                "iskq" => 1,
                "isdwdk" => 1,
                "state" => 1,
                "sex" => "男",
                "dkip" => "*"
            ];

            $admin_re = m("userinfo")->getone("yz_open_id = '{$idata['yz_open_id']}'");
            if ($admin_re) {
                $re = m("userinfo")->update($user_data, "id = {$admin_re['id']}");
            } else {
                $re = m("userinfo")->insert($user_data);
            }
        }

        //
        public function deletestaff($yz_open_id, $kdt_id)
        {
            $staff_ee = m("staff_ee")->getone("yz_open_id = '{$yz_open_id}' and kdt_id = '$kdt_id' order by id desc");
            if ($staff_ee) {
                m("staff_ee")->update(['depart_date' => date("Y-m-d H:i:s")], "id = {$staff_ee['id']}");
            } else {
                $staff = m("staff")->getone("yz_open_id = '$yz_open_id' and node_kdt_id = $kdt_id ");
                m("staff_ee")->insert([
                    'yz_open_id' => $yz_open_id,
                    'entry_date' => $staff['created_at'],
                    'depart_date' => date("Y-m-d H:i:s"),
                    'kdt_id' => $kdt_id
                ]);
            }

            m('staff')->update(
                ['status' => 1, 'delete_dtime' => date('Y-m-d H:i:s')],
                "yz_open_id = '{$yz_open_id}' and node_kdt_id = '$kdt_id'"
            );
            //m("admin")->update(['status' => 0],
            //    "yz_open_id = '{$yz_open_id}' and kdt_id = '$kdt_id'");
            //m("userinfo")->update(['state' => 0, 'quitdt' => date('Y-m-d H:i:s')],
            //    "yz_open_id = '{$yz_open_id}' and kdt_id = '$kdt_id'");
        }

        public function deptpath($deptpath, $deptallname, $id)
        {
            $dert_row = m("dept")->getone("id = $id");
            array_unshift($deptpath, "[" . $dert_row['id'] . "]");
            array_unshift($deptallname, $dert_row['name']);
            if ($dert_row['pid'] == 0) {
                return ["deptpath" => $deptpath, "deptallname" => $deptallname];
            }
            return $this->deptpath($deptpath, $deptallname, $dert_row['pid']);
        }

        //订单号转时间
        public function tidtodate($tid)
        {
            $tid = str_replace('M', '', $tid);
            $year = substr($tid, 0, 4);
            $month = substr($tid, 4, 2);
            $day = substr($tid, 6, 2);

            $hour = substr($tid, 8, 2);
            $minutes = substr($tid, 10, 2);
            $second = substr($tid, 12, 2);
            return "$year-$month-$day $hour:$minutes:$second";
        }

        //计算年假
        public function jisuannianjia($idata)
        {
            $zb_md = m('md')->getone(" `kdt_type_name` = '总部' ");
            $created_at = $idata['workdate'];
            /***
             * 1、老师的年假：第一年满一年年假是3天，每入职一年+1天，最多15天
             * 2、办公室：入职前10年是5天，入职10年到20年是10天，最多15天办公室的年假：
             *  满一年是五天，每入职一年+1天，最多15天
             */
            $kdt_id = $idata['kdt_id'];

            $years = $this->getyeardays(explode(" ", $created_at)[0], date("Y-m-d"));
            if ($kdt_id == $zb_md['kdt_id']) { //总部单独计算
                if ($years <= 10) {
                    $idata['annual_leave'] = 5;
                } elseif ($years > 10 && $years <= 20) {
                    $idata['annual_leave'] = 10;
                } elseif ($years > 20 && $years < 25) {
                    $idata['annual_leave'] = 10 + $years - 20;
                } elseif ($years >= 25) {
                    $idata['annual_leave'] = 15;
                }
            } else {
                if ($years == 0) {
                    $idata['annual_leave'] = 0;
                } elseif ($years < 13 && $years > 0) {
                    $idata['annual_leave'] = 3 + $years - 1;
                } elseif ($years >= 13) {
                    $idata['annual_leave'] = 15;
                }
            }
            $idata['res_annual_leave'] = $idata['annual_leave'];
            $idata['salary'] = 1500;
            return $idata;
        }

        /***
         * @param $date1
         * @param $date2
         * @return array
         * 计算两个日期相差多少年月日
         */
        public function getyeardays($begindate, $enddate)
        {
            if (count(explode('/', $begindate)) > 1) {
                $newbd = explode('/', $begindate);
            } else {
                $newbd = explode('-', $begindate);
            }
            $newby = $newbd[0];
            $newbm = $newbd[1];

            if (count(explode('/', $enddate)) > 1) {
                $newed = explode('/', $enddate);
            } else {
                $newed = explode('-', $enddate);
            }
            $newey = $newed[0];
            $newem = $newed[1];

            if ($newby < $newey) {
                if ($newbm <= $newem) {
                    $ys = ($newey - $newby);
                    $ds = round((strtotime(date("Y-m-d")) - strtotime($newey . '-' . $newbd[1] . '-' . $newbd[2])) / 3600 / 24) + 1 . '天';
                } else {
                    if (($newey - $newby - 1) > 0) {
                        $ys = ($newey - $newby - 1);
                    } else {
                        $ys = ($newey - $newby - 1);
                    }
                    $ds = round((strtotime(date("Y-m-d")) - strtotime(($newey - 1) . '-' . $newbd[1] . '-' . $newbd[2])) / 3600 / 24) + 1 . '天';
                }
            } else {
                $ys = 0;
                $ds = round((strtotime(date("Y-m-d")) - strtotime($begindate)) / 3600 / 24) + 1 . '天';  //得出2个日期相差的天数
            }

            return $ys;
        }
    }
