-- 跨店结算汇总表建表脚本
-- 创建时间：2025-07-22
-- 说明：用于存储跨店结算的汇总数据，包括储值本金、赠金、次卡和网店核销等各类结算信息

CREATE TABLE `oa_cross_store_settle_summary` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `period` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '汇总月份 (格式: YYYY-MM)',
  `store_id` int unsigned NOT NULL DEFAULT '0' COMMENT '门店ID',
  `store_type` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '门店类型 (新店, 老店)',
  `other_store_id` int unsigned NOT NULL DEFAULT '0' COMMENT '他店ID',
  `other_store_type` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '他店类型 (新店, 老店)',
  `total_reconciliation_amount` decimal(15,2) NOT NULL DEFAULT '0.00' COMMENT '对账金额合计',
  `p_local_consume_foreign` decimal(15,2) NOT NULL DEFAULT '0.00' COMMENT '储值本金-本店耗他店本金',
  `p_local_refund_foreign` decimal(15,2) NOT NULL DEFAULT '0.00' COMMENT '储值本金-本店回退他店本金',
  `p_foreign_consume_local` decimal(15,2) NOT NULL DEFAULT '0.00' COMMENT '储值本金-他店耗本店本金',
  `p_foreign_refund_local` decimal(15,2) NOT NULL DEFAULT '0.00' COMMENT '储值本金-他店回退本店本金',
  `p_total_amount` decimal(15,2) NOT NULL DEFAULT '0.00' COMMENT '储值本金-本金合计',
  `p_reconciliation_amount` decimal(15,2) NOT NULL DEFAULT '0.00' COMMENT '储值本金-本金对账金额',
  `b_local_consume_foreign` decimal(15,2) NOT NULL DEFAULT '0.00' COMMENT '储值赠金-本店耗他店赠金',
  `b_local_refund_foreign` decimal(15,2) NOT NULL DEFAULT '0.00' COMMENT '储值赠金-本店回退他店赠金',
  `b_foreign_consume_local` decimal(15,2) NOT NULL DEFAULT '0.00' COMMENT '储值赠金-他店耗本店赠金',
  `b_foreign_refund_local` decimal(15,2) NOT NULL DEFAULT '0.00' COMMENT '储值赠金-他店回退本店赠金',
  `b_total_amount` decimal(15,2) NOT NULL DEFAULT '0.00' COMMENT '储值赠金-赠金合计',
  `b_reconciliation_amount` decimal(15,2) NOT NULL DEFAULT '0.00' COMMENT '储值赠金-赠金对账金额',
  `cc_local_consume_foreign_count` int unsigned NOT NULL DEFAULT '0' COMMENT '次卡-本店耗他店卡(次数)',
  `cc_local_refund_foreign_count` int unsigned NOT NULL DEFAULT '0' COMMENT '次卡-本店回退他店卡(次数)',
  `cc_foreign_consume_local_count` int unsigned NOT NULL DEFAULT '0' COMMENT '次卡-他店耗本店卡(次数)',
  `cc_foreign_refund_local_count` int unsigned NOT NULL DEFAULT '0' COMMENT '次卡-他店回退本店卡(次数)',
  `cc_local_upgrade_foreign_amount` decimal(15,2) NOT NULL DEFAULT '0.00' COMMENT '次卡-本店升他店卡(金额)',
  `cc_foreign_upgrade_local_amount` decimal(15,2) NOT NULL DEFAULT '0.00' COMMENT '次卡-他店升本店卡(金额)',
  `cc_total_amount` decimal(15,2) NOT NULL DEFAULT '0.00' COMMENT '次卡-次卡跨店金额合计',
  `cc_reconciliation_amount` decimal(15,2) NOT NULL DEFAULT '0.00' COMMENT '次卡-次卡对账金额',
  `ol_local_redeem_foreign_count` int unsigned NOT NULL DEFAULT '0' COMMENT '网店核销-本店核销他店码(次数)',
  `ol_local_refund_foreign_count` int unsigned NOT NULL DEFAULT '0' COMMENT '网店核销-本店回退他店码(次数)',
  `ol_foreign_redeem_local_count` int unsigned NOT NULL DEFAULT '0' COMMENT '网店核销-他店核销本店码(次数)',
  `ol_foreign_refund_local_count` int unsigned NOT NULL DEFAULT '0' COMMENT '网店核销-他店回退本店码(次数)',
  `ol_total_amount` decimal(15,2) NOT NULL DEFAULT '0.00' COMMENT '网店核销-网店核销金额合计',
  `ol_reconciliation_amount` decimal(15,2) NOT NULL DEFAULT '0.00' COMMENT '网店核销-网店核销对账金额',
  `create_time` int NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` int NOT NULL DEFAULT '0' COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uk_store_pair_period` (`store_id`,`other_store_id`,`period`) USING BTREE COMMENT '门店配对和月份的唯一索引',
  KEY `idx_period` (`period`) USING BTREE COMMENT '月份索引',
  KEY `idx_store_id` (`store_id`) USING BTREE COMMENT '门店ID索引',
  KEY `idx_other_store_id` (`other_store_id`) USING BTREE COMMENT '他店ID索引'
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='跨店结算汇总表';