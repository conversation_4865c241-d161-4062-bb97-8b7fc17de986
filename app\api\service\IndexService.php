<?php

namespace app\api\service;

use app\api\BaseController;
use app\finance\service\ExpenseService;
use app\store\service\LizhiService;
use app\store\service\StoreService;
use think\App;
use think\facade\Db;

class IndexService
{
    protected $storeservice;

    public function __construct()
    {
        $this->storeservice = new StoreService();
    }

    //调店
    public function transtore($jsoncode)
    {
        $avail_date = $jsoncode['detail_time'];

        $now_date = strtotime(date("Y-m-d"));
        $avail_time = strtotime($avail_date);

        if ($now_date >= $avail_time) {
            $data = [
                'id' => 0,
                "aid" => $jsoncode['aid'],
                "o_did" => $jsoncode['o_did'],
                "n_did" => $jsoncode['n_did'],
                'aname' => Db::name("admin")->where(['id' => $jsoncode['aid']])->value("name"),
                'o_dname' => Db::name("Department")->where(['id' => $jsoncode['o_did']])->value("title"),
                'n_dname' => Db::name("Department")->where(['id' => $jsoncode['n_did']])->value("title"),
                "transfer_date" => $jsoncode['detail_time']
            ];

            $update = [];
            if ($jsoncode['select'] == 26) {//储备店长
                $update['label_id'] = Db::name("store_label")->where(['position_id' => $jsoncode['select']])->value('id');
                Db::name("admin")->where(['id' => $jsoncode['aid']])->update($update);
            } else if ($jsoncode['select'] == 13 || $jsoncode['select'] == 15 || $jsoncode['select'] == 17) {//
                //$update['position_id'] = Db::name("position")->where(['id' => $jsoncode['select']])->value('id');
                $this->updateTranstoreAdminDep(
                    $jsoncode['aid'],
                    $jsoncode['o_did'],
                    $jsoncode['n_did'],
                    $jsoncode['select']
                );
            } else {
                //$admin = Db::name("admin")->where(['id' => $jsoncode['aid']])->find();
                //$update['position_id'] = "{$admin['position_id']},{$jsoncode['select']}";
                if ($jsoncode['select'] == 12) { //店长
                    //晋升为店长时 修改对应门店的部门负责人
                    $this->updateDeptmentLeader(0, $jsoncode['o_did']);
                    $this->updateDeptmentLeader($jsoncode['aid'], $jsoncode['n_did']);
                    $this->updateTranstoreAdminDep(
                        $jsoncode['aid'],
                        $jsoncode['o_did'],
                        $jsoncode['n_did'],
                        $jsoncode['select']
                    );
                }
            }

           // $admin = Db::name("admin")->where(['id' => $jsoncode['aid']])->find();
//            if (!empty($admin) && $admin['pay_type'] > 1 && $admin['pay_type_did'] == $jsoncode['o_did']) {
//                $update['pay_type_did'] = $jsoncode['n_did'];
//            }


            $this->storeservice->add_store_transfer($data);

            return true;
        }
        return false;
    }

    public function updateTranstoreAdminDep($aid, $o_did, $n_did, $position_id)
    {

        $main = Db::name("admin")->where([
            'id' => $aid,
            'main_did' => $o_did
        ])->field("main_did,main_position_id")->find();

        if (!empty($main)) {
            $main_position_id = [$main['main_position_id']];
            $main_position_id[] = $position_id;

            $result = array_unique($main_position_id);

            $re_a = Db::name("admin")->where(['id' => $aid])
                ->update([
                    'main_did' => $n_did,
                    'main_position_id' => implode(",", $result)
                ]);

        } else {
            $main_b = Db::name("admin_dep")->where([
                'aid' => $aid,
                'did' => $o_did,
                'status' => 1
            ])->field("did,position_id")->find();

            if (!empty($main_b)) {
                $re_b = Db::name("admin_dep")->where([
                    'aid' => $aid,
                    'did' => $o_did,
                    'status' => 1
                ])->update([
                    'did' => $n_did,
                    'position_id' => $position_id
                ]);
            }
        }

        $this->editPositionDepartment($aid);

    }


    //离职
    public function lizhi($jsoncode)
    {
        $avail_date = $jsoncode['end_time'];
        $now_date = strtotime(date("Y-m-d"));
        $avail_time = strtotime($avail_date);
        if ($now_date < $avail_time) return false;

        $department = Db::name("Department")->where(['id' => $jsoncode['department_type']])->find();
        $aadmin = Db::name("admin")->where(['id' => $jsoncode['aid']])->find();
        $aadmin_expand = Db::name("admin_expand")->where(['id' => $jsoncode['aid']])->find();

        $data = [
            'id' => 0,
            "aid" => $jsoncode['aid'],
            'aname' => $aadmin['name'],
            "o_did" => $jsoncode['department_type'],
            'o_dname' => $department['title'],
            "transfer_date" => $jsoncode['end_time'],
            "is_black" => isset($jsoncode['is_black']) ? $jsoncode['is_black'] : 2,
        ];
        if ($department['remark'] != '门店') {
            $data['dstatus'] = 1;
            $dstatus = 1;
        } else {
            $dstatus = 2;
        }

        //将人员加到人才中心
        $admin_view_data = [
            'name' => $aadmin['name'],
            'age' => $aadmin['age'],
            'id_card' => !empty($aadmin_expand) ? $aadmin_expand['id_card_number'] : '',
            'mobile' => $aadmin['mobile'],
            'sex' => $aadmin['sex'],
            'did' => $jsoncode['department_type'],
            'dname' => $department['title'],
            'dstatus' => $dstatus,
            'status' => 6,
            'create_time' => time(),
            'edu_background' => !empty($aadmin_expand) ? $aadmin_expand['education'] : '',
            'edu_school' => !empty($aadmin_expand) ? $aadmin_expand['graduation_school'] : '',
            'edu_major' => !empty($aadmin_expand) ? $aadmin_expand['major'] : '',
            'certificate' => !empty($aadmin_expand) ? $aadmin_expand['certificates'] : '',
            "is_black" => isset($jsoncode['is_black']) ? $jsoncode['is_black'] : 2
        ];
        $re_admin_view = Db::name("admin_view")->where(['aid' => $jsoncode['aid']])->find();
        if (!empty($re_admin_view)) {
            $admin_view_id = $re_admin_view['id'];
            Db::name("admin_view")->where(['id' => $re_admin_view['id']])->update([
                'is_black' => $admin_view_data['is_black'],
                'status' => 6
            ]);
        } else {
            $admin_view_id = Db::name("admin_view")->insertGetId($admin_view_data);
        }

        $admin_view_record_data = [
            'aview_id' => $admin_view_id,
            'aid' => 1,
            'aname' => "超级员工",
            'create_time' => time(),
            'remark' => "离职时间：{$jsoncode['end_time']}，离职原因：{$jsoncode['content']}",
            's_str' => "通过离职审批",
            'type' => -1,
        ];
        Db::name("admin_view_record")->insertGetId($admin_view_record_data);
        //调离记录
        $this->storeservice->add_store_transfer($data);

        $lizhiservice = new LizhiService();
        //离职删除门店数据
        $lizhiservice->deleteStoreBusiness($aadmin['id'], $aadmin['did'], -1);
        $lizhiservice->deleteStoreBusiness_t($aadmin['id'], $aadmin['did'], -1);
        $lizhiservice->deleteQiwei($aadmin['wx_account']);

        //查看是否有对应的部门负责人
        $leader_dep = Db::name("department")
            ->where([
                'leader_id' => $aadmin['id'],
                'remark' => '门店'
            ])
            ->select()->toArray();
        foreach ($leader_dep as $k => $v) {
            //替换为区经负责人
            $p_dep = Db::name("department")->where([
                'id' => $v['pid'],
                'remark' => '门店'
            ])->find();

            if (!empty($p_dep) && !empty($p_dep['leader_id'])) {
                Db::name("department")->where(['id' => $v['id']])->update([
                    'leader_id' => $p_dep['leader_id']
                ]);
            }
        }

        return true;
    }


    //修改部门负责人
    public function updateDeptmentLeader($aid = 0, $did)
    {
        //如果没有 替换人 就把上级区经设为部门负责人
        if (empty($aid)) {
            //先移除 部门负责人
            Db::name("department")->where(['id' => $did])->update(['leader_id' => 0]);

            $dep = Db::name("department")->where(['id' => $did])->find();

            if (!empty($dep)) {
                $p_dep = Db::name("department")->where(['id' => $dep['pid']])->find();
                if (!empty($p_dep) && !empty($p_dep['leader_id'])) {
                    $aid = $p_dep['leader_id'];
                }
            }

        }

        Db::name("department")->where(['id' => $did])->update(['leader_id' => $aid]);

    }

    //转岗
    public function zhuangang($jsoncode)
    {
        $avail_date = $jsoncode['detail_time'];
        $now_date = strtotime(date("Y-m-d"));
        $avail_time = strtotime($avail_date);
        if ($now_date < $avail_time) return false;

        $admin = Db::name("admin")->where(['id' => $jsoncode['aid']])->find();

        $update = [];
        if ($jsoncode['select'] == 26) {//储备店长
            $update['label_id'] = Db::name("store_label")->where(['position_id' => $jsoncode['select']])->value('id');
        } else if ($jsoncode['select'] == 13 || $jsoncode['select'] == 15 || $jsoncode['select'] == 17) {//

            if (!in_array(90, explode(",", $admin['position_id']))) {
                $update['position_id'] = "{$admin['position_id']},90";
            }

            $update['rank_id'] = Db::name("store_rank")->where(['position_id' => $jsoncode['select']])->value('id');
        } else {
            $update['position_id'] = "{$admin['position_id']},{$jsoncode['select']}";
        }

        Db::name("admin")->where(['id' => $jsoncode['aid']])->update($update);
        return true;
    }


    /***
     * @param $jsoncode
     * @return bool
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * 晋升
     */
    public function jinsheng($jsoncode)
    {
        $avail_date = $jsoncode['promotion_time'];
        $now_date = strtotime(date("Y-m-d"));
        $avail_time = strtotime($avail_date);
        if ($now_date < $avail_time) return false;

        $update = [];

        if (substr($jsoncode['did'], -1) === ',') {
            $jsoncode['did'] = substr($jsoncode['did'], 0, -1); // 删除最后一个字符
        }

        if ($jsoncode['select'] == 26) {//储备店长
            $update['label_id'] = Db::name("store_label")->where(['position_id' => $jsoncode['select']])->value('id');
            $re = Db::name("admin")->where(['id' => $jsoncode['aid']])->update($update);
        } else if ($jsoncode['select'] == 13 || $jsoncode['select'] == 15 || $jsoncode['select'] == 17) {
            $this->updateAdminDep($jsoncode['aid'], $jsoncode['did'], $jsoncode['select']);
        } else {

            if ($jsoncode['select'] == 12) { //店长
                //晋升为店长时 修改对应门店的部门负责人
                $this->updateDeptmentLeader($jsoncode['aid'], $jsoncode['did']);
                $this->updateAdminDep($jsoncode['aid'], $jsoncode['did'], $jsoncode['select']);
            }

            //$admin = Db::name("admin")->where(['id' => $jsoncode['aid']])->find();
            //$update['position_id'] = "{$admin['position_id']},{$jsoncode['select']}";
        }

        return true;
    }

    //判断是主部门 还是附属部门 进行对应修改
    public function updateAdminDep($aid, $did, $position_id)
    {
        $re_a = Db::name("admin")->where([
            'id' => $aid,
            'main_did' => $did
        ])->update(['main_position_id' => $position_id]);

        $re_b = Db::name("admin_dep")->where([
            'aid' => $aid,
            'did' => $did,
            'status' => 1
        ])->update(['position_id' => $position_id]);


        if ($re_a || $re_b) {
            $this->editPositionDepartment($aid);
        }

    }


    public function editPositionDepartment($aid)
    {
        $ad = Db::name("admin")->where([
            'id' => $aid
        ])->field("main_did,main_position_id")->find();

        $main_did = explode(",", $ad['main_did']);
        $main_position_id = explode(",", $ad['main_position_id']);

        $admin_dep = Db::name("admin_dep")->where([
            'aid' => $aid,
            'status' => 1
        ])->field("did,position_id")->select()->toArray();

        foreach ($admin_dep as $k => $v) {
            $main_did[] = $v['did'];
            $main_position_id[] = $v['position_id'];
        }

        $main_did = array_filter($main_did, function ($value) {
            // 只移除null值，可以根据需要修改条件
            return !is_null($value);
        });

        $main_position_id = array_filter($main_position_id, function ($value) {
            // 只移除null值，可以根据需要修改条件
            return !is_null($value);
        });

        Db::name("admin")->where([
            'id' => $aid,
        ])->update([
            'did' => implode(",", array_values($main_did)),
            'position_id' => implode(",", array_values($main_position_id))
        ]);


    }


    //转正
    public function zhuanzheng($jsoncode)
    {
        $avail_date = $jsoncode['avail_time'];
        $now_date = strtotime(date("Y-m-d"));
        $avail_time = strtotime($avail_date);
        if ($now_date < $avail_time) return false;

        Db::name("admin")->where(['id' => $jsoncode['aid']])->update(['type' => 1]);
        return true;
    }

    public function shebao($jsoncode)
    {
        $avail_date = $jsoncode['detail_time'];

        $now_date = strtotime(date("Y-m-d"));
        $avail_time = strtotime($avail_date);
        if ($now_date < $avail_time) return false;

        Db::name("admin")->where(['id' => $jsoncode['aid']])->update([
            'pay_type' => $jsoncode['pay_type'],
            'pay_type_did' => $jsoncode['department_type']
        ]);
        return true;
    }


    public function fukuan($jsoncode, $in_approve)
    {
        $type = Db::name("type_pay")
            ->alias('tp')
            ->where(['tp.id' => $jsoncode['pay_type']])
            ->join('type t', 't.id = tp.type_id', 'LEFT')
            ->find();

        $be = [
            'approve_id' => $in_approve['id'],
            'pay_type' => $jsoncode['pay_type'],
            'pay_time' => date("Y-m-d"),
            'pay_amount' => $jsoncode['pay_amount'],
            'pay_text' => $jsoncode['in_text'],
            'pay_file' => $jsoncode['file_ids'],
            'is_fapiao' => (isset($jsoncode['is_invoice']) && !empty($jsoncode['is_invoice'])) ? $jsoncode['is_invoice'] : '否',
            'store' => $jsoncode['store'],
            'did' => $jsoncode['did'],
            'admin_id' => $jsoncode['aid'],
            'admin_name' => $jsoncode['aname'],
            'payee_name' => isset($jsoncode['payee_name']) ? $jsoncode['payee_name'] : $jsoncode['payee'],
            'bank_number' => $jsoncode['bank_number'],
            'bank_name' => isset($jsoncode['bank_name']) ? $jsoncode['bank_name'] : $jsoncode['open_bank'],
            'remark' => $jsoncode['remark'],
            'type_title' => !empty($type) ? $type['title'] : '',
            'create_time' => strtotime(date("Y-m-d H:i:s")),
            'create_date' => date("Y-m-d"),
            'type' => 1
        ];

        if (isset($jsoncode['finish_date']) && !empty($jsoncode['finish_date'])) {
            $be['finish_date'] = $jsoncode['finish_date'];
        }

        $bx_de[] = $be;

        $expenseService = new ExpenseService();
        $re = $expenseService->addAll($bx_de);
        return $re;
    }

    public function baoxiao($jsoncode, $in_approve)
    {
        $bx_de = array();

        foreach ($jsoncode['pay_type'] as $k => $v) {
            //$type = Db::name("type")->where(['id' => $jsoncode['pay_type'][$k]])->find();
            $type = Db::name("type_pay")
                ->alias('tp')
                ->where(['tp.id' => $jsoncode['pay_type'][$k]])
                ->join('type t', 't.id = tp.type_id', 'LEFT')
                ->find();

            $be = [
                'approve_id' => $in_approve['id'],
                'pay_type' => $jsoncode['pay_type'][$k],
                'pay_time' => $jsoncode['pay_time'][$k],
                'pay_amount' => $jsoncode['pay_amount'][$k],
                'pay_text' => $jsoncode['pay_text'][$k],
                'pay_file' => $jsoncode['detailsList'][$k],
                'is_fapiao' => $jsoncode['is_fapiao'],
                'store' => $jsoncode['store'],
                'did' => $jsoncode['did'],
                'admin_id' => $jsoncode['aid'],
                'admin_name' => $jsoncode['aname'],
                'payee_name' => $jsoncode['payee_name'],
                'bank_number' => $jsoncode['bank_number'],
                'bank_name' => $jsoncode['bank_name'],
                'remark' => $jsoncode['remark'],
                'type_title' => !empty($type) ? $type['title'] : '',
                'create_time' => strtotime(date("Y-m-d H:i:s")),
                'create_date' => date("Y-m-d"),
                'type' => 2
            ];

            if (isset($jsoncode['finish_date']) && !empty($jsoncode['finish_date'])) {
                $be['finish_date'] = $jsoncode['finish_date'];
            }
            $bx_de[] = $be;

        }
        $expenseService = new ExpenseService();
        $re = $expenseService->addAll($bx_de);
        return $re;
    }

    public function other($jsoncode, $in_approve)
    {
        if (!isset($jsoncode['pay_type']) || empty($jsoncode['pay_type'])) {
            return true;
        }

        if ($jsoncode['type'] == 46) {
            //赔付
            $jsoncode['pay_type'] = 97;
            $jsoncode['pay_amount'] = $jsoncode['s_pay_amount'];
            $jsoncode['o_amount'] = $jsoncode['t_pay_amount'];
            $jsoncode['store'] = $jsoncode['did'];
            $jsoncode['bank_name'] = $jsoncode['open_bank'];
            $jsoncode['pay_time'] = $jsoncode['start_time'];
        } elseif ($jsoncode['type'] == 50) {
            $jsoncode['pay_time'] = $jsoncode['detail_time'];
        } elseif ($jsoncode['type'] == 57) {
            $jsoncode['pay_time'] = $jsoncode['start_time'];
        } elseif ($jsoncode['type'] == 40) {
            $jsoncode['did'] = $jsoncode['application_dep_ids'];
            $jsoncode['store'] = $jsoncode['application_dep_ids'];
        } elseif ($jsoncode['type'] == 54) {
            $jsoncode['pay_amount'] = $jsoncode['ref_amount'];
        } elseif ($jsoncode['type'] == 41) {
            $jsoncode['store'] = $jsoncode['department_type'];
            $jsoncode['did'] = $jsoncode['department_type'];
            $jsoncode['pay_amount'] = $jsoncode['salary'];
            $jsoncode['payee_name'] = $jsoncode['l_salary'];
            $jsoncode['bank_name'] = $jsoncode['open_bank'];
            $jsoncode['aname'] = Db::name("admin")->where(['id' => $jsoncode['aid']])->value('name');
        } elseif ($jsoncode['type'] == 53) {
            $jsoncode['store'] = $jsoncode['did'];
        }

        $type = Db::name("type_pay")
            ->alias('tp')
            ->where(['tp.id' => $jsoncode['pay_type']])
            ->join('type t', 't.id = tp.type_id', 'LEFT')
            ->find();

        $be = [
            'approve_id' => $in_approve['id'],
            'pay_type' => $jsoncode['pay_type'],
            'pay_time' => isset($jsoncode['pay_time']) ? $jsoncode['pay_time'] : date("Y-m-d"),
            'pay_amount' => $jsoncode['pay_amount'],
            'o_amount' => isset($jsoncode['o_amount']) ? $jsoncode['o_amount'] : 0,
            'pay_file' => isset($jsoncode['file_ids']) ? $jsoncode['file_ids'] : '',
            'is_fapiao' => isset($jsoncode['is_fapiao']) ? $jsoncode['is_fapiao'] : '否',
            'store' => $jsoncode['store'],
            'did' => $jsoncode['did'],
            'admin_id' => $jsoncode['aid'],
            'admin_name' => $jsoncode['aname'],
            'payee_name' => isset($jsoncode['payee_name']) ? $jsoncode['payee_name'] : '',
            'bank_number' => isset($jsoncode['bank_number']) ? $jsoncode['bank_number'] : '',
            'bank_name' => isset($jsoncode['bank_name']) ? $jsoncode['bank_name'] : '',
            'remark' => isset($jsoncode['remark']) ? $jsoncode['remark'] : '',
            'type_title' => !empty($type) ? $type['title'] : '',
            'create_time' => strtotime(date("Y-m-d H:i:s")),
            'create_date' => date("Y-m-d"),
            'type' => 3
        ];

        if (isset($jsoncode['finish_date']) && !empty($jsoncode['finish_date'])) {
            $be['finish_date'] = $jsoncode['finish_date'];
        }

        $bx_de[] = $be;

        $expenseService = new ExpenseService();
        $re = $expenseService->addAll($bx_de);
        return $re;
    }

    public function ajax($url, $data, $type = 'POST')
    {
        // 创建CURL句柄
        $ch = curl_init();
        // 设置请求的URL地址
        curl_setopt($ch, CURLOPT_URL, $url);
        // 设置请求头信息
        curl_setopt($ch, CURLOPT_HTTPHEADER, array(
            'Content-Type: application/json; charset=utf-8'
        ));
        // 设置请求方法
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, $type);
        // 设置传递的数据
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        // 设置返回数据不直接输出
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);    // 信任任何证
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
        // 执行请求并获取响应数据
        curl_setopt($ch, CURLOPT_HTTP_VERSION, CURL_HTTP_VERSION_1_0);
        $response = curl_exec($ch);
        // 关闭CURL句柄
        curl_close($ch);
        // 输出响应数据
        if (is_bool($response)) {
            var_dump(curl_error($ch));
            exit();
        }
        return $response;
    }


}