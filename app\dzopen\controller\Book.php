<?php

namespace app\dzopen\controller;

class book
{
    public function startbook()
    {
        $a = ['code' => 200, "msg" => null, "data" => ["order_id" => "12345678"]];
        return json_encode($a);
    }

    public function cancelbook()
    {
        $a = ['code' => 200, "msg" => null, "data" => ["order_id" => "12421849"]];
        return json_encode($a);
    }

    public function bookresultnotify()
    {
        $a = ['code' => 200, "msg" => null, "data" => ["order_id" => "12421849"]];
        return json_encode($a);
    }

}