{extend name="../../base/view/common/base" /}
<!-- 主体 -->
{block name="body"}
<div class="p-3">
	<div class="gg-form-bar border-t border-x" style="background-color:#FAFAFA">
		<h3>数据还原</h3>
	</div>
	<table cellspacing="0" cellpadding="0" border="0" class="layui-table layui-table-form">
		<tr>
			<th style=" text-align: center; font-weight: 800;"><span>文件名称</span></th>
			<th style=" text-align: center; font-weight: 800;"><span>文件格式</span></th>
			<th style=" text-align: center; font-weight: 800;"><span>分隔符</span></th>
			<th style=" text-align: center; font-weight: 800;"><span>文件总大小</span></th>
			<th style=" text-align: center; font-weight: 800;"><span>分卷总数</span></th>
			<th style=" text-align: center; font-weight: 800; width:222px"><span>操作</span></th>
		</tr>
		{empty name="list"}
		<tr>
			<td colspan="6" align="center">暂无备份数据</td>
		</tr>
		{/empty}
		{volist name="list" id="vo" key="k"}
		<tr style="background-color: #fafafa;">
			<td><strong>备份时间：{$vo.time}</strong>{if $vo.timespan == $lock_time}<span
					style="color:red; margin-left:20px;">该备份不是完整备份，请删除重新备份</span>{/if}</td>
			<td align="center"><span>.sql</span></td>
			<td align="center"><span>{$vo.data.compress}</span></td>
			<td align="center"><span>{:format_bytes($vo.data.size)}</span></td>
			<td align="center"><span>{$vo.data.part}</span></td>
			<td align="center">
				<div class="layui-btn-group" data-time='{$vo.timespan}'>
					{if $vo.timespan == $lock_time}
					<a class="layui-btn layui-btn-xs layui-btn-danger" lay-event="reset">清除不完整的备份</a>
					{else/}
					<a class="layui-btn layui-btn-xs layui-btn-normal" lay-event="import">数据还原</a>
					<a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">删除备份</a>
					{/if}
				</div>
			</td>
		</tr>
		{for start="0" end="$vo.data.part" step="1"}
		<tr>
			<td colspan="5">
				{:date("Ymd",$vo.timespan)}{$vo.data.compress}{:date("His",$vo.timespan)}{$vo.data.compress}{$i+1}.sql
			</td>
			<td align="center">
				<a class="layui-btn layui-btn-xs"
					href='/home/<USER>/downfile?time={$vo.data.time}&part={$i+1}'>下载备份(分卷{$i+1})</a>
			</td>
		</tr>
		{/for}
		{/volist}
	</table>
</div>

{/block}
<!-- /主体 -->

<!-- 脚本 -->
{block name="script"}
<script>
	function gouguInit() {
		function importData(data) {
			if (data.code == 0) {
				console.log(data.msg);
				layer.closeAll();
				layer.msg(data.msg, { time: 200000 });
				if ($.isPlainObject(data.data)) {
					$.ajax({
						url: "/home/<USER>/import",
						type: 'get',
						data: { "part": data.data.part, "start": data.data.start, time: data.data.time },
						success: function (res) {
							importData(res);
						}
					})
				} else {
					layer.msg(data.msg);
					window.onbeforeunload = function () { return null; }
				}
			} else {
				layer.msg(data.msg);
			}
		}
		//监听行工具事件
		$('[lay-event="import"]').on('click', function () {
			var time = $(this).parent().data('time');
			layer.confirm('确认要还原该备份吗?', {
				icon: 3,
				title: '提示'
			}, function (index) {
				layer.msg("数据还原中...", { time: 200000 });
				$.ajax({
					url: "/home/<USER>/import?time=" + time,
					type: 'post',
					success: function (res) {
						importData(res);
					}
				})
				window.onbeforeunload = function () { return "正在还原数据库，请不要关闭！" }
				layer.close(index);
			});
			return false;
		})

		$('[lay-event="del"]').on('click', function () {
			var time = $(this).parent().data('time');
			layer.confirm('确认要删除该备份吗?', {
				icon: 3,
				title: '提示'
			}, function (index) {
				$.ajax({
					url: "/home/<USER>/del",
					data: { 'time': time },
					success: function (res) {
						layer.msg(res.msg);
						if (res.code == 0) {
							setTimeout(function () {
								location.reload();
							}, 2000)
						}
					}
				})
				layer.close(index);
			});
			return false;
		})

		$('[lay-event="reset"]').on('click', function () {
			var time = $(this).parent().data('time');
			layer.confirm('确认要清除该备份吗?', {
				icon: 3,
				title: '提示'
			}, function (index) {
				$.ajax({
					url: "/home/<USER>/del",
					data: { 'time': time, 'lock': 1 },
					success: function (res) {
						layer.msg(res.msg);
						if (res.code == 0) {
							setTimeout(function () {
								location.reload();
							}, 2000)
						}
					}
				})
				layer.close(index);
			});
			return false;
		})
	}
</script>
{/block}
<!-- /脚本 -->