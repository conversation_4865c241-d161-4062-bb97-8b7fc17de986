{extend name="../../base/view/common/base" /}
<!-- 主体 -->
{block name="body"}
<form class="layui-form p-4">
	<h3 class="pb-3">新增知识文章</h3>
    <table class="layui-table">
		<tr>
			<td class="layui-td-gray">文章标题<font>*</font></td>
			<td colspan="3"><input type="text" name="title" lay-verify="required" lay-reqText="请输入文章标题" autocomplete="off" placeholder="请输入文章标题"
				class="layui-input"></td>
				<td class="layui-td-gray">文章分类<font>*</font></td>
			<td>
			  <select name="cate_id" lay-verify="required" lay-reqText="请选择分类">
				<option value="">请选择分类</option>
				{volist name=":set_recursion(article_cate())" id="v"}
				<option value="{$v.id}">{$v.title}</option>
				{/volist}
			  </select>
			</td>
		</tr>
		<tr>
			<td class="layui-td-gray">关键字<font>*</font></td>
			<td colspan="3">
			  <input type="text" id="keyword_name" name="keyword_names" autocomplete="off" lay-verify="required" lay-reqText="请选择关键字" placeholder="请选择关键字"
				class="layui-input" readonly>
			  <input type="hidden" id="keyword_id" name="keyword_ids" autocomplete="off">
			</td>
			<td class="layui-td-gray">属性</td>
			<td>
			  <select name="type">
				<option value="">请选择属性</option>
				<option value="1">精华</option>
				<option value="2">热门</option>
				<option value="3">推荐</option>
			  </select>
			</td>
		</tr>
		<tr>
			<td class="layui-td-gray">共享设置</td>
			<td>
				<input type="radio" name="is_share" lay-filter="type" value="0" title="私有">
				<input type="radio" name="is_share" lay-filter="type" value="1" title="所有人" checked>
				<input type="radio" name="is_share" lay-filter="type" value="2" title="部门">
				<input type="radio" name="is_share" lay-filter="type" value="3" title="部分人员">
			</td>
			<td class="layui-td-gray" style="width:50px">状态</td>
			<td>
			  <input type="radio" name="status" value="1" title="正常" checked>
			  <input type="radio" name="status" value="0" title="下架">
			</td>
			<td class="layui-td-gray">排序</td>
			<td>
			  <input type="text" name="sort" value="0" placeholder="请输入排序，数字" autocomplete="off" class="layui-input">
			</td>
		</tr>
		<tr id="depament" style="display:none">
			<td class="layui-td-gray">共享部门<font>*</font></td>
			<td colspan="5">
				<input type="text" name="share_depaments" value="" placeholder="请选择共享部门" readonly class="layui-input picker-depaments">
				<input type="hidden" name="share_dids" value="">
			</td>
		</tr>
		<tr id="person" style="display:none">
			<td class="layui-td-gray">共享人员<font>*</font></td>
			<td colspan="5">
				<input type="text" name="share_names" value="" placeholder="请选择共享人员" readonly class="layui-input picker-more">
				<input type="hidden" name="share_uids" value="">
			</td>
		</tr>
		<tr>
			<td class="layui-td-gray-2">
				<div class="layui-input-inline">关联附件</div>
				<div class="layui-input-inline">
					<button type="button" class="layui-btn layui-btn-xs" id="uploadBtn"><i class="layui-icon"></i></button>
				</div>
			</td>
			<td colspan="5" style="line-height:inherit">
				<div class="layui-row" id="fileBox">
					<input type="hidden" data-type="file" name="file_ids" value="">
				</div>
			</td>
		</tr> 
      <tr>
        <td class="layui-td-gray" style="vertical-align:top;">文章摘要</td>
        <td colspan="3">
          <textarea name="desc" placeholder="请输入摘要，不能超过200个字" class="layui-textarea"></textarea>
        </td>
        <td class="layui-td-gray" style="vertical-align:top;">缩略图</td>
        <td>
            <div class="layui-upload">
              <button type="button" class="layui-btn layui-btn-normal layui-btn-sm" id="test1">缩略图(尺寸：640x360)</button>
              <div class="layui-upload-list" id="demo1" style="width: 120px; height:66px; overflow: hidden;">
                <img src="" style="max-width: 100%; height:66px;" />
                <input type="hidden" name="thumb" value="">
              </div>
            </div>
        </td>
      </tr>
      <tr>
        <td class="layui-td-gray" style="vertical-align:top;">文章内容<font>*</font></td>
        <td colspan="5">
          <textarea name="content" placeholder="请输入内容" class="layui-textarea" id="container" style="border:0;padding:0"></textarea>
        </td>
      </tr>
    </table>
    <div class="pt-3">
      <button class="layui-btn layui-btn-normal" lay-submit="" lay-filter="webform">立即提交</button>
      <button type="reset" class="layui-btn layui-btn-primary">重置</button>
    </div>
</form>
{/block}
<!-- /主体 -->

<!-- 脚本 -->
{block name="script"}
<script>
const moduleInit = ['tool','employeepicker','oaTool','tagpicker','tinymce'];
function gouguInit() {
	var form = layui.form,tool=layui.tool,oaTool = layui.oaTool, tagspicker = layui.tagpicker, upload = layui.upload;
	
	//选择共享类型
	form.on('radio(type)', function (data) {
		if(data.value==2){
			$('#person').hide();
			$('#depament').show();
		}
		else if(data.value==3){
			$('#person').show();
			$('#depament').hide();
		}
		else{
			$('#person').hide();
			$('#depament').hide();
		}
	});
	
    //编辑器初始化
	var editor = layui.tinymce;
	var edit = editor.render({
		selector: "#container",
		menubar:false,
		images_upload_url: '/api/index/upload/sourse/tinymce',//图片上传接口
		height: 500
	});

	//相关附件上传
	oaTool.addFile();	
	
	//tag选择
    var tags = new tagspicker({
      'url': "/api/index/get_keyword_cate",
      'target': 'keyword_name',
      'tag_ids': 'keyword_id',
      'tag_tags': 'keyword_name',
      'height': 500,
      'isDiy': 1
    });

    //封面上传
    var uploadInst = upload.render({
		elem: '#test1'
		, url: "/api/index/upload"
		, done: function (res) {
			layer.msg(res.msg);
			if (res.code == 0) {
				//上传成功
				$('#demo1 input').attr('value', res.data.id);
				$('#demo1 img').attr('src', res.data.filepath);
			}
		}
    });

    //监听提交
    form.on('submit(webform)', function (data) {
      data.field.content = tinyMCE.editors['container'].getContent();
		if (data.field.content == '') {
			layer.msg('请先完善文章的内容');
			return false;
		}
		if(data.field.is_share == 0 || data.field.is_share == 1){
			data.field.share_dids = '';
			data.field.share_uids = '';
		}
		else if(data.field.is_share == 2){
			data.field.share_uids = '';
			if(data.field.share_dids==''){
				layer.msg('请先选择共享的部门');
				return false;
			}
		}
		else if(data.field.is_share == 3){
			data.field.share_dids = '';
			if(data.field.share_uids==''){
				layer.msg('请先选择共享的员工');
				return false;
			}
		}	
      let callback = function (e) {
			layer.msg(e.msg);
			if (e.code == 0) {
				tool.sideClose(1000);
			}
		}
		tool.post("/article/index/add", data.field, callback);
		return false;
    });

  }

</script>
{/block}
<!-- /脚本 -->