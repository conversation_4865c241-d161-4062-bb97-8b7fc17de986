<?php

namespace app\user\controller;

use app\base\BaseController;
use app\message\service\QiWeiService;
use app\store\controller\Storebusiness;
use app\user\model\DepartmentChange;
use app\user\service\AdminService;
use app\user\service\AdminViewService;
use app\user\service\TrainService;
use think\App;
use think\facade\Db;
use think\facade\Session;
use think\facade\View;
use app\user\model\AdminView as AdminViewList;

class Adminview extends BaseController
{

    protected $status_str;
    protected $equ_background;

    public function __construct(App $app)
    {
        parent::__construct($app);
        $adminService = new AdminService();
        $param = $adminService->getParam();
        $this->status_str = $param['status_str'];
        $this->equ_background = $param['equ_background'];

    }

    public function index()
    {
        if (request()->isAjax()) {
            $param = get_params();
            $where = array();

            if (!empty($param['type'])) {
                $where[] = ['type', '=', $param['type']];
            }
            if (isset($param['position_id']) && !empty($param['position_id'])) {
                $where[] = ['position_id', '=', $param['position_id']];
            }
            if (isset($param['date_range']) && !empty($param['date_range'])) {
                $date_range = explode(" - ", $param['date_range']);
                $where[] = ['create_time', 'between', [strtotime($date_range[0]), strtotime('+1 day', strtotime($date_range[1]))]];
            }
            if (!empty($param['keywords'])) {
                $where[] = ['name|mobile|dname', 'like', '%' . $param['keywords'] . '%'];
            }
            //else{
//                if (isset($param['ismobile']) && !empty($param['ismobile'])){
//                    $where[] = ['name|mobile', 'like', '%%'];
//                }
            //}

            if (!empty($param['dstatus'])) {
                $where[] = ['dstatus', '=', $param['dstatus']];
            }

            if (isset($param['status']) && $param['status'] != '' ) {
                $where[] = ['status', '=', $param['status']];
            }

            if (isset($param['is_onboard']) && $param['is_onboard'] != '' ) {
                $where[] = ['is_onboard', '=', $param['is_onboard']];
            }

            if (isset($param['is_train']) && $param['is_train'] != '' ) {
                $where[] = ['is_train', '=', $param['is_train']];
            }

            if (isset($param['sex']) && $param['sex'] != '' ) {
                $where[] = ['sex', '=', $param['sex']];
            }

            $where[] = ['status', '>', -1];

            $rows = empty($param['limit']) ? get_config('app.page_size') : $param['limit'];

            $data = AdminViewList::where($where)
                ->order('id desc')
                ->paginate($rows, false, ['query' => $param])
                ->each(function ($item, $key) {
                    $item->status_str = $this->status_str[$item->status];

                    $item->sex = $item->sex == 1 ? '男' : '女';

                    //$item->is_train_color = $item->is_train == 0 ? 'red' : ($item->is_train == 1 ? 'blue' : 'green');

                    $is_train_str = '';

                    switch ($item->is_train){
                        case 0: $is_train_str = "未培训";break;
                        case 1: $is_train_str = "已门店培训";break;
                        case 2: $is_train_str = "已线上培训";break;
                    }
                    $item->is_train_str = $is_train_str;

                    $item->is_receive_color = $item->is_receive == 0 ? 'red' : 'green';
                    $item->is_receive = $item->is_receive == 0 ? '衣物未领' : '衣物已领';
                    $item->is_payment_color = $item->is_payment == 0 ? 'red' : 'green';
                    $item->is_payment = $item->is_payment == 0 ? '未缴费' : '已缴费';

                    $item->is_onboard_color = $item->is_onboard == 0 ? 'red' : ($item->is_onboard == 1 ? 'blue' : 'green');
                    $item->is_onboard = $item->is_onboard == 0 ? '未入职' : ($item->is_onboard == 1 ? '确认入职' : '已入职');

                    $item->status_list = $this->status_str;

                    $check_status_str = '';
                    $check_status_id = -1;
                    $approve = Db::name("approve")->where(['associated_id' => $item->id ])->order("id desc")->find();
                    if (!empty($approve)){
                        switch ($approve['check_status']){
                            case 0:$check_status_str='待审批';break;
                            case 1:$check_status_str='审批中';break;
                            case 2:$check_status_str='已通过';break;
                            case 3:$check_status_str='已拒绝';break;
                            case 4:$check_status_str='已撤销';break;
                        }
                        $check_status_id = $approve['id'];
                    }
                    $item->check_status_str = $check_status_str;
                    $item->check_status_id = $check_status_id;

                    if (!empty($item->create_id)){
                        $item->create_name = Db::name("admin")->where(['id' => $item->create_id ])->value("name");
                    }else{
                        $item->create_name = "";
                    }

                    return $item;
                });

            return table_assign(0, '', $data);
        } else {
            $is_md = false;
            $dep = Db::name("Department")->where([['id', 'in', explode(",", $this->did)], ['remark', '=', '门店']])->find();
            if ($dep) {
                $is_md = true;
            }

            View::assign('is_md', $is_md);
            View::assign('department', getmd(['门店']));
            View::assign('status_str', $this->status_str);
            return view();
        }
    }

    public function add()
    {
        $param = get_params();
        $id = isset($param['id']) ? $param['id'] : 0;
        if (request()->isAjax()) {

            //查询手机 和姓名是否存在
            if (isset($param['mobile']) && !empty($param['mobile'])) {
//                $admin_data = Db::name("admin")
//                    ->where(['mobile' => $param['mobile']])
//                    ->find();
//                if (!empty($admin_data) && $admin_data['status'] == 1) {
//                    return to_assign('1', "手机或姓名已在员工列表，请重新输入");
//                }
                //查看下这个人 是否黑名单
                $admin = Db::name("admin")
                    ->where(['mobile' => $param['mobile']])
                    ->whereOr(['name' => $param['name']])
                    ->find();

                if (!empty($admin) && ($admin['is_black'] == 1) && $param['flag'] == 0){
                    return to_assign('1', "人员已拉入黑名单，请联系人事",$this->uid);
                }

                $admin_view_data = Db::name("admin_view")
                    ->where([['mobile', '=', $param['mobile']], ['id', '<>', $id], ['did', '=', $param['did']], ['status', '>', -1]])
                    ->find();
                if (!empty($admin_view_data)) {
                    return to_assign('1', "手机或姓名已在人才中心中，请重新输入");
                }
            }

            if (empty($param['valid_start_date'])){
                unset($param['valid_start_date']);
            }

            if (empty($param['valid_end_date'])){
                unset($param['valid_end_date']);
            }

            if (isset($param['did'])) {
                if (empty($param['did'])) {
                    $param['dname'] = '总部';
                } else {
                    $dep_data = Db::name("department")
                        ->where(['id' => $param['did']])->find();
                    $param['dname'] = $dep_data['title'];
                }
            }

            unset($param['file']);
            unset($param['flag']);

            if (empty($param['valid_start_date'])){
                unset($param['valid_start_date']);
            }

            if (empty($param['valid_end_date'])){
                unset($param['valid_end_date']);
            }

            $param['edu_major'] = !empty($param['edu_major']) ? $param['edu_major'] : '无';

            if($this->uid !== 2448 || $this->uid !== 880){
                if ($param['sex'] == 1 && $param['age'] >= 40 ){
                    return to_assign('1', "年龄超过限制，请联系人事");
                }else if ($param['sex'] == 2 && $param['age'] >= 45 ){
                    return to_assign('1', "年龄超过限制，请联系人事");
                }
            }


            if ($id > 0) {
                $re = Db::name("admin_view")->where(['id' => $id])->update($param);
            } else {
                $param['create_time'] = time();
                $param['create_id'] = $this->uid;
                $re = Db::name("admin_view")->insertGetId($param);
            }

            return to_assign();
        } else {

            if ($id > 0) {
                $detail = Db::name('AdminView')->where(['id' => $id])->find();
                $detail['fileArray'] = Db::name('File')->where('id', 'in', $detail['file_ids'])->select();
                View::assign('detail', $detail);
            }

            View::assign('did', isset($detail) ? $detail['did'] : 0);
            View::assign('id', $id);
            View::assign('status_str', $this->status_str);
            View::assign('edu_background', $this->equ_background);
            return view();
        }
    }

    public function view()
    {
        $id = get_params('id');

        $detail = Db::name("admin_view")->where(['id' => $id])->find();
        $detail['status_str'] = $this->status_str[$detail['status']];
        $detail['create_time'] = !empty($detail['create_time']) ? date("Y-m-d", $detail['create_time']) : '--';
        $detail['fileArray'] = Db::name('File')->where('id', 'in', $detail['file_ids'])->select();

        $avr = Db::name("admin_view_record")->where(['aview_id' => $id])->order('create_time desc')->select()->toArray();
        $is_salary = false;
        $salary_arr = array();
        foreach ($avr as $k => $v) {
            $avr[$k]['create_time'] = date("Y-m-d H:i:s", $v['create_time']);
            $avr[$k]['fileArray'] = Db::name('File')->where('id', 'in', $v['file_ids'])->select();
            if ($v['s_str'] == '薪资确认') {
                if ($v['status'] == 0) {
                    $is_salary = true;
                }
                $salary_arr[] = $avr[$k];
                unset($avr[$k]);
            }
        }

        if ($detail['is_onboard'] == 2){
            $QiWeiService = new QiWeiService();
            $qiwei_qrcode = $QiWeiService->get_join_qrcode();
            View::assign('join_qrcode', isset($qiwei_qrcode->join_qrcode) ? $qiwei_qrcode->join_qrcode : '' );
        }

        View::assign('detail', $detail);
        View::assign('salary_arr', $salary_arr);
        View::assign('is_salary', $is_salary);
        View::assign('avr', $avr);
        View::assign('edu_background', $this->equ_background);
        add_log('view', get_params('id'));
        return view();

    }

    public function edit()
    {
        $param = get_params();

        $s_str = [
            ['面试通过', '面试不通过', '合同签订完毕'],
            ['正在培训', '培训完成', '未培训'],
            ['已领取', '未领取'],
            ['已缴费', '未缴费'],
            ['确认入职', '已入职']
        ];

        $msg = [
            'create_time' => date('Y-m-d H:i:s'),
            'action_id' => '1253',
            'title' => '',
            'from_uid' => $this->uid
        ];

        if (request()->isAjax()) {
            $u_data = array();
            $admin_view = Db::name('Admin_view')->where(['id' => $param['id']])->find();

            $DepartmentChange = new DepartmentChange();
            $dep_fenguan = $DepartmentChange->get_dep_fenguan(0, $this->uid);

            if (empty($dep_fenguan)){
                return to_assign(1, '门店信息有误');
            }

            $dids = array();
            foreach ($dep_fenguan as $k => $v){
                $dids[] = $v['id'];
            }

            $dep = Db::name("Department")->where([['id', 'in', $dids], ['remark', '=', '门店']])->find();
            if ($dep && $admin_view) {
                $re_in = in_array($admin_view['did'], $dids);
                if (!$re_in) {
                    return to_assign(1, '不能编辑非本门店的信息');
                }
            }

            if (isset($param['status'])) {
                if ($param['status'] == '面试通过') {
                    $u_data['status'] = 2;
                } elseif ($param['status'] == '面试不通过') {
                    $u_data['status'] = 1;
                } elseif ($param['status'] == '合同签订完毕') {
                    $msg = [
                        'create_time' => date('Y-m-d H:i:s'),
                        'action_id' => '1253',
                        'title' => '',
                        'from_uid' => $this->uid
                    ];
                    $msg['action_id'] = $param['id'];
                    sendMessage('884,883', 11, $msg, "『人才中心』通知【{$admin_view['dname']} {$admin_view['name']}】 已签订合同，等待进行培训");
                    $u_data['status'] = 3;
                }

                if ($param['status'] == '确认入职') {
                    //人事
                    $this->message($param['id'], '人事', 11, '确认入职，可签订合同');
                    $u_data['is_onboard'] = 1;
                } elseif ($param['status'] == '已入职') {

                    $admin = Db::name("admin")->where([
                        'username' => [$admin_view['name']]
                    ])->whereOr(['mobile' => $admin_view['mobile']])
                        ->find();

                    if (!empty($admin)){
                        $o_msg = "";

                        if ($admin['mobile'] == $admin_view['mobile'] ) $o_msg .= "，手机号已存在";
                        if ($admin['name'] == $admin_view['name'] ) $o_msg .= "，姓名已存在";
                        if ($admin['status'] == 2 ) $o_msg .= "，员工已离职";

                        return to_assign(1, "员工已存在企业员工内 $o_msg");
                    }

                    if (empty($admin_view['aid'])){
                        $admin_view_service =  new AdminViewService();

                        $aid = $admin_view_service->addUser($admin_view);
                        $u_data['aid'] = $aid;
                    }
                    $u_data['is_onboard'] = 2;
                }
            }

            if (!isset($param['file_ids']) || empty($param['file_ids'])) {
                $param['file_ids'] = '';
            }

            if (isset($param['s_str'])){
                $u_data = ['is_train' => $param['is_train']];
                $param['status'] = $param['s_str'];
            }

            Db::name('Admin_view_record')->insertGetId([
                'aview_id' => $param['id'],
                'aid' => $this->uid,
                'aname' => $this->name,
                'create_time' => time(),
                'status' => 0,
                'remark' => isset($param['remark']) ? $param['remark'] : '',
                'salary' => isset($param['salary']) ? $param['salary'] : 0,
                'type' => isset($param['type']) ? $param['type'] : 0,
                'file_ids' => isset($param['file_ids']) ? $param['file_ids'] : '',
                's_str' => isset($param['status']) ? $param['status'] : ''
            ]);

            if (!empty($u_data)) {
                Db::name('Admin_view')->where(['id' => $param['id']])->update($u_data);
            }
            return to_assign();
        } else {
            $type = $param['type'];

            View::assign('id', $param['id']);
            View::assign('status', $type);
            if ($type == -1) {
                View::assign('status_str', array());
            } else {
                View::assign('status_str', $s_str[$type]);
            }

            return view();
        }

    }

    public function interview()
    {
        if (request()->isAjax()) {

            $param = get_params();

            $adminview_i = $param['adminview_i'];
            unset($param['adminview_i']);
            $data = $param;

            $data['create_time'] = date("Y-m-d H:i:s");
            $data['update_time'] = date("Y-m-d H:i:s");

            $interview_id = Db::name("interview")->insertGetId($data);

            $interview_s = array();
            foreach ($adminview_i as $k => $v){
                $in_data = [
                    'interview_id' => $interview_id,
                    'title' => isset($v['title']) ? $v['title'] : '',
                    'keynote' => $v['keynote'],
                    'item' => isset($v['item']) ? $v['item'] : '',
                    'index' => $k,

                    'score' => isset($v['score']) ? $v['score'] : null,
                    'comment' => isset($v['comment']) ? $v['comment'] : '',
                    'type' => isset($v['score']) ? 1 : 2,

                    'create_time' => date("Y-m-d H:i:s"),
                    'update_time' => date("Y-m-d H:i:s")
                ];

                $interview_s[] = $in_data;

            }

            Db::name("interview_s")->insertAll($interview_s);


            Db::name("admin_view")->where(['id' => $data['a_view_id']])->update([
                'is_interview' => 1,
                'interview_status' => $param['check_status'],
            ]);

            return to_assign(0, "操作成功", ['is_mobile' => Session::get("is_mobile")]);
        } else {

            $param = get_params();
            $id = isset($param['id']) ? $param['id'] : 0;

            $admin_view = Db::name("admin_view")->where(['id' => $id])->find();

            View::assign('admin_name', $this->name);
            View::assign('admin_id', $this->uid);
            View::assign('admin_store', $this->storeid);

            View::assign('admin_view', $admin_view);

            View::assign('sdate', date("Y-m-d"));
            return view();
        }
    }

    public function interview_record()
    {
        if (request()->isAjax()) {

            $param = get_params();

            $interview = Db::name("interview")
                ->where(['a_view_id' => $param['id']])->select()->toArray();

            $interview_s = array();

            foreach ($interview as $k => $v){
                $interview_s[] = Db::name("interview_s")
                    ->where(['interview_id' => $v['id']])->select()->toArray();
            }

        } else {

            $param = get_params();
            $id = isset($param['id']) ? $param['id'] : 0;

            $interview = Db::name("interview")
                ->where(['a_view_id' => $id])
                ->order("create_time desc")
                ->select()->toArray();

            $headers = array();
            foreach ($interview as $k => $v){
                $dep = Db::name("department")->where(['id' => $v['did']])->find();
                $interview[$k]['dname'] = $dep['title'];
                $interview[$k]['interview_s'] = Db::name("interview_s")
                    ->where(['interview_id' => $v['id']])->select()->toArray();
            }
            View::assign("interview" , $interview);
            return view();
        }
    }

    public function examine()
    {

        if (request()->isAjax()) {

            $param = get_params();

            $param['score0'] = implode(",",$param['score0']);
            $param['score1'] = implode(",",$param['score1']);
            $param['score2'] = implode(",",$param['score2']);
            $param['score3'] = implode(",",$param['score3']);
            //unset($param['score']);

            $param['create_time'] = date("Y-m-d H:i:s");
            $param['update_time'] = date("Y-m-d H:i:s");

            $in_score = 0;
//            foreach ($score as $k => $v){
//                $param["score{$k}"] = $v;
//                if ($v >= 60){
//                    $in_score++;
//                }
//            }

            //前台 线上成绩通过即可
            //技师 线上成绩通过 线下实操 推拿分数必须合格 再有三门合格
            $param['check_status'] = 1;
//            if ($param['courseware_status'] !== '已完成'){
//                $param['check_status'] = 2;
//            }else{
//                if ($param['position'] !== '前台' && ($score[0] < 60 || $in_score < 4) ){
//                    $param['check_status'] = 2;
//                }
//            }
            unset($param['position'] );

            Db::name("admin_view")->where(['id' => $param['a_view_id']])->update(
                ['is_examine' => $param['check_status']]
            );
            Db::name("examine")->insertGetId($param);

            return to_assign(0, "操作成功", ['is_mobile' => Session::get("is_mobile")]);
        } else {

            $param = get_params();
            $id = isset($param['id']) ? $param['id'] : 0;

            $admin_view = Db::name("admin_view")->where(['id' => $id])->find();
            $interview = Db::name("interview")->where(['a_view_id' => $id])->find();

            //同步外部人员魔学院信息
            $AdminViewService = new AdminViewService();
            $admin_view = $AdminViewService->mobile_moxueyuan_out($admin_view);

            View::assign('admin_name', $this->name);
            View::assign('admin_id', $this->uid);
            View::assign('admin_store', $this->storeid);

            View::assign('admin_view', $admin_view);
            View::assign('interview', $interview);

            View::assign('sdate', date("Y-m-d"));
            return view();
        }
    }

    public function examine_record()

    {
        if (request()->isAjax()) {

            $param = get_params();

            $interview = Db::name("interview")
                ->where(['a_view_id' => $param['id']])->select()->toArray();

            $interview_s = array();

            foreach ($interview as $k => $v){
                $interview_s[] = Db::name("interview_s")
                    ->where(['interview_id' => $v['id']])->select()->toArray();
            }

        } else {

            $param = get_params();
            $id = isset($param['id']) ? $param['id'] : 0;

            $examine = Db::name("examine")
                ->where(['a_view_id' => $id])
                ->order("create_time desc")
                ->select()->toArray();

            foreach ($examine as $k => $v){
                $dep = Db::name("department")->where(['id' => $v['did']])->find();
                $examine[$k]['dname'] = $dep['title'];
                $examine[$k]['score0'] = explode(",", $examine[$k]['score0']);
                $examine[$k]['average0'] = number_format(array_sum($examine[$k]['score0']) / count($examine[$k]['score0']), 2);
                $examine[$k]['score1'] = explode(",", $examine[$k]['score1']);
                $examine[$k]['average1'] = number_format(array_sum($examine[$k]['score1']) / count($examine[$k]['score1']), 2);
                $examine[$k]['score2'] = explode(",", $examine[$k]['score2']);
                $examine[$k]['average2'] = number_format(array_sum($examine[$k]['score2']) / count($examine[$k]['score2']), 2);
                $examine[$k]['score3'] = explode(",", $examine[$k]['score3']);
                $examine[$k]['average3'] = number_format(array_sum($examine[$k]['score3']) / count($examine[$k]['score3']), 2);

                $file_array = Db::name('File')->order('create_time desc')->where([['id', 'in', $examine[$k]['file_ids']]])->select()->toArray();
                $examine[$k]['file_array'] = $file_array;
            }

            View::assign("examine" , $examine);
            return view();
        }
    }

    //培训记录
    public function train_node()
    {
        $param = get_params();
        $admin_view = Db::name('Admin_view')->where(['id' => $param['id']])->find();
        if ($param['status'] == '未培训') {
            $u_data['is_train'] = 0;
        } elseif ($param['status'] == '正在培训') {
            $this->message($param['id'], '运营', 11, '正在培训，可进行拍照及平台上架');
            $u_data['is_train'] = 1;
        } elseif ($param['status'] == '培训完成') {
            $this->message($param['id'], '运营', 11, '培训完成，可进行拍照及平台上架');
            $u_data['is_train'] = 2;
        }
        $this->insert_record($param, $u_data);
        return to_assign();
    }

    //缴费记录
    public function payment_node()
    {
        $param = get_params();

        if ($param['status'] == '未缴费') {
            $u_data['is_payment'] = 0;
        } elseif ($param['status'] == '已缴费') {
            $u_data['is_payment'] = 1;
        }
        $this->insert_record($param, $u_data);
        return to_assign();
    }

    //衣物领取记录
    public function cloth_node()
    {
        $param = get_params();
        if ($param['status'] == '未领取') {
            $u_data['is_receive'] = 0;
        } elseif ($param['status'] == '已领取') {
            $u_data['is_receive'] = 1;
        }
        $this->insert_record($param, $u_data);
        return to_assign();
    }

    //薪资节点
    public function salary_node()
    {
        $param = get_params();
        if (isset($param['salary'])) {
            $admin_view = Db::name('Admin_view')->where(['id' => $param['id']])->find();
            $msg['action_id'] = $param['id'];
            sendMessage(874, 11, $msg, "『人才中心』通知【{$admin_view['dname']} {$admin_view['name']}】 薪资待确认");
            $re = Db::name('Admin_view_record')->where(['aview_id' => $param['id'], 'type' => 10])->update(['status' => -1]);
            $u_data['status'] = 4;
        }
        $this->insert_record($param, $u_data);
        return to_assign();
    }

    public function message($admin_id, $dname, $template, $tit)
    {
        $msg = [
            'create_time' => date('Y-m-d H:i:s'),
            'action_id' => '1253',
            'title' => '',
            'from_uid' => $this->uid
        ];
        $admin_view = Db::name('Admin_view')->where(['id' => $admin_id])->find();
        if ($admin_view['dstatus'] == 2) {
            $department = Db::name("Department")->where([['title', 'like', "%$dname%"]])->find();
            if (!empty($department) && !empty($department['leader_id'])) {
                $msg['action_id'] = $admin_id;
                sendMessage($department['leader_id'], $template, $msg, "『人才中心』通知【{$admin_view['dname']} {$admin_view['name']}】 $tit");
            }
        }
    }

    //记录
    public function insert_record($param, $u_data)
    {
        Db::name('Admin_view_record')->insertGetId([
            'aview_id' => $param['id'],
            'aid' => $this->uid,
            'aname' => $this->name,
            'create_time' => time(),
            'status' => 0,
            'remark' => isset($param['remark']) ? $param['remark'] : '',
            'salary' => isset($param['salary']) ? $param['salary'] : 0,
            'type' => $param['type'],
            'file_ids' => $param['file_ids'],
            's_str' => $param['status']
        ]);
        Db::name('Admin_view')->where(['id' => $param['id']])->update($u_data);

        return true;
    }

    public function salary_confirm()
    {
        $param = get_params();
        $id = $param['id'];

        $admin_view_record = Db::name("admin_view_record")->where(['aview_id' => $id, 'type' => 10, 'status' => 0])->find();
        Db::name("admin_view_record")->where(['aview_id' => $id, 'type' => 10, 'status' => 0])->update(['status' => $param['status']]);
        $s_str = '';
        if ($param['status'] == 1) {
            Db::name("admin_view")->where(['id' => $id])->update([
                'status' => 5, 'salary' => $admin_view_record['salary']
            ]);
            $s_str = '确认了薪资';
        } else {
            $s_str = '拒绝了薪资';
        }

        //人事
        $msg = [
            'create_time' => date('Y-m-d H:i:s'),
            'action_id' => '1253',
            'title' => '',
            'from_uid' => $this->uid
        ];
        $admin_view = Db::name('Admin_view')->where(['id' => $param['id']])->find();
        $department = Db::name("Department")->where([['title', 'like', "%人事%"]])->find();
        if (!empty($department) && !empty($department['leader_id'])) {
            if ($department['leader_id'] != $this->uid) {
                $msg['action_id'] = $param['id'];
                sendMessage($department['leader_id'], 11, $msg, "『人才中心』通知【{$admin_view['dname']} {$admin_view['name']}】 {$this->name}{$s_str}");
            }
        }

        Db::name('Admin_view_record')->insertGetId([
            'aview_id' => $id,
            'aid' => $this->uid,
            'aname' => $this->name,
            'create_time' => time(),
            'status' => 0,
            'remark' => '',
            'salary' => 0,
            'type' => '-1',
            'file_ids' => '',
            's_str' => $s_str
        ]);
        return to_assign(0, "操作成功", ['is_mobile' => Session::get("is_mobile")]);

    }

    public function delete()
    {
        $param = get_params();
        Db::name("admin_view")->where(['id' => $param['id']])->update(['status' => -1]);
    }



    public function search()
    {
        return view();
    }

}


