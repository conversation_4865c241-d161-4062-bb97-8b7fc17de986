<?php
/**
 * @copyright Copyright (c) 2021 仲正堂工作室
 * @license https://opensource.org/licenses/GPL-3.0
 * @link https://www.gougucms.com
 */
declare (strict_types=1);

namespace app\api\controller;

use app\api\BaseController;
use app\api\service\IndexService;
use app\finance\service\ExpenseService;
use app\message\service\QiWeiService;
use app\oa\service\ApproveService;
use app\store\service\StoreSalaryService;
use app\store\service\StoreService;
use app\user\model\DepartmentChange;
use app\user\service\AdminViewService;
use think\App;
use think\facade\Db;
use think\facade\View;
use think\facade\Log;

class Index extends BaseController
{
    protected $index_service;

    public function __construct(App $app)
    {
        parent::__construct($app);
        $this->index_service = new IndexService();
    }

    //上传文件
    public function upload()
    {
        $param = get_params();
        //var_dump($param);exit;
        $sourse = 'file';
        if (isset($param['sourse'])) {
            $sourse = $param['sourse'];
        }
        if ($sourse == 'file' || $sourse == 'tinymce') {
            if (request()->file('file')) {
                $file = request()->file('file');
            } else {
                return to_assign(1, '没有选择上传文件');
            }
        } else {
            if (request()->file('editormd-image-file')) {
                $file = request()->file('editormd-image-file');
            } else {
                return to_assign(1, '没有选择上传文件');
            }
        }
        // 获取上传文件的hash散列值
        $sha1 = $file->hash('sha1');
        $md5 = $file->hash('md5');
        $rule = [
            'image' => 'jpg,png,jpeg,gif',
            'doc' => 'txt,doc,docx,ppt,pptx,xls,xlsx,pdf',
            'file' => 'zip,gz,7z,rar,tar',
            'video' => 'mpg,mp4,mpeg,avi,wmv,mov,flv,m4v',
        ];
        $fileExt = $rule['image'] . ',' . $rule['doc'] . ',' . $rule['file'] . ',' . $rule['video'];
        //1M=1024*1024=1048576字节
        $fileSize = 100 * 1024 * 1024;
        if (isset($param['type']) && $param['type']) {
            $fileExt = $rule[$param['type']];
        }
        if (isset($param['size']) && $param['size']) {
            $fileSize = $param['size'];
        }
        $validate = \think\facade\Validate::rule([
            'image' => 'require|fileSize:' . $fileSize . '|fileExt:' . $fileExt,
        ]);
        $file_check['image'] = $file;
        if (!$validate->check($file_check)) {
            return to_assign(1, $validate->getError());
        }
        // 日期前綴
        $dataPath = date('Ym');
        $use = 'thumb';
        $filename = \think\facade\Filesystem::disk('public')->putFile($dataPath, $file, function () use ($md5) {
            return $md5;
        });
        if ($filename) {
            //写入到附件表
            $data = [];
            $path = get_config('filesystem.disks.public.url');
            $data['filepath'] = $path . '/' . $filename;
            $data['name'] = $file->getOriginalName();
            $data['mimetype'] = $file->getOriginalMime();
            $data['fileext'] = $file->extension();
            $data['filesize'] = $file->getSize();
            $data['filename'] = $filename;
            $data['sha1'] = $sha1;
            $data['md5'] = $md5;
            $data['module'] = \think\facade\App::initialize()->http->getName();
            $data['action'] = app('request')->action();
            $data['uploadip'] = app('request')->ip();
            $data['create_time'] = time();
            $data['user_id'] = $this->uid;
            if ($data['module'] = 'admin') {
                //通过后台上传的文件直接审核通过
                $data['status'] = 1;
                $data['admin_id'] = $data['user_id'];
                $data['audit_time'] = time();
            }
            $data['use'] = request()->has('use') ? request()->param('use') : $use; //附件用处
            $res['id'] = Db::name('file')->insertGetId($data);
            $res['filepath'] = $data['filepath'];
            $res['name'] = $data['name'];
            $res['filename'] = $data['filename'];
            $res['filesize'] = $data['filesize'];
            $res['fileext'] = $data['fileext'];
            add_log('upload', $data['user_id'], $data, '文件');
            if ($sourse == 'editormd') {
                //editormd编辑器上传返回
                return json(['success' => 1, 'message' => '上传成功', 'url' => $data['filepath']]);
            } else if ($sourse == 'tinymce') {
                //tinymce编辑器上传返回
                return json(['success' => 1, 'message' => '上传成功', 'location' => $data['filepath']]);
            } else {
                //普通上传返回
                return to_assign(0, '上传成功', $res);
            }
        } else {
            return to_assign(1, '上传失败，请重试');
        }
    }

    public function getFiles()
    {
        $param = get_params();
        $file_ids = Db::name("file")->where([['id', 'in', $param['file_ids']]])->field("filepath,id")->select()->toArray();
        return to_assign(0, '', $file_ids);
    }

    //清空缓存
    public function cache_clear()
    {
        \think\facade\Cache::clear();
        return to_assign(0, '系统缓存已清空');
    }

    // 测试邮件发送
    public function email_test()
    {
        $sender = get_params('email');
        //检查是否邮箱格式
        if (!is_email($sender)) {
            return to_assign(1, '测试邮箱码格式有误');
        }
        $email_config = \think\facade\Db::name('config')->where('name', 'email')->find();
        $config = unserialize($email_config['content']);
        $content = $config['template'];
        //所有项目必须填写
        if (empty($config['smtp']) || empty($config['smtp_port']) || empty($config['smtp_user']) || empty($config['smtp_pwd'])) {
            return to_assign(1, '请完善邮件配置信息');
        }

        $send = send_email($sender, '测试邮件', $content);
        if ($send) {
            return to_assign(0, '邮件发送成功');
        } else {
            return to_assign(1, '邮件发送失败');
        }
    }

    //获取部门
    public function get_department()
    {
        $keyword = get_params('keyword');
        if ($keyword == '后勤') {
            $department = getmd('后勤');
        } else if ($keyword == '门店') {
            $department = getmd('门店');
        } else {
            $department = get_department();
        }

        return to_assign(0, '', $department);
    }

    //获取部门树形节点列表
    public function get_department_tree()
    {
        $department = get_department();
        $list = get_tree($department, 0, 2);
        $data['trees'] = $list;
        return json($data);
    }

    //获取部门树形节点列表2
    public function get_department_select()
    {
        $keyword = get_params('keyword');
        $selected = [];
        if (!empty($keyword)) {
            $selected = explode(",", $keyword);
        }
        $department = get_department();
        $list = get_select_tree($department, 0, 0, $selected);
        return to_assign(0, '', $list);
    }

    //获取子部门所有员工
    public function get_employee($did = 0)
    {
        $did = get_params('did');
        if ($did == 1) {
            $department = [$did];
        } else {
            $department = get_department_son($did);
        }
        $employee = Db::name('admin')
            ->field('a.id,a.did,a.position_id,a.mobile,a.name,a.nickname,a.sex,a.status,a.thumb,a.username,d.title as department')
            ->alias('a')
            ->join('Department d', 'a.did = d.id')
            ->where(['a.status' => 1])
            ->where('a.id', ">", 1)
            ->where('a.did', "in", $department)
            ->select()->toArray();

        $employee = [];

        foreach ($department as $k => $v) {
            $em = Db::name('admin')
                ->field('id,did,position_id,mobile,name,nickname,sex,status,thumb,username,wx_account')
                ->where(['status' => 1])
                ->where('id', ">", 1)
                ->where('did', "find in set", $v)
                ->select()->toArray();
            if (!empty($em)) {
                $employee = array_merge($employee, $em);
            }
        }

        return to_assign(0, '', $employee);
    }

    public function get_employee_sdate($did = 0)
    {
        $did = get_params('did');
        $sdate = get_params('sdate');

        $date_range = explode(" - ", $sdate);

        $employee = Db::name('store_business')
            ->field('b.aid as id,b.aname as name')
            ->alias('b')
            ->where([
                'b.status' => 1,
                'b.did' => $did,
                ['b.sdate', 'between', [$date_range[0], $date_range[1]]]
            ])
            ->group("aid")
            ->select()->toArray();

        return to_assign(0, '', $employee);
    }

    //获取所有员工
    public function get_personnel()
    {
        $param = get_params();
        $where[] = ['a.status', '=', 1];
        $where[] = ['a.id', '>', 1];
        if (!empty($param['keywords'])) {
            $where[] = ['a.name', 'like', '%' . $param['keywords'] . '%'];
        }
        if (!empty($param['ids'])) {
            $where[] = ['a.id', 'notin', $param['ids']];
        }
        $rows = empty($param['limit']) ? get_config('app.page_size') : $param['limit'];
        $list = Db::name('admin')
            ->field('a.id,a.did,a.position_id,a.mobile,a.name,a.nickname,a.sex,a.status,a.thumb,a.username,d.title as department')
            ->alias('a')
            ->join('Department d', 'a.did = d.id')
            ->where($where)
            ->order('a.id desc')
            ->paginate($rows, false, ['query' => $param]);
        return table_assign(0, '', $list);
    }

    //获取部门所有员工
    public function get_employee_select()
    {
        $keyword = get_params('keyword');
        $selected = [];
        if (!empty($keyword)) {
            $selected = explode(",", $keyword);
        }
        $employee = Db::name('admin')
            ->field('id as value,name')
            ->where(['status' => 1])
            ->select()->toArray();

        foreach ($employee as $k => &$v) {
            $v['selected'] = '';
            if (in_array($v['value'], $selected)) {
                $v['selected'] = 'selected';
            }
        }
        return to_assign(0, '', $employee);
    }

    //获取部门所有员工
    public function get_store_employee_select()
    {
        $keyword = get_params('keywords');
        $where = array(['status', '=', 1]);
        $list = array();
        if (!empty($keyword)) {
            $where[] = ['name', 'like', '%' . $keyword . '%'];
        } else {
            return to_assign(0, '', $list);
        }

        $employee = Db::name('admin')
            ->field('id,name,did')
            ->where($where)
            ->limit(10)->select();


        foreach ($employee as $k => $v) {
            $did = explode(",", $v['did']);
            $dname = '';
            foreach ($did as $kk => $vv) {
                $dep = Db::name("Department")->where(['id' => $vv, 'remark' => '门店'])->find();
                if (!empty($dep)) {
                    $did = $dep['id'];
                    $dname = $dep['title'];
                }
            }

            $list[] = ['id' => $v['id'], 'title' => $v['name'], 'did' => $did, 'dname' => $dname];
        }

        return to_assign(0, '', $list);
    }

    //获取角色列表
    public function get_position()
    {
        $position = Db::name('Position')->field('id,title as name')->where([['status', '=', 1], ['id', '>', 1]])->select();
        return to_assign(0, '', $position);
    }

    public function get_positionList()
    {
        $keyword = get_params('keyword');
        $selected = [];
        if (!empty($keyword)) {
            $selected = explode(",", $keyword);
        }

        $position = Db::name('Position')->field('id as value,title as name')->where([['status', '=', 1], ['id', '>', 1]])->select()->toArray();

        foreach ($position as $k => $v) {
            if (in_array($v['value'], $selected)) {
                $position[$k]['selected'] = 'selected';
            } else {
                $position[$k]['selected'] = '';
            }
        }

        return to_assign(0, '', $position);
    }


    public function getVacationTypeList()
    {
        $keyword = get_params('keyword');
        $selected = [];
        if (!empty($keyword)) {
            $selected = explode(",", $keyword);
        }

        $position = Db::name('VacationType')->field('id as value,name')->where([['status', '=', 1]])->select()->toArray();

        foreach ($position as $k => $v) {
            if (in_array($v['value'], $selected)) {
                $position[$k]['selected'] = 'selected';
            } else {
                $position[$k]['selected'] = '';
            }
        }

        return to_assign(0, '', $position);
    }

    public function getPayTypeList()
    {
        $keyword = get_params('keyword');
        $controller = get_params('controller');
        $selected = [];
        if (!empty($keyword)) {
            $selected = explode(",", $keyword);
        }

        $position = Db::name('TypePay')->field('id as value,name')->where([['status', '=', 1], ['controller', 'in', $controller]])->select()->toArray();

        foreach ($position as $k => $v) {
            if (in_array($v['value'], $selected)) {
                $position[$k]['selected'] = 'selected';
            } else {
                $position[$k]['selected'] = '';
            }
        }

        return to_assign(0, '', $position);
    }


    //获取审核类型
    public function get_flow_cate($type = 0)
    {
        $flows = Db::name('FlowType')->where(['type' => $type, 'status' => 1])->select()->toArray();
        return to_assign(0, '', $flows);
    }

    //获取审核步骤人员
    public function get_flow_users($id = 0)
    {
        $flow = Db::name('Flow')->order("id asc")->where(['id' => $id])->find();
        $flowData = unserialize($flow['flow_list']);

        if (!empty($flowData)) {
            foreach ($flowData as $key => &$val) {
                $val['user_id_info'] = Db::name('Admin')->field('id,name,thumb')->where('id', 'in', $val['flow_uids'])->select()->toArray();

                if (!empty($val['flow_dep'])) {
                    $val['flow_dep_info'] = Db::name('department')->field('id,title')->where('id', '=', $val['flow_dep'])->find();
                }
            }
        }
        $data['copy_uids'] = $flow['copy_uids'];
        $data['copy_unames'] = '';
        if ($flow['copy_uids'] != '') {
            $copy_unames = Db::name('Admin')->where('id', 'in', $flow['copy_uids'])->column('name');
            $data['copy_unames'] = implode(',', $copy_unames);
        }
        $data['flow_data'] = $flowData;


        return to_assign(0, '', $data);
    }

    //获取审核流程节点
    public function get_flow_nodes($id = 0, $type = 1)
    {
        $flows = Db::name('FlowStep')->where(['action_id' => $id, 'type' => $type, 'delete_time' => 0])->order('sort asc')->select()->toArray();

        foreach ($flows as $key => &$val) {
            $user_id_info = Db::name('Admin')->field('id,name,thumb,mobile,status')->where('id', 'in', $val['flow_uids'])->select()->toArray();
            foreach ($user_id_info as $k => &$v) {
                $a_status = $v['status'];

                $v['check_time'] = 0;
                $v['content'] = '';
                $v['status'] = 0;
                $check_array = Db::name('FlowRecord')->where(['check_user_id' => $v['id'], 'step_id' => $val['id']])->order('check_time desc')->select()->toArray();

                if (!empty($check_array)) {
                    $checked = $check_array[0];
                    $v['check_time'] = date('Y-m-d H:i', $checked['check_time']);
                    $v['content'] = $checked['content'];
                    $v['status'] = $checked['status'];
                }

                if ($a_status == 2) {
                    $v['status'] = -1;
                }


            }

            $check_list = Db::name('FlowRecord')
                ->field('f.*,a.name,a.thumb')
                ->alias('f')
                ->join('Admin a', 'a.id = f.check_user_id', 'left')
                ->where(['f.step_id' => $val['id']])->select()->toArray();
            foreach ($check_list as $kk => &$vv) {
                $vv['check_time_str'] = date('Y-m-d H:i', $vv['check_time']);
            }

            $val['user_id_info'] = $user_id_info;
            $val['check_list'] = $check_list;
        }

        $check_status = 0;
        $approve = Db::name('approve')->where(['id' => $id])->find();
        if (!empty($approve)) {
            $check_status = $approve['check_status'];
        }

        return to_assign(0, '', $flows, $check_status);
    }

    //获取审核流程节点
    public function get_flow_record($id = 0, $type = 1)
    {
        $check_list = Db::name('FlowRecord')
            ->field('f.*,a.name,a.thumb')
            ->alias('f')
            ->join('Admin a', 'a.id = f.check_user_id', 'left')
            ->where(['f.action_id' => $id, 'f.type' => $type])
            ->order('check_time asc')
            ->select()->toArray();
        foreach ($check_list as $kk => &$vv) {
            $vv['check_time_str'] = date('Y-m-d H:i', $vv['check_time']);
        }
        return to_assign(0, '', $check_list);
    }

    //流程审核
    public function flow_check()
    {
        $param = get_params();
        $id = $param['id'];
        $type = $param['type'];
        $detail = [];
        $subject = '一个审批';
        $is_mobile = \think\facade\Session::get("is_mobile");

        if ($type == 1) {
            //日常审核
            $detail = Db::name('Approve')->where(['id' => $id])->find();
            $subject = '一个日常审批';
            $msg_title_type = $detail['type'];
        } else if ($type == 2) {
            //报销审核
            $detail = Db::name('Expense')->where(['id' => $id])->find();
            $subject = '一个报销审批';
            $msg_title_type = 22;
        } else if ($type == 3) {
            //发票审核
            $detail = Db::name('Invoice')->where(['id' => $id])->find();
            $subject = '一个发票审批';
            $msg_title_type = 23;
        } else if ($type == 4) {
            //合同审核
            $detail = Db::name('Contract')->where(['id' => $id])->find();
            $subject = '一个合同审批';
            $msg_title_type = 24;
        }
        if (empty($detail)) {
            return to_assign(1, '审批数据错误');
        }
        //当前审核节点详情
        $step = Db::name('FlowStep')->where(['action_id' => $id, 'type' => $type, 'sort' => $detail['check_step_sort'], 'delete_time' => 0])->find();

        //审核通过
        if ($param['check'] == 1) {
            $check_admin_ids = explode(",", strval($detail['check_admin_ids']));
            if (!in_array($this->uid, $check_admin_ids)) {
                return to_assign(1, '您没权限审核该审批');
            }

            //多人会签审批
            if ($step['flow_type'] == 4) {
                //查询当前会签记录数
                $check_count = Db::name('FlowRecord')->where(['action_id' => $id, 'type' => $type, 'step_id' => $step['id']])->count();
                //当前会签记应有记录数
                $flow_count = explode(',', $step['flow_uids']);
                if (($check_count + 1) >= count($flow_count)) {
                    $next_step = Db::name('FlowStep')->where(['action_id' => $id, 'type' => $type, 'sort' => ($detail['check_step_sort'] + 1), 'delete_time' => 0])->find();
                    if ($next_step) {
                        //存在下一步审核
                        if ($next_step['flow_type'] == 1) {
                            $param['check_admin_ids'] = get_department_leader($detail['admin_id']);
                        } else if ($next_step['flow_type'] == 2) {
                            $param['check_admin_ids'] = get_department_leader($detail['admin_id'], 1);
                        } else {
                            $param['check_admin_ids'] = $next_step['flow_uids'];
                        }
                        $param['check_step_sort'] = $detail['check_step_sort'] + 1;
                        $param['check_status'] = 1;
                    } else {
                        //不存在下一步审核，审核结束
                        $param['check_status'] = 2;
                        $param['check_admin_ids'] = '';
                    }
                } else {
                    $param['check_status'] = 1;
                    $param['check_admin_ids'] = $step['flow_uids'];
                }
            } else if ($step['flow_type'] == 0) {
                //自由人审批
                if ($param['check_node'] == 2) {
                    $next_step = $detail['check_step_sort'] + 1;
                    $flow_step = array(
                        'action_id' => $id,
                        'sort' => $next_step,
                        'type' => $type,
                        'flow_uids' => $param['check_admin_ids'],
                        'create_time' => time()
                    );
                    $fid = Db::name('FlowStep')->strict(false)->field(true)->insertGetId($flow_step);
                    //下一步审核步骤
//                    $param['check_admin_ids'] = $param['check_admin_ids'];
                    $param['check_step_sort'] = $next_step;
                    $param['check_status'] = 1;
                } else {
                    //不存在下一步审核，审核结束
                    $param['check_status'] = 2;
                    $param['check_admin_ids'] = '';
                }
            } else {
                $next_step = Db::name('FlowStep')->where(['action_id' => $id, 'type' => $type, 'sort' => ($detail['check_step_sort'] + 1), 'delete_time' => 0])->find();
                if ($next_step) {
                    //存在下一步审核
                    if ($next_step['flow_type'] == 1) {
                        $param['check_admin_ids'] = get_department_leader($detail['admin_id']);
                    } else if ($next_step['flow_type'] == 2) {
                        $param['check_admin_ids'] = get_department_leader($detail['admin_id'], 1);
                    } else {
                        $param['check_admin_ids'] = $next_step['flow_uids'];
                    }
                    $param['check_step_sort'] = $detail['check_step_sort'] + 1;
                    $param['check_status'] = 1;
                } else {
                    //不存在下一步审核，审核结束
                    $param['check_status'] = 2;
                    $param['check_admin_ids'] = '';
                }

                //当前审核完成的话 将之前所有的消息结束
                Db::name("message")->where([
                    'action_id' => $id, ['create_time', '<', time()]
                ])->update(['is_notice' => 1]);
            }
            if ($param['check_status'] == 1 && empty($param['check_admin_ids'])) {
                return to_assign(1, '找不到下一步的审批人，该审批流程设置有问题，请联系HR或者管理员');
            }
            //审核通过数据操作
            $param['last_admin_id'] = $this->uid;
            $param['flow_admin_ids'] = $detail['flow_admin_ids'] . $this->uid . ',';

            if ($type == 1) {
                //新店开业 指定节点触发 新建流程 26新店开业
                if ($detail['flow_id'] == 26) {
                    $this->xdky_jd($detail, $step);
                }
                $param['node_tit'] = isset($next_step['flow_title']) ? $next_step['flow_title'] : '';
                $param['finish_time'] = time();
                //日常审核
                $res = Db::name('Approve')->strict(false)->field('check_step_sort,check_status,last_admin_id,flow_admin_ids,check_admin_ids,node_tit,finish_time')->update($param);
            } else if ($type == 2) {
                //报销审核
                $res = Db::name('Expense')->strict(false)->field('check_step_sort,check_status,last_admin_id,flow_admin_ids,check_admin_ids')->update($param);
            } else if ($type == 3) {
                //发票审核
                $res = Db::name('Invoice')->strict(false)->field('check_step_sort,check_status,last_admin_id,flow_admin_ids,check_admin_ids')->update($param);
            } else if ($type == 4) {
                //合同审核
                $res = Db::name('Contract')->strict(false)->field('check_step_sort,check_status,last_admin_id,flow_admin_ids,check_admin_ids')->update($param);
            }

            if ($res !== false) {
                $checkData = array(
                    'action_id' => $id,
                    'step_id' => $step['id'],
                    'check_user_id' => $this->uid,
                    'type' => $type,
                    'check_time' => time(),
                    'status' => $param['check'],
                    'content' => $param['content'],
                    'create_time' => time(),
                    'file_ids' => isset($param['file_ids']) ? $param['file_ids'] : "",
                    'trial_eval' => isset($param['trial_eval']) ? $param['trial_eval'] : '',
                    'overall_eval' => isset($param['overall_eval']) ? $param['overall_eval'] : '',
                    'ky_date' => isset($param['ky_date']) ? $param['ky_date'] : '',
                    'flow_title' => $step['flow_title'],
                );
                $aid = Db::name('FlowRecord')->strict(false)->field(true)->insertGetId($checkData);
                add_log('check', $param['id'], $param, $subject);
                //发送消息通知
                $msg = [
                    'create_time' => date('Y-m-d H:i:s', $detail['create_time']),
                    'action_id' => $id,
                    'title' => Db::name('FlowType')->where('id', $msg_title_type)->value('title'),
                    'from_uid' => $detail['admin_id']
                ];
                if ($param['check_status'] == 1) {
                    $users = $param['check_admin_ids'];
                    sendMessage($users, ($type * 10 + 11), $msg);
                }
                if ($param['check_status'] == 2) {
                    $users = $detail['admin_id'];
                    sendMessage($users, ($type * 10 + 12), $msg);
                }
                return to_assign(0, "操作成功", ['is_mobile' => $is_mobile]);
            } else {
                return to_assign(1, '操作失败');
            }
        } else if ($param['check'] == 2) {
            $check_admin_ids = explode(",", strval($detail['check_admin_ids']));
            if (!in_array($this->uid, $check_admin_ids)) {
                return to_assign(1, '您没权限审核该审批');
            }
            //拒绝审核，数据操作
            $param['check_status'] = 3;
            $param['last_admin_id'] = $this->uid;
            $param['flow_admin_ids'] = $detail['flow_admin_ids'] . $this->uid . ',';
            $param['check_admin_ids'] = '';
            if ($step['flow_type'] == 5) {
                //获取上一步的审核信息
                $prev_step = Db::name('FlowStep')->where(['action_id' => $id, 'type' => $type, 'sort' => ($detail['check_step_sort'] - 1), 'delete_time' => 0])->find();
                if ($prev_step) {
                    //存在上一步审核
                    $param['check_step_sort'] = $prev_step['sort'];
                    $param['check_admin_ids'] = $prev_step['flow_uids'];
                    $param['check_status'] = 1;
                } else {
                    //不存在上一步审核，审核初始化步骤
                    $param['check_step_sort'] = 0;
                    $param['check_admin_ids'] = '';
                    $param['check_status'] = 0;
                }
            }
            if ($type == 1) {
                //日常审核
                $res = Db::name('Approve')->strict(false)->field('check_step_sort,check_status,last_admin_id,flow_admin_ids,check_admin_ids')->update($param);
            } else if ($type == 2) {
                //报销审核
                $res = Db::name('Expense')->strict(false)->field('check_step_sort,check_status,last_admin_id,flow_admin_ids,check_admin_ids')->update($param);
            } else if ($type == 3) {
                //发票审核
                $res = Db::name('Invoice')->strict(false)->field('check_step_sort,check_status,last_admin_id,flow_admin_ids,check_admin_ids')->update($param);
            } else if ($type == 4) {
                //合同审核
                $res = Db::name('Contract')->strict(false)->field('check_step_sort,check_status,last_admin_id,flow_admin_ids,check_admin_ids')->update($param);
            }
            if ($res !== false) {
                $checkData = array(
                    'action_id' => $id,
                    'step_id' => $step['id'],
                    'check_user_id' => $this->uid,
                    'type' => $type,
                    'check_time' => time(),
                    'status' => $param['check'],
                    'content' => $param['content'],
                    'create_time' => time()
                );
                $aid = Db::name('FlowRecord')->strict(false)->field(true)->insertGetId($checkData);
                add_log('refue', $param['id'], $param, $subject);
                //发送消息通知
                $msg = [
                    'create_time' => date('Y-m-d H:i:s', $detail['create_time']),
                    'action_id' => $detail['id'],
                    'title' => Db::name('FlowType')->where('id', $msg_title_type)->value('title'),
                    'from_uid' => $detail['admin_id']
                ];
                $users = $detail['admin_id'];
                sendMessage($users, ($type * 10 + 13), $msg);
                return to_assign(0, "操作成功", ['is_mobile' => $is_mobile]);
            } else {
                return to_assign(1, '操作失败');
            }
        } else if ($param['check'] == 3) {
            if ($detail['admin_id'] != $this->uid) {
                return to_assign(1, '你没权限操作');
            }
            //撤销审核，数据操作
            $param['check_status'] = 4;
            $param['check_admin_ids'] = '';
            $param['check_step_sort'] = 0;
            if ($type == 1) {
                //日常审核
                $res = Db::name('Approve')->strict(false)->field('check_step_sort,check_status,check_admin_ids')->update($param);
            } else if ($type == 2) {
                //报销审核
                $res = Db::name('Expense')->strict(false)->field('check_step_sort,check_status,check_admin_ids')->update($param);
            } else if ($type == 3) {
                //发票审核
                $res = Db::name('Invoice')->strict(false)->field('check_step_sort,check_status,check_admin_ids')->update($param);
            } else if ($type == 4) {
                //合同审核
                $res = Db::name('Contract')->strict(false)->field('check_step_sort,check_status,check_admin_ids')->update($param);
            }
            if ($res !== false) {
                $checkData = array(
                    'action_id' => $id,
                    'step_id' => 0,
                    'check_user_id' => $this->uid,
                    'type' => $type,
                    'check_time' => time(),
                    'status' => $param['check'],
                    'content' => $param['content'],
                    'create_time' => time()
                );
                $aid = Db::name('FlowRecord')->strict(false)->field(true)->insertGetId($checkData);
                add_log('back', $param['id'], $param, $subject);
                return to_assign(0, "操作成功", ['is_mobile' => $is_mobile]);
            } else {
                return to_assign(1, '操作失败');
            }
        }
    }

    //新店开业分支流程发起
    public function xdky_jd($detail, $step)
    {
        $param = $detail;
        $param['a_id'] = $param['id'];
        unset($param['id']);

        $param['type'] = 34;
        $param['flow_id'] = 34;
        //店长信息
        $dz_admin = Db::name("admin")->where(['id' => $detail['dzid']])->find();
        $param['admin_id'] = $dz_admin['id'];
        $param['department_id'] = $dz_admin['did'];

        $param['check_step_sort'] = 0;
        $param['flow_admin_ids'] = '';
        $param['copy_uids'] = 877; //抄送李一鸣
        $param['last_admin_id'] = 0;
        $param['create_time'] = time();
        $param['check_status'] = 0;

        $flow_name = [];
        if ($detail['node_tit'] == "房屋租赁签约") {
            $flow_name = ["办理营业执照"];
        } elseif ($detail['node_tit'] == "平面图确认") {
            $flow_name = ["新店支付首款"];
        } elseif ($detail['node_tit'] == "设计交底确认") {
            $flow_name = ["新店账号注册", "新店装修预算", "新店物料采购"];
        } elseif ($detail['node_tit'] == "装修验收") {
            $flow_name = ["新店装修决算"];
        } elseif ($detail['node_tit'] == "确认支付尾款") {
            $flow_name = ["新店⽀付尾款"];
        }


        foreach ($flow_name as $k => $v) {
            if ($v == "新店物料采购") {
                $param['type'] = 29;
                $param['flow_id'] = 28;
            }

            $flow_list = Db::name('Flow')->where('name', $v)->value('flow_list');
            if (empty($flow_list)) continue;
            $flow = unserialize($flow_list);

            $param['node_tit'] = $v;
            $flow_uids = $flow[0]['flow_uids'];
            $param['check_admin_ids'] = $flow_uids;
            $param['fz_name'] = $v;

            $aid = Db::name('Approve')->strict(false)->field(true)->insertGetId($param);

            foreach ($flow as $kk => $vv) {
                $flow_data = [
                    'action_id' => $aid,
                    'flow_type' => 3,
                    'flow_uids' => $vv['flow_uids'],
                    'sort' => $kk,
                    'create_time' => time(),
                ];

                if ($flow_data['flow_uids'] == 0) {
                    $flow_data['flow_uids'] = $detail['dzid'];
                }

                $res = Db::name('FlowStep')->strict(false)->field(true)->insertGetId($flow_data);

                //发送消息通知
                $msg = [
                    'from_uid' => $this->uid,
                    'title' => Db::name('FlowType')->where('id', $param['flow_id'])->value('title'),
                    'action_id' => $aid
                ];
                sendMessage($flow_data['flow_uids'], 21, $msg);
            }

            $checkData = array(
                'action_id' => $aid,
                'check_user_id' => $dz_admin['id'],
                'content' => '提交申请',
                'check_time' => time(),
                'create_time' => time()
            );
            $record_id = Db::name('FlowRecord')->strict(false)->field(true)->insertGetId($checkData);
        }

    }

    //获取关键字
    public function get_keyword_cate()
    {
        $keyword = Db::name('Keywords')->where(['status' => 1])->order('id desc')->select()->toArray();
        return to_assign(0, '', $keyword);
    }

    //读取报销类型
    function get_expense_cate()
    {
        $cate = get_expense_cate();
        return to_assign(0, '', $cate);
    }

    //读取费用类型
    function get_cost_cate()
    {
        $cate = get_cost_cate();
        return to_assign(0, '', $cate);
    }

    //读取印章类型
    function get_seal_cate()
    {
        $cate = get_seal_cate();
        return to_assign(0, '', $cate);
    }

    //读取车辆类型
    function get_car_cate()
    {
        $cate = get_car_cate();
        return to_assign(0, '', $cate);
    }

    //读取企业主体
    function get_subject()
    {
        $subject = get_subject();
        return to_assign(0, '', $subject);
    }

    //读取行业类型
    function get_industry()
    {
        $industry = get_industry();
        return to_assign(0, '', $industry);
    }

    //读取服务类型
    function get_services()
    {
        $services = get_services();
        return to_assign(0, '', $services);
    }

    //获取工作类型列表
    public function get_work_cate()
    {
        $cate = get_work_cate();
        return to_assign(0, '', $cate);
    }

    public function wtfk_check()
    {
        $param = get_params();
        $is_mobile = \think\facade\Session::get("is_mobile");
        $id = $param['id'];
        $status = $param['check'];

        //当前审核单据
        $in_approve = Db::name("Approve")->where(['id' => $id])->find(); //当前审核
        $msg_title_type = $in_approve['type'];

        //当前审核节点详情
        $step = Db::name('FlowStep')->where(['action_id' => $id, 'sort' => $in_approve['check_step_sort'], 'delete_time' => 0])->find();
        //下一节点
        $next_step = Db::name('FlowStep')->where(['action_id' => $id, 'sort' => ($in_approve['check_step_sort'] + 1), 'delete_time' => 0])->find();

        if (empty($in_approve)) {
            return to_assign(1, "审批不存在");
        }
        //找到店长方案确认 节点
        $step_dz = Db::name('FlowStep')->where(['action_id' => $id, 'flow_name' => '店长方案确认', 'delete_time' => 0])->find();

        //查询我上一步节点
        $prev_step = Db::name("FlowStep")->where([
            ['action_id', '=', $in_approve['id']],
            ['delete_time', '=', 0],
            ['sort', '<', $step_dz['sort']]
        ])->order('sort asc')->select()->toArray();
        $prev_step = array_pop($prev_step);

        //消息推送内容
        $title = '';
        $to_users = '';

        /***
         * 可以处理 直接发送给店长
         */
        if ($status == 9 && $in_approve['check_step_sort'] != 5) {
            //添加店长节点 让店长进行确认
            $approve_up = [
                'check_step_sort' => $step_dz['sort'],
                'check_admin_ids' => $in_approve['admin_id'],
                'flow_admin_ids' => $in_approve['flow_admin_ids'] . ',' . $this->uid,
                'check_status' => 1,
                'node_tit' => '店长方案确认',
            ];
            Db::name("Approve")->where(['id' => $id])->update($approve_up);

            //修改店长确认节点 节点操作记录
            Db::name("FlowStep")->where(['id' => $step_dz['id']])->update(['flow_uids' => $in_approve['admin_id']]);
            //删除中间省略的节点
            Db::name("FlowStep")->where([
                ['action_id', '=', $id],
                ['sort', '>', $in_approve['check_step_sort']],
                ['sort', '<', $step_dz['sort']],
            ])->update(['delete_time' => time()]);

            //添加操作记录
            $checkData = array(
                'action_id' => $id,
                'step_id' => $step['id'],
                'check_user_id' => $this->uid,
                'type' => 1,
                'check_time' => time(),
                'status' => 1,
                'content' => $param['content'],
                'create_time' => time(),
                'file_ids' => isset($param['file_ids']) ? $param['file_ids'] : "",
                'sort' => $step_dz['sort'],
                'flow_title' => $step_dz['flow_name']
            );
            Db::name('FlowRecord')->strict(false)->field(true)->insertGetId($checkData);


            //发送消息通知 给下一节点人
            $before_admin = Db::name("Admin")->where(['id' => $in_approve['check_admin_ids']])->find();
            $to_users = $in_approve['admin_id'];
            $title = "{$before_admin['name']}处理了您提交的『问题反馈申请』，请及时查看处理方案";
        }

        /***
         * 店长确认节点
         */
        if ($in_approve['check_step_sort'] == 5) {

            if ($status == 9) { //店长确认后 将问题添加到问题反馈

                //不存在下一步审核，审核结束
                $approve_up = [
                    'check_step_sort' => $in_approve['check_step_sort'] + 1,
                    'check_status' => 2,
                    'check_admin_ids' => '',
                    'flow_admin_ids' => $in_approve['flow_admin_ids'] . ',' . $this->uid,
                    'node_tit' => '',
                ];
                Db::name("Approve")->where(['id' => $id])->update($approve_up);

                //添加操作记录
                $checkData = array(
                    'action_id' => $id,
                    'step_id' => $step['id'],
                    'check_user_id' => $this->uid,
                    'type' => 1,
                    'check_time' => time(),
                    'status' => 1,
                    'content' => $param['content'],
                    'create_time' => time(),
                    'file_ids' => isset($param['file_ids']) ? $param['file_ids'] : "",
                    'sort' => $step['sort'],
                    'flow_title' => $step['flow_name']
                );
                Db::name('FlowRecord')->strict(false)->field(true)->insertGetId($checkData);

                //上一条记录
                $prev_record = Db::name('FlowRecord')->where([
                    'action_id' => $id,
                    'step_id' => $prev_step['id'],
                    'delete_time' => 0
                ])->order('id desc')->find();
                $problem_data = [
                    'create_time' => $in_approve['create_time'],
                    'finish_time' => time(),
                    'aid' => $id,

                    'remark' => $in_approve['remark'],
                    'p_files' => $in_approve['file_ids'],
                    'p_cont' => $in_approve['content'],

                    's_files' => !empty($prev_record['file_ids']) ? $prev_record['file_ids'] : '',
                    's_cont' => !empty($prev_record['content']) ? $prev_record['content'] : '',

                    'mdid' => $in_approve['mdid'],
                    'mdname' => $in_approve['mdname'],
                    'dzid' => $in_approve['dzid'],
                    'dzname' => $in_approve['dzname'],
                ];
                Db::name('Problem')->insertGetId($problem_data);
                return to_assign(0, "操作成功", ['is_mobile' => $is_mobile]);

            } else if ($status == 2) { //驳回操作

                //还原删除的节点
                Db::name("FlowStep")->where([
                    ['action_id', '=', $id],
                    ['sort', '>', $prev_step['sort']],
                    ['sort', '<', $step_dz['sort']],
                ])->update(['delete_time' => 0]);

                //添加操作记录
                $checkData = array(
                    'action_id' => $id,
                    'step_id' => $step['id'],
                    'check_user_id' => $this->uid,
                    'type' => 1,
                    'check_time' => time(),
                    'status' => 2,
                    'content' => $param['content'],
                    'create_time' => time(),
                    'file_ids' => isset($param['file_ids']) ? $param['file_ids'] : "",
                    'sort' => $step['sort'],
                    'flow_title' => $step['flow_name']
                );
                Db::name('FlowRecord')->strict(false)->field(true)->insertGetId($checkData);

                //修改审核申请
                $approve_up = [
                    'check_step_sort' => $prev_step['sort'],
                    'check_admin_ids' => $prev_step['flow_uids'],
                    'flow_admin_ids' => $in_approve['flow_admin_ids'] . ',' . $this->uid,
                    'check_status' => 1,
                    'node_tit' => !empty($prev_step['flow_name']) ? $prev_step['flow_name'] : '',
                ];
                Db::name("Approve")->where(['id' => $id])->update($approve_up);

                $to_users = $prev_step['flow_uids'];
                $create_date = date("Y-m-d H:i:s", $in_approve['create_time']);
                $now_date = date("Y-m-d H:i:s", time());
                $title = "您在{$create_date}提交的『问题反馈方案』已于{$now_date}被驳回拒绝。";

            }
        }

        /***
         * 向上级汇报
         */
        if ($in_approve['check_step_sort'] != 5) {
            if ($status == 1 || $status == 8) { //正常向下流转
                $check_status = 4;

                //获取部门负责人
                $zrbm_id = $param['zrbm_id'];
                $zr_dep = Db::name("Department")->where(['id' => $zrbm_id])->find();
                $zr_admins = array();
                if (!empty($zr_dep)) {
                    if (empty($zr_dep['leader_id'])) { //没有部门负责人
                        $map = array();
                        $map[] = ['', 'exp', Db::raw("FIND_IN_SET('{$zrbm_id}',did)")];
                        $map[] = ['status', '=', 1];
                        $zr_admin = Db::name("Admin")->where($map)->select();

                        if (count($zr_admin) > 1) { //部门有好几个人 就要指定部门负责人
                            return to_assign(1, "当前部门无负责人，请联系HR进行添加");
                        } else {
                            foreach ($zr_admin as $k => $v) {
                                $zr_admins[] = $v['id'];
                            }
                        }
                    } else {
                        $zr_admins[] = $zr_dep['leader_id'];
                    }
                }
                $zr_admins = implode(',', $zr_admins);
                //分配部门
                if ($status == 8) {
                    Db::name("FlowStep")->where(['id' => $next_step])->update(['flow_uids' => $zr_admins]);

                    Db::name("Approve_s")->where(['id' => $id])->update(['zrbm_id' => $param['zrbm_id']]);
                    //
                    $next_step['flow_uids'] = $zr_admins;
                    $param['content'] = "将申请分配给了${zr_dep['title']}进行处理";
                    $check_status = 1;
                }

                //修改审核申请
                $approve_up = [
                    'check_step_sort' => $in_approve['check_step_sort'] + 1,
                    'check_admin_ids' => $next_step['flow_uids'],
                    'flow_admin_ids' => $in_approve['flow_admin_ids'] . ',' . $this->uid,
                    'node_tit' => !empty($next_step['flow_name']) ? $next_step['flow_name'] : '',
                ];
                //添加操作记录
                $checkData = array(
                    'action_id' => $id,
                    'step_id' => $step['id'],
                    'check_user_id' => $this->uid,
                    'type' => 1,
                    'check_time' => time(),
                    'status' => $check_status,
                    'content' => $param['content'],
                    'create_time' => time(),
                    'file_ids' => isset($param['file_ids']) ? $param['file_ids'] : "",
                    'sort' => $step['sort'],
                    'flow_title' => $step['flow_name'],
                    'zrbm_id' => !empty($param['zrbm_id']) ? $param['zrbm_id'] : 0,
                );
                //选择了部门
                if (!empty($param['zrbm_id']) && $in_approve['check_step_sort'] < 1) {
                    $approve_up['check_admin_ids'] = "{$approve_up['check_admin_ids']},{$zr_admins}";
                    Db::name('FlowStep')->where(['id' => $next_step['id']])->update([
                        'flow_uids' => $approve_up['check_admin_ids']
                    ]);
                }

                Db::name("Approve")->where(['id' => $id])->update($approve_up);
                Db::name('FlowRecord')->strict(false)->field(true)->insertGetId($checkData);

                $to_users = $approve_up['check_admin_ids'];
                $title = "{$in_approve['dzname']}提交的『问题反馈申请』已移交到您，请及时处理。";

            } elseif ($status == 2) { //驳回
                //查询我上一步节点
                $prev_step = Db::name("FlowStep")->where([
                    ['action_id', '=', $in_approve['id']],
                    ['delete_time', '=', 0],
                    ['sort', '=', $in_approve['check_step_sort'] - 1]
                ])->find();
                //添加操作记录
                $checkData = array(
                    'action_id' => $id,
                    'step_id' => $step['id'],
                    'check_user_id' => $this->uid,
                    'type' => 1,
                    'check_time' => time(),
                    'status' => 2,
                    'content' => $param['content'],
                    'create_time' => time(),
                    'file_ids' => isset($param['file_ids']) ? $param['file_ids'] : "",
                    'sort' => $step['sort'],
                    'flow_title' => $step['flow_name']
                );
                Db::name('FlowRecord')->strict(false)->field(true)->insertGetId($checkData);

                //修改审核申请
                $approve_up = [
                    'check_step_sort' => $prev_step['sort'],
                    'check_admin_ids' => $prev_step['flow_uids'],
                    'flow_admin_ids' => $in_approve['flow_admin_ids'] . ',' . $this->uid,
                    'check_status' => 1,
                    'node_tit' => !empty($prev_step['flow_name']) ? $prev_step['flow_name'] : '',
                ];
                Db::name("Approve")->where(['id' => $id])->update($approve_up);

                $to_users = $prev_step['flow_uids'];
                $now_date = date("Y-m-d H:i:s", time());
                $title = "您对于{$in_approve['dzname']}提交的『问题反馈』处理，已于{$now_date}被驳回拒绝。";

            }
        }

        add_log('check', $param['id'], $param, "问题反馈审批");
        $msg = [
            'create_time' => date('Y-m-d H:i:s', $in_approve['create_time']),
            'action_id' => $id,
            'title' => Db::name('FlowType')->where('id', $in_approve['flow_id'])->value('title'),
            'from_uid' => $to_users,
        ];
        sendMessage($to_users, 21, $msg, $title);

        return to_assign(0, "操作成功", ['is_mobile' => $is_mobile]);

    }

    //根据员工 获取所在门店id
    public function get_store_to_uid()
    {
        $param = get_params();
        $admin = Db::name("admin")->where(['id' => $param['id']])->find();

        if (empty($admin) || empty($admin['did'])) {
            return to_assign(0, '', array());
        }

        $department = Db::name("department")->order("remark desc")->where([['id', 'in', $admin['did']]])->select()->toArray();
        return to_assign(0, '', $department);
    }

    public function transtore_check()
    {
        $param = get_params();

        // 打印param
        Log::info('=== 进入日志 transtore_check param ===');
        Log::info($param);
        Log::info('=== 进入日志 transtore_check param 2 ===');

        $is_mobile = \think\facade\Session::get("is_mobile");
        $id = $param['id'];
        $status = $param['check'];

        $to_users = 0;
        $title = '';
        //当前审核单据
        $in_approve = Db::name("Approve")->where(['id' => $id])->find(); //当前审核

        $flow_type_title = Db::name("FlowType")->where(['id' => $in_approve['type']])->value('title'); //当前审核
        //当前审核节点详情
        $step = Db::name('FlowStep')->where(['action_id' => $id, 'sort' => $in_approve['check_step_sort'], 'delete_time' => 0])->find();
        $in_admin = Db::name("admin")->where(['id' => $in_approve['admin_id']])->value('name');
        $applicant_admin = Db::name("admin")->where(['id' => $in_approve['aid']])->find();

        $approve_up = [
            'flow_admin_ids' => !empty($in_approve['flow_admin_ids']) ? "{$in_approve['flow_admin_ids']},{$this->uid}" : "{$this->uid}",
            'node_tit' => !empty($prev_step['flow_title']) ? $prev_step['flow_title'] : ''
        ];
        $copy_flag = false;
        //审核完成添加调店记录
        $jsoncode = unserialize($in_approve['jsoncode']);

        // 如果是审核通过
        if ($status == 1) {
            //下一节点
            //$next_step = Db::name('FlowStep')->where(['action_id' => $id, 'sort' => ($in_approve['check_step_sort'] + 1), 'delete_time' => 0])->find();

            //当前节点的审核人
            $in_flowstep = Db::name('FlowStep')->where(['action_id' => $id, 'sort' => $in_approve['check_step_sort'], 'delete_time' => 0])->find();

            if (!in_array($this->uid, explode(",", $in_flowstep['flow_uids']))) {
                return to_assign(1, "您不是当前节点的审批人，请重新刷新页面");
            }

            $next_step = $this->next_step($id, $in_approve['check_step_sort']);

            //店长 助理店长
            $copy_position = [12, 18];
            if (empty($next_step)) { //没有下一个节点 审核结束
                //修改审核申请
                $approve_up['check_step_sort'] = $in_approve['check_step_sort'] + 1;
                $approve_up['check_admin_ids'] = '';
                $approve_up['check_status'] = 2;
                $approve_up['node_tit'] = '--';
                $approve_up['finish_time'] = time();
                $jsoncode['finish_date'] = date("Y-m-d H:i:s");
                $to_users = $in_approve['admin_id'];
                $title = "您提交的『{$flow_type_title}申请』已审批通过。";

                $re = false;
                if ($in_approve['type'] == 37 || $in_approve['type'] == 38 || $in_approve['type'] == 26) { //调店  晋升 转正
                    $position_id = isset($jsoncode['select']) ? $jsoncode['select'] : 0;
                    $in_position_id = explode(",", $applicant_admin['position_id']);
                    if (in_array($position_id, $copy_position) || !empty(array_intersect($copy_position, $in_position_id))) {
                        $copy_flag = true;
                    }
                }

                if ($in_approve['type'] == 37) { //调店
                    $re = $this->index_service->transtore($jsoncode);
                } else if ($in_approve['type'] == 19) { //离职
                    $re = $this->index_service->lizhi($jsoncode);
                } else if ($in_approve['type'] == 20) { //转岗
                    $re = $this->index_service->zhuangang($jsoncode);
                } else if ($in_approve['type'] == 38) { //晋升
                    $re = $this->index_service->jinsheng($jsoncode);
                } else if ($in_approve['type'] == 18) { //转正
                    $re = $this->index_service->zhuanzheng($jsoncode);
                } else if ($in_approve['type'] == 42) { //缴纳
                    $re = $this->index_service->shebao($jsoncode);
                } else if ($in_approve['type'] == 1) { //请假
                    $is_leave = $this->getis_leave($in_approve['admin_id'], $in_approve['detail_type']);
                    if ($is_leave !== null && $is_leave <= 0) {
                        return to_assign(1, "当前无可用假期");
                    }
                    //请的是年假 就将请假的天数写到已用年假
                    if ($in_approve['detail_type'] == 1) {
                        Db::name("admin")->where(['id' => $in_approve['admin_id']])->inc('is_annual_leave', doubleval($in_approve['duration']))->update();
                    }
                    $re = true;
                } else if ($in_approve['type'] == 23) { //报销
                    $re = $this->index_service->baoxiao($jsoncode, $in_approve);
                } else if ($in_approve['type'] == 13) { //付款
                    $re = $this->index_service->fukuan($jsoncode, $in_approve);
                } else if ($in_approve['type'] == 46
                    || $in_approve['type'] == 50
                    || $in_approve['type'] == 57
                    || $in_approve['type'] == 40
                    || $in_approve['type'] == 54
                    || $in_approve['type'] == 41
                ) { //赔付
                    $re = $this->index_service->other($jsoncode, $in_approve);
                } else if ($in_approve['type'] == 44) { //业绩升降档
                    $store_service = new StoreService();
                    $store_service->addstorerecently($jsoncode['detail_time']);

                    $store = Db::name("store")->where([
                        'did' => $jsoncode['department_type'],
                        'sdate' => $jsoncode['detail_time'],
                    ])->find();

                    $u_store['id'] = $store['id'];
                    $u_store['grade'] = $jsoncode['n_grade'];
                    $store_service->updateStore($u_store);
                } else if ($in_approve['type'] == 50 || $in_approve['type'] == 54 || $in_approve['type'] == 57) { //奖金申请
                    $type = Db::name("type_pay")
                        ->alias('tp')
                        ->where(['tp.id' => $jsoncode['pay_type']])
                        ->join('type t', 't.id = tp.type_id', 'LEFT')
                        ->find();

                    if ($in_approve['type'] == 54) {
                        $jsoncode['pay_amount'] = $jsoncode['ref_amount'];
                    }

                    $bx_de[] = [
                        'approve_id' => $in_approve['id'],
                        'pay_type' => $jsoncode['pay_type'],
                        'pay_time' => date("Y-m-d"),
                        'pay_amount' => $jsoncode['pay_amount'],
                        'pay_text' => '',
                        'pay_file' => $jsoncode['file_ids'],
                        'is_fapiao' => '否',
                        'store' => $jsoncode['store'],
                        'did' => $jsoncode['did'],
                        'admin_id' => $jsoncode['aid'],
                        'admin_name' => $jsoncode['aname'],
                        'payee_name' => isset($jsoncode['payee_name']) ? $jsoncode['payee_name'] : $jsoncode['payee'],
                        'bank_number' => $jsoncode['bank_number'],
                        'bank_name' => isset($jsoncode['bank_name']) ? $jsoncode['bank_name'] : $jsoncode['open_bank'],
                        'remark' => $jsoncode['remark'],
                        'type_title' => !empty($type) ? $type['title'] : '',
                        'create_time' => strtotime(date("Y-m-d H:i:s")),
                        'create_date' => date("Y-m-d"),
                        'type' => 3
                    ];
                    $expenseService = new ExpenseService();
                    $re = $expenseService->addAll($bx_de);
                } else if ($in_approve['type'] == 17) { //入职流程
                    if (isset($param['contract_link']) && !empty($param['contract_link'])){
                        $jsoncode['contract_link'] = $param['contract_link'];
                    }
                    $admin_view_service = new AdminViewService();
                    $admin_view_service->approve_adduser($jsoncode, $this->uid);
                }

                //将所有该单据未读的消息改为已读
                Db::name("message")->where(['action_id' => $id])->update(['read_time' => time()]);

                if ($re) {
                    $approve_up['is_script'] = 1;
                }
//                if ($flag){
//                    $storeservice->add_store_transfer($data);
//                }
            } else {

                //修改审核申请
                $approve_up['check_step_sort'] = $next_step['sort'];
                $approve_up['check_admin_ids'] = $next_step['flow_uids'];
                $approve_up['check_status'] = 1;
                $approve_up['node_tit'] = $next_step['flow_title'];
                $to_users = $next_step['flow_uids'];
                $title = "{$in_admin}提交了一个『{$flow_type_title}申请』，请及时审批。";

                //有一下个节点的或签人员消息全部完成
                if ($in_approve['type'] == 17 && isset($param['contract_link'])) { //入职流程
                    $jsoncode['contract_link'] = $param['contract_link'];
                    Db::name('approve')->where(['id' => $in_approve['id']])->update([
                        'jsoncode' => serialize($jsoncode),
                    ]);
                    //$jsoncode['contract_link'] = $param['contract_link'];
                    //$admin_view_service = new AdminViewService();
                    //$admin_view_service->approve_adduser($jsoncode, $this->uid);
                }

            }
        } else if ($status == 2) {
            //查询我上一步节点
            $first_step = Db::name("FlowStep")->where([
                ['action_id', '=', $in_approve['id']],
                ['delete_time', '=', 0],
                ['sort', '=', 0]
            ])->find();

            //修改审核申请
            $approve_up['check_step_sort'] = 0;
            $approve_up['check_admin_ids'] = $first_step['flow_uids'];
            $approve_up['check_status'] = 3;
            $approve_up['node_tit'] = $first_step['flow_title'];

            //
            if (!empty($param['check_edit'])) {
                $approve_up['is_edit'] = 1;
            } else {
                $approve_up['is_edit'] = 0;
            }

            $to_users = $in_approve['admin_id'];
            $in_admin = Db::name("admin")->where(['id' => $this->uid])->value('name');
            $title = "{$in_admin}驳回了您提交的『{$flow_type_title}申请』";
        } else if ($status == 4) {
            $approve_up['check_status'] = 4;
            $status = 3;
        }

        if ($in_approve['type'] == 13 && isset($jsoncode['rent_id'])) { //付款完成 合同
            Db::name('rent')->where(['id' => $jsoncode['rent_id']])->update([
                'check_status' => $approve_up['check_status'],
                'is_pay' => 1,
            ]);
        }

        //添加操作记录
        $checkData = array(
            'action_id' => $id,
            'step_id' => $step['id'],
            'check_user_id' => $this->uid,
            'type' => 1,
            'check_time' => time(),
            'status' => $status,
            'create_time' => time(),
            'file_ids' => isset($param['file_ids']) ? $param['file_ids'] : "",
            'sort' => $step['sort'],
            'flow_title' => $step['flow_title'],
            'content' => isset($param['content']) ? $param['content'] : ''
        );
        Db::name('FlowRecord')->strict(false)->field(true)->insertGetId($checkData);
        Db::name("Approve")->where(['id' => $id])->update($approve_up);

        if ($status != 3) {
            $m_status = 0;
            $msg = [
                'create_time' => date('Y-m-d H:i:s', $in_approve['create_time']),
                'action_id' => $id,
                'title' => Db::name('FlowType')->where('id', $in_approve['flow_id'])->value('title'),
                'from_uid' => $to_users,
            ];
            if ($status == 2) {
                $m_status = 2;
                //驳回后将之前所有关于此审批的内容 全部更新为已读
                Db::name("message")->where([
                    'action_id' => $id, ['create_time', '<', time()]
                ])->update(['read_time' => time()]);
            }
            sendMessage($to_users, 21, $msg, $title, $m_status);

            if ($copy_flag) {
                $dep_admins = Db::name('admin')->where([
                    ['', 'exp', Db::raw("FIND_IN_SET('20',did)")],
                    ['status', '=', 1]
                ])->field("id")->order("sort asc")->column('id');

                if (!empty($flow['copy_uids'])) {
                    $copys = array_unique(array_merge(explode(",", $flow['copy_uids']), $dep_admins));
                } else {
                    $copys = $dep_admins;
                }

//                $to_users = $in_approve['copy_uids'];
                $msg['from_user'] = $in_admin;
                $msg['content'] = "申请人：{$applicant_admin['name']}";
                sendMessage($copys, 12, $msg, '', $m_status);
            }


//            if ($copy_flag && !empty($in_approve['copy_uids'])) {
//                $to_users = $in_approve['copy_uids'];
//                $msg['from_user'] = $in_admin;
//                $msg['content'] = "申请人：{$applicant_admin['name']}";
//                sendMessage($to_users, 12, $msg, '', $m_status);
//            }


        } else {
            Db::name("message")->where([
                'action_id' => $id, ['create_time', '<', time()]
            ])->update(['status' => -1]);
        }
        return to_assign(0, "操作成功", ['is_mobile' => $is_mobile]);
    }

    public function get_admin_to_id()
    {
        $param = get_params();
        $id = $param['id'];
        $admin = get_admin($id);

        if (!empty($admin)) {
            $position = Db::name("position")->where([['id', 'in', $admin['position_id']]])->column('title');
            $admin['position_name'] = implode(",", $position);

            //主部门 部门名称 职位
            //子部门 部门名称 职位

            $main_position_name = Db::name("position")->where([['id', 'in', $admin['main_position_id']]])->column('title');
            $main_dname = Db::name("department")->where([['id', 'in', $admin['main_did']]])->column('title');
            $admin['admin_dep'][] = [
                'did' => $admin['main_did'],
                'position_id' => $admin['main_position_id'],
                'dname' => implode(",", $main_dname),
                'position_name' => implode(",", $main_position_name),
            ];

            $admin['entry_time'] = !empty($admin['entry_time']) ? date("Y-m-d", $admin['entry_time']) : '';

            $rank_name = Db::name('StoreRank')->where([['id', '=', $admin['rank_id']]])->field('rank')->find();
            $label_name = Db::name('StoreLabel')->where([['id', '=', $admin['label_id']]])->field('name')->find();

            if (!empty($rank_name)) {
                $admin['rank_name'] = $rank_name['rank'];
            }

            if (!empty($label_name)) {
                $admin['label_name'] = $label_name['name'];
            }

        }

        return to_assign(0, '', $admin);
    }

    public function next_step($id, $sort)
    {
        $next_step = Db::name('FlowStep')->where(['action_id' => $id, 'sort' => ($sort + 1), 'delete_time' => 0])->find();

        if (empty($next_step)) {
            return null;
        } else {

            $whereor = array();
            $in_flow_uids = explode(",", $next_step['flow_uids']);
            foreach ($in_flow_uids as $k => $v) {
                $whereor[] = ['', 'exp', Db::raw("FIND_IN_SET('{$v}',flow_uids)")];
            }
            //当前审批节点
            $before_step = Db::name('FlowStep')
                ->where([
                    ['sort', '<', $next_step['sort']],
                    'action_id' => $id,
                    'delete_time' => 0
                ])
                ->where(function ($query) use ($whereor) {
                    $query->whereOr($whereor);
                })->find();

            if (!empty($before_step)) {
                $checkData = array(
                    'action_id' => $id,
                    'step_id' => $next_step['id'],
                    'check_user_id' => $this->uid,
                    'type' => 1,
                    'check_time' => time(),
                    'status' => 1,
                    'create_time' => time(),
                    'file_ids' => isset($param['file_ids']) ? $param['file_ids'] : "",
                    'sort' => $next_step['sort'],
                    'flow_title' => $next_step['flow_title'],
                    'content' => isset($param['content']) ? $param['content'] : ''
                );
                Db::name('FlowRecord')->strict(false)->field(true)->insertGetId($checkData);

                return $this->next_step($id, $next_step['sort']);
            }

            $next_uids_status = Db::name("admin")->where(['id' => $next_step['flow_uids']])->value("status");
            if (empty($next_step['flow_uids']) || $next_uids_status == 2) {
                return $this->next_step($id, $next_step['sort']);
            } else {
                return $next_step;
            }
        }

    }

    public function get_personnel_dep()
    {
        $param = get_params();
        $where[] = ['a.status', '=', 1];
        $where[] = ['a.id', '>', 1];
        if (!empty($param['keywords'])) {
            $where[] = ['a.name', 'like', '%' . $param['keywords'] . '%'];
        }
        if (!empty($param['ids'])) {
            $where[] = ['a.id', 'notin', $param['ids']];
        }

        $dids = [];
        $fen_dep = Db::name('department')->where([['leader_id', '=', $this->uid], ['title', 'like', '%分管%']])->find();
        if (!empty($fen_dep)) {
            $dids = get_department_son($fen_dep['id']);
        } else {
            $st_dep = Db::name('department')->where([['leader_id', '=', $this->uid], ['remark', '=', '门店']])->select()->toArray();
            if (!empty($st_dep)) {
                foreach ($st_dep as $st_dep_key => $st_dep_value) {
                    $dids[] = implode(",", get_department_son($st_dep_value['id']));
                }
            } else {
                $admin = Db::name('admin')->where([['id', '=', $this->uid]])->find();
                foreach (explode(',', $admin['did']) as $k => $v) {
                    $dep = Db::name('department')->where([['id', '=', $v], ['remark', '=', '门店']])->find();
                    if (!empty($dep)) {
                        $dids[] = $dep['id'];
                        break;
                    }
                }
            }
        }

        $whereor = [];
        if (empty($dids)) {
            $dids = Db::name('department')->column('id');
        }
        foreach ($dids as $k => $v) {
            $whereor[] = ['a.did', 'find in set', $v];
        }

        $rows = empty($param['limit']) ? get_config('app.page_size') : $param['limit'];
        $list = Db::name('admin')
            ->field('a.id,a.did,a.position_id,a.mobile,a.name,a.nickname,a.sex,a.status,a.thumb,a.username,
                d.title as department')
            ->alias('a')
            ->join('Department d', 'a.did = d.id')
            ->where($where)
            ->where(function ($query) use ($whereor) {
                $query->whereOr($whereor);
            })
            ->order('a.id desc')
            ->paginate($rows, false, ['query' => $param])->toArray();

//        foreach ($list['data'] as $k => $v) {
//            $did = Db::name("department")->field("remark desc")->where([['id', 'in', $v['did']]])->column("id");
//            $list['data'][$k]['did'] = implode(",",$did);
//        }

        return table_assign(0, '', $list);
    }

    public function findStoreRstatus()
    {
        $param = get_params();
        $where = array();

        if (isset($param['sdate']) && $param['sdate'] != '') {
            $where[] = ['sdate', '=', $param['sdate']];
        } else {
            return null;
        }

        if (isset($param['did']) && !empty($param['did'])) {
            $where[] = ['did', '=', $param['did']];
        } else {
            return null;
        }

        $store = Db::name("store")->where($where)->find();

        return $store['rstatus'];
    }

    //根据选择的人 获取对于的审批节点
    public function userDepartment_flows()
    {
        $param = get_params();

        $uid = $param['uid'];
        $type = $param['type'];
        $position_ids_js = isset($param['position_ids_js']) ? $param['position_ids_js'] : 0;

        //$admin = Db::name("admin")->where(['id' => $uid])->find();
        $DepartmentChange = new DepartmentChange();
        $dep = $DepartmentChange->getDirectlyDepartment($uid);

        $flows = get_cate_department_flows($type, implode(",", $dep), $uid, 0, 0, 0, $position_ids_js);

        return $flows;
    }

    public function finance_flows()
    {
        $param = get_params();

        $uid = $param['uid'];
        $type = $param['type'];
        //$admin = Db::name("admin")->where(['id' => $uid])->find();
        $DepartmentChange = new DepartmentChange();
        $dep = $DepartmentChange->getDirectlyDepartment($uid);

        if (isset($param['store'])) {
            $flows = get_cate_finance_flows($type, $param['store'], $uid, $param);
        } else {
            $flows = get_cate_finance_flows($type, implode(",", $dep), $uid, $param);
        }

        return $flows;
    }

    public function qingjia_flows()
    {
        $param = get_params();

        $type = 1;
        $did = $param['did'];
        $aid = $param['aid'];
        $vacation_type = $param['vacation_type'];
        $time_conditions = $param['time_conditions'];

        $DepartmentChange = new DepartmentChange();
        $dep = $DepartmentChange->getDirectlyDepartment($param['aid']);

        $flows = get_cate_department_flows($type, implode(",", $dep), $aid, $vacation_type, $time_conditions);

        return to_assign(0, '', $flows);
    }

    //获取当前人员的可用假期
    public function is_leave()
    {
        $param = get_params();

        $aid = $param['aid'];
        $type = $param['type'];

        return $this->getis_leave($aid, $type);
    }

    public function getis_leave($aid, $type)
    {
        $admin = get_admin($aid);
        $is_leave = 0;
        if ($type == 1) {//年假

            $is_leave = $admin['annual_leave'] - $admin['is_annual_leave'];
        } else {
            $vacation_type = Db::name("vacation_type")->where(['id' => $type])->find();
            if (!empty($vacation_type) && $vacation_type['days'] != null) {
                //获取已经请了的假期
                $sum = Db::name("approve")
                    ->where([['check_status', 'in', '0,1,2'], 'type' => 1, 'detail_type' => $type])->sum("duration");
                $is_leave = $vacation_type['days'] - $sum;
            } else {
                $is_leave = null;
            }
        }
        return $is_leave;
    }

    public function note_view()
    {
        $id = empty(get_params('id')) ? 0 : get_params('id');
        $note = Db::name('Note')->where(['id' => $id])->find();
        $note['cate_title'] = Db::name('NoteCate')->where(['id' => $note['cate_id']])->value('title');
        $note['admin_name'] = Db::name('Admin')->where(['id' => $note['admin_id']])->value('name');

        if ($note['file_ids'] != '') {
            $fileArray = Db::name('File')->where('id', 'in', $note['file_ids'])->select();
            $note['fileArray'] = $fileArray;
        }

        View::assign('note', $note);
        return view();
    }

    public function qiweitest()
    {
        $qiweiservice = new QiWeiService();
        $qiweiservice->webAuto();
    }

    public function getAccessToken()
    {
//        Cache::set('scx', 'value', 3600); // 3600秒过期
//        $value = Cache::get('scx');
        // 获取缓存

    }

    public function getPosition()
    {

        $param = get_params();

        $where = ['status' => 1];

        if (isset($param['title'])) {
            $where[] = ['title', 'like', "%{$param['title']}%"];
        }

        $list['data'] = Db::name("position")->where($where)->select()->toArray();

        return table_assign(0, '', $list);
    }

    public function datastatics()
    {


    }


    public function entry_time()
    {
        $admin = Db::name("admin")->select()->toArray();
        foreach ($admin as $k => $v) {
            $entry_date = date("Y-m-d H:i:s", $v['entry_time']);
            Db::name("admin")
                ->where(['id' => $v['id']])
                ->update(
                    ['entry_date' => $entry_date]
                );
        }
    }

    //根据流程名称模糊查询和flow_cate参数进行联合查询获取流程信息
    public function get_flow_info_by_name_cate()
    {
        $name = get_params('name');
        $flow_cate = get_params('flow_cate');

        if (empty($name)) {
            return to_assign(1, '流程名称关键词不能为空');
        }

        $where = [
            ['name', 'like', '%' . $name . '%'],
            ['status', '=', 1]
        ];

        if (!empty($flow_cate)) {
            $where[] = ['flow_cate', '=', $flow_cate];
        }

        $flow = Db::name('Flow')->where($where)->find();
        if (empty($flow)) {
            return to_assign(1, '未找到对应流程');
        }

        return to_assign(0, '获取成功', $flow);
    }


    public function get_account()
    {
        $param = get_params();

        $payee_name = $param['payee_name'];

        if (!isset($payee_name) || empty($payee_name)) {
            return [];
        }

        $account = Db::name("account")
            ->where([
                'payee_name' => $payee_name,
                'status' => 1
            ])
            ->order("update_time desc")
            ->find();

        return $account;

    }


    public function approve()
    {
        $approve = Db::name("approve")->where(['id' => 4454])->find();

        $jsoncode = unserialize($approve['jsoncode']);

        $jsoncode['duration'] = 2;

        $approve = Db::name("approve")->where(['id' => 4454])->update(
            ['jsoncode' => serialize($jsoncode)]
        );

    }

    public function getStoreBusinessT()
    {
        $param = get_params();

        $ApproveService = new ApproveService();

        $list = $ApproveService->getStoreBusinessT($param['aid']);

        return to_assign(0, 'success', $list);

    }


    public function work_overtime()
    {
        $StoreSalaryService = new StoreSalaryService();
        $StoreSalaryService->work_overtime(
            'sqNC4yv21032617014440534016',
            '********',
            '2025-06',
            '2025-06-30',
            "06,07"
        );
    }


    //晋升指标
    public function jinshen_zhibiao()
    {
        $months = 0;
        $n_position = 13;
        $aid = 2518;
        $target_zb = [];
        $target_zb_cont = "";

        $param = get_params();

        $n_position = isset($param['n_position']) ? $param['n_position'] : 0;
        $aid = isset($param['aid']) ? $param['aid'] : 0;

        if (empty($n_position) || empty($aid)) {
            return to_assign(0, 'success', []);
        }

        //大师傅 六个月 3B&1A
        //技术指导 六个月 A
        //储备店长 三个月 2B
        //店长 三个月 2C
        if ($n_position == 13 || $n_position == 15) {
            $months = 6;
            if ($n_position == 13) {
                $target_zb = ["A" => 1, "B" => 3, "C" => 0];
                $target_zb_cont = "六个月【3B&1A】；二次服务累计达到【300】个及以上";
            } else {
                $target_zb = ["A" => 6, "B" => 0, "C" => 0];
                $target_zb_cont = "六个月【6A】；二次服务连续三个月【360】个；带教 两个大师傅";
            }

        } else if ($n_position == 26 || $n_position == 12) {
            $months = 3;
            if ($n_position == 26) {
                $target_zb = ["A" => 0, "B" => 2, "C" => 0];
                $target_zb_cont = "三个月【2B】；企业文化考试：【80】；店长课程考试：【80】";
            } else {
                $target_zb = ["A" => 0, "B" => 0, "C" => 2];
                $target_zb_cont = "三个月【2C】";
            }
        }

        $date = date("Y-m");
        $pre_date = date('Y-m', strtotime("-$months month"));

        $store_business_t = Db::name("store_business_t")->where([
            ['sdate', '>=', $pre_date],
            ['sdate', '<', $date],
            ['aid', '=', $aid],
            ['status', '=', 1],
        ])
            ->field("id,r_name,r_id,sdate,aname,aid,did,dname,kaj,dianzhong,second_service")
            ->order("sdate asc")
            ->select()->toArray();

        $heji = [
            'sdate' => '合计',
            'kaj' => 0,
            'dianzhong' => 0,
            'second_service' => 0,
        ];
        $col_A = 0;
        $col_B = 0;
        $col_C = 0;
        foreach ($store_business_t as $k => $v) {

            $rc_where = [
                ['', 'exp', Db::raw("FIND_IN_SET('{$v['r_id']}',r_id)")],
                ['sdate', '=', $v['sdate']],
                ['project', '=', 'kat'],
            ];

            if (strtotime($v['sdate']) == strtotime("2025-01")) {

                $store = DB::name("store")->where(['did' => $v['did'], 'sdate' => $v['sdate']])->find();

                if (!empty($store)) {
                    $ratio = $store['ratio'];
                }

                if ($ratio == 2) {
                    $rc_where['ratio'] = 2;
                } else {
                    $rc_where['ratio'] = 1;
                }
            }

            $rank_calculate = Db::name("StoreRankCalculate")->where($rc_where)->order("sort asc")->select()->toArray();

            $target = array();
            $target_kaj = array();


            foreach ($rank_calculate as $kk => $vv) {
                if (!empty($rank_calculate['target'])) {
                    $target = $vv;
                    break;
                } else {
                    if ($kk == 0) {
                        if ($v['kaj'] >= $rank_calculate[$kk]['target']) {
                            $target = $vv;
                            break;
                        }
                    } else if ($kk > 0 && $kk + 1 < count($rank_calculate)) {
                        if ($rank_calculate[$kk - 1]['target'] > $v['kaj'] && $v['kaj'] >= $rank_calculate[$kk]['target']) {
                            $target = $vv;
                            break;
                        }
                    } else {
                        $target = $vv;
                        break;
                    }
                }
            }

            foreach ($rank_calculate as $kk => $vv) {
                $target_kaj[] = $vv['target'];
            }

            if (empty($target) || $v['kaj'] < intval($target['target'])) {
                $target['symbol'] = 1;
            }

            switch (intval($target['symbol'])) {
                case 4:
                    $col_A++;
                    $col_B++;
                    $col_C++;
                    break;
                case 3:
                    $col_B++;
                    $col_C++;
                    break;
                case 2:
                    $col_C++;
                    break;
            }

            $store_business_t[$k]['level'] = $target['symbol'];
            $store_business_t[$k]['target_kaj'] = implode(",", array_unique($target_kaj));
            $heji['kaj'] += $v['kaj'];
            $heji['dianzhong'] += $v['dianzhong'];
            $heji['second_service'] += $v['second_service'];
        }
        $store_business_t[] = $heji;

        $flag = true;

        foreach ($target_zb as $k => $v) {
            if ($k == "A" && $col_A < $v) {
                $flag = false;
            } else if ($k == "B" && $col_B < $v) {
                $flag = false;
            } else if ($k == "C" && $col_C < $v) {
                $flag = false;
            }
        }
        $data = array();
        $data['flag'] = $flag ? '合格' : '不合格';

        $data['target_zb_cont'] = $target_zb_cont;
        $data['list'] = $store_business_t;

        //当前门店的人员信息
        $d_count = Db::name("admin")->where([
            ['', 'exp', Db::raw("FIND_IN_SET('13',position_id)")],
            ['status', '=', 1],
            ['', 'exp', Db::raw("FIND_IN_SET('{$param['did']}',did)")],
        ])->count();
        $data['d_count'] = $d_count;


        return to_assign(0, 'success', $data);

    }

    /**
     * 通过合同编号查询租聘合同信息
     * @return \think\response\Json
     */
    public function get_contract_by_code()
    {
        try {
            $param = get_params();
            $code = $param['code'] ?? '';

            if (empty($code)) {
                return json(['code' => 1, 'msg' => '合同编号不能为空']);
            }

            // 查询租聘合同表
            $contract = Db::name('contract_rent')
                ->where('code', $code)
                ->where('status', 1) // 只查询启用状态的合同
                ->field('id, code, party_a_name, party_b_name, start_date, end_date')
                ->find();

            if (empty($contract)) {
                return json(['code' => 1, 'msg' => '未找到对应的合同信息']);
            }

            return json([
                'code' => 0,
                'msg' => '查询成功',
                'data' => $contract
            ]);

        } catch (\Exception $e) {
            Log::error('查询合同信息失败：' . $e->getMessage());
            return json([
                'code' => 1,
                'msg' => '查询合同信息失败：' . $e->getMessage()
            ]);
        }
    }

}









