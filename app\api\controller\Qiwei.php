<?php
/**
 * @copyright Copyright (c) 2021 仲正堂工作室
 * @license https://opensource.org/licenses/GPL-3.0
 * @link https://www.gougucms.com
 */
declare (strict_types=1);

namespace app\api\controller;

use app\api\BaseController;
use app\message\service\QiWeiService;
use app\oa\controller\Approve;
use app\store\service\LizhiService;
use PhpOffice\PhpSpreadsheet\Calculation\Logical\Boolean;
use think\facade\Db;
use think\Session;

class Qiwei
{

    public function get_join_qrcode()
    {
        $QiWeiService = new QiWeiService();
        $QiWeiService->get_join_qrcode();
    }

    public function batchdelete()
    {
        $QiWeiService = new LizhiService();
        $QiWeiService->deleteQiwei('SunChangXuCeShi');
    }

    public function get_department()
    {
        $QiWeiService = new QiWeiService();

        $response =  $QiWeiService->get_department_details();
        foreach ($response->department as $k => $v){
            $dname = str_replace('路', "", $v->name);
            $dname = str_replace('店', "", $dname);
            $department = DB::name("department")->where([['title', 'like', '%' . $dname . '%']])->find();

            if (!empty($department)){
                DB::name("department")
                    ->where(['id' => $department['id']])
                    ->update(['qw_id' => $v->id]);
            }

        }

    }

    public function transfer_store()
    {
        $o_did = "";
        $n_did = "";
        $aid = "";

        $QiWeiService = new QiWeiService();
        if (!empty($n_did)){

        }


    }

}
