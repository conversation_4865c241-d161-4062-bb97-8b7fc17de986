{extend name="../../base/view/common/base" /}

{block name="style"}
<style>
    /* 编辑页面样式 */
    .layui-card {
        border: 1px solid #e6e6e6;
        border-radius: 4px;
        margin-bottom: 15px;
    }

    .layui-card-header {
        background-color: #f8f8f8;
        padding: 10px 15px;
        border-bottom: 1px solid #e6e6e6;
        font-weight: bold;
    }

    .layui-card-body {
        padding: 15px;
    }

    .layui-td-gray {
        background-color: #f5f5f5;
        font-weight: bold;
        text-align: left;
        vertical-align: middle;
        width: 140px;
        padding: 8px 12px;
    }

    /* 表格样式优化 */
    .layui-table {
        margin-bottom: 0;
    }

    .layui-table td {
        padding: 8px 12px;
        vertical-align: middle;
    }

    .layui-table th {
        background-color: #f8f8f8;
        font-weight: bold;
    }

    /* 必填项红色星号 */
    .required-mark {
        color: #FF6347;
        margin-left: 3px;
    }

    /* 页面内容区域 */
    .edit-content {
        padding: 20px;
        height: 100%;
        box-sizing: border-box;
        overflow-y: auto;
    }

    /* 金额输入框样式 - 参考门店分红明细表 */
    .amount-input {
        text-align: right;
        font-weight: bold;
    }

    /* 所有数字输入框的通用样式 */
    input[type="number"] {
        font-weight: bold;
        text-align: right;
    }

    /* 表格中的数字显示样式 */
    .layui-table td span {
        font-weight: bold;
    }



    /* 系统提示信息样式 */
    .system-info {
        color: #999;
        font-size: 12px;
        font-style: italic;
        padding: 5px 0;
    }
</style>
{/block}

{block name="body"}
<div class="edit-content">
    <!-- 页面标题 -->
    <div style="margin-bottom: 20px;">
        <h3 style="margin: 0 0 15px 0; font-size: 18px; font-weight: bold; color: #333;">编辑门店卡余额</h3>
    </div>

    <form class="layui-form" lay-filter="editForm">
        <input type="hidden" name="id" value="{$detail.id}">

        <!-- 基础信息 -->
        <div class="layui-card">
            <div class="layui-card-header">基础信息</div>
            <div class="layui-card-body">
                <table class="layui-table">
                    <tr>
                        <td class="layui-td-gray">月份</td>
                        <td style="width: 200px;">
                            <span style="font-weight: bold;">{$detail.period}</span>
                            <input type="hidden" name="period" value="{$detail.period}">
                        </td>
                        <td class="layui-td-gray">门店</td>
                        <td style="width: 200px;">
                            <span style="font-weight: bold;">{$detail.store_name|default='未知门店'}</span>
                            <input type="hidden" name="store_id" value="{$detail.store_id}">
                        </td>
                    </tr>
                    <tr>
                        <td class="layui-td-gray">卡余额<span class="required-mark">*</span></td>
                        <td>
                            <input type="number" name="card_balance"
                                   value="{$detail.card_balance}"
                                   placeholder="请输入卡余额"
                                   autocomplete="off" class="layui-input amount-input"
                                   lay-verify="required|number" step="0.01" min="0">
                        </td>
                        <td class="layui-td-gray">逻辑门店类型<span class="required-mark">*</span><span style="font-size: 12px; color: #999; display: block;">（余额≥60万为老店，否则为新店）</span></td>
                        <td>
                            <select name="logical_store_type" lay-verify="required">
                                <option value="">请选择逻辑门店类型</option>
                                <option value="新店" {if $detail.logical_store_type == '新店'}selected{/if}>新店</option>
                                <option value="老店" {if $detail.logical_store_type == '老店'}selected{/if}>老店</option>
                            </select>
                        </td>
                    </tr>
                </table>
            </div>
        </div>

        <!-- 门店类型信息 -->
        <div class="layui-card">
            <div class="layui-card-header">门店类型信息</div>
            <div class="layui-card-body">
                <table class="layui-table">
                    <tr>
                        <td class="layui-td-gray">上月逻辑类型<span class="required-mark">*</span><span style="font-size: 12px; color: #999; display: block;">（上个月的逻辑门店类型）</span></td>
                        <td style="width: 200px;">
                            <select name="previous_month_logical_type" lay-verify="required">
                                <option value="">请选择上月逻辑类型</option>
                                <option value="新店" {if $detail.previous_month_logical_type == '新店'}selected{/if}>新店</option>
                                <option value="老店" {if $detail.previous_month_logical_type == '老店'}selected{/if}>老店</option>
                            </select>
                        </td>
                        <td class="layui-td-gray">结算门店类型<span class="required-mark">*</span><span style="font-size: 12px; color: #999; display: block;">（延迟一个月生效，一旦变老，永不降级）</span></td>
                        <td style="width: 200px;">
                            <select name="settlement_store_type" lay-verify="required">
                                <option value="">请选择结算门店类型</option>
                                <option value="新店" {if $detail.settlement_store_type == '新店'}selected{/if}>新店</option>
                                <option value="老店" {if $detail.settlement_store_type == '老店'}selected{/if}>老店</option>
                            </select>
                        </td>
                    </tr>
                </table>
            </div>
        </div>

        <!-- 提交按钮 -->
        <div style="margin-top: 15px;">
            <button class="layui-btn layui-btn-normal" lay-submit lay-filter="submitForm">保存修改</button>
            <button type="button" class="layui-btn layui-btn-primary" onclick="parent.layer.closeAll()">取消</button>
        </div>
    </form>
</div>
{/block}

{block name="script"}
<script>
    const moduleInit = ['form'];
    function gouguInit() {
        var form = layui.form, laydate = layui.laydate;

        // 初始化月份选择器
        laydate.render({
            elem: '#period',
            type: 'month',
            format: 'yyyy-MM'
        });

        // 表单提交
        form.on('submit(submitForm)', function(data) {
            var loadIndex = layer.load(2, {shade: 0.3});

            $.post('/cross/balance/edit', data.field, function(res) {
                layer.close(loadIndex);
                if (res.code == 0) {
                    layer.msg(res.msg, {icon: 1, time: 2000}, function() {
                        parent.layer.closeAll();
                        // 触发父页面刷新
                        if (parent.layui && parent.layui.pageTable) {
                            parent.layui.pageTable.reload();
                        }
                    });
                } else {
                    layer.msg(res.msg, {icon: 2, time: 3000});
                }
            }).fail(function() {
                layer.close(loadIndex);
                layer.msg('网络错误，请重试', {icon: 2});
            });

            return false;
        });

        // 表单渲染
        form.render();
    }
</script>
{/block}