<?php

namespace app\message\service;

use think\facade\Db;
use think\facade\Request;

class QiWeiService
{

    protected $qwCorpid;
    protected $qwOaCorpsecret;
    protected $qwTxlCorpsecret;
    protected $qwAgentid;

    protected $qwGgCorpsecret;
    protected $qwGgAgentid;


    public function __construct()
    {
        $this->qwCorpid = get_config('youzan.qwCorpid');
        $this->qwOaCorpsecret = get_config('youzan.qwOaCorpsecret');
        $this->qwTxlCorpsecret = get_config('youzan.qwTxlCorpsecret');
        $this->qwAgentid = get_config('youzan.qwAgentid');


        $this->qwGgCorpsecret = get_config('youzan.qwGgCorpsecret');
        $this->qwGgAgentid = get_config('youzan.qwGgAgentid');
    }

    public function index()
    {
        var_dump(1111111);
    }

    //获取token
    public function gettoken($corpsecret)
    {
        $url = 'https://qyapi.weixin.qq.com/cgi-bin/gettoken?corpid=' . $this->qwCorpid .
            '&corpsecret=' . $corpsecret;
        $response = $this->ajax($url, [], 'GET');
        $access_token = json_decode($response)->access_token;
//        $expires_in = $data->expires_in;
        return $access_token;
    }

    /**
     * 发送站内信
     * @param  $user_id 接收人
     * @param  $template 消息模板
     * @param  $data 操作内容
     * @return
     */
    function sendMessage($send_data, $template = "", $vlink = '')
    {

        $HTTP_HOST = $_SERVER['HTTP_HOST'];
        if (!empty($HTTP_HOST) &&  strpos($HTTP_HOST, "localhost") !== false ){
            return;
        }

        $url = 'https://qyapi.weixin.qq.com/cgi-bin/message/send?access_token=' . $this->gettoken($this->qwOaCorpsecret);

        $agentid = $this->qwAgentid;
        if ($template == 1){
            $url = 'https://qyapi.weixin.qq.com/cgi-bin/message/send?access_token=' . $this->gettoken($this->qwGgCorpsecret);
            $agentid = $this->qwGgAgentid;
        }

        $to_uids = [];
        foreach ($send_data as $k => $v) {
            $admin = Db::name("admin")->where(['id' => $v['to_uid']])->find();
            if (!empty($admin) && !empty($admin['wx_account'])) {
                $to_uids[] = $admin['wx_account'];
            }
        }

        $approve = Db::name("approve")->where(['id' => $send_data[0]['action_id']])->find();

        $datetime = date("Y-m-d H:i:s",);

        $title = "消息通知";
        $aname = "";

        if (!empty($approve)) {
            $flow_type = Db::name("flow_type")->where(['id' => $approve['type']])->find();
            $title = "『{$flow_type['title']}』申请";
            $aid = $approve['admin_id'];
            if (!empty($approve['aid'])) {
                $aid = $approve['aid'];
            }
            $aname = Db::name("admin")->where(['id' => $aid])->value('name');
        }

        // 物品维修流程特殊处理
        if ($template >= 60 && $template <= 65) {
            if ($template == 60) {
                $title = "『物品维修』申请";
            } else if($template == 61) {
                $title = "『物品维修』拒绝";
            } else if($template == 63) {
                $title = "『物品维修』预计时间";
            } else if($template == 64) {
                $title = "『物品维修』完成";
            } else if($template == 65) {
                $title = "『物品维修』转审";
            }
            // 使用与模板60相同的处理方式
            $aname = $send_data[0]['manager_name'] ?? '';
        }
        $msg = [
            'touser' => implode("|", $to_uids),
//            'touser' => 'SunChangXu',
            'msgtype' => "textcard",
            'agentid' => $agentid,
            "textcard" => [
                "title" => $title,
                "description" => "
                    <div class=\"gray\">{$datetime}</div> <div class=\"normal\">{$send_data[0]['title']}</div><div class=\"highlight\">申请人：{$aname}</div>",
                "url" => $vlink,
                "btntxt" => "更多"
            ],
            "enable_id_trans" => 0,
            "enable_duplicate_check" => 0,
            "duplicate_check_interval" => 1800
        ];

        if ($template == 1) {
            $msg["textcard"]["description"] = "
                    <div class=\"gray\">{$datetime}</div> <div class=\"normal\">{$send_data[0]['title']}</div><div class=\"highlight\">{$send_data[0]['content']}</div>";

            $state = urlencode("/note/index/view?id={$send_data[0]['action_id']}");
            $msg["textcard"]["url"] = "https://open.weixin.qq.com/connect/oauth2/authorize?appid=wwe89f37f6e6cd3c86&redirect_uri=https%3A%2F%2Foa.zztcaigou.cn%2Fmobile%2Findex%2Fqiweinoteview&response_type=code&scope=snsapi_base&state={$state}&agentid=1000033#wechat_redirect";
        } else if($template >= 60 && $template <= 65) { // 维修流程所有消息模板
            $state = urlencode("/repair/process/view?id={$send_data[0]['action_id']}");
            // 此处修改一下维修流程消息模板内容，添加上维修编号
            $process_no = isset($send_data[0]['process_no']) ? $send_data[0]['process_no'] : '';
            $msg["textcard"]["description"] = "
                    <div class=\"gray\">{$datetime}</div> <div class=\"normal\">{$send_data[0]['title']}</div><div class=\"highlight\">申请人：{$aname}</div><div class=\"highlight\">流程编号：{$process_no}</div>";
            $msg["textcard"]["url"] = "https://open.weixin.qq.com/connect/oauth2/authorize?appid=wwe89f37f6e6cd3c86&redirect_uri=https%3A%2F%2Foa.zztcaigou.cn%2Fmobile%2Findex%2Fqiweinoteview&response_type=code&scope=snsapi_base&state={$state}&agentid=1000033#wechat_redirect";
        } else {
            $state = urlencode("/oa/approve/view?id={$send_data[0]['action_id']}");
            $msg["textcard"]["url"] = "https://open.weixin.qq.com/connect/oauth2/authorize?appid=wwe89f37f6e6cd3c86&redirect_uri=https%3A%2F%2Foa.zztcaigou.cn%2Fmobile%2Findex%2Fqiweinoteview&response_type=code&scope=snsapi_base&state={$state}&agentid=1000033#wechat_redirect";

        }

        $response = $this->ajax($url, $msg, 'POST');
    }

    function sendMessage_customize($aids, $title, $description = "" , $url)
    {
        $url = 'https://qyapi.weixin.qq.com/cgi-bin/message/send?access_token=' . $this->gettoken($this->qwOaCorpsecret);

        $to_uids = [];
        foreach ($aids as $k => $v) {
            if (empty($v)) continue;
            $admin = Db::name("admin")->where(['id' => $v])->find();
            if (!empty($admin) && !empty($admin['wx_account'])) {
                $to_uids[] = $admin['wx_account'];
            }else{
                $to_uids[] = 'SunChangXu';
            }
        }

        $msg = [
            'touser' => implode("|", $to_uids),
//            'touser' => 'SunChangXu',
            'msgtype' => "markdown",
            'agentid' => $this->qwAgentid,
            "markdown"=> [
                "content" => $description
           ],
            "enable_id_trans" => 0,
            "enable_duplicate_check" => 0,
            "duplicate_check_interval" => 1800
        ];

        $response = $this->ajax($url, $msg, 'POST');
    }


    function getOAtoken()
    {
        return $this->gettoken($this->qwOaCorpsecret);
    }

    public function webAuto()
    {
        header('Content-Type: text/html; charset=UTF-8');
        $REDIRECT_URI = urlencode("https://oa.zztcaigou.cn/mobile/index/qiweinoteview");
        $url = "https://open.weixin.qq.com/connect/oauth2/authorize?appid=" . $this->qwCorpid . "&redirect_uri=" . $REDIRECT_URI . "&response_type=code&scope=snsapi_base&agentid=" . $this->qwAgentid . "#wechat_redirect";
//        $url = "https://qyapi.weixin.qq.com/cgi-bin/auth/getuserinfo?access_token=". $this->gettoken($this->qwOaCorpsecret) ."&code=CODE";
        $re = $this->ajax($url, [], 'GET');
        dump($url);
    }

    function sendQiweiUserList()
    {
        $url = 'https://qyapi.weixin.qq.com/cgi-bin/user/list_id?access_token=' . $this->gettoken($this->qwTxlCorpsecret);
        $response = $this->ajax($url, [], 'POST');
        $dept_user = json_decode($response)->dept_user;

        $OA_TOKEN = $this->gettoken($this->qwOaCorpsecret);
        foreach ($dept_user as $k => $v) {
            $user_url = "https://qyapi.weixin.qq.com/cgi-bin/user/get?access_token={$OA_TOKEN}&userid={$v->userid}";
            $user_info = $this->ajax($user_url, [], 'POST');
            $user_info = json_decode($user_info);

            if ($user_info->errcode == 0) {
                $data = [
                    'userid' => $user_info->userid,
                    'department' => implode(",", $user_info->department),
                    'mobile' => $user_info->telephone,
                    'name' => $user_info->name,
                ];

                $qiwei_user = Db::name("qiwei")->where(["userid" => $user_info->userid])->find();

                $admin = Db::name("admin")
                    ->where(['name' => $user_info->name])
                    ->find();

                if (!empty($admin)) {
                    $data['aid'] = $admin['id'];
                    Db::name("admin")->where(["id" => $admin['id']])->update(['wx_account' => $user_info->userid]);
                }

                if (!empty($qiwei_user)) {
                    Db::name("qiwei")->where(["id" => $qiwei_user['id']])->update($data);
                } else {
                    Db::name("qiwei")->insertGetId($data);
                }

            }
        }
    }

    function get_join_qrcode()
    {
        $url = 'https://qyapi.weixin.qq.com/cgi-bin/corp/get_join_qrcode?access_token=' . $this->gettoken($this->qwTxlCorpsecret);
        $response = $this->ajax($url, [], 'POST');
        $response = json_decode($response);
        return $response;
    }

    function batchdelete($wx_account)
    {
        $url = 'https://qyapi.weixin.qq.com/cgi-bin/user/delete?access_token=' . $this->gettoken($this->qwTxlCorpsecret) . "&userid={$wx_account}";
        $response = $this->ajax($url, [], 'GET');
        $response = json_decode($response);
        return $response;
    }

    function get_department()
    {
        $url = 'https://qyapi.weixin.qq.com/cgi-bin/department/simplelist?access_token=' . $this->gettoken($this->qwTxlCorpsecret);
        $response = $this->ajax($url, [], 'POST');
        $response = json_decode($response);
        return $response;
    }

    function get_department_details()
    {
        $url = "https://qyapi.weixin.qq.com/cgi-bin/department/list?access_token=". $this->gettoken($this->qwOaCorpsecret) ;
        $response = $this->ajax($url, [], 'GET');
        $response = json_decode($response);
        return $response;
    }

    function transtore()
    {
        $url = "https://qyapi.weixin.qq.com/cgi-bin/user/update?access_token=". $this->gettoken($this->qwOaCorpsecret) ;
        $response = $this->ajax($url, [], 'GET');
        $response = json_decode($response);
        return $response;
    }


    public function ajax($url, $data, $type = 'POST')
    {
        // 创建CURL句柄
        $ch = curl_init();
        // 设置请求的URL地址
        curl_setopt($ch, CURLOPT_URL, $url);
        // 设置请求头信息
        curl_setopt($ch, CURLOPT_HTTPHEADER, array(
            'Content-Type: application/json; charset=utf-8'
        ));
        // 设置请求方法
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, $type);
        // 设置传递的数据
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        // 设置返回数据不直接输出
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);    // 信任任何证
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
        // 执行请求并获取响应数据
        curl_setopt($ch, CURLOPT_HTTP_VERSION, CURL_HTTP_VERSION_1_0);
        $response = curl_exec($ch);

        // 输出响应数据
        if (is_bool($response)) {
            var_dump(curl_error($ch));
            exit();
        }
        // 关闭CURL句柄
        curl_close($ch);
        return $response;
    }
}