<?php
/**
 * 完整分红数据计算脚本 - 2025年4月
 * 按顺序执行：门店分红计算 → 公司股东分红计算 → 分红人清单计算
 * 
 * 使用方法：
 * php full_calculate_2025_04.php
 */

// 引入ThinkPHP框架
require_once __DIR__ . '/../../../vendor/autoload.php';

use think\facade\Db;
use think\facade\Config;
use app\dividend\service\DividendCalculateService;
use app\dividend\service\DividendCompanyCalculateService;
use app\dividend\service\DividendPaymentCalculateService;

// 初始化应用
$app = new think\App();
$app->initialize();

echo "=== 完整分红数据计算脚本 ===\n";
echo "计算周期：2025年4月\n";
echo "开始时间：" . date('Y-m-d H:i:s') . "\n";
echo "================================\n\n";

$period = '2025-04';
$totalStartTime = time();

try {
    // 第一步：门店分红计算
    echo "🔄 第一步：执行门店分红计算...\n";
    $stepStartTime = time();
    
    $storeResult = DividendCalculateService::calculateDividend($period);
    
    if ($storeResult['success']) {
        echo "✅ 门店分红计算成功！\n";
        echo "消息：{$storeResult['message']}\n";
        echo "耗时：" . (time() - $stepStartTime) . " 秒\n\n";
        
        // 显示门店分红统计
        $storeStats = DividendCalculateService::getCalculateStatistics($period);
        echo "📊 门店分红统计：\n";
        echo "门店数量：{$storeStats['store_count']} 个\n";
        echo "总分红利润：" . number_format($storeStats['total_dividend_profit'], 2) . " 元\n\n";
    } else {
        throw new \Exception("门店分红计算失败：{$storeResult['message']}");
    }

    // 第二步：公司股东分红计算
    echo "🔄 第二步：执行公司股东分红计算...\n";
    $stepStartTime = time();
    
    $companyResult = DividendCompanyCalculateService::calculateCompanyDividend($period);
    
    if ($companyResult['success']) {
        echo "✅ 公司股东分红计算成功！\n";
        echo "消息：{$companyResult['message']}\n";
        echo "耗时：" . (time() - $stepStartTime) . " 秒\n\n";
        
        // 显示公司股东分红统计
        $companyStats = DividendCompanyCalculateService::getCalculateStatistics($period);
        echo "📊 公司股东分红统计：\n";
        echo "门店数量：{$companyStats['store_count']} 个\n";
        echo "总分红利润：" . number_format($companyStats['total_dividend_profit'], 2) . " 元\n\n";
    } else {
        throw new \Exception("公司股东分红计算失败：{$companyResult['message']}");
    }

    // 第三步：分红人清单计算
    echo "🔄 第三步：执行分红人清单计算...\n";
    $stepStartTime = time();
    
    $paymentResult = DividendPaymentCalculateService::calculatePaymentData($period);
    
    if ($paymentResult['success']) {
        echo "✅ 分红人清单计算成功！\n";
        echo "消息：{$paymentResult['message']}\n";
        echo "耗时：" . (time() - $stepStartTime) . " 秒\n\n";
        
        // 显示分红人清单统计
        $paymentStats = DividendPaymentCalculateService::getCalculateStatistics($period);
        echo "📊 分红人清单统计：\n";
        echo "分红人数量：{$paymentStats['shareholder_count']} 人\n";
        echo "总应付金额：" . number_format($paymentStats['total_payable_amount'], 2) . " 元\n\n";
    } else {
        throw new \Exception("分红人清单计算失败：{$paymentResult['message']}");
    }

    // 汇总报告
    echo "🎉 所有计算步骤完成！\n";
    echo str_repeat('=', 80) . "\n";
    echo "📈 最终汇总报告：\n";
    echo str_repeat('-', 80) . "\n";
    
    echo "门店分红：\n";
    echo "  - 门店数量：{$storeStats['store_count']} 个\n";
    echo "  - 总收入：" . number_format($storeStats['total_income'], 2) . " 元\n";
    echo "  - 总支出：" . number_format($storeStats['total_expense'], 2) . " 元\n";
    echo "  - 总分红利润：" . number_format($storeStats['total_dividend_profit'], 2) . " 元\n\n";
    
    echo "公司股东分红：\n";
    echo "  - 门店数量：{$companyStats['store_count']} 个\n";
    echo "  - 总分红利润：" . number_format($companyStats['total_dividend_profit'], 2) . " 元\n\n";
    
    echo "分红人清单：\n";
    echo "  - 分红人数量：{$paymentStats['shareholder_count']} 人\n";
    echo "  - 总应付金额：" . number_format($paymentStats['total_payable_amount'], 2) . " 元\n";
    echo "  - 总实际应付：" . number_format($paymentStats['total_actual_payable_amount'], 2) . " 元\n";
    echo "  - 总未付金额：" . number_format($paymentStats['total_unpaid_amount'], 2) . " 元\n\n";
    
    echo str_repeat('-', 80) . "\n";
    echo "总耗时：" . (time() - $totalStartTime) . " 秒\n";
    
} catch (\Exception $e) {
    echo "❌ 计算过程中发生异常！\n";
    echo "异常信息：" . $e->getMessage() . "\n";
    echo "异常文件：" . $e->getFile() . " 第 " . $e->getLine() . " 行\n";
    echo "总耗时：" . (time() - $totalStartTime) . " 秒\n";
    exit(1);
}

echo "\n================================\n";
echo "结束时间：" . date('Y-m-d H:i:s') . "\n";
echo "=== 完整分红数据计算完成 ===\n";
