{extend name="../../base/view/common/base" /}
{block name="style"}
<link rel="stylesheet" href="{__GOUGU__}/gougu/css/layout.css" media="all">
{/block}
<!-- 主体 -->
{block name="body"}
<div class="layui-layout-body">
    <div id="GouguApp">
        <div class="layui-layout gg-layout layout-menu-{$web.menu_mode|default='classical'}">
            <div class="layui-header">
                <!-- 头部区域 -->
                <div class="layui-layout-left">
                    <span class="gg-head-item">
                        <a href="javascript:;" gg-event="shrink" title="侧边伸缩"><i class="layui-icon layui-icon-shrink-right"></i></a>
                    </span>
                    <span class="gg-head-item gg-head-cache">
                        <a href="javascript:;" gg-event="cache" data-href="/api/index/cache_clear" title="清空缓存"><i class="layui-icon layui-icon-fonts-clear"></i></a>
                    </span>
                    <span class="gg-head-item gg-head-home">
                        <a href="" target="_blank" title="前台首页"><i class="layui-icon layui-icon-website"></i></a>
                    </span>
                </div>

                <div class="layui-layout-right">
                    <span class="gg-head-item gg-head-refresh">
                        <a href="javascript:;" class="refreshThis" gg-event="refresh" title="刷新页面">
                            <i class="layui-icon layui-icon-refresh"></i>
                        </a>
                    </span>
                    <span class="gg-head-item gg-head-screen" title="切换全屏">
                        <a href="javascript:;" gg-event="screen" data-screen="full">
                            <i class="fullScreen layui-icon layui-icon-screen-full"></i>
                        </a>
                    </span>
					<span class="gg-head-item gg-head-set">
                        <a href="javascript:;" id="theme" title="切换主题">
                            <i class="layui-icon layui-icon-set"></i>
                        </a>
                    </span>
					<span class="gg-head-item gg-head-set">
                        <a href="/home/<USER>/lock.html" title="锁屏">
                            <i class="iconfont icon-suozhu" style="font-size:21px; font-weight:800"></i>
                        </a>
                    </span>
                    <span class="gg-head-item gg-head-message">
                        <a href="javascript:;" data-href="/message/index/inbox" data-id="1000" data-title="消息中心" class="tab-a" title="消息中心">
                            <i class="layui-icon layui-icon-notice"></i>
                            <!-- 如果有新消息，则显示 -->
                            <div class="gg-message-num" id="msgNum" style="display: none;"><span>0</span></div>
                        </a>
                    </span>
                    <span class="gg-head-item gg-head-avatar">
                        <ul class="layui-nav">
                            <li class="layui-nav-item">
                                <a href="javascript:;">
									<img src="{$login_admin.thumb}" onerror="javascript:this.src='{__IMG__}/nonepic360x360.jpg';this.onerror=null;">
                                    <cite>{$login_admin.name}</cite>
                                </a>
                                <dl class="layui-nav-child" style="text-align: center; cursor: pointer;">
                                    <dd><a data-href="/home/<USER>/edit_personal" data-id="1001" data-title="基本资料" class="tab-a">基本资料</a></dd>
                                    <dd><a data-href="/home/<USER>/edit_password" data-id="1002" data-title="修改密码" class="tab-a">修改密码</a></dd>
                                    <hr>
                                    <dd gg-event="logout"><a>退出</a></dd>
                                </dl>
                            </li>
                        </ul>
                    </span>
                </div>
            </div>

            <!-- 侧边菜单 -->
			{empty name="$web.menu_mode"}
				{include file="/index/menu_classical" /}
			{else/}
				{if ($web.menu_mode == 'expand') }
					{include file="/index/menu_expand" /}
				{else/}
					{include file="/index/menu_classical" /}
				{/if}
			{/empty}
            <!-- 页面标签 -->
            <div id="pageTabs" class="page-tabs">
                <div class="layui-icon gg-tabs-control layui-icon-prev" gg-event="tabRollLeft"></div>
                <div class="layui-icon gg-tabs-control layui-icon-next" gg-event="tabRollRight"></div>
                <div class="layui-icon gg-tabs-control layui-icon-down">
                    <ul class="layui-nav gg-tabs-select">
                        <li class="layui-nav-item">
                            <a href="javascript:;"></a>
                            <dl class="layui-nav-child layui-anim-fadein">
                                <dd gg-event="closeThisTabs"><a href="javascript:;">关闭当前</a></dd>
                                <dd gg-event="closeOtherTabs"><a href="javascript:;">关闭其它</a></dd>
                                <dd gg-event="closeAllTabs"><a href="javascript:;">关闭全部</a></dd>
                            </dl>
                        </li>
                    </ul>
                </div>
                <div class="layui-tab gg-admin-tab" lay-unauto lay-allowClose="true" lay-filter="gg-admin-tab">
                    <ul class="layui-tab-title" id="pageTabUl">
                        <li lay-id="0" lay-attr="view/home/<USER>" class="layui-this"><i class="iconfont icon-xueshuguanli"></i></li>
                    </ul>
                </div>
            </div>

            <!-- 主体内容 -->
            <div class="layui-body" id="GouguAppBody">
                <div class="gg-tab-page layui-show" id="tabItem0" data-id="0">
                    <iframe id="0" data-frameid="0" title="仲正堂OA" name="myiframe" src="{:url('/home/<USER>/main')}" frameborder="0" data-timestamp="0" align="left" width="100%" height="100%" scrolling="yes"></iframe>
                </div>
            </div>
            <!-- 辅助元素，用于移动设备下遮罩 -->
            <div class="gg-body-shade" gg-event="shade"></div>
            <div class="notification" style="display:none;">
				<audio id="msgSound">
					<source src="{__STATIC__}/home/<USER>/msg.mp3" type="audio/mpeg">
				</audio>
			</div>
        </div>
    </div>
</div>
<!-- /主体 -->
{/block}
<!-- 脚本 -->
{block name="script"}
<script>
	const msg_sound = {$web.msg_sound|default=1};
	const watermark = {$web.watermark|default=1};
	const watermarkTxt = '{$admin.name|default="仲正堂OA"} {:substr($admin.mobile, -4)}';
	const moduleInit = ['tool','admin','notice'];

	function gouguInit() {
        var notice = layui.notice; // 允许别名 toastr

		let admin = layui.admin;
		let tabs = admin.getCookie('gougutab');
		if(tabs && tabs!=''){
			let tab_parse = JSON.parse(tabs);
			let tab_id = tab_parse.tab_id,tab_array = tab_parse.tab_array;
			if(tab_array.length>0){
				for(let a=0; a<tab_array.length;a++){
					admin.tabTem(tab_array[a].id, tab_array[a].url,tab_array[a].title);
				}
				if(tab_id>0){
					admin.tabChange(tab_id);
				}
			}
		}
		admin.loading();
		menuInit();
		if(watermark==1){
			createWatermark();
		}
		$('#GouguApp').on("click",'[gg-event="logout"]',function () {
			layer.confirm('确认注销登录吗?', { icon: 7, title: '警告' }, function (index) {
				//注销
				$.ajax({
					url: "/home/<USER>/login_out",
					success: function (e) {
						layer.msg(e.msg);
						if (e.code == 0) {
							setTimeout(function () {
								location.href = "{:url('home/login/index')}"
							}, 1000)
						}
					}
				})
				layer.close(index);
			});
		});
		layui.dropdown.render({
			elem: '#theme',
			trigger: 'mousedown',
			align: 'center',
			data: [{
				title: '经典黑',
				theme: 'black'
			},{
				title: '简约白',
				theme: 'white'
			},{
				title: '海军蓝',
				theme: 'blue'
			}],
			click: function(data, othis){
				$.ajax({
					url: "/home/<USER>/set_theme",
					data:{'theme':data.theme},
					success: function (e) {
						layer.msg(e.msg);
						if (e.code == 0) {
							setTimeout(function () {
								parent.location.reload();
							}, 1000)
						}
					}
				})
			}
		});
		
		//轮循获取消息
		let msg_num = 0,msg_bool=false;
		const msgSound = document.getElementById("msgSound");
		$(document).ready(function(){
			$(document).one('click',function(){
				msg_bool = true;
				//console.log("页面被点击了");
			});
		});
		
		$("#GouguAppBody").find('.gg-tab-page.layui-show iframe').on("load", function() {
			var iframeDocument = $(this).contents();
			iframeDocument.one('click',function(){
				msg_bool = true;
				//console.log("子页面被点击了");
			});
		});	
		
		// var getStatus = setInterval(function () {
		//
		// }, 10000);

        $.ajax({
            url: "/home/<USER>/index",
            type:'post',
            success:function(e){
                if(e.code==0 && e.data!==''){

                    console.log(e.data.count)

                    if(e.data.count > 0){
                        $('#msgNum').show().find('span').html(e.data.count);
                    }else{
                        $('#msgNum').hide().find('span').html(0);
                    }
                    for (let i = 0; i < e.data.list.length; i++) {
                        // 初始化配置，同一样式只需要配置一次，非必须初始化，有默认配置
                        if (e.data.list[i].m_status == 2){
                            notice.error(e.data.list[i].title + " " + e.data.list[i].create_time);
                        }else{
                            notice.info(e.data.list[i].title  + " " +  e.data.list[i].create_time);
                        }
                    }
                }else{
                    layer.closeAll();
                }
            }
        })


        // notice.warning("这是一个警告通知");
        // notice.info("这是一个信息通知");
        // notice.error("这是一个错误通知");
        // notice.success("这是一个成功通知");


	}
	function createWatermark(){
		var canvas = document.createElement('canvas');
		canvas.width = 300;
		canvas.height = 200;
		var ctx = canvas.getContext('2d');
		ctx.font = '14px Arial';
		ctx.fillStyle = '#dddddd';
		ctx.textAlign = 'left';
		ctx.textBaseline = 'top';
		ctx.rotate(15 * Math.PI / 180);
		ctx.fillText(watermarkTxt, 20, 50);
		ctx.fillText(watermarkTxt, 180, 120);
		var dataURL = canvas.toDataURL('image/png');
		let node = document.createElement("div");
		node.style.pointerEvents = "none";
		node.style.position = "fixed";
		node.style.width = "100%";
		node.style.height = "100%";
		node.style.top = "0";
		node.style.left = "0";
		node.style.opacity = "0.382";
		node.style.zIndex = "998";
		node.style.background = 'url("' + dataURL + '")  0 0 repeat';
		// 将创建的元素插入body中，作为body的子元素
		document.body.appendChild(node);
	}
</script>
{/block}
<!-- /脚本 -->