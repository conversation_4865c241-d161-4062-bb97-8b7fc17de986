-- 次卡跨店结算明细表建表脚本
-- 创建时间：2025-07-23
-- 说明：用于存储次卡跨店消费的详细结算记录

CREATE TABLE `oa_cross_store_settle_card_detail` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `settlement_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '结算类型',
  `consume_store_id` int unsigned NOT NULL DEFAULT 0 COMMENT '消费门店ID (关联 oa_department.id)',
  `product_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '商品名称',
  `product_quantity` int unsigned NOT NULL DEFAULT 0 COMMENT '商品数量',
  `card_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '次卡名称',
  `settlement_amount` decimal(15,2) NOT NULL DEFAULT '0.00' COMMENT '结算金额',
  `settlement_time` datetime DEFAULT NULL COMMENT '结算时间',
  `card_open_store_id` int unsigned NOT NULL DEFAULT 0 COMMENT '开卡门店ID (关联 oa_department.id)',
  `order_number` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '发生订单号',
  `related_order_number` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '关联订单号',
  `customer_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '客户姓名',
  `customer_mobile` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '客户手机号',
  `customer_belong_store_id` int unsigned NOT NULL DEFAULT 0 COMMENT '客户归属门店ID (关联 oa_department.id)',
  `period` varchar(7) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '快照月份 (格式: YYYY-MM)',
  `create_time` int NOT NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int NOT NULL DEFAULT 0 COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_consume_store_id` (`consume_store_id`) USING BTREE COMMENT '消费门店索引',
  KEY `idx_card_open_store_id` (`card_open_store_id`) USING BTREE COMMENT '开卡门店索引',
  KEY `idx_customer_belong_store_id` (`customer_belong_store_id`) USING BTREE COMMENT '客户归属门店索引',
  KEY `idx_settlement_time` (`settlement_time`) USING BTREE COMMENT '结算时间索引',
  KEY `idx_order_number` (`order_number`) USING BTREE COMMENT '订单号索引',
  KEY `idx_customer_mobile` (`customer_mobile`) USING BTREE COMMENT '客户手机号索引',
  KEY `idx_period` (`period`) USING BTREE COMMENT '快照月份索引'
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='次卡跨店结算明细表';
