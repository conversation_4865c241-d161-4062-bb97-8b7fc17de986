{extend name="../../base/view/common/base" /}
{block name="style"}
<style>
.checkbox16 .layui-form-checkbox span{font-size:15px;font-weight:800;}
.layui-checkbox-disabled span,.layui-checkbox-disabled[lay-skin=primary] span{color: #666666!important;}
.layui-td-gray{width: 80px;}
.layui-td-gray{color: #2f2f2f;font-weight: bolder;font-size: 18px}
td{color: #6b6a6a;
	font-size: 16px!important;
}

.layui-table-form-scx .layui-td-gray{width: 8%}
.layui-table-form-scx td{width: 42%}
.pb-3{color: #0f6674}


/** 文件 **/
.subject { flex: 1; overflow: hidden; overflow-y: auto; margin: 10px; box-sizing: border-box; }
.subject:hover::-webkit-scrollbar { display: block; }
.subject ul { display: flex; flex-wrap: wrap; }
.subject ul li { position: relative; height: 120px; margin: 5px; padding: 9px; border: 1px solid rgba(0, 0, 0, 0.05); border-radius: 3px; transition: all 0.2s ease-in-out; }
.subject ul li:hover {border: 1px solid #cccccc; }
.subject ul li.on {border: 1px solid #ff5722; }
.subject ul li img { width: 100px; height: 100px; border-radius: 2px; }
.subject ul li video { width: 100px; height: 100px; border-radius: 3px; }
.subject ul li p {overflow: hidden; margin: 5px 0 0; width: 98px; font-size: 13px; text-align: center; text-overflow: ellipsis; white-space: nowrap; }
.subject ul li .file-check{position: absolute; width:22px; height:22px; right: 0; bottom: 0; display: none; font-size: 14px; border-radius:4px 0 2px 0; text-align: center; line-height: 22px; color: #ffffff; background-color:#fff; border-left:1px solid #ccc; border-top:1px solid #ccc; cursor:pointer;}
.subject ul li:hover .file-check{display: block;}
.subject ul li.on .file-check{ display: block; background: #ff5722; border-color:#ff5722}
.subject ul li .layui-btn-group{position:absolute; top:3px; right:3px; display:none;}
.subject ul li:hover .layui-btn-group{display:block}
.train_sign_result_title{
	display: inline-block;width: 80px;font-weight: bolder;
}
</style>
{/block}
<!-- 主体 -->
{block name="body"}
<form class="layui-form p-4">
	<input type="hidden" name="id" value="{$detail.id}"/>
	<div class="layui-tabs layui-tabs-card" lay-options="{index: 0}">
		<ul class="layui-tabs-header">
			<li>员工信息 {if condition="$detail.is_black == 1"}<span style="color: red">注意：人员已添加黑名单</span>{/if}</li>
			<li>薪资信息</li>
			<li>社保信息</li>
			<li>个人信息</li>
			<li>学历信息</li>
			<li>合同信息</li>
			<li>工资卡信息</li>
			<li><a href="/user/adminview/interview_record?id={$detail.a_view_id}" target="_blank" class="layui-font-blue">面试记录</a></li>
			<li><a href="/user/adminview/examine_record?id={$detail.a_view_id}" target="_blank" class="layui-font-blue">考核记录</a></li>
			<li>培训记录</li>
		</ul>
		<div class="layui-tabs-body">
			<div class="layui-tabs-item">
				<table class="layui-table layui-table-form">
					<tr>
						<td  class="layui-td-gray">头像</td>
						<td  valign="top" style="width: 100px;">
							<div class="layui-upload">
								<img src="{$detail.thumb}" style="width:100px; height:100px; max-width:100%" />
							</div>
						</td>
					</tr>
					<tr>
						<td class="layui-td-gray">登录账号</td>
						<td>
							{$detail.username}
						</td>
						<td class="layui-td-gray">登录密码</td>
						<td>
							{empty name="$detail.reg_pwd"}
							密码已重置
							<span class="layui-btn layui-btn-normal layui-btn-xs reset-psw" data-id="{$detail.id}">重设新密码</span>
							{else/}
							{$detail.reg_pwd}
							<span class="layui-btn layui-btn-normal layui-btn-xs" onclick="copyToClip('{$detail.reg_pwd}');">复制初始密码</span>
							{/empty}
						</td>
						<td class="layui-td-gray">员工状态</td>
						<td>
							{eq name="$detail.status" value="0"}<span style="color:#FF5722">禁止登录</span>{/eq}
							{eq name="$detail.status" value="1"}<span style="color:#009688">正常</span>{/eq}
							{eq name="$detail.status" value="2"}<span style="color:#FFB800">已离职</span>{/eq}
						</td>

					</tr>
					<tr>
						<td class="layui-td-gray">员工姓名</td>
						<td>
							{$detail.name}
							{eq name="$detail.sex" value="1"}  <span class="layui-badge layui-bg-green">男</span>{/eq}
							{eq name="$detail.sex" value="2"}  <span class="layui-badge layui-bg-red">女</span>{/eq}
						</td>
						<td class="layui-td-gray">手机号码</td>
						<td>
							{$detail.mobile}
						</td>
						<td class="layui-td-gray">电子邮箱</td>
						<td>
							{$detail.email}
						</td>
					</tr>
					<tr>
						<td class="layui-td-gray">一级部门</td>
						<td>{$detail.d0}</td>

						<td class="layui-td-gray">二级部门</td>
						<td>{$detail.d1}</td>


					</tr>
					<tr>
						<td class="layui-td-gray">三级部门</td>
						<td>{$detail.d2}</td>

						<td class="layui-td-gray">四级部门</td>
						<td>{$detail.d3}</td>
					</tr>
					<tr>
						<td class="layui-td-gray">岗位职称</td>
						<td>{$detail.position}</td>
						<td class="layui-td-gray">职级</td>
						<td>{$detail.rank_name}</td>
						<td class="layui-td-gray">标签</td>
						<td>{$detail.label_name}</td>
					</tr>
					<tr>
						<td class="layui-td-gray">兼岗</td>
						<td colspan="5">{$detail.o_admin_dep}</td>
					</tr>


					<tr>
						<td class="layui-td-gray">员工类型</td>
						<td colspan="2">
							{eq name="$detail.type" value="3"}<span style="color:#5FB878">实 习 生</span>{/eq}
							{eq name="$detail.type" value="2"}<span style="color:#01AAED">试用员工</span>{/eq}
							{eq name="$detail.type" value="1"}<span style="color:#393D49">正式员工</span>{/eq}
						</td>
						<td class="layui-td-gray">司龄</td>
						<td colspan="2">
							{$detail.c_age}
						</td>

					</tr>

					<tr>
						<td class="layui-td-gray">入职日期</td>
						<td colspan="2">
							{$detail.entry_time | date='Y-m-d'}
						</td>
						<td class="layui-td-gray">离职日期</td>
						<td colspan="2">
							{$detail.res_date}
						</td>
					</tr>

					<tr>
						<td class="layui-td-gray">黑名单</td>
						<td>
							{if condition="($detail.is_black == 1)"} <span style="color: red;font-weight: bolder"> 是 </span>{/if}
							{if condition="($detail.is_black == 2)"} 否 {/if}
						</td>
					</tr>

					<tr>
						<td class="layui-td-gray">员工简介</td>
						<td colspan="7">
							{$detail.desc|default=""}
						</td>
					</tr>
				</table>
			</div>
			<div class="layui-tabs-item">
				<table class="layui-table layui-table-form layui-table-form-scx">
					<tr>
						<td class="layui-td-gray" >岗位工资</td>
						<td>
							{$detail.position_salary}
						</td>
						<td class="layui-td-gray" >基本工资</td>
						<td>
							{$detail.base_salary}
						</td>
					</tr>
					<tr>
						<td class="layui-td-gray">绩效工资</td>
						<td>
							{$detail.performance_salary}
						</td>
						<td class="layui-td-gray">试用期工资</td>
						<td>
							{$detail.trial_salary}
						</td>
					</tr>
				</table>
			</div>
			<div class="layui-tabs-item">
				<table class="layui-table layui-table-form layui-table-form-scx">
					<tr>
						<td class="layui-td-gray">缴纳类型</td>
						<td>
							{if condition="($detail.pay_type == 0)"}
							不缴纳
							{elseif condition="($detail.pay_type == 1)"}
							公积金
							{elseif condition="($detail.pay_type == 2)"}
							社保
							{elseif condition="($detail.pay_type == 3)"}
							公积金&社保
							{/if}
						</td>
						<td class="layui-td-gray">代缴门店</td>
						<td>{$detail.pay_type_dname}</td>
					</tr>
				</table>
			</div>
			<div class="layui-tabs-item">
				<table class="layui-table layui-table-form layui-table-form-scx">
					<tr>
						<td class="layui-td-gray" >年龄</td>
						<td>
							{$detail.age}
						</td>
						<td class="layui-td-gray" >生日</td>
						<td>
							{$detail.birthday}
						</td>
					</tr>
					<tr>
						<td class="layui-td-gray">民族</td>
						<td>
							{$detail.ethnicity}
						</td>

					</tr>
					<tr>
						<td class="layui-td-gray">紧急联系人</td>
						<td>
							{$detail.emergency_contact}
						</td>
						<td class="layui-td-gray">联系人电话</td>
						<td>
							{$detail.contact_mobile}
						</td>
					</tr>

					<tr>
						<td class="layui-td-gray">关系</td>
						<td>
							{$detail.relationship}
						</td>
					</tr>

					<tr>
						<td class="layui-td-gray">籍贯</td>
						<td>
							{$detail.origin_place}
						</td>
						<td class="layui-td-gray">婚姻</td>
						<td>
							{eq name="$detail.marriage_status" value="1"}<span>未婚</span>{/eq}
							{eq name="$detail.marriage_status" value="2"}<span>已婚</span>{/eq}
							{eq name="$detail.marriage_status" value="3"}<span>没男朋友</span>{/eq}
						</td>
					</tr>

					<tr>
						<td class="layui-td-gray">残疾证</td>
						<td colspan="3">
							{if condition="$detail.is_cjz == null"}
							未填写
							{elseif condition="$detail.is_cjz == 1"/}
							有
							{else/}
							无
							{/if}
						</td>
					</tr>


					<tr>
						<td class="layui-td-gray">身份证号</td>
						<td colspan="3">
							{$detail.id_card_number}
						</td>
					</tr>
					<tr>
						<td class="layui-td-gray">身份证有效期</td>
						<td >
							{if condition="$detail.is_valid == 1"}
							<span class="layui-btn layui-btn-success" data-status="1">长期有效</span>
							{/if}
							有效期 从 {$detail.valid_start_date} 至 {$detail.valid_end_date}
						</td>
					</tr>
					<tr>

					</tr>

					<tr>
						<td class="layui-td-gray">身份证正面</td>
						<td>
							<div style="width: 100%;text-align: center">
								<div class="layui-upload-list">
									<img class="layui-upload-img" {if condition='!empty($detail.front_idcard)'}src="{$detail.front_idcard_file}"{/if} id="ID-upload-front_idcard" style="width: 100%">
								</div>
								<div class="layui-progress layui-progress-big" lay-showPercent="yes" lay-filter="filter-demo" hidden="hidden">
									<div class="layui-progress-bar" lay-percent=""></div>
								</div>
								<input type="text" id="front_idcard" name="front_idcard" value="{$detail.front_idcard}"  hidden="hidden" >
							</div>
						</td>
						<td class="layui-td-gray">身份证反面</td>
						<td>
							<div style="width: 100%;text-align: center">
								<div class="layui-upload-list">
									<img class="layui-upload-img" {if condition='!empty($detail.reverse_idcard)'}src="{$detail.reverse_idcard_file}"{/if} id="ID-upload-reverse_idcard" style="width: 100%">
								</div>
								<div class="layui-progress layui-progress-big" lay-showPercent="yes" lay-filter="filter-demo" hidden="hidden">
									<div class="layui-progress-bar" lay-percent=""></div>
								</div>
								<input type="text" id="reverse_idcard" name="reverse_idcard" value="{$detail.reverse_idcard}"  hidden="hidden" >
							</div>
						</td>
					</tr>
					<tr>
						<td class="layui-td-gray">现住址</td>
						<td colspan="3">
							{$detail.current_address}
						</td>
					</tr>
					<tr>
						<td class="layui-td-gray">家庭住址</td>
						<td colspan="3">
							{$detail.home_address}
						</td>
					</tr>

				</table>
			</div>
			<div class="layui-tabs-item">
				<table class="layui-table layui-table-form layui-table-form-scx">
					<tr>
						<td class="layui-td-gray">毕业学校</td>
						<td>
							{$detail.graduation_school}
						</td>
						<td class="layui-td-gray">专业</td>
						<td>
							{$detail.major}
						</td>
					</tr>
					<tr>
						<td class="layui-td-gray" >学历</td>
						<td>{$detail.education}</td>
						<td class="layui-td-gray" >技能证书</td>
						<td>{$detail.certificates}</td>
					</tr>
					<tr>
						<td class="layui-td-gray">技能证书附件</td>
						<td colspan="3">
							<div class="subject">
								<ul id="filesBox">

								</ul>
							</div>
							<input type="text" id="certificate_files" name="certificate_files" value="{$detail.certificate_files}" hidden="hidden">
						</td>
					</tr>

				</table>
			</div>
			<div class="layui-tabs-item">
				<table class="layui-table layui-table-form layui-table-form-scx">
					<tr>
						<td class="layui-td-gray" >合同类别</td>
						<td colspan="3" width="94%">
							{$detail.contract_type}
						</td>
					</tr>
					<tr>
						<td class="layui-td-gray" >开始时间</td>
						<td>
							{$detail.contract_start_date}
						</td>
						<td class="layui-td-gray" >结束时间</td>
						<td>
							{$detail.contract_end_date}
						</td>
					</tr>
					<tr>
						<td class="layui-td-gray">合同ID</td>
						<td colspan="3">
							<a href="{$detail.contract_link}" target="_blank">{$detail.contract_link}</a>
						</td>
					</tr>
					<tr>
						<td class="layui-td-gray">合同附件</td>
						<td colspan="3">
							<div class="subject">
								<ul id="filesBox2"></ul>
							</div>
							<input type="text" id="contract_files" name="contract_files" value="{$detail.contract_files}" hidden="hidden">
						</td>
					</tr>
				</table>
			</div>
			<div class="layui-tabs-item">
				<table class="layui-table layui-table-form layui-table-form-scx">
					<tr>
						<td class="layui-td-gray" >开户行</td>
						<td>
							{$detail.bank_name}
						</td>
						<td class="layui-td-gray" >工资卡号</td>
						<td>
							{$detail.salary_card_number}
						</td>
					</tr>
				</table>
			</div>
			<div class="layui-tabs-item">内容-4</div>
			<div class="layui-tabs-item">内容-4</div>
			<div class="layui-tabs-item">
				<table class="layui-hide" id="ID-table-demo-css"></table>
			</div>
		</div>
	</div>

	<script type="text/html" id="ID_train_sign_result">
		<ul>
			<li><span class="train_sign_result_title">培训名称:</span> {{= d.trainName }} </li>
			<li><span class="train_sign_result_title">培训分类:</span> {{= d.categoryName }} </li>
		</ul>
	</script>
	<script type="text/html" id="ID_train_sign_result_user">
		<ul>
			<li><span class="train_sign_result_title">姓名:</span> {{= d.姓名 }} </li>
			<li><span class="train_sign_result_title">门店:</span> {{= d.门店 }} </li>
			<li><span class="train_sign_result_title">培训时间:</span> {{= d.培训时间 }} </li>
			<li><span class="train_sign_result_title">是否合格:</span> {{= d.是否合格 }} </li>
		</ul>
	</script>
<!--	<h3 class="pb-3">员工信息 {if condition="$detail.is_black == 1"}<span style="color: red">注意：人员已添加黑名单</span> {/if}</h3>-->


<!--	<h3 class="pb-3">薪资信息</h3>-->


<!--	<h3 class="pb-3">社保信息</h3>-->


<!--	<h3 class="pb-3">个人信息</h3>-->


<!--	<h3 class="pb-3">学历信息</h3>-->


<!--	<h3 class="pb-3">合同信息</h3>-->


<!--	<h3 class="pb-3">工资卡信息</h3>-->

	

<!--	<div class="layui-collapse">-->
<!--		<div class="layui-colla-item">-->
<!--			<h3 class="layui-colla-title">用户权限</h3>-->
<!--		  	<div class="layui-colla-content layui-hide">-->
<!--				<table class="layui-table layui-table-form">-->
<!--					{volist name="role_rule" id="vo"}-->
<!--					<tr>-->
<!--						<td style="font-weight:800; text-align:center;">-->
<!--						  <input type="checkbox" disabled value="{$vo.id}" title="{$vo.title}" {eq name="$vo.checked" value="true" }checked{/eq}>-->
<!--						</td>-->
<!--						{notempty name="vo.children"}-->
<!--							<td>-->
<!--								<div style="padding:0 0 0 10px;">-->
<!--								{volist name="vo.children" key="k" id="voo"}-->
<!--									<div class="checkbox16" style="padding:10px 0;">-->
<!--										<input type="checkbox" disabled lay-filter="rule" name="rule[]" value="{$voo.id}" lay-skin="primary" title="{$voo.title}" {eq name="$voo.checked" value="true" }checked{/eq}>-->
<!--									</div>-->
<!--									{notempty name="voo.children"}-->
<!--										<div style="padding:0 0 3px; {if condition='$k != count($vo.children)'}margin-bottom:3px; padding-bottom:16px; border-bottom:1px solid #eee;{/if}">-->
<!--										{volist name="voo.children" id="vooo"}-->
<!--											<div class="layui-input-inline" style="margin-right:10px;">-->
<!--												<input type="checkbox" disabled data-rule="{$voo.id}" name="rule[]" value="{$vooo.id}" lay-skin="primary" title="{$vooo.title}" {eq name="$vooo.checked" value="true" }checked{/eq}>-->
<!--											</div>-->
<!--										{/volist}-->
<!--										</div>-->
<!--									{/notempty}-->
<!--								{/volist}-->
<!--								</div>-->
<!--							</td>-->
<!--						{/notempty}-->
<!--					</tr>-->
<!--					{/volist}-->
<!--				</table>-->
<!--			</div>-->
<!--		</div>-->
<!--	  </div>-->
</form>
{/block}
<!-- /主体 -->

<!-- 脚本 -->
{block name="script"}
<script>
	const moduleInit = ['tool','tablePlus'];
	function gouguInit() {
		var tool=layui.tool;
		var table = layui.tablePlus
		//重设密码
		$('.reset-psw').on('click',function(){
			let id=$(this).data('id');
			layer.confirm('确定要重设该用户的密码？', {
				icon: 3,
				title: '提示'
			}, function (index) {
				let callback = function (e) {
					layer.msg(e.msg);
					if (e.code == 0) {
						layer.msg(e.msg);
						if(e.code==0){
							setTimeout(function(){
								location.reload();
							},2000);
						}	
					}
				}
				tool.post("/user/admin/reset_psw",{id: id}, callback);
				layer.close(index);
			})	
		});

		table.render({
			elem: '#ID-table-demo-css',
			url:'/user/train/train_sign_result_aid', // 此处为静态模拟数据，实际使用时需换成真实接口
			where:{aid:$('[name="id"]').val()},
			page: false,
			height: 'full-35',
			lineStyle: 'height: 151px;', // 定义表格的多行样式
			css: [ // 直接给当前表格主容器重置 css 样式
				'.layui-table-page{text-align: center;}' // 让分页栏居中
			].join(''),
			className: 'layui-table-testcss', // 用于给表格主容器追加 css 类名
			cols: [[
				{field:'username', width:300, title: '培训信息', templet: '#ID_train_sign_result'},
				{field:'username', width:300, title: '基础信息', templet: '#ID_train_sign_result_user'},
				// 设置单元格样式
				{field:'其他', minWidth:100, title: '分数结论', style:'color: #000;',
					templet: function (d) {
						let html = `${d.其他}`;
						return html;
					}}
			]]
		});


		var element = layui.element;
		$('body').on('click', '.layui-upload-img', function () {
			let href = $(this).attr('src');
			if (href) {
				let photos = {"data": [{"src": href}]};
				layer.photos({
					photos: {
						"title": "Photos Demo",
						"start": 0,
						"data": [
							{
								"alt": "浩瀚宇宙",
								"pid": 5,
								"src": href,
							}
						]
					},
					footer: false // 是否显示底部栏 --- 2.8.16+
				});
			}
		});

		ajax_contract_f()
		function ajax_contract_f(){
			let contract_f = $('[name="contract_files"]').val();
			let certificate_f = $('[name="certificate_files"]').val();
			$.ajax({
				url: "/api/index/getFiles",
				type:'post',
				data: {'file_ids':contract_f},
				success: function (e) {
					console.log(e)
					for (let i = 0; i < e.data.length; i++) {
						contract_files.push({
							'id': e.data[i].id,
							'filepath': e.data[i].filepath
						})
						initcontractfiles(e.data[i].id,e.data[i].filepath)
					}
				}
			})
		}

		let files = [];
		let contract_files = [];
		ajax_certificate_f()
		function ajax_certificate_f(){
			let certificate_f = $('[name="certificate_files"]').val();
			$.ajax({
				url: "/api/index/getFiles",
				type:'post',
				data: {'file_ids':certificate_f},
				success: function (e) {
					console.log(e)
					for (let i = 0; i < e.data.length; i++) {
						files.push({
							'id': e.data[i].id,
							'filepath': e.data[i].filepath
						})
						initfiles(e.data[i].id,e.data[i].filepath)
					}
				}
			})
		}

		function initfiles(id,filepath){
			let li = '<li  data-id="'+id+'" >' +
					'<img src="'+filepath+'" style="object-fit: contain;" class="file-item">' +
					'<div class="layui-btn-group">' +
					'<span data-href="'+filepath+'" class="layui-btn layui-btn-xs layui-btn-normal file-view-img-scx">预览</span>'+
					'</div>' +
					'</li>'
			$('#filesBox').append(li)

			let ids = [];
			for (let i = 0; i < files.length; i++) {
				ids.push(files[i].id)
			}
			$('#certificate_files').val(ids.join(","))
		}
		function initcontractfiles(id,filepath){
			let li = '<li  data-id="'+id+'" >' +
					'<img src="'+filepath+'" style="object-fit: contain;" class="file-item">' +
					'<div class="layui-btn-group">' +
					'<span data-href="'+filepath+'" class="layui-btn layui-btn-xs layui-btn-normal file-view-img-scx">预览</span>'+
					'</div>' +
					'</li>'
			$('#filesBox2').append(li)
		}

		$('#filesBox').on('click', '.file-view-img-scx', function () {
			let data = [];
			for (let i = 0; i < files.length; i++) {
				data.push({
					"alt": "浩瀚宇宙",
					"pid": files[i].id,
					"src": files[i].filepath,
				})
			}
			if (files.length > 0) {
				layer.photos({
					photos: {
						"title": "Photos Demo",
						"start": 0,
						"data": data
					},
					footer: false // 是否显示底部栏 --- 2.8.16+
				});
			}
		});
		$('#filesBox2').on('click', '.file-view-img-scx', function () {
			let data = [];
			for (let i = 0; i < contract_files.length; i++) {
				data.push({
					"alt": "浩瀚宇宙",
					"pid": contract_files[i].id,
					"src": contract_files[i].filepath,
				})
			}
			if (contract_files.length > 0) {
				layer.photos({
					photos: {
						"title": "Photos Demo",
						"start": 0,
						"data": data
					},
					footer: false // 是否显示底部栏 --- 2.8.16+
				});
			}
		});
	}
	
	//拷贝密码
	function copyToClip(content) {
		if (navigator.clipboard) {
			navigator.clipboard.writeText(content);
		} else {
			var copy_textarea = document.createElement('textarea');
			copy_textarea.style.position = 'fixed';
			copy_textarea.style.clip = 'rect(0 0 0 0)';
			copy_textarea.style.top = '10px';
			copy_textarea.value = content;
			document.body.appendChild(copy_textarea);
			copy_textarea.select();
			document.execCommand('copy', true);
			document.body.removeChild(copy_textarea);
		}
		if (content != '') {
			layer.msg('复制成功');
		}
	}
</script>
{/block}
<!-- /脚本 -->