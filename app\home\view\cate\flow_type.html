{extend name="../../base/view/common/base" /}
<!-- 主体 -->
{block name="body"}
<div class="p-3">
    <form class="layui-form gg-form-bar border-t border-x" lay-filter="barsearchform">
        <div class="layui-input-inline" style="width:136px">
            <select name="type" lay-search="" id="dep" placeholder="请选择门店"
                    lay-filter="demo-select-filter" >
                <option value="">请选择分类</option>
                {volist name="type" id="v"}
                <option value="{$v.id}" >{$v.title}</option>
                {/volist}
            </select>
        </div>
    </form>
    <table class="layui-hide" id="test" lay-filter="test"></table>
</div>
<script type="text/html" id="toolbarDemo">
  <div class="layui-btn-container">
  	<button class="layui-btn layui-btn-sm addNew" type="button">+ 添加审批类型</button>
  </div>
</script>
{/block}
<!-- /主体 -->

<!-- 脚本 -->
{block name="script"}
<script>
	const moduleInit = ['tool'];
	function gouguInit() {
		var table = layui.table, tool = layui.tool , form = layui.form;
		layui.pageTable = table.render({
			elem: '#test'
			,toolbar: '#toolbarDemo'
			,defaultToolbar: false
			,title:'审批模块列表'
			,url: "/home/<USER>/flow_type"
			,page: false
			,cellMinWidth: 80
			,cols: [[
				{field:'id',width:80, title: 'ID号', align:'center'}
				,{field:'title',title: '名称',width:120}
				,{field:'department',title: '应用部门'}
				,{field:'name',title: '标识',width:150}
				,{field:'icon',title: '图标',width:220,templet: function(d){
					var html='<strong class="iconfont '+d.icon+'"></strong> '+d.icon;
					return html;
				}}
				,{field:'type_name', title: '所属分类',width:90,align:'center'}
				,{field:'status', title: '状态',width:80,align:'center',templet: function(d){
					var html1='<span class="green">正常</span>';
					var html2='<span class="yellow">禁用</span>';
					if(d.status==1){
						return html1;
					}
					else{
						return html2;
					}
				}}
				,{width:100,title: '操作', align:'center',templet: function(d){
					var html='';
					var btn='<a class="layui-btn layui-btn-normal  layui-btn-xs" lay-event="edit">编辑</a>';
					var btn1='<a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="disable">禁用</a>';
					var btn2='<a class="layui-btn layui-btn-xs" lay-event="open">启用</a>';
					if(d.status==1){
						html = '<div class="layui-btn-group">'+btn+btn1+'</div>';
					}
					else{
						html = '<div class="layui-btn-group">'+btn+btn2+'</div>';
					}
					return html;
				}}
			]]
		});
			
		table.on('tool(test)',function (obj) {
			if(obj.event === 'edit'){
				tool.side("/home/<USER>/flow_type_add?id="+obj.data.id);
			}
			if(obj.event === 'disable'){
				layer.confirm('确定要禁用该模块吗?', {icon: 3, title:'提示'}, function(index){
					let callback = function (e) {
						layer.msg(e.msg);
						if (e.code == 0) {
							layui.pageTable.reload();
						}
					}
					tool.post("/home/<USER>/flow_type_check", { id: obj.data.id,status: 0}, callback);
					layer.close(index);
				});
			}
			if(obj.event === 'open'){
				layer.confirm('确定要启用该模块吗?', {icon: 3, title:'提示'}, function(index){
					let callback = function (e) {
						layer.msg(e.msg);
						if (e.code == 0) {
							layui.pageTable.reload();
						}
					}
					tool.post("/home/<USER>/flow_type_check", { id: obj.data.id,status: 1}, callback);
					layer.close(index);
				});
			}
		});
		
		$('body').on('click','.addNew',function(){
			tool.side("/home/<USER>/flow_type_add");
			return false;
		});

        form.on('select(demo-select-filter)', function(data){
            let type = data.value;
            layui.pageTable.reloadData({
                where: {
                    type: type
                }
            });
            // layer.msg(this.innerHTML + ' 的 value: '+ value); // this 为当前选中 <option> 元素对象
        });
	}
</script>
{/block}
<!-- /脚本 -->