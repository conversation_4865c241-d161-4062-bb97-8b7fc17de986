{extend name="../../base/view/common/base" /}
<!-- 主体 -->
{block name="body"}
<div class="p-3">
    <form class="layui-form gg-form-bar border-t border-x" lay-filter="barsearchform">
        <div class="layui-input-inline" style="width:180px">
            <select name="type" lay-search="" lay-filter="type" id="type" placeholder="请选择应用模块" >
                <option value="">请选择应用模块</option>
                {volist name="type" id="v"}
                <option value="{$v.id}" >{$v.title}</option>
                {/volist}
            </select>
        </div>

        <div class="layui-input-inline" style="width:180px">
            <select name="flow_cate" lay-filter="flowcate" lay-verify="required" lay-reqText="请选择审批类型">
                <option value="">--请先选择审批类型--</option>
            </select>
        </div>
    </form>

    <table class="layui-hide" id="test" lay-filter="test"></table>
</div>
<script type="text/html" id="toolbarDemo">
  <div class="layui-btn-container">
  	<button class="layui-btn layui-btn-sm addNew" type="button">+ 添加审批流程</button>
  </div>
</script>
{/block}
<!-- /主体 -->

<!-- 脚本 -->
{block name="script"}
	<script>
	const moduleInit = ['tool'];
	function gouguInit() {
		var table = layui.table, tool = layui.tool, form = layui.form;


		layui.pageTable = table.render({
			elem: '#test'
			,toolbar: '#toolbarDemo'
			,defaultToolbar: false
			,title:'审批流程列表'
			,url: "/home/<USER>/index"
			,page: false //开启分页
			,limit: 20
			,cellMinWidth: 80
			,cols: [[
					{field:'id',width:80, title: 'ID号', align:'center'}
					,{field:'name',title: '流程名称',width:180}
					,{field:'check_type',title: '流程类型', align:'center',width:120,templet:function(d){
						var html = '<span class="green">固定审批流</span>';
						if(d.check_type==2){
							html = '<span class="blue">自由审批流</span>';
						}
						if(d.check_type==3){
							html = '<span class="yellow">可回退的审批流</span>';
						}
						return html;
					}}
					,{field:'type_name',title: '应用模块',width:80, align:'center'}
					,{field:'flow_cate',title: '审批类型',width:100, align:'center'}
					,{field:'department',title: '适用部门'}
					,{field:'username',title: '最后修改人',width:90,align:'center'}
					,{field:'status', title: '状态',width:80,align:'center',templet: function(d){
						var html1='<span class="green">正常</span>';
						var html2='<span class="yellow">禁用</span>';
						if(d.status==1){
							return html1;
						}
						else{
							return html2;
						}
					}}
					,{width:100,title: '操作', align:'center',templet: function(d){
						var html='';
						var btn='<a class="layui-btn layui-btn-normal  layui-btn-xs" lay-event="edit">编辑</a>';
						var btn1='<a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="disable">禁用</a>';
						var btn2='<a class="layui-btn layui-btn-xs" lay-event="open">启用</a>';
						if(d.status==1){
							html = '<div class="layui-btn-group">'+btn+btn1+'</div>';
						}
						else{
							html = '<div class="layui-btn-group">'+btn+btn2+'</div>';
						}
						return html;
					}}
				]]
			});
			
			table.on('tool(test)',function (obj) {
				if(obj.event === 'edit'){		
					tool.side('/home/<USER>/add?id='+obj.data.id);
				}
				if(obj.event === 'disable'){
					layer.confirm('确定要禁用该模块吗?', {icon: 3, title:'提示'}, function(index){
						let callback = function (e) {
							layer.msg(e.msg);
							if (e.code == 0) {
								layui.pageTable.reload();
							}
						}
						tool.post("/home/<USER>/check", { id: obj.data.id,status: 0 }, callback);
						layer.close(index);						
					});
				}
				if(obj.event === 'open'){
					layer.confirm('确定要启用该模块吗?', {icon: 3, title:'提示'}, function(index){
						let callback = function (e) {
							layer.msg(e.msg);
							if (e.code == 0) {
								layui.pageTable.reload();
							}
						}
						tool.post("/home/<USER>/check", { id: obj.data.id,status: 1 }, callback);
						layer.close(index);	
					});
				}
			});
			
			$('body').on('click','.addNew',function(){
				tool.side("/home/<USER>/add");
				return false;
			});

            form.on('select(type)', function(data){
                let callback = function (e) {
                    if (e.code == 0) {
                        if(e.data.length>0){
                            let ops='<option value="">--请选择--</option>';
                            for(var i=0;i<e.data.length;i++){
                                ops+='<option value="'+e.data[i].id+'">'+e.data[i].title+'</option>';
                            }
                            $('[name="flow_cate"]').html(ops);
                            form.render();
                        }
                    }
                }
                tool.get("/api/index/get_flow_cate", {type:data.value}, callback);
                relodata()
            });

            form.on('select(flowcate)', function(data){
                relodata()
            });


            function relodata(){
                let type=$('[name="type"]').val();
                let flow_cate=$('[name="flow_cate"]').val();
                layui.pageTable.reloadData({
                    where: {
                        type: type,
                        flow_cate: flow_cate
                    }
                });
            }

		}

	</script>
{/block}
<!-- /脚本 -->