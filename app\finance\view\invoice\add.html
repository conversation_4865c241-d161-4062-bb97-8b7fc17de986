{extend name="../../base/view/common/base" /}
{block name="style"}
<style>
.layui-table-min th{font-size:13px; text-align:center; background-color:#f8f8f8;}
.layui-table-min td{font-size:13px; padding:6px;text-align:center;}
.layui-form-radio{margin-right:6px}
.layui-form-radio>i{margin-right:3px; }
</style>
{/block}
<!-- 主体 -->
{block name="body"}
<form class="layui-form p-4">
	<h3 class="pb-3">申请开票</h3>
	{if condition="($id == 0)"}
	<table class="layui-table">
		<tr>
			<td class="layui-td-gray">开票金额<font>*</font></td>
			<td>
				<input type="text" class="layui-input" name="amount" lay-verify="required|number" placeholder="请输入开票金额" lay-reqText="请输入开票金额" value="">	
			</td>
			<td class="layui-td-gray">开票类型<font>*</font></td>
			<td>
			<select name="invoice_type" lay-verify="required" lay-reqText="请选择开票类型">
				<option value="">请选择开票类型</option>
				<option value="1">增值税专用发票</option>
				<option value="2">普通发票</option>
				<option value="3">专业发票</option>
			  </select>
			</td>
			<td class="layui-td-gray">开票主体<font>*</font></td>
			<td>
				<select name="invoice_subject" lay-verify="required" lay-reqText="请选择开票主体">
					<option value="">请选择开票主体</option>
					{volist name=":finance_invoice_subject()" id="vo"}
					<option value="{$vo.id}">{$vo.title}</option>
					{/volist}
				</select>
			</td>
		</tr>
		<tr>
			<td class="layui-td-gray">抬头类型<font>*</font></td>
			<td>
				<input type="radio" name="type" lay-filter="type" value="1" title="企业" checked>
				<input type="radio" name="type" lay-filter="type" value="2" title="个人">
			</td>
			<td class="layui-td-gray">开票抬头<font>*</font></td>
			<td><input type="text" name="invoice_title" class="layui-input" value="" lay-verify="required"  lay-reqText="请输入开票抬头"></td>
			<td class="layui-td-gray">电话号码</td>
			<td><input type="text" name="invoice_phone" class="layui-input" value=""></td>
		</tr>
		<tr class="invoice-type">
			<td class="layui-td-gray-2">纳税人识别号</td>
			<td><input type="text" name="invoice_tax" class="layui-input" value=""></td>
			<td class="layui-td-gray">开户行</td>
			<td><input type="text" name="invoice_bank" class="layui-input" value=""></td>
			<td class="layui-td-gray">银行账号</td>
			<td><input type="text" name="invoice_account" class="layui-input" value=""></td>
		</tr>
		<tr class="invoice-type">
			<td class="layui-td-gray-2">银行营业网点</td>
			<td><input type="text" name="invoice_banking" class="layui-input" value=""></td>
			<td class="layui-td-gray">地址</td>
			<td colspan="3"><input type="text" name="invoice_address" class="layui-input" value=""></td>
		</tr>
		{gt name=":isModule('contract')" value="0"}
		<tr>
			<td class="layui-td-gray">关联合同</td>
			<td colspan="5">
				<input type="text" class="layui-input picker-contract" name="contract_name" placeholder="请选择需要关联的合同" readonly value="">		
				<input type="hidden" class="layui-input" name="contract_id" value="0">		
			</td>
		</tr>
		{/gt}
		<tr>
			<td class="layui-td-gray">
				<div class="layui-input-inline">关联附件</div>
				<div class="layui-input-inline">
					<button type="button" class="layui-btn layui-btn-xs" id="uploadBtn"><i class="layui-icon"></i></button>
				</div>
			</td>
			<td colspan="5" style="line-height:inherit">
				<div class="layui-row" id="fileBox">
					<input type="hidden" data-type="file" name="file_ids" value="">
				</div>
			</td>
		</tr>
		<tr>
			<td class="layui-td-gray">备注信息</td>
			<td colspan="5"><textarea name="remark" placeholder="请输入备注信息" class="layui-textarea"></textarea></td>
		</tr>
		<tr>
			<td class="layui-td-gray">审批流程<font>*</font></td>
			<td colspan="5">
				<select name="flow_id" lay-verify="required" lay-filter="flowtype" lay-reqText="请选择审批流程">
				<option value="">--请选择--</option>
				{volist name="flows" id="vo"}
				  <option value="{$vo.id}" title="{$vo.check_type}">{$vo.name}</option>
				{/volist}
				</select>
			</td>
		</tr>
		<tr id="flow_tr">
			<td class="layui-td-gray">审核人<font>*</font></td>
			<td colspan="5">
				<input type="hidden" name="check_admin_ids" value="" readonly><input type="text" name="check_admin_name" value="" autocomplete="off" placeholder="请选择审核人" lay-verify="required" lay-reqText="请选择审核人" class="layui-input" readonly>
			</td>
		</tr>
		<tr>
			<td class="layui-td-gray">抄送人</td>
			<td colspan="5">
				<input type="text" name="copy_names" value="" autocomplete="off" placeholder="请选择审核人" class="layui-input picker-more" readonly><input type="hidden" name="copy_uids" value="" readonly>
			</td>
		</tr>
	</table>
	{else/}
		<table class="layui-table">
		<tr>
			<td class="layui-td-gray">开票金额<font>*</font></td>
			<td>
				<input type="text" class="layui-input" name="amount" lay-verify="required|number" placeholder="请输入开票金额" lay-reqText="请输入开票金额" value="{$detail.amount}">	
			</td>
			<td class="layui-td-gray">开票类型<font>*</font></td>
			<td>
			<select name="invoice_type" lay-verify="required" lay-reqText="请选择开票类型">
				<option value="">请选择开票类型</option>
				<option value="1" {eq name="$detail.invoice_type" value="1"}selected{/eq}>增值税专用发票</option>
				<option value="2" {eq name="$detail.invoice_type" value="2"}selected{/eq}>普通发票</option>
				<option value="3" {eq name="$detail.invoice_type" value="3"}selected{/eq}>专业发票</option>
			  </select>
			</td>
			<td class="layui-td-gray">开票主体<font>*</font></td>
			<td>
				<select name="invoice_subject" lay-verify="required" lay-reqText="请选择开票主体">
					<option value="">请选择开票主体</option>
					{volist name=":finance_invoice_subject()" id="vo"}
					<option value="{$vo.id}" {eq name="$vo.id" value="$detail.invoice_subject"}selected{/eq}>{$vo.title}</option>
					{/volist}
				</select>
			</td>
		</tr>
		<tr>
			<td class="layui-td-gray">抬头类型<font>*</font></td>
			<td>
				<input type="radio" name="type" lay-filter="type" value="1" title="企业" {eq name="$detail.type" value="1"}checked{/eq}>
				<input type="radio" name="type" lay-filter="type" value="2" title="个人" {eq name="$detail.type" value="2"}checked{/eq}>
			</td>
			<td class="layui-td-gray">开票抬头<font>*</font></td>
			<td><input type="text" name="invoice_title" class="layui-input" value="{$detail.invoice_title}"></td>
			<td class="layui-td-gray">电话号码</td>
			<td><input type="text" name="invoice_phone" class="layui-input" value="{$detail.invoice_phone}"></td>
		</tr>
		<tr class="invoice-type" {eq name="$detail.type" value="2"}style="display:none"{/eq}>
			<td class="layui-td-gray-2">纳税人识别号</td>
			<td><input type="text" name="invoice_tax" class="layui-input" value="{$detail.invoice_tax}"></td>
			<td class="layui-td-gray">开户行</td>
			<td><input type="text" name="invoice_bank" class="layui-input" value="{$detail.invoice_bank}"></td>
			<td class="layui-td-gray">银行账号</td>
			<td><input type="text" name="invoice_account" class="layui-input" value="{$detail.invoice_account}"></td>
		</tr>
		<tr class="invoice-type" {eq name="$detail.type" value="2"}style="display:none"{/eq}>
			<td class="layui-td-gray-2">银行营业网点</td>
			<td><input type="text" name="invoice_banking" class="layui-input" value="{$detail.invoice_banking}"></td>
			<td class="layui-td-gray">地址</td>
			<td colspan="3"><input type="text" name="invoice_address" class="layui-input" value="{$detail.invoice_address}"></td>
		</tr>
		{gt name=":isModule('contract')" value="0"}
		<tr>
			<td class="layui-td-gray">关联合同</td>
			<td colspan="5">
				<input type="text" class="layui-input picker-contract" name="contract_name" placeholder="请选择需要关联的合同" readonly value="{$detail.contract_name}">		
				<input type="hidden" class="layui-input" name="contract_id" value="{$detail.contract_id}">		
			</td>
		</tr>
		{/gt}
		<tr>
			<td class="layui-td-gray">
				<div class="layui-input-inline">关联附件</div>
				<div class="layui-input-inline">
					<button type="button" class="layui-btn layui-btn-xs" id="uploadBtn"><i class="layui-icon"></i></button>
				</div>
			</td>
			<td colspan="5" style="line-height:inherit">
				<div class="layui-row" id="fileBox">
					<input type="hidden" data-type="file" name="file_ids" value="{$detail.file_ids}">
					{notempty name="$detail.file_ids"}
					{volist name="$detail.fileArray" id="vo"}
					<div class="layui-col-md4" id="uploadImg{$vo.id}">{:file_card($vo)}</div>
					{/volist}
					{/notempty}
				</div>
			</td>
		</tr>
		<tr>
			<td class="layui-td-gray">备注信息</td>
			<td colspan="5"><textarea name="remark" placeholder="请输入备注信息" class="layui-textarea">{$detail.remark}</textarea></td>
		</tr>
		<tr>
			<td class="layui-td-gray">审批流程<font>*</font></td>
			<td colspan="5">
				<select name="flow_id" lay-verify="required" lay-filter="flowtype" lay-reqText="请选择审批流程">
				<option value="">--请选择--</option>
				{volist name="flows" id="vo"}
				  <option value="{$vo.id}" title="{$vo.check_type}">{$vo.name}</option>
				{/volist}
				</select>
			</td>
		</tr>
		<tr id="flow_tr">
			<td class="layui-td-gray">审核人<font>*</font></td>
			<td colspan="5">
				<input type="hidden" name="check_admin_ids" value="" readonly><input type="text" name="check_admin_name" value="" autocomplete="off" placeholder="请选择审核人" lay-verify="required" lay-reqText="请选择审核人" class="layui-input" readonly>
			</td>
		</tr>
		<tr>
			<td class="layui-td-gray">抄送人</td>
			<td colspan="5">
				<input type="text" name="copy_names" value="" autocomplete="off" placeholder="请选择审核人" class="layui-input picker-more" readonly><input type="hidden" name="copy_uids" value="" readonly>
			</td>
		</tr>
	</table>
	{/if}

	<div class="py-3">
		<input name="id" id="id" type="hidden" value="{$id}">
		<button class="layui-btn layui-btn-normal " lay-submit="" lay-filter="webform">保存并提交审核</button>
		<button type="reset" class="layui-btn layui-btn-primary">重置</button>
	</div>
</form>
{/block}
<!-- /主体 -->

<!-- 脚本 -->
{block name="script"}
<script>
const moduleInit = ['tool','employeepicker','oaTool'];
function gouguInit() {
	var form = layui.form,upload = layui.upload,tool=layui.tool,table=layui.table,oaTool = layui.oaTool,laydate = layui.laydate;	

	//选择抬头类型
	form.on('radio(type)', function (data) {
		if(data.value==2){
			$('.invoice-type').hide();
		}
		else{
			$('.invoice-type').show();
		}
	});	
	
	//相关附件上传
	oaTool.addFile();	
	
	//监听提交
	form.on('submit(webform)', function(data){		  	
		$.ajax({
			url: "/finance/invoice/add",
			type:'post',
			data:data.field,
			success:function(e){
				layer.msg(e.msg);
				if (e.code == 0) {
					tool.sideClose(1000);
				}
			}
		})
		return false;
	});
	
	form.on('select(flowtype)', function(data){
		var check_type = data.elem[data.elem.selectedIndex].title;
		var formHtml='<td class="layui-td-gray">审核人<font>*</font></td>\
		<td colspan="5">\
			<input type="text" name="check_admin_name" value="" autocomplete="off" placeholder="请选择审核人" lay-verify="required" lay-reqText="请选择审核人" class="layui-input picker-one"><input type="hidden" name="check_admin_ids" value="">\
		</td>';
		if(check_type == 2){
			$('#flow_tr').html(formHtml);
			form.render();
		}
		if(data.value==''){
			return false;
		}
		$.ajax({
			url: "/api/index/get_flow_users",
			type:'get',
			data:{id:data.value},
			success: function (e) {
				if (e.code == 0) {
					var flowLi='';
					var flow_data = e.data.flow_data;
					if(e.data.copy_uids && e.data.copy_uids !=''){
						$('[name="copy_names"]').val(e.data.copy_unames);
						$('[name="copy_uids"]').val(e.data.copy_uids.split(','));
					}
					if(check_type == 1 || check_type == 3){
						for(var a=0;a<flow_data.length;a++){
							var userList='',sign_type = '';
							if(check_type == 1){
								if(flow_data[a].flow_type==1){
									userList+= '<li style="padding:3px 0">当前部门负责人</li>';
								}
								else if(flow_data[a].flow_type==2){
									userList+= '<li style="padding:3px 0">上级部门负责人</li>';
								}
								else{
									if(flow_data[a].flow_type==3){
										sign_type= ' <span class="layui-badge layui-bg-blue">或签</span>';
									}
									if(flow_data[a].flow_type==4){
										sign_type= ' <span class="layui-badge layui-bg-blue">会签</span>';
									}
									for(var b=0;b<flow_data[a].user_id_info.length;b++){
										userList+= '<li style="padding:3px 0"><img src="'+flow_data[a].user_id_info[b].thumb+'" style="width:24px; height:24px; border-radius:50%; margin-right:8px;" />'+flow_data[a].user_id_info[b].name+'</li>';
									}
								}
							}
							else if(check_type == 3){
								sign_type= ' <span class="layui-badge layui-bg-blue">'+flow_data[a].flow_name+'</span>'
								for(var b=0;b<flow_data[a].user_id_info.length;b++){
									userList+= '<li style="padding:3px 0"><img src="'+flow_data[a].user_id_info[b].thumb+'" style="width:24px; height:24px; border-radius:50%; margin-right:8px;" />'+flow_data[a].user_id_info[b].name+'</li>';
								}
							}
							flowLi+='<li class="layui-timeline-item">\
								<i class="layui-icon layui-timeline-axis">&#xe63f;</i>\
								<div class="layui-timeline-content">\
								  <p class="layui-timeline-title"><strong>第'+(a+1)+'级审批</strong>'+sign_type+'</p>\
								  <ul>'+userList+'</ul>\
								</div>\
							</li>';
						}							
						formHtml = '<td class="layui-td-gray">审批流程</td>\
									<td colspan="7">\
										<ul id="flowList" class="layui-timeline">'+flowLi+'</ul>\
									</td>';
						$('#flow_tr').html(formHtml);
					}
				}
			}
		})
	});
}		
</script>
{/block}
<!-- /脚本 -->