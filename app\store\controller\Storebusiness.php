<?php

namespace app\store\controller;


use app\base\BaseController;
use app\store\model\StoreRecord;
use app\store\service\StoreBusinessService;
use app\store\service\StoreService;
use app\user\model\DepartmentChange;
use think\App;
use think\facade\Db;
use think\facade\Session;
use think\facade\View;

class Storebusiness extends BaseController
{
    protected $flag;
    protected $dep_model;

    protected $StoreRecordModel;
    protected $StoreService;
    protected $StoreBusinessService;

    public function __construct(App $app)
    {
        parent::__construct($app);
        $this->flag = false;
        $this->dep_model = new DepartmentChange();
        $this->StoreRecordModel = new StoreRecord();
        $this->StoreService = new StoreService();
        $this->StoreBusinessService = new StoreBusinessService();
    }

    public function index()
    {
        $admin = Db::name("admin")->where(['id' => $this->uid])->find();

        $did = 0;
        if (!empty($admin)) {
            $dep = Db::name("department")->where([['id', 'in', $admin['did']], 'remark' => '门店'])->find();
            if (!empty($dep)) {
                $did = $dep['id'];
                $this->flag = true;
            }
        }

        if (request()->isAjax()) {
            $param = get_params();
            $where = array();
            $swhere = array();

            if (empty($param['sdate'])) {
                $param['sdate'] = date('Y-m-d', strtotime('-1 day', strtotime(date('Y-m-d'))));
            }

            $where[] = ['sdate', '=', $param['sdate']];
            $swhere[] = ['sb.sdate', '=', $param['sdate']];

            if (empty($param['dep'])) {
                if (empty($did)) {
                    return ['code' => 0, 'data' => []];
                } else {
                    $param['dep'] = $did;
                }
            }

//            $where[] = ['aid', '=', 2135];
//            $swhere[] = ['sb.aid', '=', 2135];

            $where[] = ['did', '=', $param['dep']];
            $swhere[] = ['sb.did', '=', $param['dep']];

            $where[] = ['status', '=', 1];
            $swhere[] = ['sb.status', '=', 1];


            //调店离职导致数据 删除先检测还原
            $this->StoreService->re_store_data($param['sdate'], $param['dep'], 0, 'store_business');

            $list = Db::name("store_business")
                ->where($where)
                ->select();

            $totalRow = array();
            if (!empty($list)) {

                foreach ($list as $k => $v) {
                    $udata = array();

                    //删除离职人员
                    $admin = Db::name('admin')->where(['id' => $v['aid']])->find();

                    if (empty($admin)) {

                        Db::name('store_business')->where([
                            'id' => $v['id'],
                            ['total_yj' , '<>' , 0]
                        ])->update(['status' => -1]);
                    }

                    if (!empty($admin['res_date']) && $admin['status'] == 2) {
                        Db::name('store_business')->where([
                            ['aid', '=', $admin['id']],
                            ['sdate', '>=', $admin['res_date']],
                            ['did', '=', $param['dep']],
                            ['total_yj' , '=' , 0]
                        ])->update(['status' => -1]);

                        Db::name('store_business')->where([
                            ['aid', '=', $admin['id']],
                            ['sdate', '<', $admin['res_date']],
                            ['did', '=', $param['dep']]
                        ])->update(['status' => 1]);
                    }

                    //调离记录
                    $re = $this->StoreService->outstore($param['sdate'], $param['dep'], $v['aid']);

//                    if ($re === true) {
//                        Db::name('store_business')->where(['id' => $v['id']])->update(['status' => -1]);
//                        continue;
//                    } else {
//                        Db::name('store_business')->where(['id' => $v['id']])->update(['status' => 1]);
//                    }
                    $this->jisuan($v, $v['id'], $udata, true);
                }

                $list = Db::name("store_business")
                    ->field('sb.*,a.id as a_id,a.sort as sort')
                    ->alias('sb')
                    ->join('Admin a', 'sb.aid = a.id', 'LEFT')
                    ->where($swhere)
                    ->order('a.sort asc,sb.id asc')
                    ->select()->toArray();

                foreach ($list as $kk => $vv) {
                    foreach ($vv as $kkk => $vvv) {
                        if ($kkk == "id" || $kkk == "sdate" || $kkk == "create_time" || $kkk == "update_time" ||
                            $kkk == "aid" || $kkk == "aname" || $kkk == "yz_name" || $kkk == "did" || $kkk == "dname" || $kkk == "status" ||
                            $kkk == "sort") {
                            continue;
                        }
                        $list[$kk][$kkk] = $vvv != 0 ? $vvv : "";

                    }
                }

                $totalRow = $this->StoreService->getTotalRow("store_business", $where);

            }

            //判断修改依据
            $store_service = new StoreService();
            $isEditData_s = $store_service->isEditData($this->uid, $param['dep'], $param['sdate']);

            //查看是否当天提交过已通过申请  flow_id 36
            $approve = Db::name("Approve")->where([
                'flow_id' => 36,
                'mdid' => $param['dep'],
                'admin_id' => $this->uid,
                'check_status' => 2,
                'detail_time' => strtotime($param['sdate'])
            ])->order('create_time desc')->find();

            $isEditData = false;
            if ($this->uid == 1){
                $isEditData = true;
                $isEditData_s = true;
            }

            if (!empty($approve)) {
                //创建时间
                $finish_time = date("Y-m-d", $approve['finish_time']);

                //要修改日期
                $detail_time = date("Y-m-d", $approve['detail_time']);
                if ($finish_time == date("Y-m-d") && $detail_time == $param['sdate']) {
                    $isEditData_s = true;
                    $isEditData = true;

                }
            }

            foreach ($list as $k => $v) {
                $list[$k]['isEditData'] = $isEditData;
                $list[$k]['isEditData_s'] = $isEditData_s;
            }

            return ['code' => 0, 'data' => $list, "totalRow" => $totalRow];
        }

        $isShow = false;
        if (!empty($this->storeid)){
            $isShow = true;
        }

        View::assign('isShow', $isShow);

        //查看一下是否分管
        $dep_fenguan = $this->dep_model->get_dep_fenguan(0, $this->uid);
        View::assign('dep', $dep_fenguan);
        View::assign('aid', $this->uid);
        View::assign('did', $did);
        View::assign('flag', $this->flag);
        return view();
    }

    public function add()
    {
        if (request()->isAjax()) {

            set_time_limit(0);

            $param = get_params();

            $sdate = isset($param['sdate']) ? $param['sdate'] : 0;
            $did = isset($param['did']) ? $param['did'] : 0;

            if (empty($sdate)) {
                return to_assign(1, '日期数据有误');
            }

            if (isset($param['type']) && $param['type'] == 'refresh'){
                $store_business = Db::name('store_business')->where([
                    'sdate' => $sdate, 'did' => $did
                ])->select()->toArray();

                $dep = Db::name('department')->where(['id' => $did])->find();
                foreach ($store_business as $k => $v){
                    $admin = Db::name('admin')->where(['id' => $v['aid']])->find();
                    $this->StoreBusinessService->everydayDataToOrder($admin, $dep, $sdate , 1);
                }
                return to_assign();
            }

            $this->StoreBusinessService->addData($did, $sdate, $this->uid, $this->name);

            return to_assign();
        } else {
            $param = get_params();
            $did = !empty($param['dep']) ? $param['dep'] : 0;

            $dep_model = new DepartmentChange();
            $dlist = $dep_model->get_dep_store();

            $session_admin = get_config('app.session_admin');
            $uid = Session::get($session_admin);
            $admin = Db::name("admin")->where(['id' => $uid])->find();

            if (empty($did)) {
                if (!empty($admin)) {
                    $dep = Db::name("department")->where([['id', 'in', $admin['did']], 'remark' => '门店'])->find();
                    $did = $dep['id'];
                }
            }

            if (!$did) {
                return;
            }

            $sdate = isset($param['sdate']) ? $param['sdate'] : 0;
            $dep = isset($param['dep']) ? $param['dep'] : 0;

            $list = Db::name('store_business')->where(['sdate' => $sdate, 'did' => $dep])->select();
            View::assign('dlist', $dlist);
            View::assign('did', $did);
            View::assign('id', 0);

            return view();
        }
    }

    public function update()
    {
        $param = get_params();
        if (request()->isAjax()) {
            $id = isset($param['id']) ? $param['id'] : 0;
            $field = isset($param['field']) ? $param['field'] : '';
            $val = isset($param['val']) ? $param['val'] : '';

            if (empty($id)) {
                return to_assign(1, '数据有误: id为空');
            }

            if (!is_numeric($val) && $field != 'yz_name') {
                return to_assign(1, '请填写正确的内容');
            }

            if ($field == 'yz_name' && empty($val)) {
                $val = "";
            }

            $udata = [$field => $val];
            $store_business = Db::name('store_business')->where(['id' => $id])->find();
            $store_business[$field] = $val;

            if ($field == 'sort') {
                Db::name('admin')->where(['id' => $store_business['aid']])->update(['sort' => $val]);
                return to_assign();
            }

            $re = false;
            if ($field == 'yz_name') {
                Db::startTrans();
                try {
                    $re = Db::name('store_business')->where(['id' => $id])->update(['yz_name' => $val]);
                    Db::name('store_business')->where(['aid' => $store_business['aid']])->update(['yz_name' => $val]);
                    Db::name('admin')->where(['id' => $store_business['aid']])->update(['yz_name' => $val]);
                } catch (\Exception $e) { ##这里参数不能删除($e：错误信息)
                    Db::rollback();
                }
            } else {
                $store_service = new StoreService();
                //添加编辑记录
                $this->StoreRecordModel->addedit($id, $this->uid, $this->name);
                //每日数据重新计算
                $re = $this->jisuan($store_business, $id, $udata,$field);
                //老师数据重新计算
                $s_date = date("Y-m-01", strtotime($store_business['sdate']));
                $bu_t_where = [
                    ['sdate', 'between', [$s_date, getLastMonth($s_date)]],
                    ['did', '=', $store_business['did']],
                    'aid' => $store_business['aid']
                ];
                $store_service->record_store_business_t($bu_t_where, ['sdate' => date("Y-m", strtotime($store_business['sdate']))]);
                //门店汇总重新计算
                $bu_m_where = [
                    'did' => $store_business['did'],
                    'sdate' => $store_business['sdate'],
                    'status' => 1
                ];
                $store_service->record_store_business_m($bu_m_where);
                //店长手工提成
                //店长业绩提成
            }

            add_log('edit', get_params('id'));
            Db::commit();
            return to_assign();

        }
    }

    public function view()
    {
        $admin = Db::name("admin")->where(['id' => $this->uid])->find();
        $array = array();

        $did = 0;
        if (!empty($admin)) {
            $dep = Db::name("department")->where([['id', 'in', $admin['did']], 'remark' => '门店'])->find();
            if (!empty($dep)) {
                $did = $dep['id'];
            }
        }

        if (request()->isAjax()) {
            $param = get_params();
            $where = array();
            $whereTotoal = array();

            if (!isset($param['date_range']) || empty($param['date_range'])) {
                $where[] = ['sb.sdate', 'between', [date("Y-m-01"), date("Y-m-t")]];
                $whereTotoal[] = ['sdate', 'between', [date("Y-m-01"), date("Y-m-t")]];
            } else {
                $date_range = explode(" - ", $param['date_range']);
                $where[] = ['sb.sdate', 'between', [$date_range[0], $date_range[1]]];
                $whereTotoal[] = ['sdate', 'between', [$date_range[0], $date_range[1]]];
            }

            if (!isset($param['dep']) || empty($param['dep'])) {
                if (empty($did)) {
                    return ['code' => 0, 'data' => $array];
                } else {
                    $param['dep'] = $did;
                }
            }

            $flag = false;
            if (isset($param['aid']) && !empty($param['aid'])) {
                $flag = true;
                $where[] = ['sb.aid', '=', $param['aid']];
                $whereTotoal[] = ['aid', '=', $param['aid']];
            }

            $where[] = ['sb.did', '=', $param['dep']];
            $where[] = ['sb.status', '=', 1];

            $whereTotoal['did'] = $param['dep'];
            $whereTotoal['status'] = 1;

            $list = Db::name("store_business")
                ->field('sb.*,a.id as a_id,a.sort as sort')
                ->alias('sb')
                ->join('Admin a', 'sb.aid = a.id', 'LEFT')
                ->where($where)
                ->order('sb.sdate asc,a.sort asc')
                ->select()->toArray();

            $store_service = new StoreService();
            $v_sdate = '';
            foreach ($list as $k => $v) {

                foreach ($v as $kkk => $vvv) {
                    if ($kkk == "id" || $kkk == "sdate" || $kkk == "create_time" || $kkk == "update_time" ||
                        $kkk == "aid" || $kkk == "aname" || $kkk == "yz_name" || $kkk == "did" || $kkk == "dname" || $kkk == "status" ||
                        $kkk == "sort") {
                        continue;
                    }
                    $v[$kkk] = $vvv != 0 ? $vvv : "";
                }

                if ($flag) {
                    $array[] = $v;
                    if (($k + 1) == count($list)) {
                        $totalRow = $store_service->getTotalRow("store_business", $whereTotoal);
                        $array[] = [
                            'id' => 0,
                            'sdate' => '合计',
                            'project_268' => $totalRow['project_268_tot'],
                            'guasha' => $totalRow['guasha_tot'],
                            'dianzhong' => $totalRow['dianzhong_tot'],
                            'haoping' => $totalRow['haoping_tot'],

                            'sanke' => $totalRow['sanke_tot'],
                            'sanke2' => $totalRow['sanke2_tot'],
                            'sanke_yj' => $totalRow['sanke_yj_tot'],
                            'sanke_kdj' => $totalRow['sanke_kdj_tot'],

                            'deal_num' => $totalRow['deal_num_tot'],
                            'sanke_deal_rate' => $totalRow['sanke_deal_rate_tot'],

                            'service_num' => $totalRow['service_num_tot'],
                            'second_service' => $totalRow['second_service_tot'],

                            'cika_amount_1' => $totalRow['cika_amount_1_tot'],
                            'cika_amount_2' => $totalRow['cika_amount_2_tot'],
                            'xka_num_1' => $totalRow['xka_num_1_tot'],
                            'xka_amount_1' => $totalRow['xka_amount_1_tot'],
                            'xka_num_2' => $totalRow['xka_num_2_tot'],
                            'xka_amount_2' => $totalRow['xka_amount_2_tot'],
                            'xcz_num_1' => $totalRow['xcz_num_1_tot'],
                            'xcz_amount_1' => $totalRow['xcz_amount_1_tot'],
                            'xcz_num_2' => $totalRow['xcz_num_2_tot'],
                            'xcz_amount_2' => $totalRow['xcz_amount_2_tot'],

                            'total_yj' => $totalRow['total_yj_tot'],
                            'total_kdj' => $totalRow['total_kdj_tot'],
                            'haoka_yj' => $totalRow['haoka_yj_tot'],
                        ];
                    }
                } else {
                    $whereTotoal['sdate'] = $v_sdate;
                    if (empty($v_sdate) || $v_sdate == $v['sdate']) {
                        $array[] = $v;
                        $v_sdate = $v['sdate'];
                        //查询 合计
                        if (($k + 1) == count($list)) {
                            $totalRow = $store_service->getTotalRow("store_business", $whereTotoal);
                            $array[] = [
                                'id' => 0,
                                'sdate' => '合计',
                                'project_268' => $totalRow['project_268_tot'],
                                'guasha' => $totalRow['guasha_tot'],
                                'dianzhong' => $totalRow['dianzhong_tot'],
                                'haoping' => $totalRow['haoping_tot'],

                                'sanke' => $totalRow['sanke_tot'],
                                'sanke2' => $totalRow['sanke2_tot'],
                                'sanke_yj' => $totalRow['sanke_yj_tot'],
                                'sanke_kdj' => $totalRow['sanke_kdj_tot'],
                                'second_service' => $totalRow['second_service_tot'],

                                'deal_num' => $totalRow['deal_num_tot'],
                                'sanke_deal_rate' => $totalRow['sanke_deal_rate_tot'],

                                'service_num' => $totalRow['service_num_tot'],

                                'cika_amount_1' => $totalRow['cika_amount_1_tot'],
                                'cika_amount_2' => $totalRow['cika_amount_2_tot'],
                                'xka_num_1' => $totalRow['xka_num_1_tot'],
                                'xka_amount_1' => $totalRow['xka_amount_1_tot'],
                                'xka_num_2' => $totalRow['xka_num_2_tot'],
                                'xka_amount_2' => $totalRow['xka_amount_2_tot'],
                                'xcz_num_1' => $totalRow['xcz_num_1_tot'],
                                'xcz_amount_1' => $totalRow['xcz_amount_1_tot'],
                                'xcz_num_2' => $totalRow['xcz_num_2_tot'],
                                'xcz_amount_2' => $totalRow['xcz_amount_2_tot'],

                                'total_yj' => $totalRow['total_yj_tot'],
                                'total_kdj' => $totalRow['total_kdj_tot'],
                                'haoka_yj' => $totalRow['haoka_yj_tot'],
                            ];
                        }

                        continue;
                    }
                    //查询 合计
                    if ($v_sdate != $v['sdate']) {
                        $whereTotoal['sdate'] = $v_sdate;
                        $totalRow = $store_service->getTotalRow("store_business", $whereTotoal);
                        $array[] = [
                            'id' => 0,
                            'sdate' => '合计',
                            'project_268' => $totalRow['project_268_tot'],
                            'guasha' => $totalRow['guasha_tot'],
                            'dianzhong' => $totalRow['dianzhong_tot'],
                            'haoping' => $totalRow['haoping_tot'],

                            'sanke' => $totalRow['sanke_tot'],
                            'sanke2' => $totalRow['sanke2_tot'],
                            'sanke_yj' => $totalRow['sanke_yj_tot'],
                            'sanke_kdj' => $totalRow['sanke_kdj_tot'],
                            'second_service' => $totalRow['second_service_tot'],

                            'deal_num' => $totalRow['deal_num_tot'],
                            'sanke_deal_rate' => $totalRow['sanke_deal_rate_tot'],

                            'service_num' => $totalRow['service_num_tot'],

                            'cika_amount_1' => $totalRow['cika_amount_1_tot'],
                            'cika_amount_2' => $totalRow['cika_amount_2_tot'],
                            'xka_num_1' => $totalRow['xka_num_1_tot'],
                            'xka_amount_1' => $totalRow['xka_amount_1_tot'],
                            'xka_num_2' => $totalRow['xka_num_2_tot'],
                            'xka_amount_2' => $totalRow['xka_amount_2_tot'],
                            'xcz_num_1' => $totalRow['xcz_num_1_tot'],
                            'xcz_amount_1' => $totalRow['xcz_amount_1_tot'],
                            'xcz_num_2' => $totalRow['xcz_num_2_tot'],
                            'xcz_amount_2' => $totalRow['xcz_amount_2_tot'],

                            'total_yj' => $totalRow['total_yj_tot'],
                            'total_kdj' => $totalRow['total_kdj_tot'],
                            'haoka_yj' => $totalRow['haoka_yj_tot'],
                        ];
                        $array[] = $v;
                        $v_sdate = $v['sdate'];
                    }
                }

            }
            return ['code' => 0, 'data' => $array];
        }


        //查看一下是否分管
        $dep_fenguan = $this->dep_model->get_dep_fenguan($did, $this->uid);
        View::assign('dep', $dep_fenguan);
        View::assign('did', $did);
        return view();
    }

    public function jisuan($store_business, $id, $udata, $field = false)
    {
        if ($field == 'sanke_yj' || $field == 'sanke' || $field == true) {
            $sanke_yj = $store_business['sanke_yj'];
            $sanke = $store_business['sanke'];
            if (is_numeric($sanke_yj) && $sanke_yj != 0 && is_numeric($sanke) && $sanke != 0) {
                $udata['sanke_kdj'] = floatBc("/", $sanke_yj, $sanke);
            }
            if ($sanke_yj == 0 || $sanke == 0) {
                $udata['sanke_kdj'] = 0;
            }
        }
        if ($field == 'xka_num_1' || $field == 'xka_num_2' || $field == 'cika_num' || $field == true) {
            $udata['deal_num'] = $store_business['cika_num'] + $store_business['xka_num_1'] + $store_business['xka_num_2'];
        }
        if ($field == 'total_yj' || $field == 'sanke_yj' || $field == true) {
            $udata['haoka_yj'] = floatBc("-", $store_business['total_yj'], $store_business['sanke_yj']);
        }

        $re = Db::name('store_business')->where(['id' => $id])->update($udata);

        $re_d = Db::name('store_business')->where(['id' => $id])
            ->field("*, IF(sanke = 0, NULL, deal_num / sanke) AS result , IF(service_num = 0, NULL, (sanke_yj + haoka_yj) / service_num) AS total_kdj ")->find();

        if (!empty($re_d)) {
            if (empty($re_d['result'])) {
                $re_d['result'] = 0;
            }
            if (empty($re_d['total_kdj'])) {
                $re_d['total_kdj'] = 0;
            }
            Db::name('store_business')->where(['id' => $id])->update(
                ['sanke_deal_rate' => $re_d['result'] * 100,
                    'total_kdj' => $re_d['total_kdj']]);
        }

        return $re;

    }

}
