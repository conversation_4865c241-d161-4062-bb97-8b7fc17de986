<?php
/**
 * @copyright Copyright (c) 2021 仲正堂工作室
 * @license https://opensource.org/licenses/GPL-3.0
 * @link https://www.gougucms.com
 */
declare (strict_types=1);

namespace app\api\controller;

use app\api\BaseController;
use app\message\service\QiWeiService;
use app\oa\controller\Approve;
use PhpOffice\PhpSpreadsheet\Calculation\Logical\Boolean;
use think\facade\Db;
use think\Session;

require_once '../extend/qiyuesuo/Util.php';

class Qiyuesuo
{

    public function qiyuesuo()
    {
        $param = get_params();

        if (empty($param['contract_id'])) {
            return to_assign(0, '上传成功', []);
        }
        $arr = $this->get_qiyue_file($param['contract_id']);

        return to_assign(0, '上传成功', $arr);
    }


    public function get_qiyue_file($contract_id)
    {
        //3369560479918728166

        $sdkClient = \Util::getSDk();
        $request = new \ContractDownloadUrlRequest();
        $request->setContractId($contract_id);
        $request->setDownloadItems(['ATTACHMENT']);
        $result = $sdkClient->service($request);

        $result = json_decode($result);

        $arr = [];

        if ($result->code != 0) {
            return $arr;
        }

        $result = $result->result;

        //dump($result);

        $downloadUrls = $result->downloadUrls;
        foreach ($downloadUrls as $k => $v) {
            //dump($downloadUrls[$k]);
            //dump($downloadUrls[$k]->title);
            $arr[] = [
                'title' => $downloadUrls[$k]->title,
                'url' => $downloadUrls[$k]->downloadUrl
            ];

            //file_put_contents( "./storage/qiyuesuo/" .  $downloadUrls[$k]->title, $downloadUrls[$k]->downloadUrl);
            //file_put_contents("./storage/qiyuesuo/" . $downloadUrls[$k]->title, fopen($downloadUrls[$k]->downloadUrl, 'r'));
            //$this->downloadFileFromUrl($downloadUrls[$k]->downloadUrl,"./storage/qiyuesuo/");
        }

        return $arr;

    }

    public function contract_detail($contract_id)
    {
        $sdkClient = \Util::getSDk();
        $request = new \ContractDetailRequest();
        $request->setContractId($contract_id);
        $result = $sdkClient->service($request);

        if (!isset($result['result']) || empty($result['result'])){
            return null;
        }
        return $result;
    }

    public function contract_stream($contract_id)
    {
        $sdkClient = \Util::getSDk();
        $request = new \ContractStreamRequest();
        $request->setContractId("3373563817542889879");
        $result = $sdkClient->service($request);

        return $result;
    }

    function downloadFileFromUrl($url, $savePath)
    {
        $url = 'https://m.qiyuesuo.com/stateless/download?key=c9d3ce5c-b3b8-4393-b796-56c5ff717766';

// 初始化cURL会话
        $ch = curl_init($url);

// 初始化保存文件的目录名
        $dir = './';

// 函数返回文件名
        $file_name = basename($url);

// 将文件保存到给定位置
        $save_file_loc = $dir . $file_name;

// 打开文件
        $fp = fopen($save_file_loc, 'wb');

// 为cURL传输设置一个选项
        curl_setopt($ch, CURLOPT_FILE, $fp);
        curl_setopt($ch, CURLOPT_HEADER, 0);

// 执行cURL会话
        curl_exec($ch);

// 关闭cURL会话并释放所有资源
        curl_close($ch);

// 关闭文件
        fclose($fp);
    }


    public function ajax($url, $data, $type = 'POST')
    {
        // 创建CURL句柄
        $ch = curl_init();
        // 设置请求的URL地址
        curl_setopt($ch, CURLOPT_URL, $url);
        // 设置请求头信息
        curl_setopt($ch, CURLOPT_HTTPHEADER, array(
            'Content-Type: application/json; charset=utf-8'
        ));
        // 设置请求方法
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, $type);
        // 设置传递的数据
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        // 设置返回数据不直接输出
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);    // 信任任何证
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
        // 执行请求并获取响应数据
        curl_setopt($ch, CURLOPT_HTTP_VERSION, CURL_HTTP_VERSION_1_0);
        $response = curl_exec($ch);

        // 输出响应数据
        if (is_bool($response)) {
            var_dump(curl_error($ch));
            exit();
        }
        // 关闭CURL句柄
        curl_close($ch);
        return $response;
    }


    public function asyn_admin()
    {
        $admin = Db::name('Admin')
            ->alias('a')
            ->field('a.*,
            ae.*,a.id as id')
            ->leftJoin('admin_expand ae ', 'a.id= ae.id')
            ->where([
                ['ae.contract_link', 'EXP', Db::raw("IS not NULL")],
                ['ae.contract_link', '<>', ''],
                ['ae.contract_start_date', '=', ''],
            ])->select()->toArray();

        foreach ($admin as $k => $v){
            details_qiyuesuo($v);
        }

    }


    public function getTemplateDetail()
    {
        $sdkClient = \Util::getSDk();
        // 模板详情
        $templateDetailRequest = new \TemplateDetailRequest();
        $templateDetailRequest->setTemplateId('3299228424031302306');
        $result = $sdkClient->service($templateDetailRequest);
        dump($result);
    }


    public function getT()
    {
        $sdkClient = \Util::getSDk();
        /*\* 合同基本信息 *\*/
$contract = new \Contract();
//        $contract->setId("");
        $contract->setSubject("测试合同");
        $contract->setDescription("合同描述");
//        $contract->setSn("123321");
//        $contract->setExpireTime("2019-07-25 00:00:00");
//        $contract->setOrdinal(true);
//        $contract->setSend(false);
        /*\*指定业务分类*\*/
$category = new \Category();
//        $category->setId("3299312802044420351");
        $contract->setCategory($category);
        $creator = new \User();
        $creator->setContact("13918744562");
        $creator->setContactType("MOBILE");
        $contract->setCreator($creator);


        /*\*个人签署方*\*/
$personalSignatory = new \Signatory();
        $personalSignatory->setTenantType("PERSONAL");
        $personalSignatory->setTenantName("PERSONAL-50");
        $personalSignatory->setSerialNo(2);
        $receiver = new \User();
        $receiver->setContact("10100000000");
        $receiver->setContactType("MOBILE");
        $personalSignatory->setReceiver($receiver);
        /*\*所有签署方*\*/
$signatories = array();
        array_push($signatories, $personalSignatory);
        /*\*填写模板参数*\*/
$templateParam1 = new \TemplateParam();
        $templateParam1->setName("名称");
        $templateParam1->setValue("1111111");
        $templateParams = array();
        array_push($templateParams, $templateParam1);
        /*\*合同签署方、文档、文档参数*\*/
$contract->setSignatories($signatories);
        $contract->setTemplateParams($templateParams);
        $baseRequest = new \ContractDraftRequest($contract);
        $result = $sdkClient->service($baseRequest);
        print_r($result);
    }

}
