<?php
namespace app\dividend\model;

use think\Model;

class DividendPayment extends Model
{
    protected $table = 'oa_dividend_payment';
    
    // 设置字段信息
    protected $schema = [
        'id'                    => 'int',
        'shareholder_name'      => 'string',
        'shareholder_ids'       => 'string',
        'period'                => 'string',
        'payable_amount'        => 'decimal',
        'adjustment_amount'     => 'decimal',
        'actual_payable_amount' => 'decimal',
        'paid_amount'           => 'decimal',
        'unpaid_amount'         => 'decimal',
        'remark'                => 'string',
        'is_delete'             => 'int',
        'delete_time'           => 'int',
        'create_time'           => 'int',
        'update_time'           => 'int',
    ];

    // 设置字段自动时间戳
    protected $autoWriteTimestamp = true;
    protected $createTime = 'create_time';
    protected $updateTime = 'update_time';

    // 软删除
    protected $deleteTime = 'delete_time';

    /**
     * 获取股东分红清单列表
     * @param array $where 查询条件
     * @return array
     */
    public function getPaymentList($where = [])
    {
        $query = $this->where('is_delete', 0);
        
        // 年份筛选
        if (!empty($where['year'])) {
            $query->where('period', 'like', $where['year'] . '-%');
        }
        
        // 分红人筛选
        if (!empty($where['shareholder_name'])) {
            $query->where('shareholder_name', 'like', '%' . $where['shareholder_name'] . '%');
        }
        
        return $query->order('shareholder_name asc, period asc')->select()->toArray();
    }

    /**
     * 获取某个分红人的年度数据
     * @param string $shareholderName 分红人姓名
     * @param string $year 年份
     * @return array
     */
    public function getShareholderYearData($shareholderName, $year)
    {
        return $this->where('is_delete', 0)
            ->where('shareholder_name', $shareholderName)
            ->where('period', 'like', $year . '-%')
            ->order('period asc')
            ->select()
            ->toArray();
    }

    /**
     * 更新或创建分红清单记录（修改时先软删除再创建，实现修改留痕）
     * @param array $data 数据
     * @return bool
     */
    public function savePaymentData($data)
    {
        // 检查是否存在记录
        $existing = $this->where('is_delete', 0)
            ->where('shareholder_name', $data['shareholder_name'])
            ->where('period', $data['period'])
            ->find();

        if ($existing) {
            // 修改时先软删除再创建，实现修改留痕
            $this->startTrans();
            try {
                // 1. 软删除现有记录（为避免唯一索引冲突，修改分红人姓名添加时间戳后缀）
                $deleteTime = time();
                $existing->save([
                    'shareholder_name' => $existing['shareholder_name'] . '_deleted_' . $deleteTime,
                    'is_delete' => 1,
                    'delete_time' => $deleteTime
                ]);

                // 2. 创建新记录
                $newRecord = new self();
                $result = $newRecord->save($data);

                $this->commit();
                return $result;
            } catch (\Exception $e) {
                $this->rollback();
                return false;
            }
        } else {
            // 创建新记录
            return $this->save($data);
        }
    }

    /**
     * 批量更新分红清单数据
     * @param string $shareholderName 分红人姓名
     * @param array $monthlyData 月度数据数组
     * @return bool
     */
    public function batchUpdatePaymentData($shareholderName, $monthlyData)
    {
        $this->startTrans();
        try {
            foreach ($monthlyData as $data) {
                $data['shareholder_name'] = $shareholderName;
                if (!$this->savePaymentData($data)) {
                    throw new \Exception('保存数据失败');
                }
            }
            $this->commit();
            return true;
        } catch (\Exception $e) {
            $this->rollback();
            return false;
        }
    }

    /**
     * 获取指定周期的所有分红人列表
     * @param string $period 统计周期
     * @return array
     */
    public function getShareholdersByPeriod($period)
    {
        return $this->where('is_delete', 0)
            ->where('period', $period)
            ->field('shareholder_name, shareholder_ids')
            ->group('shareholder_name')
            ->select()
            ->toArray();
    }

    /**
     * 检查指定周期是否已有计算数据
     * @param string $period 统计周期
     * @return bool
     */
    public function hasCalculatedData($period)
    {
        $count = $this->where('is_delete', 0)
            ->where('period', $period)
            ->count();

        return $count > 0;
    }

    /**
     * 获取分红人在指定年份的汇总数据
     * @param string $shareholderName 分红人姓名
     * @param string $year 年份
     * @return array
     */
    public function getShareholderYearSummary($shareholderName, $year)
    {
        $data = $this->where('is_delete', 0)
            ->where('shareholder_name', $shareholderName)
            ->where('period', 'like', $year . '-%')
            ->field([
                'SUM(payable_amount) as total_payable_amount',
                'SUM(adjustment_amount) as total_adjustment_amount',
                'SUM(actual_payable_amount) as total_actual_payable_amount',
                'SUM(paid_amount) as total_paid_amount',
                'SUM(unpaid_amount) as total_unpaid_amount',
                'COUNT(id) as month_count'
            ])
            ->find();

        return $data ? $data->toArray() : [
            'total_payable_amount' => 0,
            'total_adjustment_amount' => 0,
            'total_actual_payable_amount' => 0,
            'total_paid_amount' => 0,
            'total_unpaid_amount' => 0,
            'month_count' => 0
        ];
    }

    /**
     * 删除指定周期的所有数据（软删除）
     * @param string $period 统计周期
     * @return bool
     */
    public function deleteByPeriod($period)
    {
        $deleteTime = time();
        return $this->where('is_delete', 0)
            ->where('period', $period)
            ->update([
                'is_delete' => 1,
                'delete_time' => $deleteTime,
                'shareholder_name' => \think\facade\Db::raw("CONCAT(shareholder_name, '_deleted_', {$deleteTime})")
            ]);
    }
}
