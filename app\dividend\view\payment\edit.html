{extend name="../../base/view/common/base" /}

{block name="style"}
<style>
    /* 编辑页面样式 */
    .layui-card {
        border: 1px solid #e6e6e6;
        border-radius: 4px;
        margin-bottom: 15px;
    }

    .layui-card-header {
        background-color: #f8f8f8;
        padding: 10px 15px;
        border-bottom: 1px solid #e6e6e6;
        font-weight: bold;
    }

    .layui-card-body {
        padding: 15px;
    }

    /* 表格样式优化 */
    .layui-table {
        margin-bottom: 0;
    }

    .layui-table td {
        padding: 8px;
        vertical-align: middle;
    }

    .layui-table th {
        background-color: #f8f8f8;
        font-weight: bold;
        text-align: left;
    }

    /* 必填项红色星号 */
    font {
        color: #FF6347;
        margin-left: 3px;
    }

    /* 页面内容区域 */
    .edit-content {
        padding: 20px;
        height: 100%;
        box-sizing: border-box;
        overflow-y: auto;
    }

    /* 月份数据表格样式 */
    #monthlyTable .layui-input {
        height: 32px;
        line-height: 32px;
    }

    /* 只读字段样式 */
    .readonly-field {
        background: #f5f5f5;
        color: #666;
        cursor: not-allowed;
        border: none;
    }

    /* 计算字段样式 */
    .calculated-field {
        background: #f0f8ff;
        font-weight: bold;
    }
</style>
{/block}

{block name="body"}
<div class="edit-content">
    <!-- 页面标题 -->
    <div style="margin-bottom: 20px;">
        <h3 style="margin: 0 0 15px 0; font-size: 18px; font-weight: bold; color: #333;">编辑股东分红清单表</h3>

        <!-- 重要提示 -->
        <div class="layui-alert layui-alert-warning" style="border: 2px solid #ff0000; border-left-width: 8px; border-radius: 6px; box-shadow: 0 2px 8px rgba(255,0,0,0.08); padding: 18px 20px; margin-bottom: 18px;">
            <h4 style="color: #d32f2f; font-weight: bold; margin-bottom: 10px;">重要提示*</h4>
            <p style="font-size: 14px; color: #ff0000; font-weight: bold; margin-bottom: 10px;">
                1. 此操作将影响股东分红清单表，请务必谨慎操作！
            </p>
            <p style="font-size: 14px; color: #d32f2f; font-weight: bold; margin-bottom: 10px;">
                2. 调整金额为正数表示扣减，负数表示增加，实际应付金额 = 应付金额 - 调整金额
            </p>
            <p style="font-size: 14px; color: #d32f2f; font-weight: bold; margin-bottom: 0;">
                3. 只有数据库中存在的月份数据才可编辑，其他月份仅供查看
            </p>
        </div>
    </div>

    <form class="layui-form" lay-filter="editForm" id="editForm">
        <input type="hidden" name="shareholder_name" id="edit_shareholder_name">
        <input type="hidden" name="year" id="edit_year">
        <input type="hidden" name="shareholder_ids" id="edit_shareholder_ids">

        <!-- 分红人基础信息 -->
        <div class="layui-card">
            <div class="layui-card-header">分红人基础信息</div>
            <div class="layui-card-body">
                <table class="layui-table">
                    <tr>
                        <td style="width: 120px; background-color: #f5f5f5; font-weight: bold; text-align: left;">分红人姓名</td>
                        <td style="width: 200px;">
                            <span id="edit_shareholder_name_display" style="font-weight: bold;"></span>
                        </td>
                        <td style="width: 120px; background-color: #f5f5f5; font-weight: bold; text-align: left;">统计年份</td>
                        <td style="width: 200px;">
                            <span id="edit_year_display" style="font-weight: bold;"></span>
                        </td>
                    </tr>
                </table>
            </div>
        </div>

        <!-- 月度分红数据 -->
        <div class="layui-card">
            <div class="layui-card-header">月度分红数据</div>
            <div class="layui-card-body">
                <table class="layui-table" id="monthlyTable">
                    <thead>
                        <tr>
                            <th style="width: 8%;">月份</th>
                            <th style="width: 15%;">应付金额(元)</th>
                            <th style="width: 15%;">调整金额(元)<font>*</font></th>
                            <th style="width: 15%;">实际应付金额(元)<font>*</font>
                                <br />
                                <span style="color: #999; margin-left: 5px; font-size: 12px;">
                                    (应付金额 - 调整金额)
                                </span>
                            </th>
                            <th style="width: 15%;">实付金额(元)<font>*</font></th>
                            <th style="width: 15%;">未付金额(元)
                                <br />
                                <span style="color: #999; margin-left: 5px; font-size: 12px;">
                                    (实际应付金额 - 实付金额)
                                </span>
                            </th>
                            <th style="width: 22%;">备注</th>
                        </tr>
                    </thead>
                    <tbody id="monthlyTableBody">
                        <!-- 动态生成月份行 -->
                    </tbody>
                </table>
            </div>
        </div>

        <!-- 提交按钮 -->
        <div style="margin-top: 15px;">
            <button class="layui-btn layui-btn-normal" lay-submit lay-filter="editSubmit">保存修改</button>
            <button type="button" class="layui-btn layui-btn-primary" id="cancelEditBtn">取消</button>
        </div>
    </form>
</div>
{/block}

{block name="script"}
<style>
/* 金额颜色样式 */
.positive-amount {
    color: #4CAF50 !important;
    font-weight: bold;
}

.negative-amount {
    color: #F44336 !important;
    font-weight: bold;
}

.zero-amount {
    color: #000000 !important;
}

/* 只读字段样式 */
.readonly-field {
    background-color: #f5f5f5 !important;
    cursor: not-allowed !important;
}

/* 计算字段样式 */
.calculated-field {
    background-color: #f0f8ff !important;
    cursor: not-allowed !important;
}

/* 不可编辑行样式 */
.non-editable-row {
    background-color: #fafafa !important;
    opacity: 0.7;
}

.non-editable-row input {
    background-color: #f0f0f0 !important;
    color: #999 !important;
    cursor: not-allowed !important;
    border-color: #ddd !important;
}

.non-editable-row td {
    background-color: #fafafa !important;
}

/* 不可编辑行的提示样式 */
.non-editable-row:hover {
    background-color: #f0f0f0 !important;
}
</style>
<script>
    // 限制输入框小数位数的函数
    function limitDecimalPlaces(input, maxDecimalPlaces) {
        var value = input.value;
        if (value.indexOf('.') !== -1) {
            var parts = value.split('.');
            if (parts[1] && parts[1].length > maxDecimalPlaces) {
                input.value = parts[0] + '.' + parts[1].substring(0, maxDecimalPlaces);
            }
        }
    }

    // 计算实际应付金额和未付金额（全局函数）
    function calculateAmountsForInput(input) {
        var $row = $(input).closest('tr');
        var payableAmount = parseFloat($row.find('.payable-amount').val()) || 0;
        var adjustmentAmount = parseFloat($row.find('.adjustment-amount').val()) || 0;
        var paidAmount = parseFloat($row.find('.paid-amount').val()) || 0;

        // 计算实际应付金额
        var actualPayableAmount = payableAmount - adjustmentAmount;
        $row.find('.actual-payable-amount').val(actualPayableAmount.toFixed(2));

        // 计算未付金额
        var unpaidAmount = actualPayableAmount - paidAmount;
        $row.find('.unpaid-amount').val(unpaidAmount.toFixed(2));

        // 更新颜色
        updateAmountColors($row);
    }

    // 更新金额字段的颜色（全局函数）
    function updateAmountColors($row) {
        // 应付金额颜色
        var payableAmount = parseFloat($row.find('.payable-amount').val()) || 0;
        var payableColor = payableAmount > 0 ? '#4CAF50' : (payableAmount < 0 ? '#F44336' : '#000000');
        $row.find('.payable-amount').css('color', payableColor);

        // 调整金额颜色
        var adjustmentAmount = parseFloat($row.find('.adjustment-amount').val()) || 0;
        var adjustmentColor = adjustmentAmount > 0 ? '#F44336' : '#000000';
        $row.find('.adjustment-amount').css('color', adjustmentColor);

        // 实际应付金额颜色
        var actualPayableAmount = parseFloat($row.find('.actual-payable-amount').val()) || 0;
        var actualPayableColor = actualPayableAmount > 0 ? '#4CAF50' : (actualPayableAmount < 0 ? '#F44336' : '#000000');
        $row.find('.actual-payable-amount').css('color', actualPayableColor);

        // 实付金额颜色
        var paidAmount = parseFloat($row.find('.paid-amount').val()) || 0;
        var paidColor = paidAmount > 0 ? '#4CAF50' : '#000000';
        $row.find('.paid-amount').css('color', paidColor);

        // 未付金额颜色
        var unpaidAmount = parseFloat($row.find('.unpaid-amount').val()) || 0;
        var unpaidColor = unpaidAmount > 0 ? '#F44336' : '#000000';
        $row.find('.unpaid-amount').css('color', unpaidColor);
    }

    const moduleInit = ['tool'];

    function gouguInit() {
        var form = layui.form, tool = layui.tool;

        // 获取URL参数
        function getUrlParam(name) {
            var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)");
            var r = window.location.search.substr(1).match(reg);
            if (r != null) return decodeURIComponent(r[2]);
            return null;
        }

        var shareholderName = getUrlParam('shareholder_name');
        var year = getUrlParam('year');

        if (!shareholderName || !year) {
            layer.msg('参数错误', {icon: 2});
            return;
        }

        // 加载分红人数据
        loadShareholderData(shareholderName, year);

        // 获取分红人年度数据
        function loadShareholderData(shareholderName, year) {
            layer.load(1);
            $.ajax({
                url: '/dividend/payment/getShareholderYearData',
                type: 'post',
                dataType: 'json',
                data: {
                    shareholder_name: shareholderName,
                    year: year
                },
                success: function(res) {
                    layer.closeAll('loading');
                    if (res.code == 0) {
                        fillFormData(res.data);
                    } else {
                        layer.msg(res.msg, {icon: 2});
                    }
                },
                error: function() {
                    layer.closeAll('loading');
                    layer.msg('获取分红人数据失败', {icon: 2});
                }
            });
        }

        // 填充表单数据
        function fillFormData(data) {
            // 填充基础信息
            $('#edit_shareholder_name').val(data.shareholder_name);
            $('#edit_year').val(data.year);
            $('#edit_shareholder_name_display').text(data.shareholder_name);
            $('#edit_year_display').text(data.year);

            // 填充shareholder_ids隐藏字段
            $('#edit_shareholder_ids').val(data.shareholder_ids || '');

            // 填充月度数据
            fillMonthlyData(data.data || []);
        }

        // 填充月度数据
        function fillMonthlyData(monthlyData) {
            var tbody = $('#monthlyTableBody');
            tbody.empty();

            if (monthlyData && monthlyData.length > 0) {
                $.each(monthlyData, function(index, monthData) {
                    addMonthRow(monthData, index);
                });
            }

            // 初始化所有行的颜色
            updateAllRowColors();
        }

        // 添加月份行
        function addMonthRow(data, index) {
            data = data || {};
            var period = data.period || '';
            var monthDisplay = period ? period.split('-')[1] + '月' : '';
            // 处理exists_in_db字段
            var existsInDb = data.exists_in_db;

            // 根据数据是否存在决定样式和是否可编辑
            var rowClass = existsInDb ? '' : 'non-editable-row';
            var disabledAttr = existsInDb ? '' : 'disabled';
            var readonlyAttr = existsInDb ? '' : 'readonly';
            var titleAttr = existsInDb ? '' : 'title="此月份数据不存在，无法编辑"';

            var html = '<tr class="' + rowClass + '" ' + titleAttr + '>' +
                '<td style="text-align: left; font-weight: bold; background-color: #f5f5f5;">' + monthDisplay + '</td>' +
                '<td>' +
                    '<input type="number" name="monthly_data[' + index + '][payable_amount]" ' +
                    'class="layui-input payable-amount readonly-field" placeholder="0.00" ' +
                    'value="' + (data.payable_amount || '0.00') + '" step="0.01" readonly ' +
                    'oninput="limitDecimalPlaces(this, 2)">' +
                    '<input type="hidden" name="monthly_data[' + index + '][period]" value="' + period + '">' +
                    '<input type="hidden" name="monthly_data[' + index + '][exists_in_db]" value="' + existsInDb + '">' +
                    '<input type="hidden" name="monthly_data[' + index + '][shareholder_ids]" value="' + (data.shareholder_ids || '') + '">' +
                '</td>' +
                '<td>' +
                    '<input type="number" name="monthly_data[' + index + '][adjustment_amount]" ' +
                    'class="layui-input adjustment-amount" placeholder="0.00" ' +
                    'value="' + (data.adjustment_amount || '0.00') + '" step="0.01" ' +
                    disabledAttr + ' ' + readonlyAttr + ' ' +
                    'oninput="limitDecimalPlaces(this, 2); calculateAmountsForInput(this)">' +
                '</td>' +
                '<td>' +
                    '<input type="number" name="monthly_data[' + index + '][actual_payable_amount]" ' +
                    'class="layui-input actual-payable-amount calculated-field" placeholder="0.00" ' +
                    'value="' + (data.actual_payable_amount || '0.00') + '" step="0.01" readonly ' +
                    'oninput="limitDecimalPlaces(this, 2); updateAmountColors($(this).closest(\'tr\'))">' +
                '</td>' +
                '<td>' +
                    '<input type="number" name="monthly_data[' + index + '][paid_amount]" ' +
                    'class="layui-input paid-amount" placeholder="0.00" ' +
                    'value="' + (data.paid_amount || '0.00') + '" step="0.01" ' +
                    disabledAttr + ' ' + readonlyAttr + ' ' +
                    'oninput="limitDecimalPlaces(this, 2); calculateAmountsForInput(this)">' +
                '</td>' +
                '<td>' +
                    '<input type="number" name="monthly_data[' + index + '][unpaid_amount]" ' +
                    'class="layui-input unpaid-amount readonly-field" placeholder="0.00" ' +
                    'value="' + (data.unpaid_amount || '0.00') + '" step="0.01" readonly>' +
                '</td>' +
                '<td>' +
                    '<input type="text" name="monthly_data[' + index + '][remark]" ' +
                    'class="layui-input" placeholder="备注" ' +
                    'value="' + (data.remark || '') + '" ' + disabledAttr + ' ' + readonlyAttr + '>' +
                '</td>' +
                '</tr>';

            $('#monthlyTableBody').append(html);
        }

        // 更新所有行的颜色
        function updateAllRowColors() {
            $('#monthlyTableBody tr').each(function() {
                updateAmountColors($(this));
            });
        }

        // 监听表单提交
        form.on('submit(editSubmit)', function(data) {
            // 过滤只提交数据库中真实存在的数据
            var filteredData = {
                shareholder_name: data.field.shareholder_name,
                year: data.field.year,
                shareholder_ids: data.field.shareholder_ids || '',  // 添加shareholder_ids字段
                monthly_data: []
            };

            // 手动解析monthly_data数组结构
            // LayUI表单序列化会将monthly_data[0][field]格式的字段保持为扁平结构
            var monthlyDataMap = {};

            // 遍历所有字段，找出monthly_data相关的字段
            Object.keys(data.field).forEach(function(key) {
                var match = key.match(/^monthly_data\[(\d+)\]\[(.+)\]$/);
                if (match) {
                    var index = parseInt(match[1]);
                    var fieldName = match[2];

                    if (!monthlyDataMap[index]) {
                        monthlyDataMap[index] = {};
                    }
                    monthlyDataMap[index][fieldName] = data.field[key];
                }
            });

            // 遍历月度数据，只保留数据库中存在的数据
            Object.keys(monthlyDataMap).forEach(function(index) {
                var monthData = monthlyDataMap[index];

                // 检查exists_in_db字段，只有为true的数据才提交
                if (monthData.exists_in_db === 'true') {
                    filteredData.monthly_data.push(monthData);
                }
            });

            // 如果没有可编辑的数据，提示用户
            if (filteredData.monthly_data.length === 0) {
                layer.msg('没有可编辑的数据', {icon: 2});
                return false;
            }

            // 提交数据
            layer.load(1);
            $.ajax({
                url: '/dividend/payment/edit',
                type: 'post',
                dataType: 'json',
                data: filteredData,
                success: function(res) {
                    layer.closeAll('loading');
                    if (res.code == 0) {
                        layer.msg('保存成功', {icon: 1});
                        // 关闭抽屉并刷新父页面
                        tool.sideClose(1000);
                    } else {
                        layer.msg(res.msg, {icon: 2});
                    }
                },
                error: function() {
                    layer.closeAll('loading');
                    layer.msg('保存失败', {icon: 2});
                }
            });

            return false;
        });

        // 取消编辑按钮事件
        $(document).on('click', '#cancelEditBtn', function() {
            tool.sideClose();
        });
    }
</script>
{/block}
