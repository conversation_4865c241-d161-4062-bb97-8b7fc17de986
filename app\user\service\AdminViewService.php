<?php

namespace app\user\service;

use think\facade\Db;

class AdminViewService
{

    public function addUser($admin_view)
    {
        $admin = Db::name("admin")->where(['mobile' => $admin_view['mobile']])->find();

        if (!empty($admin)) return;
        $data = array();
        $data['username'] = $admin_view['mobile']; //
        $data['pwd'] = "52dab9db1f7d480b20bca9559bf64c01"; //
        $data['salt'] = "svm5wu27kgdpet031z6x"; //
        $data['name'] = $admin_view['name']; //
        $data['mobile'] = $admin_view['mobile']; //
        $data['sex'] = $admin_view['sex']; //
        //$data['email'] = $admin_view['email']; //
        $data['thumb'] = isset($admin_view['thumb']) ? $admin_view['thumb'] : "/static/home/<USER>/icon.png"; //
        $data['theme'] = "white"; //

        $entry_time = time();
        $data['create_time'] = time(); //
        $data['entry_time'] = isset($admin_view['entry_time']) ? $admin_view['entry_time'] : $entry_time;
        $data['entry_date'] = isset($admin_view['entry_date']) ? $admin_view['entry_date'] : date("Y-m-d H:i:s", $entry_time);
        $data['type'] = 2;

        if ($admin_view['did'] != 0) {
            $data['did'] = $admin_view['did'];
            $data['main_did'] = $admin_view['did'];
        } else {
            $data['did'] = '';
        }

        if ($admin_view['dstatus'] == 2) {
            $data['position_id'] = 17;
            $data['main_position_id'] = 17;
            $data['rank_id'] = 0;
        }elseif ($admin_view['dstatus'] == 1){
            $data['position_id'] = 95;
            $data['main_position_id'] = 95;
            $data['rank_id'] = 0;
        }

        $re_id = Db::name("admin")->insertGetId($data);

        Db::name("admin")->where(['id' => $re_id])->update(['workno' => numberStrPad($re_id)]);

        $re_age = getAgeFromIdCard($admin_view['id_card']);

        $ex_data = [
            'id' => $re_id,
            'id_card_number' => $admin_view['id_card'],
            'birthday' => $re_age != false ? $re_age['birthday'] : null,
            'age' => $re_age != false ? $re_age['age'] : $admin_view['age'],
            'education' => $admin_view['edu_background'],
            'graduation_school' => $admin_view['edu_school'],
            'major' => $admin_view['edu_major'],
            'certificates' => empty($admin_view['certificate']) ? $admin_view['certificate'] : '',
            'file_ids_edu' => isset($admin_view['file_ids_edu']) && empty($admin_view['file_ids_edu']) ? $admin_view['file_ids_edu'] : '',
            'is_cjz' => empty($admin_view['is_cjz']) ? 0 : 1,

            'bank_name' => isset($admin_view['bank_name']) ? $admin_view['bank_name'] : '',
            'salary_card_number' => isset($admin_view['salary_card_number']) ? $admin_view['salary_card_number'] : '',

            'contract_link' => isset($admin_view['contract_link']) ? $admin_view['contract_link'] : '',
            'is_valid' => isset($admin_view['is_valid']) ? $admin_view['is_valid'] : 0,
            'valid_start_date' => isset($admin_view['valid_start_date']) ? $admin_view['valid_start_date'] : null,
            'valid_end_date' => isset($admin_view['valid_end_date']) ? $admin_view['valid_end_date'] : null,
        ];
        Db::name("admin_expand")->insertGetId($ex_data);

        $kdt_id = Db::name("department")->where(['id' => $admin_view['did']])->value('kdt_id');

        if (!empty($kdt_id)) {
            $staff_info = Db::connect('mt')->table('staff_info')
                ->where([
                    ['name', 'like', '%' . $admin_view['name'] . '%'],
                    ['kdt_id', '=', $kdt_id]])
                ->field("id,name,mobile,yz_open_id")
                ->find();

            if (!empty($staff_info)) {
                $admin = Db::connect('mysql')->table('oa_admin')
                    ->where(['id' => $re_id])->update(['yz_uid' => $staff_info['yz_open_id']]);
            }
        }

        //
        $data['id'] = $re_id;
        details_qiyuesuo($data);

        return $re_id;

    }

    public function approve_adduser($jsoncode, $in_aid)
    {
        $in_admin_view = Db::name("admin_view")->where(['id' => $jsoncode['associated_id']])->find();

        $year = substr($jsoncode['id_card'], 0, 4);
        $month = substr($jsoncode['id_card'], 4, 2);
        $day = substr($jsoncode['id_card'], 6, 2);


        $admin_view = [
            'mobile' => $jsoncode['mobile'],
            'name' => $jsoncode['a_view_name'],
            'sex' => $jsoncode['sex'],
            'entry_time' => strtotime($jsoncode['entry_date']),
            'entry_date' => $jsoncode['entry_date'],
            'did' => $jsoncode['did'],
            'dstatus' => $jsoncode['position'] == "技师" ? 2 : 1,
            'id_card' => $jsoncode['id_card'],
            'birthday' => "{$year}-{$month}-{$day}",
            'age' => $jsoncode['age'],
            'edu_background' => $jsoncode['edu_background'],
            'edu_school' => $jsoncode['edu_school'],
            'edu_major' => $jsoncode['edu_major'],
            'certificate' => $in_admin_view['certificate'],
            'is_cjz' => $jsoncode['is_cjz'],
            'bank_name' => $jsoncode['bank_name'],
            'salary_card_number' => $jsoncode['bank_number'],
            'thumb' => $jsoncode['thumb'],
            'contract_link' => $jsoncode['contract_link'],
        ];

        $aid = $this->addUser($admin_view);

        if (!empty($aid)) {
            Db::name("admin_view")->where(['id' => $jsoncode['associated_id']])->update([
                'aid' => $aid,
                'is_onboard' => 2
            ]);
        }
        Db::name("admin_view_record")->insertGetId(
            [
                'aview_id' => $jsoncode['a_view_id'],
                'aid' => $in_aid,
                'aname' => Db::name("admin")->where(['id' => $in_aid])->value('name'),
                'create_time' => time(),
                's_str' => '审批完成入职',
                'type' => 4,
            ]
        );

        return $aid;

    }


    //外部人员 魔学院账号同步
    public function mobile_moxueyuan_out($admin_view)
    {
        $TrainService = new TrainService();
//        $admin_view['mobile'] = ***********;

        if (empty($admin_view['mxy_id'])){
            $userid = $TrainService->convertToUserid($admin_view['mobile']);
            Db::name("admin_view")->where(['id' => $admin_view['id']])->update(
                ['mxy_id' => $userid]
            );
        }else{
            $userid = $admin_view['mxy_id'];
        }

        //培训记录
        $admin_view['courseware_status'] = "";
        if (!empty($userid)) {
            $admin_view['userid'] = $userid;
            $admin_view['courseware_status'] = $TrainService->syn_train_progress_userid(
                382539,
                $userid ,
                $admin_view['id'] ,
                $admin_view['name']
            );

            if(!empty($admin_view['courseware_status'])){
                Db::name("admin_view")->where(['id' => $admin_view['id']])->update(
                    ['is_train' => 2]
                );
            }

            $re_ = $TrainService->syn_exams_result_userid(
                5150648,
                $userid ,
                $admin_view['id'] ,
                $admin_view['name']
            );

            $admin_view['courseware_score'] = empty($re_['score']) ? 0 : $re_['score'];
            $admin_view['courseware_times'] = empty($re_['makeUp'] ) ? 0 : $re_['makeUp'] + 1;
        }

        return $admin_view;
    }

}