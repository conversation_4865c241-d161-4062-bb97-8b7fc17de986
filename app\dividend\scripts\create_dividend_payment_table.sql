-- 创建股东分红清单表
CREATE TABLE `oa_dividend_payment` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `shareholder_name` varchar(50) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '分红人姓名',
  `shareholder_ids` varchar(255) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '分红人ID列表（多个ID用逗号分隔）',
  `period` varchar(20) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '月份（如：2024-05）',
  `payable_amount` decimal(15,2) NOT NULL DEFAULT '0.00' COMMENT '应付金额（元）',
  `adjustment_amount` decimal(15,2) NOT NULL DEFAULT '0.00' COMMENT '调整金额（元）',
  `actual_payable_amount` decimal(15,2) NOT NULL DEFAULT '0.00' COMMENT '实际应付金额（元）',
  `paid_amount` decimal(15,2) NOT NULL DEFAULT '0.00' COMMENT '实付金额（元）',
  `unpaid_amount` decimal(15,2) NOT NULL DEFAULT '0.00' COMMENT '未付金额（元）',
  `remark` text COLLATE utf8mb4_general_ci COMMENT '备注信息',
  `is_delete` tinyint(1) NOT NULL DEFAULT '0' COMMENT '删除标记(0:正常,1:已删除)',
  `delete_time` int NOT NULL DEFAULT '0' COMMENT '删除时间',
  `create_time` int NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` int NOT NULL DEFAULT '0' COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uk_shareholder_period` (`shareholder_name`,`period`) USING BTREE COMMENT '分红人月份唯一索引',
  KEY `idx_period` (`period`) USING BTREE COMMENT '月份索引',
  KEY `idx_is_delete` (`is_delete`) USING BTREE COMMENT '删除标记索引',
  KEY `idx_create_time` (`create_time`) USING BTREE COMMENT '创建时间索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='股东分红清单表';

-- 插入测试数据
INSERT INTO `oa_dividend_payment` (`shareholder_name`, `shareholder_ids`, `period`, `payable_amount`, `adjustment_amount`, `actual_payable_amount`, `paid_amount`, `unpaid_amount`, `remark`, `create_time`, `update_time`) VALUES
('张三', '1', '2025-01', 10000.00, 500.00, 9500.00, 8000.00, 1500.00, '1月分红', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('张三', '1', '2025-02', 12000.00, 0.00, 12000.00, 12000.00, 0.00, '2月分红', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('张三', '1', '2025-03', 8000.00, 200.00, 7800.00, 5000.00, 2800.00, '3月分红', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('李四', '2', '2025-01', 15000.00, 1000.00, 14000.00, 10000.00, 4000.00, '1月分红', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('李四', '2', '2025-02', 18000.00, 0.00, 18000.00, 18000.00, 0.00, '2月分红', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('王五', '3', '2025-01', 5000.00, 0.00, 5000.00, 3000.00, 2000.00, '1月分红', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('王五', '3', '2025-03', 6000.00, 300.00, 5700.00, 5700.00, 0.00, '3月分红', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('公司股东', '1,2,3', '2025-01', 50000.00, 2000.00, 48000.00, 40000.00, 8000.00, '1月公司股东分红', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('公司股东', '1,2,3', '2025-02', 60000.00, 1500.00, 58500.00, 58500.00, 0.00, '2月公司股东分红', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('公司股东', '1,2,3', '2025-03', 45000.00, 1000.00, 44000.00, 35000.00, 9000.00, '3月公司股东分红', UNIX_TIMESTAMP(), UNIX_TIMESTAMP());

-- 回滚脚本（如果需要删除表）
-- DROP TABLE IF EXISTS `oa_dividend_payment`;
