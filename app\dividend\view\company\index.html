{extend name="../../base/view/common/base" /}
{block name="style"}
<style>
    .layui-form-item {
        margin-bottom: 0;
        display: inline-block;
        vertical-align: top;
    }

    .layui-form-item .layui-inline {
        margin-bottom: 0;
        margin-right: 5px;
    }

    .layui-input-block {
        min-height: 24px;
    }

    .gg-form-bar .layui-input-inline{
        margin-bottom: 0;
        margin-right: 5px;
        vertical-align: top;
    }

    /* 多选下拉样式 */
    .layui-form-select dl {
        min-width: 100%;
    }
    .layui-form-select dl dd.layui-this {
        background-color: #5FB878;
        color: #fff;
    }

    .label-content {
        max-height: 34px !important;
        width: 125px !important;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }

    .xm-select {
        height: 34px !important;
        line-height: 34px !important;
    }

    /* 按钮组样式 */
    .filter-button-group .layui-btn {
        display: inline-block !important;
        vertical-align: top;
        margin-left: 5px;
    }
    .filter-button-group .layui-btn:first-child {
        margin-left: 0;
    }

    /* 合计行样式 */
    .layui-table-total {
        background-color: #f8f8f8;
        font-weight: bold;
    }

    .layui-table-total td {
        border-top: 2px solid #e6e6e6;
    }

</style>
{/block}

{block name="body"}
<div class="p-3" style="height:100%; box-sizing: border-box;">
    <div class="body-table" style="overflow:hidden;">
        <!-- 搜索表单 -->
        <form class="layui-form gg-form-bar border-t border-x" lay-filter="webform" style="padding-bottom:10px;">
            <div class="layui-form-item" style="margin-bottom: 10px">
                <input type="text" name="period" id="period"
                       placeholder="请选择统计周期"
                       autocomplete="off" class="layui-input">
            </div>
            <div id="store-select-container" class="layui-input-inline" style="width:200px; height: 38px;"></div>
            <div class="layui-form-item" style="margin-bottom: 10px">
                <input type="text" name="shareholder_name" id="shareholder_name"
                       placeholder="请输入分红人姓名"
                       autocomplete="off" class="layui-input">
            </div>
            <!-- 按钮组 -->
            <div class="layui-input-inline filter-button-group" style="width: auto; white-space: nowrap;">
                <button class="layui-btn layui-btn-normal" lay-submit="" lay-filter="webform"><i
                        class="layui-icon layui-icon-search mr-1"></i>搜索
                </button>
                <button type="button" class="layui-btn layui-btn-primary" id="resetBtn"><i
                        class="layui-icon layui-icon-refresh mr-1"></i>重置
                </button>
            </div>
        </form>
        <!-- 增加外边距，方便页面布局更美观 -->
        <div style="margin: 10px 0;">
            <span style="font-weight:600">公司股东分红表</span>
            <div style="font-weight:600; color: #F44336; font-size:12px;">[调整金额]是减数，正数表示扣减，负数表示增加</div>
        </div>
        <!-- 数据表格 -->
        <table class="layui-hide" id="dataTable" lay-filter="dataTable"></table>
    </div>
</div>

{/block}

{block name="script"}
<style>
/* 合计行样式 */
.layui-table-total .layui-table-cell {
    font-weight: bold;
}

/* 合并单元格样式 */
.layui-table-body td[rowspan] {
    vertical-align: middle !important;
    border-bottom: 1px solid #e6e6e6 !important;
}
</style>
<script>
    const moduleInit = ['tool', 'tablePlus', 'tableMerge'];

    function gouguInit() {
        var table = layui.tablePlus, tool = layui.tool, laydate = layui.laydate, form = layui.form;
        var tableMerge = layui.tableMerge;

        // 初始化统计周期选择器 - 默认上一个月
        var lastMonth = new Date();
        lastMonth.setMonth(lastMonth.getMonth() - 1);
        var lastMonthStr = lastMonth.getFullYear() + '-' + String(lastMonth.getMonth() + 1).padStart(2, '0');

        laydate.render({
            elem: '#period',
            type: 'month',
            value: lastMonthStr,
            done: function(value, date, endDate) {
                // 不立即刷新
            }
        });

        // 初始化门店多选组件
        var storeMultiSelect = xmSelect.render({
            el: '#store-select-container',
            name: 'store_ids',
            language: 'zn',
            filterable: true,
            data: [],
            model: { label: { type: 'xm-select-count', max: 0 } },
            prop: {
                name: 'title',
                value: 'id'
            },
            placeholder: '全部门店',
            on: function(data){
                // 门店选择变化时不立即刷新
            }
        });

        // 加载门店列表
        function loadStoreList() {
            $.ajax({
                url: '/api/dividend/getStoreList',
                type: 'post',
                dataType: 'json',
                success: function(res) {
                    if (res.code == 0) {
                        var storesForXmSelect = [];
                        if (res.data && res.data.length > 0) {
                            storesForXmSelect = res.data.map(function(item) {
                                return { id: item.id, title: item.title };
                            });
                        }
                        storeMultiSelect.update({
                            data: storesForXmSelect,
                            autoRow: true
                        });
                    } else {
                        layer.msg(res.msg, {icon: 2});
                    }
                },
                error: function(){
                    layer.msg('获取门店列表失败', {icon:2});
                }
            });
        }

        // 初始化加载门店列表
        loadStoreList();

        // 应用合计行颜色策略函数
        function applyTotalRowColors() {
            setTimeout(function() {
                // 直接通过属性选择器找到合计行的单元格
                $('.layui-table-total td').each(function(index) {
                    var $cell = $(this);
                    var cellText = $cell.text().trim();
                    var cellValue = parseFloat(cellText.replace(/[^\d.-]/g, ''));

                    // 根据列索引应用不同的颜色策略
                    switch(index) {
                        case 1: // 分红利润字段
                            var color = cellValue > 0 ? '#4CAF50' : (cellValue < 0 ? '#F44336' : '#000000');
                            $cell.find('.layui-table-cell').css('color', color);
                            break;
                        case 5: // 金额字段
                            var color = cellValue > 0 ? '#4CAF50' : (cellValue < 0 ? '#F44336' : '#000000');
                            $cell.find('.layui-table-cell').css('color', color);
                            break;
                        case 6: // 调整金额字段
                            if (cellText !== '-') {
                                var color = cellValue > 0 ? '#F44336' : '#000000';
                                $cell.find('.layui-table-cell').css('color', color);
                            }
                            break;
                        case 7: // 应付金额字段
                            if (cellText !== '-') {
                                var color = cellValue > 0 ? '#4CAF50' : (cellValue < 0 ? '#F44336' : '#000000');
                                $cell.find('.layui-table-cell').css('color', color);
                            }
                            break;
                    }
                });
            }, 200);
        }

        // 渲染表格
        try {
            layui.pageTable = table.render({
                elem: '#dataTable',
                title: '公司股东分红明细列表',
                page: false, // 不分页
                height: 'full-150',
                url: "/dividend/company/index",
                loading: true,
                even: true,
                totalRow: true,
                toolbar: true,
                where: {
                    period: lastMonthStr, // 默认加载上月数据
                    store_ids: [],
                    shareholder_name: ''
                },
                parseData: function(res) {
                    this.hasButtonAccess = res.hasButtonAccess;
                    return {
                        "code": res.code,
                        "msg": res.msg,
                        "count": res.count,
                        "data": res.data,
                        "totalRow": res.totalRow
                    };
                },
                cols: [[
                    {
                        field: 'store_name',
                        title: '门店',
                        align: 'center',
                        merge: true,
                        totalRowText: '合计'
                    },
                    {
                        field: 'dividend_profit',
                        title: '分红利润(元)',
                        align: 'center',
                        merge: ['store_name', 'dividend_profit'],
                        totalRow: true,
                        templet: function(d) {
                            var value = parseFloat(d.dividend_profit);
                            var color = value > 0 ? '#4CAF50' : (value < 0 ? '#F44336' : '#000000');
                            return '<span style="color: ' + color + ';">' + d.dividend_profit + '</span>';
                        }
                    },
                    {
                        field: 'company_shareholding_ratio',
                        title: '公司持股比例(%)',
                        align: 'center',
                        merge: ['store_name', 'company_shareholding_ratio']
                    },
                    {
                        field: 'shareholder_name',
                        title: '分红人',
                        align: 'center'
                    },
                    {
                        field: 'shareholding_ratio',
                        title: '持股比例(%)',
                        align: 'center'
                    },
                    {
                        field: 'actual_shareholding',
                        title: '实际持股(%)',
                        align: 'center'
                    },
                    {
                        field: 'amount',
                        title: '金额(元)',
                        align: 'center',
                        totalRow: true,
                        templet: function(d) {
                            var value = parseFloat(d.amount);
                            var color = value > 0 ? '#4CAF50' : (value < 0 ? '#F44336' : '#000000');
                            return '<span style="color: ' + color + ';">' + d.amount + '</span>';
                        }
                    },
                    {
                        field: 'adjustment_amount',
                        title: '调整金额(元)',
                        align: 'center',
                        totalRow: true,
                        templet: function(d) {
                            var value = parseFloat(d.adjustment_amount);
                            var color = value > 0 ? '#F44336' : '#000000';
                            return '<span style="color: ' + color + ';">' + d.adjustment_amount + '</span>';
                        }
                    },
                    {
                        field: 'payable_amount',
                        title: '应付金额(元)',
                        align: 'center',
                        totalRow: true,
                        templet: function(d) {
                            var value = parseFloat(d.payable_amount);
                            var color = value > 0 ? '#4CAF50' : (value < 0 ? '#F44336' : '#000000');
                            return '<span style="color: ' + color + ';">' + d.payable_amount + '</span>';
                        }
                    },
                    {
                        field: 'operate',
                        title: '操作',
                        align: 'center',
                        width: 120,
                        merge: ['store_name'],
                        templet: function(d) {
                            if (layui.pageTable.config.hasButtonAccess) {
                                return '<button class="layui-btn layui-btn-xs edit-store-btn" data-store-id="' + d.store_id + '" data-period="' + d.period + '">编辑</button>';
                            }
                            return '<span style="color: #999;"></span>';
                        }
                    }
                ]],
                done: function(res, curr, count) {
                    // 表格渲染完成后的回调
                    if (res.code === 0) {
                        // 使用 tableMerge 插件实现垂直合并单元格
                        tableMerge.render(this);

                        // 应用合计行颜色策略
                        applyTotalRowColors();
                    }
                }
            });
        } catch (e) {
            console.error('表格初始化失败:', e);
        }

        // 监听搜索按钮
        form.on('submit(webform)', function(data) {
            var selectedStoreIds = storeMultiSelect.getValue('value');
            layui.pageTable.reload({
                where: {
                    period: $('#period').val(),
                    store_ids: selectedStoreIds,
                    shareholder_name: $('#shareholder_name').val()
                },
                done: function(res, curr, count) {
                    // 重新加载后也要合并单元格
                    if (res.code === 0) {
                        // 使用正确的表格实例
                        tableMerge.render(this);

                        // 应用合计行颜色策略
                        applyTotalRowColors();
                    }
                }
            });
            return false;
        });

        // 监听重置按钮
        $('#resetBtn').on('click', function(){
            $('#period').val(lastMonthStr);
            $('#shareholder_name').val('');
            storeMultiSelect.setValue([]);
            form.render();

            // 重置后自动执行搜索，应用上月筛选条件
            layui.pageTable.reload({
                where: {
                    period: lastMonthStr,
                    store_ids: [],
                    shareholder_name: ''
                },
                done: function(res, curr, count) {
                    // 重新加载后也要合并单元格
                    if (res.code === 0) {
                        // 使用正确的表格实例
                        tableMerge.render(this);

                        // 应用合计行颜色策略
                        applyTotalRowColors();
                    }
                }
            });
        });

        // 监听编辑按钮点击
        $(document).on('click', '.edit-store-btn', function() {
            var storeId = $(this).data('store-id');
            var period = $(this).data('period');

            // 使用抽屉打开编辑页面
            tool.side('/dividend/company/edit?store_id=' + storeId + '&period=' + period, '编辑公司股东分红表');
        });

        // 监听抽屉关闭事件，刷新表格
        $(document).on('sideClose', function() {
            // 刷新表格数据
            if (layui.pageTable && layui.pageTable.reload) {
                layui.pageTable.reload({
                    done: function(res, curr, count) {
                        // 重新加载后也要合并单元格
                        if (res.code === 0) {
                            // 使用正确的表格实例
                            tableMerge.render(this);

                            // 应用合计行颜色策略
                            applyTotalRowColors();
                        }
                    }
                });
            }
        });
    }
</script>
{/block}
