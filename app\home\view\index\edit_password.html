{extend name="../../base/view/common/base" /}
<!-- 主体 -->
{block name="body"}
<form class="layui-form p-4">
	{notempty name="$admin.reg_pwd"}
	<div style="text-align:center; padding:20px 0; font-size:18px; color:#FF5722">初始化密码才能正常使用系统</div>
	{/notempty}
	<h3 class="pb-3">重置密码</h3>
	<table class="layui-table layui-table-form">
		<tr>
		  <td class="layui-td-gray">旧密码<font>*</font></td>
		  <td>
			<input type="password" lay-verify="required" name="old_pwd" placeholder="请输入旧密码" lay-reqText="请输入旧密码" autocomplete="off" class="layui-input">
		  </td>
		  <td class="layui-td-gray">用户名</td>
		  <td>{$admin.username}</td>
		</tr>
		<tr>
		  <td class="layui-td-gray">新密码<font>*</font></td>
		  <td>
			<input type="password" lay-verify="required" name="pwd" placeholder="请输入新密码" lay-reqText="请输入新密码"  autocomplete="off" class="layui-input">
		  </td>
		  <td class="layui-td-gray-2">确认新密码<font>*</font></td>
		  <td>
			<input type="password" lay-verify="required" name="pwd_confirm" placeholder="请再次输入新密码"  lay-reqText="请再次输入新密码" autocomplete="off" class="layui-input">
		  </td>
		</tr>
	</table>
	<div style="padding: 10px 0">
		<button class="layui-btn layui-btn-normal" lay-submit="" lay-filter="webform">立即提交</button>
		<button type="reset" class="layui-btn layui-btn-primary">重置</button>
	</div>
</form>
{/block}
<!-- /主体 -->

<!-- 脚本 -->
{block name="script"}
<script>
	const moduleInit = ['tool'];
	function gouguInit() {
		var form = layui.form;
    //监听提交
    form.on('submit(webform)', function (data) {
      $.ajax({
        url: "/home/<USER>/edit_password",
        type: 'post',
        data: data.field,
        success: function (e) {
          if (e.code == 0) {
            layer.msg('修改密码成功,使用新密码登录');
			//注销
			$.ajax({
				url: "/home/<USER>/login_out.html",
				success: function (e) {
					if (e.code == 0) {
						setTimeout(function () {
							parent.location.href = "/home/<USER>/index.html"
						}, 2000)
					}
				}
			})
          } else {
            layer.msg(e.msg);
          }
        }
      })
      return false;
    });
  }

</script>
{/block}
<!-- /脚本 -->