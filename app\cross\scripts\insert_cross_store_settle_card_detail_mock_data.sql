-- 次卡跨店结算明细表模拟数据插入脚本
-- 创建时间：2025-07-24
-- 说明：为次卡跨店结算明细表生成2024年7月份模拟数据，用于测试和开发

-- 清空现有数据（可选，根据需要取消注释）
-- TRUNCATE TABLE oa_cross_store_settle_card_detail;

-- 插入2024年7月份模拟数据
INSERT INTO `oa_cross_store_settle_card_detail` (
    `settlement_type`, `consume_store_id`, `product_name`, `product_quantity`,
    `card_name`, `settlement_amount`, `settlement_time`, `card_open_store_id`,
    `order_number`, `related_order_number`, `customer_name`, `customer_mobile`,
    `customer_belong_store_id`, `period`, `create_time`, `update_time`
) VALUES

-- 2024-07月份数据 - 次卡跨店消费
('次卡跨店消费', 41, '面部深层清洁护理', 2, '美容护理10次卡', 380.00, '2024-07-01 10:30:00', 40, 'ORD20240701123456', '', '张美丽', '13812345678', 40, '2024-07', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('次卡跨店消费', 42, 'SPA全身按摩', 1, 'SPA体验15次卡', 580.00, '2024-07-01 14:20:00', 41, 'ORD20240701234567', '', '李雅静', '13823456789', 41, '2024-07', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('次卡跨店消费', 43, '肩颈按摩护理', 3, '按摩理疗20次卡', 450.00, '2024-07-02 09:15:00', 42, 'ORD20240702345678', '', '王舒心', '13834567890', 42, '2024-07', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('次卡跨店消费', 45, '足底按摩', 2, '足疗保健12次卡', 320.00, '2024-07-02 11:00:00', 44, 'ORD20240702567890', '', '刘婷婷', '13856789012', 44, '2024-07', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('次卡跨店消费', 46, '眼部护理', 1, '眼部护理8次卡', 180.00, '2024-07-03 15:30:00', 45, 'ORD20240703678901', '', '赵雪梅', '13867890123', 45, '2024-07', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('次卡跨店消费', 48, '背部按摩', 2, '按摩理疗20次卡', 360.00, '2024-07-03 17:10:00', 47, 'ORD20240703890123', '', '周美玲', '13889012345', 47, '2024-07', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('次卡跨店消费', 49, '面部补水护理', 1, '美容护理10次卡', 220.00, '2024-07-04 10:45:00', 48, 'ORD20240704901234', '', '吴秀英', '13890123456', 48, '2024-07', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('次卡跨店消费', 50, '头部按摩', 3, '头疗养生15次卡', 480.00, '2024-07-04 14:15:00', 49, 'ORD20240704012345', '', '郑雅芳', '13901234567', 49, '2024-07', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('次卡跨店消费', 51, '肩颈理疗', 1, '理疗康复25次卡', 350.00, '2024-07-05 09:30:00', 50, 'ORD20240705123456', '', '冯丽娟', '13912345678', 50, '2024-07', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('次卡跨店消费', 52, '面部抗衰护理', 2, '抗衰美容12次卡', 680.00, '2024-07-05 16:20:00', 51, 'ORD20240705234567', '', '何美霞', '13923456789', 51, '2024-07', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),

('次卡跨店消费', 54, '足疗按摩', 2, '足疗保健12次卡', 280.00, '2024-07-06 15:50:00', 53, 'ORD20240706456789', '', '许雅琴', '13945678901', 53, '2024-07', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('次卡跨店消费', 55, '眼部精华护理', 1, '眼部护理8次卡', 200.00, '2024-07-07 10:10:00', 54, 'ORD20240707567890', '', '苏美玉', '13956789012', 54, '2024-07', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('次卡跨店消费', 56, '全身淋巴排毒', 1, 'VIP尊享30次卡', 800.00, '2024-07-07 14:30:00', 55, 'ORD20240707678901', '', '叶秀兰', '13967890123', 55, '2024-07', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('次卡跨店消费', 57, '背部刮痧', 2, '中医理疗18次卡', 420.00, '2024-07-08 09:45:00', 56, 'ORD20240708789012', '', '蔡雅文', '13978901234', 56, '2024-07', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('次卡跨店消费', 58, '面部深度清洁', 1, '美容护理10次卡', 260.00, '2024-07-08 16:25:00', 57, 'ORD20240708890123', '', '谢丽萍', '13989012345', 57, '2024-07', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('次卡跨店消费', 60, '头皮护理', 3, '头疗养生15次卡', 390.00, '2024-07-09 17:40:00', 59, 'ORD20240709012345', '', '曾雅丽', '14001234567', 59, '2024-07', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('次卡跨店消费', 61, '肩颈热敷', 2, '理疗康复25次卡', 320.00, '2024-07-10 10:20:00', 60, 'ORD20240710123456', '', '彭秀芬', '14012345678', 60, '2024-07', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('次卡跨店消费', 62, '面部紧致护理', 1, '抗衰美容12次卡', 380.00, '2024-07-10 15:10:00', 61, 'ORD20240710234567', '', '董美琳', '14023456789', 61, '2024-07', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('次卡跨店消费', 63, 'SPA水疗', 1, 'SPA体验15次卡', 450.00, '2024-07-11 11:30:00', 62, 'ORD20240711345678', '', '范雅君', '14034567890', 62, '2024-07', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('次卡跨店消费', 65, '眼部去皱', 1, '眼部护理8次卡', 220.00, '2024-07-11 09:40:00', 64, 'ORD20240711567890', '', '邓美华', '14056789012', 64, '2024-07', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),

('次卡跨店消费', 66, '全身精油按摩', 1, 'VIP尊享30次卡', 720.00, '2024-07-12 14:20:00', 65, 'ORD20240712678901', '', '石雅慧', '14067890123', 65, '2024-07', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('次卡跨店消费', 67, '背部拔罐', 3, '中医理疗18次卡', 450.00, '2024-07-12 10:50:00', 66, 'ORD20240712789012', '', '龚秀梅', '14078901234', 66, '2024-07', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('次卡跨店消费', 68, '面部美白', 2, '美容护理10次卡', 420.00, '2024-07-13 15:35:00', 67, 'ORD20240713890123', '', '汤丽芳', '14089012345', 67, '2024-07', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('次卡跨店消费', 69, '头部精油护理', 1, '头疗养生15次卡', 180.00, '2024-07-13 11:15:00', 68, 'ORD20240713901234', '', '黎美玲', '14090123456', 68, '2024-07', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('次卡跨店消费', 70, '肩颈推拿', 2, '理疗康复25次卡', 380.00, '2024-07-14 16:45:00', 69, 'ORD20240714012345', '', '易雅琪', '14101234567', 69, '2024-07', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('次卡跨店消费', 71, '面部深层护理', 1, '美容护理10次卡', 320.00, '2024-07-15 10:15:00', 70, 'ORD20240715123456', '', '罗美娟', '14112345678', 70, '2024-07', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('次卡跨店消费', 72, 'SPA香薰护理', 2, 'SPA体验15次卡', 640.00, '2024-07-15 14:40:00', 71, 'ORD20240715234567', '', '毛雅静', '14123456789', 71, '2024-07', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('次卡跨店消费', 73, '全身按摩', 1, '按摩理疗20次卡', 280.00, '2024-07-16 09:25:00', 72, 'ORD20240716345678', '', '邱丽华', '14134567890', 72, '2024-07', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('次卡跨店消费', 75, '足疗保健', 3, '足疗保健12次卡', 420.00, '2024-07-16 11:20:00', 74, 'ORD20240716567890', '', '薛雅芳', '14156789012', 74, '2024-07', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('次卡跨店消费', 76, '眼部抗衰', 1, '眼部护理8次卡', 240.00, '2024-07-17 15:55:00', 75, 'ORD20240717678901', '', '雷秀英', '14167890123', 75, '2024-07', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),

('次卡跨店消费', 77, '背部理疗', 2, '理疗康复25次卡', 360.00, '2024-07-17 10:35:00', 76, 'ORD20240717789012', '', '贺美玲', '14178901234', 76, '2024-07', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('次卡跨店消费', 79, '头部按摩', 2, '头疗养生15次卡', 320.00, '2024-07-18 09:50:00', 78, 'ORD20240718901234', '', '侯丽娟', '14190123456', 78, '2024-07', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('次卡跨店消费', 80, 'SPA水疗护理', 1, 'SPA体验15次卡', 480.00, '2024-07-18 14:10:00', 79, 'ORD20240718012345', '', '邹美华', '14201234567', 79, '2024-07', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('次卡跨店消费', 81, '肩颈舒缓', 3, '理疗康复25次卡', 450.00, '2024-07-19 11:40:00', 80, 'ORD20240719123456', '', '高雅文', '14212345678', 80, '2024-07', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('次卡跨店消费', 40, '面部紧肤', 1, '抗衰美容12次卡', 350.00, '2024-07-19 16:25:00', 81, 'ORD20240719234567', '', '常美丽', '14223456789', 81, '2024-07', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('次卡跨店消费', 41, '足底按摩', 2, '足疗保健12次卡', 300.00, '2024-07-20 10:05:00', 84, 'ORD20240720345678', '', '武雅静', '14234567890', 84, '2024-07', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('次卡跨店消费', 42, '眼部精护', 1, '眼部护理8次卡', 190.00, '2024-07-20 15:30:00', 85, 'ORD20240720456789', '', '乔丽华', '14245678901', 85, '2024-07', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('次卡跨店消费', 44, '背部刮痧', 2, '中医理疗18次卡', 380.00, '2024-07-21 17:15:00', 91, 'ORD20240721678901', '', '秦美霞', '14267890123', 91, '2024-07', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('次卡跨店消费', 45, '面部深度清洁', 1, '美容护理10次卡', 280.00, '2024-07-22 09:20:00', 92, 'ORD20240722789012', '', '尹雅芳', '14278901234', 92, '2024-07', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('次卡跨店消费', 46, '头皮护理', 3, '头疗养生15次卡', 420.00, '2024-07-22 14:50:00', 95, 'ORD20240722890123', '', '姚秀英', '14289012345', 95, '2024-07', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),

('次卡跨店消费', 48, '肩颈热石', 2, '理疗康复25次卡', 400.00, '2024-07-23 16:35:00', 97, 'ORD20240723012345', '', '江雅琴', '14301234567', 97, '2024-07', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('次卡跨店消费', 49, '面部抗氧化', 1, '抗衰美容12次卡', 420.00, '2024-07-23 10:25:00', 98, 'ORD20240723123456', '', '童丽娟', '14312345678', 98, '2024-07', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('次卡跨店消费', 50, '足疗理疗', 2, '足疗保健12次卡', 340.00, '2024-07-24 15:40:00', 102, 'ORD20240724234567', '', '颜美华', '14323456789', 102, '2024-07', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('次卡跨店消费', 51, '眼部去黑眼圈', 1, '眼部护理8次卡', 210.00, '2024-07-24 09:55:00', 104, 'ORD20240724345678', '', '游雅文', '14334567890', 104, '2024-07', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),

-- 次卡跨店消费退款数据
('次卡跨店消费退款', 44, '面部美白护理', 1, '美容护理10次卡', -280.00, '2024-07-02 16:45:00', 43, 'ORD20240702456789', 'ORD20240701123456', '陈丽华', '13845678901', 43, '2024-07', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('次卡跨店消费退款', 53, 'SPA芳疗', 1, 'SPA体验15次卡', -520.00, '2024-07-06 11:40:00', 52, 'ORD20240706345678', 'ORD20240701234567', '朱晓红', '13934567890', 52, '2024-07', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('次卡跨店消费退款', 64, '足底反射疗法', 2, '足疗保健12次卡', -300.00, '2024-07-11 16:50:00', 63, 'ORD20240711456789', 'ORD20240702567890', '方丽珍', '14045678901', 63, '2024-07', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('次卡跨店消费退款', 78, '面部补水', 1, '美容护理10次卡', -200.00, '2024-07-17 17:20:00', 77, 'ORD20240717890123', 'ORD20240715123456', '夏雅琴', '14189012345', 77, '2024-07', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('次卡跨店消费退款', 47, 'SPA芳香疗法', 1, 'SPA体验15次卡', -460.00, '2024-07-23 11:10:00', 96, 'ORD20240723901234', 'ORD20240715234567', '卢美玲', '14290123456', 96, '2024-07', UNIX_TIMESTAMP(), UNIX_TIMESTAMP());
