{extend name="../../base/view/common/base" /}
<!-- 主体 -->
{block name="body"}
<script src="{__GOUGU__}/table2excel.js"></script>
<div class="p-3">
	<form class="layui-form gg-form-bar border-x border-t" lay-filter="barsearchform">
		<div class="layui-input-inline" style="width:150px;">
			<select name="cate_id" >
				<option value="">请选择租赁类型</option>
				{volist name=":get_type_pay('fukuan','contract_rent')" id="v"}
				<option value="{$v.id}" >{$v.name}</option>
				{/volist}
			</select>
		</div>
		<div class="layui-input-inline" style="width:136px">
			<select name="did" lay-search="" placeholder="请选择门店"
					lay-filter="demo-select-filter" >
				<option value="">请选择门店</option>
				<option value="1" {if condition="($did == 1)" }selected="" {/if}>总部</option>
				{volist name="dep" id="v"}
				<option value="{$v.id}" {if condition="($did == $v.id)" }selected="" {/if}>{$v.title}</option>
				{/volist}
			</select>
		</div>
		<div class="layui-input-inline" style="width:150px;">
			<select name="status">
				<option value="0">全部</option>
				<option value="1" selected>生效中</option>
				<option value="-1">已作废</option>
				<option value="2">已中止</option>
			</select>
		</div>
		<div class="layui-input-inline" style="width:150px;">
			<select name="is_invoice">
				<option value="0">-是否有发票-</option>
				<option value="-1">否</option>
				<option value="1">是</option>
			</select>
		</div>
		<div class="layui-input-inline" style="width:240px;">
			<input type="text" name="keyword" placeholder="输入关键字" class="layui-input" autocomplete="off" />
		</div>
		<div class="layui-input-inline" style="width:150px;">
			<button class="layui-btn layui-btn-normal" lay-submit="" lay-filter="webform"><i class="layui-icon layui-icon-search mr-1"></i>搜索</button>
			<button type="reset" class="layui-btn layui-btn-reset" lay-filter="reset">清空</button>
		</div>
	</form>
	<table class="layui-hide" id="test" lay-filter="test"></table>
</div>

<script type="text/html" id="toolbarDemo">
<div class="layui-btn-container">
   <span class="layui-btn layui-btn-sm" title="添加合同" lay-event="add">+ 添加合同</span>
	<span class="layui-btn layui-btn-danger"  lay-event="del">批量删除</span>
	<span class="layui-btn layui-btn-warm"  lay-event="enable">批量启用</span>
	<span class="layui-btn layui-btn-warm"  lay-event="discontinue">批量中止</span>
</div>
</script>

<script type="text/html" id="ID-table-demo-page-pagebar">
	<div class="layui-btn-container">
		<button class="layui-btn layui-btn-sm" lay-event="footerDemoBtn1">底部按钮1</button>
		<button class="layui-btn layui-btn-sm" lay-event="footerDemoBtn2">底部按钮2</button>
	</div>
</script>

{/block}
<!-- /主体 -->


<!-- 脚本 -->
{block name="script"}
<script>
	const moduleInit = ['tool'];
	function gouguInit() {
		var table = layui.table, tool = layui.tool ,form = layui.form;

		var screenHeight = window.innerHeight;

		layui.pageTable = table.render({
			elem: '#test',
			title: '合同列表',
			toolbar: '#toolbarDemo',
			defaultToolbar:['filter', {title:'导出EXCEL',layEvent: 'LAYTABLE_EXCEL',icon: 'layui-icon-export'}],	
			url: "/contract/contractrent/index", //数据接口
			totalRow: true,
			cols: [
				[ //表头
					{type: 'checkbox', fixed: 'left'},
					{
						field: 'id',
						title: '编号',
						align: 'center',
						sort: true,
						width: 80
					},
					{
						title: '状态',
						align: 'center',
						sort: true,
						width: 80,
						templet: function(d) {
							var html = '<span class="layui-btn layui-btn-xs layui-btn-';
							switch (d.status) {
								case -1:
									html += 'danger ">已作废';
									break;
								case 1:
									html += 'success ">生效中';
									break;
								case 2:
									html += 'warm ">已中止';
									break;
							}
							html += '</span>';
							return html;
						}
					},
					// { field: 'check_status', title: '状态', align: 'center', width: 80, templet: function (d) {
					// 	var html = '<span class="layui-btn layui-btn-xs layui-bg-' + d.check_status + '">' + d.status_name + '</span>';
					// 	return html;
					// 	}
					// },
					{
						title: '最近付款日期',
						width: 160,
						sort: true,
						templet: function(d){
							var html = '<span>';
							if (d.recent_pay != null){
								html=`${d.recent_pay} <span class="layui-btn layui-btn-xs layui-btn-`;
								if (d.recent_is_pay == 1){
									html+= 'success ">已付款'
								}else{
									switch (d.recent_check_status){
										case -1: html+= 'normal ">未申请' ;break;
										case 0: html+= 'normal ">已申请' ;break;
										case 1: html+= 'warm ">正在审批' ;break;
										case 2: html+= 'success ">审批完成' ;break;
										case 3: html+= 'danger ">已驳回' ;break;
										case 4: html+= 'danger ">已撤销' ;break;
									}
								}
							}

							html += '</span>';
							return html;
						}
					},
					{
						field: 'code',
						title: '合同编号',
						width: 160
					},
					{
						field: 'diff_days',
						title: '距离付款',

						width: 100,
						templet: function (d) {
							let html = `<span style="display: inline-block;padding:0 6px;font-size:12px;border-radius: 2px;height: 18px;
    line-height: 18px;" class="layui-btn `
							if (!d.recent_pay){
								html += `layui-btn-primary layui-bg-green">已付完</span>`
								return html
							}
							let diff = getDaysDiff(d.recent_pay);

							//let diff = d.diff_days;
							if (diff <= 0){
								html += `layui-btn-primary layui-border-red`
							}else if (diff <= 15){
								html += `layui-bg-red`
							}else if (diff <= 30){
								html += `layui-bg-orange`
							}else{
								html += `layui-bg-blue`
							}
							html += `">${diff} 天</span>`
							return html;
						}
					},
					{
						field: 'ei_amount',
						title: '每期金额',
						width: 160,
						totalRow:true
					},
					{
						field: 'dname',
						title: '门店名称',
						width: 160
					},
					{
						field: 'create_aname',
						title: '录入人',
						width: 100
					},
					{
						title: '收款信息',
						width: 100,
						templet: function (d) {
							let html = `<span class="layui-btn layui-btn-success layui-btn-xs">${d.account_name}</span>`
							return html;
						}
					},
					{
						field: 'is_invoice',
						title: '是否开发票',
						width: 100,
						templet: function (d) {
							let html = ``
							if (d.is_invoice == 1){
								html = '<span class="layui-btn layui-btn-success layui-btn-xs">是</span>'
							}else{
								html = '<span class="layui-btn layui-btn-xs" style="background-color:#535353">否</span>'
							}
							return html;
						}
					},
					{
						field: 'cate_name',
						title: '合同类别',
						width: 160
					},
					{
						field: 'start_date',
						title: '合同起始日期',
						width: 160
					},
					{
						field: 'end_date',
						title: '合同终止日期',
						width: 160,
						sort: true,
						templet: function (d) {
							let diff = getDaysDiff(d.end_date);
							let color = '#16baaa';
							if (diff <= 0){
								color = '#ff5722';
							}else if (diff <= 90){
								color = `#ffb800`
							}else if (diff <= 180){
								color = `#1e9fff`
							}
							let html = `<span style="color:${color}">${d.end_date}</span>`
							return html;
						}
					},
					{
						field: 'end_diff_days',
						title: '距离合同终止',
						width: 160,
						templet: function (d) {
							let diff = getDaysDiff(d.end_date);
							let color = '#16baaa';
							if (diff <= 0){
								color = '#ff5722';
							}else if (diff <= 90){
								color = `#ffb800`
							}else if (diff <= 180){
								color = `#1e9fff`
							}
							let html = `<span style="color:${color}">${diff}天</span>`
							return html;
						}
					},
					{
						field: 'create_time',
						title: '创建时间',
						width: 160,
					},
					{
						field: 'right',
						fixed:'right',
						title: '操作',
						width: 200,
						align: 'center',
						templet: function (d) {
							var html = '<div class="layui-btn-group">';
							var btn0='<span class="layui-btn layui-btn-normal layui-btn-xs" lay-event="details">明细</span>';
							var btn3 = '';
							if (d.status == 2){
								btn3='<span class="layui-btn layui-btn-warm layui-btn-xs" lay-event="enable">启用</span>';
							}else{
								btn3='<span class="layui-btn layui-btn-warm layui-btn-xs" lay-event="discontinue">中止</span>';
							}

							var btn1='<span class="layui-btn layui-btn-xs" lay-event="edit">编辑</span>';
							var btn2='<span class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">删除</span>';
							let btn4='<span class="layui-btn layui-bg-purple layui-btn-xs" lay-event="copy">复制</span>';

							return html+btn0+btn1+btn3+btn2+btn4+'</div>';
						}						
					}
				]
			],
			height:screenHeight-100,
			where:{
				did:$('[name="did"]').val()
			},
			page: { // 支持传入 laypage 组件的所有参数（某些参数除外，如：jump/elem） - 详见文档
				layout: ['count'], //自定义分页布局
			},
			// pagebar: '#ID-table-demo-page-pagebar', // 分页栏模板
			done: function (res, curr, count) {
				//导出
				let header_tr = $("#test").next().find(".layui-table-header:first").find("tr");
				let body_tr = $("#test").next().find(".layui-table-body").children(':first').find("tr");
				let header_html = "";
				let body_html = "";
				// 获取表头html，包括单元格的合并
				$.each(header_tr, function (i, tr) {
					let header_th = $(tr).find("th");
					header_html += "<tr>";
					$.each(header_th, function (j, th) {
						let rowspan_num = $(th).attr("rowspan");// 行合并数
						let colspan_num = $(th).attr("colspan");// 列合并数
						if (rowspan_num && !colspan_num) {
							header_html += '<th style="text-align: center" rowspan= "' + rowspan_num + '">';
						} else if (colspan_num && !rowspan_num) {
							header_html += '<th style="text-align: center" colspan= "' + colspan_num + '">';
						} else if (rowspan_num && colspan_num) {
							header_html += '<th style="text-align: center" rowspan= "' + rowspan_num + '" colspan="' + colspan_num + '">';
						} else {
							header_html += '<th>';
						}
						header_html += $(th).children().children().text() + '</th>';// 获取表头名称并拼接th标签
					})
					header_html += '</tr>';
				})
				// 获取表格body数据
				$.each(body_tr, function (i, tr) {
					let body_td = $(tr).find("td");
					body_html += '<tr>';
					$.each(body_td, function (j, td) {
						body_html += '<td>' + $(td).children().text() + '</td>';
					})
					body_html += '</tr>';
				})
				$("#test tr").remove();// 清除之前的doom结构
				$("#test").append(header_html).append(body_html);
				$("#test").hide();

			}
		});

		// 行单击事件( 双击事件为: rowDouble )
		table.on('row(test)', function(obj){
			var data = obj.data; // 获取当前行数据
			// 标注当前点击行的选中状态
			obj.setRowChecked({
				type: 'radio' // radio 单选模式；checkbox 复选模式
			});
		});

		//表头工具栏事件
		table.on('toolbar(test)', function(obj){
			var checkStatus = table.checkStatus(obj.config.id);
			var data = checkStatus.data;

			let ids = [];
			data.map(i => {
				ids.push(i.id)
			})

			if (obj.event === 'add') {
				tool.side('/contract/contractrent/add');
				return;
			}
			if (obj.event === 'enable') {
				chanleStatus('确定要启用吗?' , ids , 1);
				return;
			}
			if (obj.event === 'discontinue') {
				chanleStatus('确定要中止吗?' , ids , 2);
				return;
			}
			if (obj.event === 'del') {
				console.log(ids)
				chanleStatus('确定要删除吗?' , ids , -1);
				return;
			}
			if(obj.event === 'LAYTABLE_EXCEL'){
				let table2excel = new Table2Excel();
				let now = Date.now()
				table2excel.export($('#test'), `合同_${now}`);
				return;
			}
		});
		//监听行工具事件
		table.on('tool(test)', function(obj) {
			var data = obj.data;
			if(obj.event === 'edit'){
				tool.side('/contract/contractrent/add?id='+data.id);
				return;
			}
			if(obj.event === 'details'){
				tool.side('/rent/rent/index?in_cr_id='+data.id);
				return;
			}
			if(obj.event === 'copy'){
				tool.side('/contract/contractrent/add?type=copy&id='+data.id);
				return;
			}

			if (obj.event === 'enable') {
				chanleStatus('确定要启用吗?' , data.id , 1);
			}
			if (obj.event === 'discontinue') {
				chanleStatus('确定要中止吗?' , data.id , 2);
			}
			if (obj.event === 'del') {
				chanleStatus('确定要删除吗?' , data.id , -1);
			}
		});

		function chanleStatus(title , id , status){
			layer.confirm(title, {
				icon: 3,
				title: '提示'
			}, function(index) {
				let callback = function (e) {
					layer.msg(e.msg);
					if (e.code == 0) {
						reload();
					}
				}
				tool.delete("/contract/contractrent/delete", {id: id,status:status}, callback);
				layer.close(index);
			});
		}

		//监听搜索提交
		form.on('submit(webform)', function(data) {
			reload();
			return false;
		});

		function getDaysDiff(endDate) {
			var currentDate = new Date(); //当前日期

			var dateStart = new Date(currentDate);
			var dateEnd = new Date(endDate);
			//difValue 就是相差天数 如果目标日期小于当前日期，则difValue 为负数
			var difValue = Math.ceil((dateEnd - dateStart) / (1000 * 60 * 60 * 24));
			return difValue;
		}

		function reload(){

			layui.pageTable.reload({
				where: {
					keywords: $('[name="keyword"]').val(),
					cate_id: $('[name="cate_id"]').val(),
					status: $('[name="status"]').val(),
					did: $('[name="did"]').val(),
					is_invoice: $('[name="is_invoice"]').val(),
				}
			});
		}

	}
</script>
{/block}
<!-- /脚本 -->
