# 门店汇总接口性能优化说明

## 优化内容

针对 `/store/storebusinessm/index` 接口的查询速度进行了以下优化：

1. **代码层面优化**：
   - 添加了结果缓存机制，减少重复计算
   - 优化了SQL查询，添加了索引提示（FORCE INDEX）
   - 减少了不必要的数据库操作和JOIN查询
   - 添加了参数验证，避免无效查询
   - 改进了数据处理逻辑，减少重复计算

2. **数据库层面优化**：
   - 添加了多个组合索引，提高查询性能
   - 优化了表之间的关联查询
   - 减少了对不必要字段的查询

## 如何应用索引优化

执行以下命令来添加所需的索引：

```bash
# 使用MySQL客户端执行SQL文件
mysql -u用户名 -p密码 数据库名 < app/common/sql/add_performance_indexes.sql

# 或者在phpmyadmin中导入执行
```

> **注意**: 在执行索引添加操作前，请先备份数据库，确保数据安全。

## 回滚索引更改

如果索引添加后出现问题需要回滚，可以执行以下命令删除添加的索引：

```bash
# 使用MySQL客户端执行SQL文件
mysql -u用户名 -p密码 数据库名 < app/common/sql/remove_performance_indexes.sql

# 或者在phpmyadmin中导入执行
```

> **注意**: 删除索引前，请确认是否真的需要这样做，因为它可能会恢复之前的性能问题。

## 其他优化建议

1. **数据分表**：如果数据量非常大，可以考虑按月或按年分表
2. **使用Redis缓存**：对于高频访问的数据，可以使用Redis缓存
3. **定期清理旧数据**：定期归档或清理历史数据
4. **优化查询时间段**：减少查询的日期范围，避免大范围查询

## 性能提升评估

优化后，接口响应速度预计可提升5-10倍，具体取决于数据量大小和服务器配置。 