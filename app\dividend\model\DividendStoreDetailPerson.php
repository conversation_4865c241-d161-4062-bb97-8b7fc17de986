<?php
/**
 * @copyright Copyright (c) 2021 仲正堂工作室
 * @license https://opensource.org/licenses/GPL-3.0
 * @link https://www.gougucms.com
 */

declare (strict_types=1);

namespace app\dividend\model;

use think\Model;

class DividendStoreDetailPerson extends Model
{
    protected $name = 'dividend_store_detail_person';

    // 设置字段信息
    protected $schema = [
        'id'                          => 'int',
        'dividend_store_detail_id'           => 'int',
        'shareholder_id'              => 'int',
        'shareholder_name'            => 'string',
        'store_shareholding_ratio'    => 'decimal',
        'shareholder_type'            => 'int',
        'payable_amount'              => 'decimal',
        'other_adjustment'            => 'decimal',
        'actual_payable_amount'       => 'decimal',
        'is_delete'                   => 'int',
        'delete_time'                 => 'int',
        'create_time'                 => 'int',
        'update_time'                 => 'int',
        'remark'                      => 'string',
    ];

    // 自动时间戳
    protected $autoWriteTimestamp = true;
    protected $createTime = 'create_time';
    protected $updateTime = 'update_time';

    // 股东类型常量
    const TYPE_COMPANY = 1;  // 公司股东
    const TYPE_PERSONAL = 2; // 个人股东

    /**
     * 关联分红明细主表
     */
    public function dividendStoreDetail()
    {
        return $this->belongsTo('app\dividend\model\DividendStoreDetail', 'dividend_store_detail_id', 'id');
    }

    /**
     * 关联股东信息
     */
    public function shareholder()
    {
        return $this->belongsTo('app\dividend\model\DividendShareholder', 'shareholder_id', 'id');
    }

    /**
     * 获取股东类型文本
     * @param int $type 股东类型
     * @return string
     */
    public static function getTypeText($type)
    {
        $types = [
            self::TYPE_COMPANY => '公司股东',
            self::TYPE_PERSONAL => '个人股东'
        ];

        return $types[$type] ?? '未知类型';
    }

    /**
     * 批量保存分红人信息
     * @param int $dividendStoreId 分红明细主表ID
     * @param array $persons 分红人数据
     * @return bool
     */
    public static function savePersons($dividendStoreId, $persons)
    {
        try {
            // 先删除原有的分红人记录（软删除）
            self::where('dividend_store_detail_id', $dividendStoreId)->update([
                'is_delete' => 1,
                'delete_time' => time()
            ]);

            // 批量插入新的分红人记录
            if (!empty($persons)) {
                $data = [];
                foreach ($persons as $person) {
                    $data[] = [
                        'dividend_store_detail_id' => $dividendStoreId,
                        'shareholder_id' => $person['shareholder_id'] ?? 0,
                        'shareholder_name' => $person['shareholder_name'],
                        'store_shareholding_ratio' => $person['store_shareholding_ratio'],
                        'shareholder_type' => $person['shareholder_type'],
                        'payable_amount' => $person['payable_amount'],
                        'create_time' => time(),
                        'update_time' => time()
                    ];
                }

                if (!empty($data)) {
                    return self::insertAll($data);
                }
            }

            return true;
        } catch (\Exception $e) {
            throw $e;
        }
    }

    /**
     * 根据分红明细ID获取分红人列表
     * @param int $dividendStoreId 分红明细主表ID
     * @return array
     */
    public static function getPersonsByDetailId($dividendStoreId)
    {
        $persons = self::where('dividend_store_detail_id', $dividendStoreId)
            ->where('is_delete', 0)
            ->order('shareholder_type asc, id asc')
            ->select()
            ->toArray();

        // 处理数据格式
        foreach ($persons as &$person) {
            $person['type_text'] = self::getTypeText($person['shareholder_type']);
            $person['store_shareholding_ratio_formatted'] = number_format($person['store_shareholding_ratio'], 2) . '%';
            $person['payable_amount_formatted'] = number_format($person['payable_amount'], 2);
            $person['other_adjustment_formatted'] = number_format($person['other_adjustment'] ?? 0, 2);
            $person['actual_payable_amount_formatted'] = number_format($person['actual_payable_amount'] ?? 0, 2);
        }

        return $persons;
    }

    /**
     * 计算分红人应付金额
     * @param float $dividendProfit 分红利润
     * @param float $shareholdingRatio 持股比例
     * @param int $shareholderType 股东类型
     * @param float $companyShareholdingRatio 公司持股比例（仅在计算公司汇总时使用）
     * @return float
     */
    public static function calculatePayableAmount($dividendProfit, $shareholdingRatio, $shareholderType, $companyShareholdingRatio = 0)
    {
        // 所有股东都按照相同的逻辑计算：分红利润 * 持股比例 / 100
        return $dividendProfit * $shareholdingRatio / 100;
    }

    /**
     * 根据股东信息自动生成分红人数据
     * @param int $storeId 门店ID
     * @param float $dividendProfit 分红利润
     * @param float $companyShareholdingRatio 公司持股比例
     * @return array
     */
    public static function generatePersonsFromShareholders($storeId, $dividendProfit, $companyShareholdingRatio)
    {
        try {
            // 获取门店分红信息
            $storeInfo = \think\facade\Db::name('DividendStoreInfo')
                ->where('store_id', $storeId)
                ->where('is_delete', 0)
                ->find();

            if (empty($storeInfo)) {
                throw new \Exception('该门店暂无分红信息配置');
            }

            // 获取股东信息
            $shareholders = \think\facade\Db::name('DividendShareholder')
                ->where('dividend_store_info_id', $storeInfo['id'])
                ->where('is_delete', 0)
                ->order('shareholder_type asc, sort_order asc')
                ->select()
                ->toArray();

            $persons = [];
            $companyTotalAmount = 0;

            // 处理所有股东
            foreach ($shareholders as $shareholder) {
                if ($shareholder['shareholder_type'] == self::TYPE_PERSONAL) {
                    // 个人股东：直接按持股比例计算
                    $payableAmount = self::calculatePayableAmount(
                        $dividendProfit,
                        $shareholder['store_shareholding_ratio'],
                        self::TYPE_PERSONAL
                    );

                    $persons[] = [
                        'shareholder_id' => $shareholder['id'],
                        'shareholder_name' => $shareholder['shareholder_name'],
                        'store_shareholding_ratio' => $shareholder['store_shareholding_ratio'],
                        'shareholder_type' => self::TYPE_PERSONAL,
                        'payable_amount' => $payableAmount,
                        'other_adjustment' => 0, // 默认为0
                        'actual_payable_amount' => $payableAmount, // 默认等于应付金额
                        'remark' => ''
                    ];
                }
                // 注意：公司股东不在这里处理，因为他们会被汇总到"公司"这一条记录中
            }

            // 计算公司股东的总分红（按公司持股比例计算）
            $companyTotalAmount = self::calculatePayableAmount(
                $dividendProfit,
                $companyShareholdingRatio,
                self::TYPE_COMPANY
            );

            // 添加公司股东汇总记录（无论金额正负都要显示）
            if ($companyTotalAmount != 0) {
                $persons[] = [
                    'shareholder_id' => 0, // 公司股东汇总不关联具体股东
                    'shareholder_name' => '公司',
                    'store_shareholding_ratio' => $companyShareholdingRatio,
                    'shareholder_type' => self::TYPE_COMPANY,
                    'payable_amount' => $companyTotalAmount,
                    'other_adjustment' => 0, // 默认为0
                    'actual_payable_amount' => $companyTotalAmount, // 默认等于应付金额
                    'remark' => ''
                ];
            }

            return $persons;
        } catch (\Exception $e) {
            throw $e;
        }
    }
}
