{extend name="../../base/view/common/base" /}

{block name="head"}
<style>
/* 优化表格和表单显示 */
.layui-table {
    margin-bottom: 15px;
}

.layui-form-item {
    margin-bottom: 15px;
}

/* 防止select元素闪烁 */
.admin-select {
    min-height: 38px;
}

/* 优化按钮样式 */
.remove-row {
    min-width: 50px;
}

/* 防止布局抖动 */
.layui-table td {
    vertical-align: middle;
}

/* 防止LayUI渲染错误 */
.layui-form-select {
    position: relative;
}

.layui-form-select dl {
    position: absolute;
    z-index: 999;
}

/* 表头必填项红色星号样式 */
.layui-table th font {
    color: #FF6347;
    margin-left: 3px;
}

/* 编辑状态下禁用字段样式 */
.disabled-field {
    background-color: #f5f5f5 !important;
    color: #999 !important;
    cursor: not-allowed !important;
}

.disabled-field:hover {
    background-color: #f5f5f5 !important;
}

/* 禁用状态的select样式 */
.layui-form-select.layui-form-selected .layui-select-title {
    background-color: #f5f5f5 !important;
    color: #999 !important;
    cursor: not-allowed !important;
}

/* 自动计算开关样式 */
.auto-calc-switch {
    margin: 10px 0;
    padding: 10px;
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 4px;
}

.auto-calc-switch .switch-label {
    font-size: 14px;
    color: #495057;
    margin-right: 10px;
}

/* 统计行样式 */
.summary-row {
    background-color: #f8f9fa !important;
    font-weight: bold;
    border-top: 2px solid #007bff !important;
}

.summary-row td {
    color: #495057 !important;
    font-size: 14px;
}
</style>
{/block}

{block name="body"}
<form class="layui-form p-4" lay-filter="dataForm">
    {empty name="detail.id"}
    <h3 class="pb-3">新增门店分红信息</h3>
    {else/}
    <h3 class="pb-3">编辑门店分红信息</h3>
    {/empty}

    <!-- 基础信息 -->
    <table class="layui-table">
        <tr>
            <td class="layui-td-gray" style="width: 60px;">关联门店<font>*</font></td>
            <td style="width: 120px;">
                {empty name="detail.id"}
                <!-- 新增状态：可选择门店 -->
                <select name="store_id" lay-verify="required" lay-search style="width: 180px;">
                    <option value="">请选择门店</option>
                    {volist name="store_list" id="store"}
                    <option value="{$store.id}" {eq name="store.id" value="$detail.store_id|default=0"}selected{/eq}>
                        {$store.title}
                    </option>
                    {/volist}
                </select>
                {else/}
                <!-- 编辑状态：禁用门店选择 -->
                <input type="text" value="{$detail.store_name|default='未知门店'}"
                       class="layui-input disabled-field" readonly style="width: 180px;">
                <input type="hidden" name="store_id" value="{$detail.store_id}">
                {/empty}
            </td>
            <td class="layui-td-gray" style="width: 60px;">已计提风险金<font>*</font></td>
            <td style="width: 120px;">
                {empty name="detail.id"}
                <!-- 新增状态：可输入风险金 -->
                <input type="number" name="risk_reserve" lay-verify="required|number"
                       placeholder="请输入金额" class="layui-input" style="width: 100%;"
                       value="{$detail.risk_reserve|default='0.00'}" step="0.01" min="0"
                       oninput="limitDecimalPlaces(this, 2)">
                {else/}
                <!-- 编辑状态：禁用风险金输入 -->
                <input type="text" value="{$detail.risk_reserve|default='0.00'}"
                       class="layui-input disabled-field" readonly style="width: 100%;">
                <input type="hidden" name="risk_reserve" value="{$detail.risk_reserve}">
                {/empty}
            </td>
            <td class="layui-td-gray" style="width: 60px;">公司持股比例(%)<font>*</font></td>
            <td style="width: 120px;">
                <input type="number" name="company_shareholding_ratio" lay-verify="required|number"
                       placeholder="请输入比例" class="layui-input" id="companyShareholdingRatio" style="width: 100%;"
                       value="{$detail.company_shareholding_ratio|default='0.000'}" step="0.001" min="0" max="100"
                       oninput="limitDecimalPlaces(this, 3)">
            </td>
        </tr>
        <tr>
            <td class="layui-td-gray" style="width: 60px;">备注</td>
            <td colspan="5">
                <textarea name="remark" placeholder="请输入备注信息" class="layui-textarea"
                          maxlength="500" style="width: 100%; height: 60px;">{$detail.remark|default=''}</textarea>
            </td>
        </tr>
    </table>

    <!-- 警告 -->
    <!-- 添加显眼的边线，增强警告提示的视觉效果 -->
    <div class="layui-alert layui-alert-warning" style="border: 2px solid #ff0000; border-left-width: 8px; border-radius: 6px; box-shadow: 0 2px 8px rgba(255,0,0,0.08); padding: 18px 20px; margin-bottom: 18px;">
        <h4 style="color: #d32f2f; font-weight: bold; margin-bottom: 10px;">重要提示*</h4>
        <!-- 股东姓名是一个人的唯一标识，绝对禁止二人同名，如果实在同名，请附带不同标记，并固定使用此标记，如：张三-南京、张三-上海 -->
        <p style="font-size: 14px; color: #ff0000; font-weight: bold; margin-bottom: 10px;">
            1. 【股东姓名】是一个人的【唯一标识】，【绝对禁止】二人同名，如果实在同名，请附带【不同标记】，并【固定使用】此标记，如：张三-南京、张三-上海
        </p>
        <p style="font-size: 14px; color: #d32f2f; font-weight: bold;">
            2. 【公司股东持股详情】模块门店持股比例计算逻辑：公司股东的门店持股比例 = 公司股东持股比例 × 公司内部持股比例 ÷ 100，系统会自动计算，但支持手动修改覆盖。
        </p>
    </div>

    <!-- 公司股东持股详情 -->
    <h4 class="pt-3 pb-2">公司股东持股详情
        <button type="button" class="layui-btn layui-btn-sm" id="addCompanyShareholder" style="float: right;">
            + 添加公司股东
        </button>
    </h4>

    <!-- 自动计算开关 -->
    <div class="auto-calc-switch">
        <span class="switch-label">门店持股比例自动计算：</span>
        <input type="checkbox" name="auto_calc_switch" lay-skin="switch" lay-text="开启|关闭" lay-filter="autoCalcSwitch" id="autoCalcSwitch">
    </div>
    <table class="layui-table" id="companyShareholderTable">
        <thead>
            <tr>
                <th style="width: 18%;">股东姓名<font style="color: #FF6347;">*</font></th>
                <th style="width: 18%;">关联人员</th>
                <th style="width: 18%;">公司内部持股比例(%)<font style="color: #FF6347;">*</font></th>
                <th style="width: 18%;">门店持股比例(%)<font style="color: #FF6347;">*</font></th>
                <th style="width: 18%;">备注</th>
                <th style="width: 10%;">操作</th>
            </tr>
        </thead>
        <tbody id="companyShareholderBody">
            {volist name="company_shareholders" id="shareholder"}
            <tr>
                <td>
                    <input type="text" name="company_shareholders[{$key}][shareholder_name]"
                           class="layui-input" placeholder="请输入股东姓名"
                           value="{$shareholder.shareholder_name}" lay-verify="required">
                </td>
                <td>
                    <select name="company_shareholders[{$key}][admin_id]" lay-search class="admin-select" lay-filter="adminSelect_company_{$key}">
                        <option value="">请选择关联人员</option>
                        {notempty name="shareholder.admin_id"}
                        <option value="{$shareholder.admin_id}" selected>{$shareholder.admin_name|default='已选择'}</option>
                        {/notempty}
                    </select>
                </td>
                <td>
                    <input type="number" name="company_shareholders[{$key}][company_shareholding_ratio]"
                           class="layui-input company-internal-ratio" value="{$shareholder.company_shareholding_ratio}"
                           step="0.001" min="0" max="100" required
                           oninput="limitDecimalPlaces(this, 3)">
                </td>
                <td>
                    <input type="number" name="company_shareholders[{$key}][store_shareholding_ratio]"
                           class="layui-input store-ratio company-store-ratio"
                           value="{$shareholder.store_shareholding_ratio}"
                           step="0.001" min="0" max="100" lay-verify="required|number" required
                           oninput="limitDecimalPlaces(this, 3)">
                </td>
                <td>
                    <input type="text" name="company_shareholders[{$key}][remark]"
                           class="layui-input" value="{$shareholder.remark}">
                </td>
                <td>
                    <button type="button" class="layui-btn layui-btn-danger layui-btn-xs remove-row">删除</button>
                </td>
            </tr>
            {/volist}
            {empty name="company_shareholders"}
            <tr class="no-data">
                <td colspan="6" style="text-align: center; color: #999;">暂无公司股东信息</td>
            </tr>
            {/empty}
        </tbody>
        <tfoot>
            <tr class="summary-row" id="companySummaryRow">
                <td colspan="2" style="text-align: right; padding-right: 10px;">合计：</td>
                <td id="companyInternalTotal">0.000</td>
                <td id="companyStoreTotal">0.000</td>
                <td colspan="2"></td>
            </tr>
        </tfoot>
    </table>

    <!-- 个人股东持股详情 -->
    <h4 class="pt-3 pb-2">个人股东持股详情
        <button type="button" class="layui-btn layui-btn-sm" id="addPersonalShareholder" style="float: right;">
            + 添加个人股东
        </button>
    </h4>
    <table class="layui-table" id="personalShareholderTable">
        <thead>
            <tr>
                <th style="width: 18%;">股东姓名<font style="color: #FF6347;">*</font></th>
                <th style="width: 18%;">关联人员</th>
                <th style="width: 18%;">门店持股比例(%)<font style="color: #FF6347;">*</font></th>
                <th style="width: 36%;">备注</th>
                <th style="width: 10%;">操作</th>
            </tr>
        </thead>
        <tbody id="personalShareholderBody">
            {volist name="personal_shareholders" id="shareholder"}
            <tr>
                <td>
                    <input type="text" name="personal_shareholders[{$key}][shareholder_name]"
                           class="layui-input" placeholder="请输入股东姓名"
                           value="{$shareholder.shareholder_name}" lay-verify="required">
                </td>
                <td>
                    <select name="personal_shareholders[{$key}][admin_id]" lay-search class="admin-select" lay-filter="adminSelect_personal_{$key}">
                        <option value="">请选择关联人员</option>
                        {notempty name="shareholder.admin_id"}
                        <option value="{$shareholder.admin_id}" selected>{$shareholder.admin_name|default='已选择'}</option>
                        {/notempty}
                    </select>
                </td>
                <td>
                    <input type="number" name="personal_shareholders[{$key}][store_shareholding_ratio]"
                           class="layui-input store-ratio personal-store-ratio"
                           value="{$shareholder.store_shareholding_ratio}"
                           step="0.001" min="0" max="100" required
                           oninput="limitDecimalPlaces(this, 3)">
                </td>
                <td>
                    <input type="text" name="personal_shareholders[{$key}][remark]"
                           class="layui-input" value="{$shareholder.remark}">
                </td>
                <td>
                    <button type="button" class="layui-btn layui-btn-danger layui-btn-xs remove-row">删除</button>
                </td>
            </tr>
            {/volist}
            {empty name="personal_shareholders"}
            <tr class="no-data">
                <td colspan="5" style="text-align: center; color: #999;">暂无个人股东信息</td>
            </tr>
            {/empty}
        </tbody>
        <tfoot>
            <tr class="summary-row" id="personalSummaryRow">
                <td colspan="2" style="text-align: right; padding-right: 10px;">合计：</td>
                <td id="personalStoreTotal">0.000</td>
                <td colspan="2"></td>
            </tr>
        </tfoot>
    </table>

    <!-- 提交按钮 -->
    <div class="py-3">
        <input type="hidden" name="id" value="{$detail.id|default='0'}" />
        <button class="layui-btn" lay-submit lay-filter="webform">立即提交</button>
    </div>
</form>
{/block}

<!-- 脚本 -->
{block name="script"}
<script>
    // 限制输入框小数位数的函数
    function limitDecimalPlaces(input, maxDecimalPlaces) {
        var value = input.value;
        if (value.indexOf('.') !== -1) {
            var parts = value.split('.');
            if (parts[1] && parts[1].length > maxDecimalPlaces) {
                input.value = parts[0] + '.' + parts[1].substring(0, maxDecimalPlaces);
            }
        }
    }

    const moduleInit = ['tool'];
    function gouguInit() {
        var form = layui.form, tool = layui.tool;

        // 全局错误处理
        window.addEventListener('error', function(e) {
            if (e.message && (e.message.includes('LAY_NUM') || e.message.includes('layui.ready'))) {
                console.error('LayUI 错误已被捕获:', e.message);
                // 不显示给用户，只记录日志
                return true; // 阻止默认错误处理
            }
        });

        // 捕获未处理的Promise错误
        window.addEventListener('unhandledrejection', function(e) {
            if (e.reason && e.reason.message && e.reason.message.includes('layui')) {
                console.error('LayUI Promise 错误已被捕获:', e.reason);
                e.preventDefault(); // 阻止默认错误处理
            }
        });

        // 使用更安全的变量初始化方式
        var companyShareholderIndex = parseInt('{$company_shareholders_count|default=0}') || 0; // 公司股东索引
        var personalShareholderIndex = parseInt('{$personal_shareholders_count|default=0}') || 0; // 个人股东索引
        var adminListCache = null; // 缓存人员列表数据

        // 计算门店持股比例（公司股东）
        function calculateCompanyStoreRatio() {
            // 检查自动计算开关是否开启
            var autoCalcEnabled = $('#autoCalcSwitch').prop('checked');
            if (!autoCalcEnabled) {
                return; // 如果开关关闭，不进行自动计算
            }

            var companyRatio = parseFloat($('#companyShareholdingRatio').val()) || 0;

            $('.company-internal-ratio').each(function() {
                var internalRatio = parseFloat($(this).val()) || 0;
                var storeRatio = (companyRatio * internalRatio / 100).toFixed(3);
                $(this).closest('tr').find('.company-store-ratio').val(storeRatio);
            });

            // 计算完成后更新统计
            updateSummaryTotals();
        }

        // 更新统计总计
        function updateSummaryTotals() {
            // 计算公司股东统计
            var companyInternalTotal = 0;
            var companyStoreTotal = 0;

            $('#companyShareholderBody tr:not(.no-data)').each(function() {
                var internalRatio = parseFloat($(this).find('.company-internal-ratio').val()) || 0;
                var storeRatio = parseFloat($(this).find('.company-store-ratio').val()) || 0;
                companyInternalTotal += internalRatio;
                companyStoreTotal += storeRatio;
            });

            $('#companyInternalTotal').text(companyInternalTotal.toFixed(3));
            $('#companyStoreTotal').text(companyStoreTotal.toFixed(3));

            // 计算个人股东统计
            var personalStoreTotal = 0;

            $('#personalShareholderBody tr:not(.no-data)').each(function() {
                var storeRatio = parseFloat($(this).find('.personal-store-ratio').val()) || 0;
                personalStoreTotal += storeRatio;
            });

            $('#personalStoreTotal').text(personalStoreTotal.toFixed(3));
        }

        // 验证小数位数（最多三位小数）
        function validateDecimalPlaces(value, fieldName) {
            if (value === '' || value === null || value === undefined) {
                return true; // 空值由其他验证处理
            }

            var strValue = value.toString();
            var decimalIndex = strValue.indexOf('.');

            if (decimalIndex !== -1) {
                var decimalPlaces = strValue.length - decimalIndex - 1;
                if (decimalPlaces > 3) {
                    layer.msg(fieldName + '最多只能输入三位小数', {icon: 2});
                    return false;
                }
            }
            return true;
        }

        // 验证所有持股比例字段的小数位数
        function validateAllDecimalPlaces() {
            var isValid = true;
            var errorMsg = '';

            // 验证公司股东持股比例
            var companyRatio = $('#companyShareholdingRatio').val();
            if (!validateDecimalPlaces(companyRatio, '公司股东持股比例')) {
                return false;
            }

            // 验证公司股东表格中的比例字段
            $('#companyShareholderBody tr:not(.no-data)').each(function() {
                var $row = $(this);
                var companyInternalRatio = $row.find('input[name*="[company_shareholding_ratio]"]').val();
                var storeRatio = $row.find('input[name*="[store_shareholding_ratio]"]').val();

                if (!validateDecimalPlaces(companyInternalRatio, '公司内部持股比例')) {
                    isValid = false;
                    return false;
                }
                if (!validateDecimalPlaces(storeRatio, '门店持股比例')) {
                    isValid = false;
                    return false;
                }
            });

            // 验证个人股东表格中的比例字段
            if (isValid) {
                $('#personalShareholderBody tr:not(.no-data)').each(function() {
                    var $row = $(this);
                    var storeRatio = $row.find('input[name*="[store_shareholding_ratio]"]').val();

                    if (!validateDecimalPlaces(storeRatio, '门店持股比例')) {
                        isValid = false;
                        return false;
                    }
                });
            }

            return isValid;
        }

        // 验证总持股比例
        function validateTotalRatio() {
            var companyTotal = 0;
            var personalTotal = 0;

            // 计算公司股东持股比例总和
            $('.company-store-ratio').each(function() {
                var val = parseFloat($(this).val()) || 0;
                companyTotal += val;
            });

            // 计算个人股东持股比例总和
            $('.personal-store-ratio').each(function() {
                var val = parseFloat($(this).val()) || 0;
                personalTotal += val;
            });

            var totalRatio = companyTotal + personalTotal;

            // 检查是否超过100%
            if (totalRatio > 100) {
                layer.msg('总持股比例不能超过100%，当前为' + totalRatio.toFixed(3) + '%', {icon: 2});
                return false;
            }

            // 检查是否等于100%（允许小于100%，但给出提示）
            if (totalRatio < 100 && totalRatio > 0) {
                var confirmMsg = '当前总持股比例为' + totalRatio.toFixed(3) + '%，未达到100%。<br><br>' +
                    '<span style="color: #ff6600;">建议：</span><br>' +
                    '• 检查是否遗漏了某些股东<br>' +
                    '• 确认各股东的持股比例是否正确<br><br>' +
                    '是否仍要继续提交？';
                return new Promise(function(resolve) {
                    layer.confirm(confirmMsg, {
                        icon: 3,
                        title: '持股比例提醒',
                        btn: ['继续提交', '返回修改'],
                        area: ['400px', 'auto']
                    }, function(index) {
                        layer.close(index);
                        resolve(true);
                    }, function(index) {
                        layer.close(index);
                        resolve(false);
                    });
                });
            }

            return true;
        }

        // 添加公司股东
        $('#addCompanyShareholder').click(function() {
            var html = '<tr>' +
                '<td><input type="text" name="company_shareholders[' + companyShareholderIndex + '][shareholder_name]" class="layui-input" placeholder="请输入股东姓名" lay-verify="required"></td>' +
                '<td>' +
                    '<select name="company_shareholders[' + companyShareholderIndex + '][admin_id]" lay-search class="admin-select" lay-filter="adminSelect_company_' + companyShareholderIndex + '">' +
                        '<option value="">请选择关联人员</option>' +
                    '</select>' +
                '</td>' +
                '<td><input type="number" name="company_shareholders[' + companyShareholderIndex + '][company_shareholding_ratio]" class="layui-input company-internal-ratio" step="0.001" min="0" max="100" required oninput="limitDecimalPlaces(this, 3)"></td>' +
                '<td><input type="number" name="company_shareholders[' + companyShareholderIndex + '][store_shareholding_ratio]" class="layui-input store-ratio company-store-ratio" step="0.001" min="0" max="100" lay-verify="required|number" required oninput="limitDecimalPlaces(this, 3)"></td>' +
                '<td><input type="text" name="company_shareholders[' + companyShareholderIndex + '][remark]" class="layui-input"></td>' +
                '<td><button type="button" class="layui-btn layui-btn-danger layui-btn-xs remove-row">删除</button></td>' +
                '</tr>';

            $('#companyShareholderBody .no-data').remove();
            $('#companyShareholderBody').append(html);

            // 为新添加的select加载人员选项
            var newSelect = $('#companyShareholderBody tr:last-child .admin-select');
            loadAdminOptions(newSelect);

            companyShareholderIndex++;

            // 延迟渲染，确保DOM已经更新
            setTimeout(function() {
                try {
                    form.render('select');
                    updateSummaryTotals(); // 添加行后更新统计
                } catch (e) {
                    console.error('表单select渲染失败:', e);
                }
            }, 50);
        });

        // 添加个人股东
        $('#addPersonalShareholder').click(function() {
            var html = '<tr>' +
                '<td><input type="text" name="personal_shareholders[' + personalShareholderIndex + '][shareholder_name]" class="layui-input" placeholder="请输入股东姓名" lay-verify="required"></td>' +
                '<td>' +
                    '<select name="personal_shareholders[' + personalShareholderIndex + '][admin_id]" lay-search class="admin-select" lay-filter="adminSelect_personal_' + personalShareholderIndex + '">' +
                        '<option value="">请选择关联人员</option>' +
                    '</select>' +
                '</td>' +
                '<td><input type="number" name="personal_shareholders[' + personalShareholderIndex + '][store_shareholding_ratio]" class="layui-input store-ratio personal-store-ratio" step="0.001" min="0" max="100" required oninput="limitDecimalPlaces(this, 3)"></td>' +
                '<td><input type="text" name="personal_shareholders[' + personalShareholderIndex + '][remark]" class="layui-input"></td>' +
                '<td><button type="button" class="layui-btn layui-btn-danger layui-btn-xs remove-row">删除</button></td>' +
                '</tr>';

            $('#personalShareholderBody .no-data').remove();
            $('#personalShareholderBody').append(html);

            // 为新添加的select加载人员选项
            var newSelect = $('#personalShareholderBody tr:last-child .admin-select');
            loadAdminOptions(newSelect);

            personalShareholderIndex++;

            // 延迟渲染，确保DOM已经更新
            setTimeout(function() {
                try {
                    form.render('select');
                    updateSummaryTotals(); // 添加行后更新统计
                } catch (e) {
                    console.error('表单select渲染失败:', e);
                }
            }, 50);
        });

        // 初始化人员数据（只获取一次）
        function initAdminList() {
            if (adminListCache !== null) {
                return Promise.resolve(adminListCache);
            }

            return new Promise(function(resolve, reject) {
                $.ajax({
                    url: '/api/dividend/getAdminList',
                    type: 'GET',
                    dataType: 'json',
                    timeout: 10000, // 设置超时时间
                    success: function(res) {
                        if (res && res.code == 0) {
                            adminListCache = res.data || []; // 缓存数据
                            resolve(adminListCache);
                        } else {
                            reject(res.msg || '获取人员列表失败');
                        }
                    },
                    error: function(xhr, status, error) {
                        console.error('AJAX请求失败:', status, error);
                        reject('网络请求失败: ' + error);
                    }
                });
            });
        }

        // 加载人员选项到指定的select元素
        function loadAdminOptions(selectElement) {
            if (!adminListCache || !Array.isArray(adminListCache)) {
                console.warn('人员数据未加载或格式错误');
                return;
            }

            try {
                var options = '<option value="">请选择关联人员</option>';
                $.each(adminListCache, function(index, item) {
                    if (item && item.id && item.name) {
                        options += '<option value="' + item.id + '">' + item.name + '</option>';
                    }
                });

                if (selectElement && selectElement.length > 0) {
                    selectElement.html(options);
                } else {
                    $('.admin-select').each(function() {
                        // 保存当前选中的值
                        var currentValue = $(this).val();
                        var currentText = $(this).find('option:selected').text();
                        
                        // 更新所有选项
                        $(this).html(options);
                        
                        // 如果之前有选中值，重新选中（确保选项存在）
                        if (currentValue && currentValue !== '') {
                            // 检查当前值是否在新选项中存在
                            var optionExists = $(this).find('option[value="' + currentValue + '"]').length > 0;
                            if (optionExists) {
                                $(this).val(currentValue);
                            } else {
                                // 如果选项不存在，添加一个临时选项以保持当前显示
                                $(this).append('<option value="' + currentValue + '" selected>' + currentText + '</option>');
                            }
                        }
                    });
                }

                // 确保在DOM更新后再渲染
                setTimeout(function() {
                    try {
                        form.render('select');
                    } catch (e) {
                        console.error('渲染select失败:', e);
                    }
                }, 10);
            } catch (e) {
                console.error('加载人员选项失败:', e);
            }
        }

        // 删除股东行
        $(document).on('click', '.remove-row', function() {
            var $tr = $(this).closest('tr');
            var $tbody = $tr.closest('tbody');

            layer.confirm('确定删除该股东信息吗？', {icon: 3, title: '删除确认'}, function(index) {
                $tr.remove();

                // 如果没有数据行了，显示无数据提示
                if ($tbody.find('tr:not(.no-data)').length === 0) {
                    var colspan = $tbody.attr('id') === 'companyShareholderBody' ? 6 : 5;
                    $tbody.append('<tr class="no-data"><td colspan="' + colspan + '" style="text-align: center; color: #999;">暂无股东信息</td></tr>');
                }

                calculateCompanyStoreRatio();
                updateSummaryTotals(); // 删除行后更新统计
                layer.close(index);
            });
        });

        // 监听自动计算开关变化
        form.on('switch(autoCalcSwitch)', function(data) {
            if (data.elem.checked) {
                // 开关打开时，立即计算一次
                calculateCompanyStoreRatio();
                layer.msg('已开启自动计算', {icon: 1, time: 1500});
            } else {
                layer.msg('已关闭自动计算', {icon: 2, time: 1500});
            }
        });

        // 监听公司股东持股比例变化
        $(document).on('input', '#companyShareholdingRatio', function() {
            calculateCompanyStoreRatio();
        });

        // 监听公司内部持股比例变化
        $(document).on('input', '.company-internal-ratio', function() {
            calculateCompanyStoreRatio();
        });

        // 监听门店持股比例变化（用于更新统计）
        $(document).on('input', '.store-ratio', function() {
            updateSummaryTotals();
        });

        // 实时验证小数位数 - 公司股东持股比例
        $(document).on('blur', '#companyShareholdingRatio', function() {
            var value = $(this).val();
            if (value && !validateDecimalPlaces(value, '公司股东持股比例')) {
                $(this).focus();
            }
        });

        // 实时验证小数位数 - 公司内部持股比例
        $(document).on('blur', '.company-internal-ratio', function() {
            var value = $(this).val();
            if (value && !validateDecimalPlaces(value, '公司内部持股比例')) {
                $(this).focus();
            }
        });

        // 实时验证小数位数 - 门店持股比例
        $(document).on('blur', '.store-ratio', function() {
            var value = $(this).val();
            if (value && !validateDecimalPlaces(value, '门店持股比例')) {
                $(this).focus();
            }
        });

        // 验证股东信息
        function validateShareholderData() {
            var isValid = true;
            var errorMsg = '';

            // 验证公司股东
            $('#companyShareholderBody tr:not(.no-data)').each(function() {
                var $row = $(this);
                var shareholderName = $row.find('input[name*="[shareholder_name]"]').val().trim();
                var companyRatio = parseFloat($row.find('input[name*="[company_shareholding_ratio]"]').val()) || 0;
                var storeRatio = parseFloat($row.find('input[name*="[store_shareholding_ratio]"]').val()) || 0;

                if (!shareholderName) {
                    errorMsg = '公司股东姓名不能为空';
                    isValid = false;
                    return false;
                }
                if (companyRatio <= 0) {
                    errorMsg = '公司内部持股比例必须大于0';
                    isValid = false;
                    return false;
                }
                if (storeRatio <= 0) {
                    errorMsg = '公司股东的门店持股比例必须大于0';
                    isValid = false;
                    return false;
                }
            });

            // 验证个人股东
            if (isValid) {
                $('#personalShareholderBody tr:not(.no-data)').each(function() {
                    var $row = $(this);
                    var shareholderName = $row.find('input[name*="[shareholder_name]"]').val().trim();
                    var storeRatio = parseFloat($row.find('input[name*="[store_shareholding_ratio]"]').val()) || 0;

                    if (!shareholderName) {
                        errorMsg = '个人股东姓名不能为空';
                        isValid = false;
                        return false;
                    }
                    if (storeRatio <= 0) {
                        errorMsg = '个人股东的门店持股比例必须大于0';
                        isValid = false;
                        return false;
                    }
                });
            }

            if (!isValid) {
                layer.msg(errorMsg, {icon: 2});
            }
            return isValid;
        }

        // 表单提交
        form.on('submit(webform)', function (data) {
            // 验证小数位数
            if (!validateAllDecimalPlaces()) {
                return false;
            }

            // 验证股东数据
            if (!validateShareholderData()) {
                return false;
            }

            // 验证是否至少有一个股东
            var hasCompanyShareholder = $('#companyShareholderBody tr:not(.no-data)').length > 0;
            var hasPersonalShareholder = $('#personalShareholderBody tr:not(.no-data)').length > 0;

            if (!hasCompanyShareholder && !hasPersonalShareholder) {
                layer.msg('请至少添加一个股东信息', {icon: 2});
                return false;
            }

            // 异步验证持股比例
            var ratioValidation = validateTotalRatio();

            // 处理异步验证结果
            if (ratioValidation instanceof Promise) {
                ratioValidation.then(function(isValid) {
                    if (isValid) {
                        // 用户确认了不完整比例，传递允许标志
                        submitForm(data, true);
                    }
                });
                return false; // 阻止默认提交，等待异步验证完成
            } else if (ratioValidation === false) {
                return false; // 验证失败
            } else {
                // 验证通过，直接提交
                submitForm(data, false);
                return false; // 阻止默认提交，使用自定义提交
            }
        });

        // 提交表单的具体实现
        function submitForm(data, allowIncompleteRatio) {
            // 如果允许不完整比例，添加参数
            if (allowIncompleteRatio) {
                data.field.allow_incomplete_ratio = 1;
            }
            let callback = function (e) {
                // 确保响应对象存在
                if (!e) {
                    layer.msg('服务器响应异常', {icon: 2});
                    return;
                }

                // 处理响应
                var code = e.code !== undefined ? e.code : 1;
                var msg = e.msg || (code == 0 ? '操作成功' : '操作失败');

                layer.msg(msg, {icon: code == 0 ? 1 : 2});
                if (code == 0) {
                    tool.sideClose(1000);
                }
            }

            // 使用更详细的错误处理
            $.ajax({
                url: "/dividend/index/add",
                type: "POST",
                data: data.field,
                dataType: 'json',
                timeout: 30000,
                success: function(res) {
                    callback(res);
                },
                error: function(xhr, status, error) {
                    console.error('AJAX请求失败:', xhr, status, error);

                    // 尝试解析响应
                    var response = null;
                    try {
                        response = JSON.parse(xhr.responseText);
                    } catch (e) {
                        console.error('响应解析失败:', e);
                    }

                    if (response) {
                        callback(response);
                    } else {
                        layer.msg('网络请求失败: ' + (error || status), {icon: 2});
                    }
                }
            });
        }

        // 页面初始化函数
        function initializePage() {
            // 检查必要的DOM元素是否存在
            if (!$('#companyShareholdingRatio').length) {
                console.error('关键DOM元素缺失');
                return;
            }

            try {
                // 先渲染表单
                form.render();

                // 然后初始化人员数据
                initAdminList().then(function() {
                    loadAdminOptions(); // 加载人员选项到所有现有的select
                    calculateCompanyStoreRatio(); // 计算一次比例
                    updateSummaryTotals(); // 初始化统计
                }).catch(function(error) {
                    console.error('获取人员列表失败:', error);
                    // 不显示错误消息，因为这不是关键错误
                });
            } catch (e) {
                console.error('页面初始化失败:', e);
            }
        }

        // 页面初始化 - 等待DOM完全加载
        $(document).ready(function() {
            // 延迟初始化，确保所有DOM元素都已准备就绪
            setTimeout(initializePage, 100);
        });
    }
</script>
{/block}
