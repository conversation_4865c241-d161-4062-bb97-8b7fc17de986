{extend name="../../base/view/common/base" /}
{block name="style"}
<style>
    body { display: flex; overflow: hidden; height: 100vh; }
    ::-webkit-scrollbar { display: none; width: 6px; height: 6px; }
    ::-webkit-scrollbar-thumb { border-radius: 10px; background-color: #e1e1e1; }
	.layui-tab{margin:0;}
	.container { display: flex; flex: 1; flex-direction: column; overflow: hidden; padding:12px;}
    .file-container { display: flex; flex: 1; flex-direction: column; overflow: hidden; border-radius: 2px; background: #ffffff; box-shadow: rgba(0, 0, 0, 0.05) 0 1px 2px 0; }
    .file-item {display: flex; flex: 1; justify-content: space-between; overflow: hidden; }
    .file-header {padding-top: 4px; }

    /** 分组 **/
    .file-item .group { position: relative; display: flex; flex-direction: column; padding-top: 8px; width: 200px; border-right: 1px solid #f6f6f6; }
    .file-item .group ul::-webkit-scrollbar { display: block; }
    .file-item .group ul { flex: 1; overflow: hidden; overflow-y: auto; }
    .file-item .group ul li {display: flex; align-items: center; justify-content: space-between; cursor:pointer; padding: 0 12px; height: 38px; font-size: 13px; color: #666666; }
    .file-item .group ul li i.iconfont{ margin-right:8px; font-size:24px; color:#E1AF1A; font-weight:800;}
    .file-item .group ul li > span { display: flex; flex: 1; overflow: hidden; margin-right: 0.5rem; text-overflow: ellipsis; white-space: nowrap; }
    .file-item .group ul li.active { background: #edefff; }
    .file-item .group ul li:hover { background: #f5f7fa; }
    .file-item .group ul li .dropdown:hover dl { display: block; }
    .file-item .group ul li .dropdown dl { position: absolute; top: 36px; right: -15px; z-index: 10000; display: none; padding: 5px 0; width: 88px; border-radius: 2px; text-align: center; background-color: #ffffff; box-shadow: #cccccc 0 0 10px; }
    .file-item .group ul li .dropdown dl dd { height: 32px; line-height: 32px; }
    .file-item .group ul li .dropdown dl dd:hover { color: #4a5dff; background-color: #edefff; }
    .file-item .group ul li .dropdown dl::before { position: absolute; top: -16px; left: 44px; z-index: 12; display: block; padding: 0; width: 0; height: 0; border-top: 8px solid transparent; border-right: 8px solid transparent; border-bottom: 8px solid #ffffff; border-left: 8px solid transparent; content: ""; box-sizing: content-box; }
    .file-item .group .footer { display: flex; align-items: center; justify-content: center; width: 100%; height: 50px; border-top: 1px solid #f2f2f2; border-right: 1px solid #f2f2f2; }

    /** 菜单 **/
    .file-item .attach #move:hover { position: relative; opacity: 1; }
    .file-item .attach #move:hover .dropdown { display: block; background-color: #ffffff; }
    .file-item .attach .dropdown { position: absolute; top: 38px; z-index: 100000; display: none; padding: 5px 0; width: 150px; border: 1px solid #dddddd; text-align: left; background-color: #ffffff; line-height: 1.6; }
    .file-item .attach .dropdown em { font-size: 13px; font-weight: 400; color: #333333; font-style: normal; }
    .file-item .attach .dropdown em { display: block; clear: both; padding: 6px 20px; white-space: nowrap; }
    .file-item .attach .dropdown em:hover { background: #eeeeee; }
    .file-item .attach .dropdown em:first-child { font-size: 12px; color: #999999; }
    .file-item .attach .dropdown::before { position: absolute; top: -16px; left: 21px; z-index: 12; display: block; padding: 0; width: 0; height: 0; border-top: 8px solid transparent; border-right: 8px solid transparent; border-bottom: 8px solid #ffffff; border-left: 8px solid transparent; content: ""; box-sizing: content-box; }
    .file-item .attach .dropdown::after { position: absolute; top: -18px; left: 20px; z-index: 10; display: block; padding: 0; width: 0; height: 0; border-top: 9px solid transparent; border-right: 9px solid transparent; border-bottom: 9px solid #cccccc; border-left: 9px solid transparent; content: ""; box-sizing: content-box; }

    /** 文件 **/
    .file-item .attach { position: relative; display: flex; flex: 1; flex-direction: column; }
    .file-item .attach .header { display: flex; align-items: center; justify-content: space-between; padding:12px 15px; margin:0;}
    .file-item .attach .header .search{ display: flex; }
    .file-item .attach .header .search input { height: 30px; border-color: #eeeeee; border-top-right-radius: 0; border-bottom-right-radius: 0; }
    .file-item .attach .header .search button { border-color: #eeeeee; background: #f5f7fa; border-top-left-radius: 0; border-bottom-left-radius: 0; }
    .file-item .attach .header .search button:hover { background-color: #eeeeef; }
    .file-item .attach .subject { flex: 1; overflow: hidden; overflow-y: auto; margin: 10px; box-sizing: border-box; }
    .file-item .attach .subject:hover::-webkit-scrollbar { display: block; }
    .file-item .attach .subject ul { display: flex; flex-wrap: wrap; }
    .file-item .attach .subject ul li { position: relative; height: 120px; margin: 5px; padding: 9px; border: 1px solid rgba(0, 0, 0, 0.05); border-radius: 3px; transition: all 0.2s ease-in-out; }
    .file-item .attach .subject ul li:hover {border: 1px solid #cccccc; }
    .file-item .attach .subject ul li.on {border: 1px solid #ff5722; }
    .file-item .attach .subject ul li img { width: 100px; height: 100px; border-radius: 2px; }
    .file-item .attach .subject ul li video { width: 100px; height: 100px; border-radius: 3px; }
    .file-item .attach .subject ul li p {overflow: hidden; margin: 5px 0 0; width: 98px; font-size: 13px; text-align: center; text-overflow: ellipsis; white-space: nowrap; }
    .file-item .attach .subject ul li .file-check{position: absolute; width:22px; height:22px; right: 0; bottom: 0; display: none; font-size: 14px; border-radius:4px 0 2px 0; text-align: center; line-height: 22px; color: #ffffff; background-color:#fff; border-left:1px solid #ccc; border-top:1px solid #ccc; cursor:pointer;}	
	.file-item .attach .subject ul li:hover .file-check{display: block;}
    .file-item .attach .subject ul li.on .file-check{ display: block; background: #ff5722; border-color:#ff5722}
	.file-item .attach .subject ul li .layui-btn-group{position:absolute; top:3px; right:3px; display:none;}
	.file-item .attach .subject ul li:hover .layui-btn-group{display:block}
	
    .file-item .attach .footer { display: flex; align-items: center; justify-content: end; padding: 5px 15px 0; height: 45px; border-top: 1px solid #f2f2f2; text-align: center; background: #ffffff; }

    /** 无数据 **/
    .file-item .empty { display: flex; flex: 1; align-items: center; flex-direction: column; justify-content: center; overflow: hidden; text-align: center; color: #cccccc; }
    .file-item .empty i { font-size: 180px; }
    .file-item .empty p { width: 180px; text-align: center; }
</style>
{/block}
<!-- 主体 -->
{block name="body"}
<div class="container">
	<div class="file-container">
		<div class="file-header">
			<div class="layui-tab layui-tab-brief" lay-filter="tab">
				<ul class="layui-tab-title">
					<li class="layui-this" data-tab="0">全部</li>
					<li data-tab="1">图片</li>
					<li data-tab="2">视频</li>
					<li data-tab="3">文档</li>
					<li data-tab="4">压缩包</li>
				</ul>
			</div>
		</div>
		<div class="file-item">
			<!-- 分组 -->
			<div class="group">
				<ul id="group" class="groups">					

				</ul>
				<div class="footer">
					<span class="layui-btn layui-btn-primary layui-btn-sm add-new">添加分组</span>
				</div>
			</div>
			<!-- 文件 -->
			<div class="attach">
				<!-- 工具 -->
				<form class="layui-form" lay-filter="barsearchform" style="display:block; margin:0;padding:0;">
					<div class="header">
						<div class="layui-btn-group">
							<span class="layui-btn layui-btn-sm layui-btn-normal" id="fileUpload">上传文件</span>
							<span class="layui-btn layui-btn-sm layui-btn-danger" id="fileDelete">批量删除</span>
							<span class="layui-btn layui-btn-sm layui-btn-warm" id="fileMove">移动至 <i class="layui-icon layui-icon-triangle-d"></i></span>
							<span style="margin-left:36px;"><input type="checkbox" name="select_all" lay-filter="all" title="全选"></span>
						</div>

						<div class="search">
							<input type="hidden" name="limit" value="44" />
							<input type="hidden" name="page" value="1" />
							<input type="hidden" name="tab" value="0" />
							<input type="hidden" name="group_id" value="" />
							<label><input type="text" name="keywords" placeholder="请输入名称" autocomplete="off" class="layui-input"></label>
							<button type="button" class="layui-btn layui-btn-sm layui-btn-primary" lay-submit="" lay-filter="webform"><i class="layui-icon layui-icon-search"></i></button>
						</div>
					</div>
				</form>
				<!-- 主体 -->
				<div class="subject">
					<ul id="filesBox"></ul>
				</div>
				<!-- 页脚 -->
				<div class="footer">
					<div id="laypage"></div>
				</div>
			</div>
		</div>
        </div>
</div>
<script type="text/html" id="toolbarDemo">
<h3 class="h3-title" style="height:28px;">审批列表</h3>
</script>						
{/block}
<!-- /主体 -->

<!-- 脚本 -->
{block name="script"}
<script>
	const moduleInit = ['tool'];
	function gouguInit() {
		var table = layui.table, tool = layui.tool ,form = layui.form,laypage = layui.laypage,upload = layui.upload, element=layui.element, dropdown=layui.dropdown;
		let fileGroup = [{id:0,title:'未分组'}];
		element.on('tab(tab)', function(data){
			$('[name="tab"]').val(data.index);
			$('[lay-filter="webform"]').click();
			return false;
		});	
		
		//监听多选框点击事件  通过 lay-filter="menu"来监听
		form.on('checkbox(all)', function (data) {
		　　let val = data.value;
			if(data.elem.checked){
				//判断当前多选框是选中还是取消选中
				$('#filesBox li').addClass('on');
			}
			else{
				$('#filesBox li').removeClass('on');
			}
		});

		var uploadInst = upload.render({
			elem: '#fileUpload'
			,url: "/api/index/upload"
			, accept: 'file' //普通文件
			, exts: 'jpeg|jpg|png|gif|doc|docx|ppt|pptx|xls|xlsx|pdf|zip|rar|7z|txt|mp4|psd|svg' //只允许上传文件
			, before: function (obj) {
				layer.msg('上传中...', { time: 3600000 });
			}
			,done: function (res) {
				layer.msg(res.msg);
				$('[lay-filter="webform"]').click();
			}, error: function (index, upload) {
				layer.msg('上传失败');
			}
		});
		
		$('body').on('click','.add-new',function(){
			add_group(0,'');	
		});
		
		$('#group').on('click','li',function(){
			let id=$(this).data('id');
			$(this).siblings().removeClass('active');
			$(this).addClass('active');
			$('[name="group_id"]').val(id);
			$('[lay-filter="webform"]').click();
		});
			
		$('#fileDelete').on('click',function(){
			let select_array = [];
			$('#filesBox').find('li.on').each(function(index,item){
				select_array.push($(item).data('id'));			
			})
			if(select_array.length<1){
				layer.msg('请先选择文件');
			}
			else{
				layer.confirm('确定要删除这些文件吗？请慎重', { icon: 3, title: '提示' }, function (index) {
					let callback = function (e) {
						layer.closeAll();
						layer.msg(e.msg);
						$('[lay-filter="webform"]').click();
					}
					tool.delete("/home/<USER>/delete", {ids:select_array.join(',')}, callback);
				});
			}
		});
		
		$('#fileMove').on('click',function(){
			let that = this;
			dropdown.render({
				elem: that,
				align: 'right',
				show: true, // 外部事件触发即显示
				data: fileGroup,
				click: function(obj){
					let select_array = [];
					$('#filesBox').find('li.on').each(function(index,item){
						select_array.push($(item).data('id'));		
					})
					if(select_array.length<1){
						layer.msg('请先选择文件');
					}
					else{				
						layer.confirm('确定要把选中的文件移动到『'+obj.title+'』分组吗？', { icon: 3, title: '提示' }, function (index) {
							let callback = function (e) {
								layer.closeAll();
								layer.msg(e.msg);
								$('[lay-filter="webform"]').click();
							}
							tool.delete("/home/<USER>/move", {group_id:obj.id,ids:select_array.join(',')}, callback);
						});
					}
				}
			});
		})
					
		function add_group(id,val){
			var title = '新增分组';
			if(id>0){
				title = '编辑分组';
			}
			layer.prompt({
				title: title,
				value: val,
				yes: function(index, layero) {
					// 获取文本框输入的值
					var value = layero.find(".layui-layer-input").val();
					if (value!='') {
						let callback = function (e) {
							layer.msg(e.msg);
							if(e.code==0){
								location.reload();
							}
							/*
							if(id==0){
								let item='<li data-id="'+e.data+'"><i class="iconfont icon-sucaiziyuan"></i><span>'+value+'</span></li>';
								$('#group').append(item);
							}
							else{
								$('#group').find('[data-id="'+id+'"]').data('title',value).find('span').html(value);
							}
							*/
						}
						tool.post("/home/<USER>/add_group", {id: id,title: value}, callback);
						layer.close(index);
					} else {
						layer.msg('请填写分组名称');
					}
				}
			})
		}
			
		var data = form.val('barsearchform');
		get_files(data);
		get_group();
		
		function get_group(param){
			$.ajax({
				url:"/home/<USER>/get_group",
				success:function(res){
					if(res.code==0){
						var group=res.data,item='';

						if(group.length>0){
							for(var a=0;a<group.length;a++){
								item+='<li data-id="'+group[a].id+'" data-title="'+group[a].title+'"><i class="iconfont icon-sucaiziyuan"></i><span>'+group[a].title+'</span><i class="layui-icon layui-icon-more-vertical dropdown-on"></i></li>';
							}
							$('#group').append(item);
							fileGroup = fileGroup.concat(group);
							// 自定义事件
							dropdown.render({
								elem: '.dropdown-on',
								trigger: 'hover',
								align: 'right',
								data: [{
								  title: '重命名分组',
								  id: 100
								},{
								  title: '删除该分组',
								  id: 101
								}],
								click: function(data, othis){
									let id = $(this.elem).parent().data('id');
									let title = $(this.elem).parent().data('title');
									if(data.id==100){
										add_group(id,title);
									}else{
										del_group(id);
									}							
								}
							});
						}
					}
				}
			});
		}
		
		function del_group(id) {
			layer.confirm('确定要删除该分组吗？请慎重', { icon: 3, title: '提示' }, function (index) {
				let callback = function (e) {
					layer.closeAll();
					layer.msg(e.msg);
					//$('#group').find('[data-id="'+id+'"]').remove();
					if(e.code==0){
						location.reload();
					}
				}
				tool.delete("/home/<USER>/del_group", {ids:id}, callback);
			});
		}
		
		
		function get_files(param){
			var loadIndex = layer.load(0);
			$.ajax({
				url:"/home/<USER>/index",
				data:param,
				complete:function(){
					setTimeout(function(){
						layer.close(loadIndex)
					}, 200);
				},
				success:function(res){
					$('[name="select_all"]').prop('checked', false);
					form.render('checkbox');
					if(res.code==0){
						laypage.render({
							elem: 'laypage',
							limit:param['limit'],
							curr:param['page'],
							count: res.count, // 数据总数
							jump: function(obj, first){
								console.log(obj.curr); // 得到当前页，以便向服务端请求对应页的数据。
								console.log(obj.limit); // 得到每页显示的条数
								// 首次不执行
								if(!first){
									var data = form.val('barsearchform');
									data['page'] = obj.curr;
									get_files(data);
								}
							}
						});				
					
						var item=res.data,li='';
						if(item.length>0){
							for(var a=0;a<item.length;a++){
								let image = ['jpg','jpeg','png','gif'];
								let doc = ['doc','docx','xls','xlsx','ppt','pptx','txt','pdf','zip','rar','7z'];
								let down = '<a href="'+item[a].filepath+'" target="_blank" class="layui-btn layui-btn-xs layui-btn-normal" download="'+item[a].name+'">下载</a>';
								// 判断元素是否在数组中
								let path='/static/home/<USER>/icon/file.png';
								if (image.includes(item[a].fileext)) {
									path=item[a].filepath;
									down = '<span data-href="'+item[a].filepath+'" class="layui-btn layui-btn-xs layui-btn-normal file-view-img">预览</span>';
								} else if (doc.includes(item[a].fileext)){
									path='/static/home/<USER>/icon/'+item[a].fileext+'.png';
								}
								
								if(item[a].fileext == 'pdf'){
									down = '<span data-href="'+item[a].filepath+'" class="layui-btn layui-btn-xs layui-btn-normal file-view-pdf">预览</span>';
								}
								li+='<li data-id="'+item[a].id+'" data-title="'+item[a].name+'" data-ext="'+item[a].fileext+'">' +
										'<img src="'+path+'" alt="'+item[a].filename+'" style="object-fit: contain;" class="file-item">' +
										'<p title="'+item[a].name+'">'+item[a].name+'</p>' +
										'<div class="layui-btn-group">' +
										'<span class="layui-btn layui-btn-xs file-edit">重命名</span>'+down+
										'<span class="layui-btn layui-btn-xs layui-btn-danger file-del">删除</span>' +
										'</div>' +
										'<div class="file-check">✔</div>' +
										'</li>';
							}
							$('#filesBox').html(li);
							$('#laypage').show();
						}
						else{
							$('#filesBox').html('<div class="empty"><i class="layui-icon layui-icon-upload"></i><p>无文件文件，赶紧去上传吧!</p></div>');
							$('#laypage').hide();
						}
					}	
				}				
			});
		}
		
		$('#filesBox').on('click','.file-check',function(){
			$(this).parent().toggleClass("on");
		})
		
		$('#filesBox').on('click','.file-del',function(){
			let id = $(this).parent().parent().data('id');
			layer.confirm('确定要删除该文件吗？请慎重', { icon: 3, title: '提示' }, function (index) {
				let callback = function (e) {
					layer.closeAll();
					layer.msg(e.msg);
					$('[lay-filter="webform"]').click();
				}
				tool.delete("/home/<USER>/delete", {ids:id}, callback);
			});
		})
		
		$('#filesBox').on('click','.file-edit',function(){
			let id = $(this).parent().parent().data('id');
			let title = $(this).parent().parent().data('title');
			let ext = $(this).parent().parent().data('ext');
			layer.prompt({
				title: '重命名',
				value: title.replace(/\.[^.]+$/, ""),
				yes: function(index, layero) {
					// 获取文本框输入的值
					var value = layero.find(".layui-layer-input").val();
					if (value!='') {
						let callback = function (e) {
							layer.msg(e.msg);
							$('[lay-filter="webform"]').click();
						}
						tool.post("/home/<USER>/edit", {id: id,title: value+'.'+ext}, callback);
						layer.close(index);
					} else {
						layer.msg('请填写分组名称');
					}
				}
			})
		})
		
		//监听搜索提交
		form.on('submit(webform)', function(data) {
			get_files(data.field);
			return false;
		});

	}
</script>
{/block}
<!-- /脚本 -->