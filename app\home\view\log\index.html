{extend name="../../base/view/common/base" /}
<!-- 主体 -->
{block name="body"}
<div class="p-3">
	<form class="layui-form gg-form-bar border-t border-x">
		<div class="layui-input-inline" style="width:300px;">
			<input type="text" name="keywords" placeholder="昵称/操作数据id" class="layui-input" autocomplete="off" />
		</div>
		<div class="layui-input-inline">
			<select name="action">
				<option value="">请选择类型</option>
				{volist name="$type_action" id="vo"}
				<option value="{$vo}">{$vo}</option>
				{/volist}
			</select>
		</div>
		<div class="layui-input-inline" style="width:150px;">
			<button class="layui-btn layui-btn-normal" lay-submit="" lay-filter="webform"><i class="layui-icon layui-icon-search mr-1"></i>搜索</button>
			<button type="reset" class="layui-btn layui-btn-reset" lay-filter="reset">清空</button>
		</div>
	</form>
	<table class="layui-hide" id="log" lay-filter="log"></table>
</div>
{/block}
<!-- /主体 -->

<!-- 脚本 -->
{block name="script"}
<script>
	function gouguInit() {
		var table = layui.table, form = layui.form;
		var tableIns = table.render({
			elem: '#log',
			title: '操作日志列表',
			url: "/home/<USER>/index", //数据接口	
			page: true, //开启分页
			limit: 20,
			cols: [
				[ //表头
					{
						field: 'id',
						title: 'ID号',
						align: 'center',
						width: 90
					}, {
						field: 'action',
						title: '操作',
						align: 'center',
						width: 80
					}, {
						field: 'content',
						title: '操作描述',
						width: 200
					}, {
						field: 'param_id',
						title: '操作数据ID',
						align: 'center',
						width: 100
					},{
						field: 'param',
						title: '操作详细数据信息',
					}, {
						field: 'ip',
						title: 'IP地址',
						align: 'center',
						width: 130
					}, {
						field: 'create_time',
						title: '操作时间',
						fixed: 'right',
						align: 'center',
						width: 160
					}
				]
			]
		});

		//监听搜索提交
		form.on('submit(webform)', function (data) {
			tableIns.reload({
				where: {
					keywords: data.field.keywords,
					action: data.field.action
				},
				page: {
					curr: 1
				}
			});
			return false;
		});
	}
</script>
{/block}
<!-- /脚本 -->