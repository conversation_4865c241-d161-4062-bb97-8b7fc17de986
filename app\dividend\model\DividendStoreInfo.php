<?php
/**
 * @copyright Copyright (c) 2021 仲正堂工作室
 * @license https://opensource.org/licenses/GPL-3.0
 * @link https://www.gougucms.com
 */

namespace app\dividend\model;

use think\Model;
use think\facade\Db;

class DividendStoreInfo extends Model
{
    protected $name = 'dividend_store_info';

    // 设置字段信息
    protected $schema = [
        'id'                          => 'int',
        'store_id'                    => 'int',
        'risk_reserve'                => 'decimal',
        'company_shareholding_ratio'  => 'decimal',
        'remark'                      => 'string',
        'is_delete'                   => 'int',
        'delete_time'                 => 'int',
        'create_time'                 => 'int',
        'update_time'                 => 'int',
    ];

    // 自动时间戳
    protected $autoWriteTimestamp = true;
    protected $createTime = 'create_time';
    protected $updateTime = 'update_time';

    /**
     * 关联门店信息
     */
    public function store()
    {
        return $this->belongsTo('app\user\model\Department', 'store_id', 'id');
    }

    /**
     * 关联股东信息
     */
    public function shareholders()
    {
        return $this->hasMany('app\dividend\model\DividendShareholder', 'dividend_store_info_id', 'id')
            ->where('is_delete', 0);
    }

    /**
     * 关联公司股东
     */
    public function companyShareholders()
    {
        return $this->hasMany('app\dividend\model\DividendShareholder', 'dividend_store_info_id', 'id')
            ->where('is_delete', 0)
            ->where('shareholder_type', 1);
    }

    /**
     * 关联个人股东
     */
    public function personalShareholders()
    {
        return $this->hasMany('app\dividend\model\DividendShareholder', 'dividend_store_info_id', 'id')
            ->where('is_delete', 0)
            ->where('shareholder_type', 2);
    }

    /**
     * 获取门店分红列表
     * @param array $where 查询条件
     * @param int $page 页码
     * @param int $limit 每页数量
     * @return array
     */
    public static function getList($where = [], $page = 1, $limit = 20)
    {
        $query = self::alias('dsi')
            ->join('oa_department d', 'd.id = dsi.store_id', 'LEFT')
            ->field('dsi.*, d.title as store_name')
            ->where('dsi.is_delete', 0);

        if (!empty($where)) {
            $query->where($where);
        }

        $list = $query->order('dsi.id desc')
            ->paginate($limit, false, ['page' => $page])
            ->each(function ($item) {
                // 计算公司股东持股比例总和
                $companyTotal = DividendShareholder::where('dividend_store_info_id', $item['id'])
                    ->where('shareholder_type', 1)
                    ->where('is_delete', 0)
                    ->sum('store_shareholding_ratio');
                // 确保是数值类型，避免number_format报错
                $companyTotal = is_numeric($companyTotal) ? floatval($companyTotal) : 0;
                $item['company_shareholding_total'] = number_format($companyTotal, 3);

                // 计算个人股东持股比例总和
                $personalTotal = DividendShareholder::where('dividend_store_info_id', $item['id'])
                    ->where('shareholder_type', 2)
                    ->where('is_delete', 0)
                    ->sum('store_shareholding_ratio');
                // 确保是数值类型，避免number_format报错
                $personalTotal = is_numeric($personalTotal) ? floatval($personalTotal) : 0;
                $item['personal_shareholding_total'] = number_format($personalTotal, 3);

                // 格式化已计提风险金
                $riskReserve = is_numeric($item['risk_reserve']) ? floatval($item['risk_reserve']) : 0;
                $item['risk_reserve_formatted'] = number_format($riskReserve, 2);
                $item['create_time'] = date('Y-m-d H:i:s', $item['create_time']);
            });

        return $list;
    }

    /**
     * 获取门店分红详情
     * @param int $id 分红信息ID
     * @return array
     */
    public static function getDetail($id)
    {
        $detail = self::alias('dsi')
            ->join('oa_department d', 'd.id = dsi.store_id', 'LEFT')
            ->field('dsi.*, d.title as store_name')
            ->where('dsi.id', $id)
            ->where('dsi.is_delete', 0)
            ->find();

        if (empty($detail)) {
            return [];
        }

        // 获取股东信息
        $detail['company_shareholders'] = DividendShareholder::where('dividend_store_info_id', $id)
            ->where('shareholder_type', 1)
            ->where('is_delete', 0)
            ->order('sort_order asc')
            ->select()
            ->toArray();

        $detail['personal_shareholders'] = DividendShareholder::where('dividend_store_info_id', $id)
            ->where('shareholder_type', 2)
            ->where('is_delete', 0)
            ->order('sort_order asc')
            ->select()
            ->toArray();

        return $detail;
    }

    /**
     * 检查门店是否已存在分红信息
     * @param int $storeId 门店ID
     * @param int $excludeId 排除的分红信息ID
     * @return bool
     */
    public static function checkStoreExists($storeId, $excludeId = 0)
    {
        $where = [
            ['store_id', '=', $storeId],
            ['is_delete', '=', 0]
        ];

        if ($excludeId > 0) {
            $where[] = ['id', '<>', $excludeId];
        }

        $exists = self::where($where)->find();
        return !empty($exists);
    }

    /**
     * 软删除分红信息及关联股东
     * @param int $id 分红信息ID
     * @return bool
     */
    public static function softDelete($id)
    {
        Db::startTrans();
        try {
            // 软删除主表记录
            self::where('id', $id)->update([
                'is_delete' => 1,
                'delete_time' => time()
            ]);

            // 软删除关联的股东记录
            DividendShareholder::where('dividend_store_info_id', $id)->update([
                'is_delete' => 1,
                'delete_time' => time()
            ]);

            Db::commit();
            return true;
        } catch (\Exception $e) {
            Db::rollback();
            return false;
        }
    }
}
