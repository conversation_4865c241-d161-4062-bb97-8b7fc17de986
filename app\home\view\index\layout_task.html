<div class="layui-row layui-col-space16">
	<div class="layui-col-md12">
		<div class="layui-card">
			<div class="layui-card-title">任务列表</div>
			<div style="padding: 12px;">
				<table id="Task" lay-filter="Task" class="layui-hide" style="margin-top:0"></table>
			</div>
		</div>
	</div>
</div>
<script>
//任务
function layoutTask(table){
	table.render({
		elem: '#Task'
		, url: "/home/<USER>/get_task_list" //数据接口
		, page: false //开启分页
		, cols: [[ //表头
			{ field: 'id', title: '任务编号', align: 'center','width': 90, templet: function (d) {
					return 'T' + d.id;
				}
			},
			{field: 'flow_status', title: '状态', align: 'center', width: 90, templet: function (d) {
					var html = '<span class="layui-color-' + d.flow_status + '">『' + d.flow_name + '』</span>';
					return html;
				}
			},
			{ field: 'title', title: '任务主题',templet: '<div><a data-href="/project/task/view/id/{{d.id}}.html" class="side-a">{{d.title}}</a></div>'},
			{ field: 'director_name', title: '负责人', align: 'center', width: 80},
			{ field: 'end_time', title: '计划完成日期', align: 'center', width: 190},
		]]
	});
}
</script>