<?php
/**
 * @copyright Copyright (c) 2021 仲正堂工作室
 * @license https://opensource.org/licenses/GPL-3.0
 * @link https://www.gougucms.com
 */

namespace app\dividend\validate;

use think\Validate;

class DividendCheck extends Validate
{
    protected $rule = [
        'id' => 'require|number',
        'store_id' => 'require|number|gt:0',
        'risk_reserve' => 'require|float|egt:0',
        'company_shareholding_ratio' => 'require|float|between:0,100',
        'remark' => 'max:500',
        'company_shareholders' => 'array',
        'personal_shareholders' => 'array',
        'adjust_type' => 'require|in:increase,decrease',
        'adjust_amount' => 'require|float|gt:0|checkDecimalPlaces',
    ];

    protected $message = [
        'id.require' => '缺少更新条件',
        'id.number' => 'ID必须为数字',
        'store_id.require' => '请选择关联门店',
        'store_id.number' => '门店ID必须为数字',
        'store_id.gt' => '请选择有效的门店',
        'risk_reserve.require' => '请输入已计提风险金',
        'risk_reserve.float' => '已计提风险金必须为数字',
        'risk_reserve.egt' => '已计提风险金不能为负数',
        'company_shareholding_ratio.require' => '请输入公司股东持股比例',
        'company_shareholding_ratio.float' => '公司股东持股比例必须为数字',
        'company_shareholding_ratio.between' => '公司股东持股比例必须在0-100之间',
        'remark.max' => '备注信息最多500个字符',
        'company_shareholders.array' => '公司股东数据格式错误',
        'personal_shareholders.array' => '个人股东数据格式错误',
        'adjust_type.require' => '请选择调整类型',
        'adjust_type.in' => '调整类型只能是增加或减少',
        'adjust_amount.require' => '请输入调整金额',
        'adjust_amount.float' => '调整金额必须为数字',
        'adjust_amount.gt' => '调整金额必须大于0',
        'adjust_amount.checkDecimalPlaces' => '调整金额最多支持两位小数',
    ];

    protected $scene = [
        'add' => ['store_id', 'risk_reserve', 'company_shareholding_ratio', 'remark', 'company_shareholders', 'personal_shareholders'],
        'edit' => ['id', 'store_id', 'risk_reserve', 'company_shareholding_ratio', 'remark', 'company_shareholders', 'personal_shareholders'],
        'adjust' => ['store_id', 'adjust_type', 'adjust_amount', 'remark'],
    ];

    /**
     * 自定义验证规则：检查小数位数（调整金额保持2位小数）
     */
    protected function checkDecimalPlaces($value, $rule, $data)
    {
        $valueStr = (string)$value;
        if (strpos($valueStr, '.') !== false) {
            $decimalPart = explode('.', $valueStr)[1];
            if (strlen($decimalPart) > 2) {
                return false;
            }
        }
        return true;
    }

    /**
     * 自定义验证规则：检查持股比例小数位数（三位小数）
     */
    protected function checkRatioDecimalPlaces($value, $rule, $data)
    {
        $valueStr = (string)$value;
        if (strpos($valueStr, '.') !== false) {
            $decimalPart = explode('.', $valueStr)[1];
            if (strlen($decimalPart) > 3) {
                return false;
            }
        }
        return true;
    }

    /**
     * 自定义验证规则：验证股东信息
     */
    protected function checkShareholders($value, $rule, $data)
    {
        if (empty($value) || !is_array($value)) {
            return true; // 允许为空
        }

        foreach ($value as $shareholder) {
            // 验证股东姓名
            if (empty($shareholder['shareholder_name'])) {
                return '股东姓名不能为空';
            }

            if (mb_strlen($shareholder['shareholder_name']) > 50) {
                return '股东姓名最多50个字符';
            }

            // 验证门店持股比例
            if (!isset($shareholder['store_shareholding_ratio']) ||
                !is_numeric($shareholder['store_shareholding_ratio'])) {
                return '门店持股比例必须为数字';
            }

            $storeRatio = floatval($shareholder['store_shareholding_ratio']);
            if ($storeRatio < 0 || $storeRatio > 100) {
                return '门店持股比例必须在0-100之间';
            }

            // 如果是公司股东，验证公司内部持股比例
            if (isset($shareholder['company_shareholding_ratio'])) {
                if (!is_numeric($shareholder['company_shareholding_ratio'])) {
                    return '公司内部持股比例必须为数字';
                }

                $companyRatio = floatval($shareholder['company_shareholding_ratio']);
                if ($companyRatio < 0 || $companyRatio > 100) {
                    return '公司内部持股比例必须在0-100之间';
                }
            }

            // 验证备注长度
            if (isset($shareholder['remark']) && mb_strlen($shareholder['remark']) > 500) {
                return '股东备注最多500个字符';
            }
        }

        return true;
    }

    /**
     * 自定义验证规则：验证持股比例总和
     */
    protected function checkTotalRatio($value, $rule, $data)
    {
        $companyTotal = 0;
        $personalTotal = 0;

        // 计算公司股东持股比例总和
        if (!empty($data['company_shareholders']) && is_array($data['company_shareholders'])) {
            foreach ($data['company_shareholders'] as $shareholder) {
                if (!empty($shareholder['store_shareholding_ratio'])) {
                    $companyTotal += floatval($shareholder['store_shareholding_ratio']);
                }
            }
        }

        // 计算个人股东持股比例总和
        if (!empty($data['personal_shareholders']) && is_array($data['personal_shareholders'])) {
            foreach ($data['personal_shareholders'] as $shareholder) {
                if (!empty($shareholder['store_shareholding_ratio'])) {
                    $personalTotal += floatval($shareholder['store_shareholding_ratio']);
                }
            }
        }

        $totalRatio = $companyTotal + $personalTotal;
        if ($totalRatio > 100) {
            return '总持股比例不能超过100%，当前为' . number_format($totalRatio, 3) . '%';
        }

        if ($totalRatio < 100 && $totalRatio > 0) {
            return '总持股比例未达到100%，当前为' . number_format($totalRatio, 3) . '%，请检查持股比例配置';
        }

        return true;
    }
}
