<div class="layui-row layui-col-space16">
	<div class="layui-col-md12">
		<div class="layui-card">
			<div id="chartMap" style="width: 100%;height:800px;"></div>
		</div>
	</div>
</div>
<script>
var chartMapView;
function layoutChartMap() {
	chartMapView = echarts.init(document.getElementById('chartMap'));
	$.ajax({
		url: "/home/<USER>/get_store_month",
		type: 'get',
		data: {},
		success: function (e) {
			if (e.code == 0) {
				let data = [];

				e.data.map((val,key)=>{
					data.push({
						name:val.dname,
						value:val.zyj,
						address:val.address
					})
				})
				// const geoCoordMap = {
				// 	zzt: [121.49, 31.17],
				// 	zzt2: [121.39, 31.19],
				// 	zzt3: [121.47,31.25],
				// };
				const convertData = function (data) {
					var res = [];
					for (var i = 0; i < data.length; i++) {
						var geoCoord = [data[i].address.split(',')[0],data[i].address.split(',')[1]];
						if (geoCoord) {
							res.push({
								// name: data[i].name + '【'+ data[i].value +'】',
								name: data[i].name,
								value: geoCoord.concat(data[i].value)
							});
						}
					}
					return res;
				};
				let option = {
					backgroundColor: 'transparent',
					title: {
						text: '上海仲正堂门店业绩情况',
						subtext: '',
						sublink: '',
						left: 'center'
					},
					tooltip: {
						trigger: 'item'
					},
					bmap: {
						center: [121.49, 31.17],
						zoom: 12,
						roam: true,
						mapStyle: {
							styleJson: [
								{
									featureType: 'water',
									elementType: 'all',
									stylers: {
										color: '#d1d1d1'
									}
								},
								{
									featureType: 'land',
									elementType: 'all',
									stylers: {
										color: '#f3f3f3'
									}
								},
								{
									featureType: 'railway',
									elementType: 'all',
									stylers: {
										visibility: 'off'
									}
								},
								{
									featureType: 'highway',
									elementType: 'all',
									stylers: {
										color: '#fdfdfd'
									}
								},
								{
									featureType: 'highway',
									elementType: 'labels',
									stylers: {
										visibility: 'off'
									}
								},
								{
									featureType: 'arterial',
									elementType: 'geometry',
									stylers: {
										color: '#fefefe'
									}
								},
								{
									featureType: 'arterial',
									elementType: 'geometry.fill',
									stylers: {
										color: '#fefefe'
									}
								},
								{
									featureType: 'poi',
									elementType: 'all',
									stylers: {
										visibility: 'off'
									}
								},
								{
									featureType: 'green',
									elementType: 'all',
									stylers: {
										visibility: 'off'
									}
								},
								{
									featureType: 'subway',
									elementType: 'all',
									stylers: {
										visibility: 'off'
									}
								},
								{
									featureType: 'manmade',
									elementType: 'all',
									stylers: {
										color: '#d1d1d1'
									}
								},
								{
									featureType: 'local',
									elementType: 'all',
									stylers: {
										color: '#d1d1d1'
									}
								},
								{
									featureType: 'arterial',
									elementType: 'labels',
									stylers: {
										visibility: 'off'
									}
								},
								{
									featureType: 'boundary',
									elementType: 'all',
									stylers: {
										color: '#fefefe'
									}
								},
								{
									featureType: 'building',
									elementType: 'all',
									stylers: {
										color: '#d1d1d1'
									}
								},
								{
									featureType: 'label',
									elementType: 'labels.text.fill',
									stylers: {
										visibility: 'off'
									}
								}
							]
						}
					},
					series: [
						{
							name: 'pm2.5',
							type: 'scatter',
							coordinateSystem: 'bmap',
							data: convertData(data),
							symbolSize: function (val) {
								return 0.5;
							},
							encode: {
								value: 2
							},
							label: {
								formatter: '{b}',
								position: 'right',
								show: false
							},
							emphasis: {
								label: {
									show: true
								}
							}
						},
						{
							name: 'Top 5',
							type: 'effectScatter',
							coordinateSystem: 'bmap',
							data: convertData(
									data
											.sort(function (a, b) {
												return b.value - a.value;
											})
											.slice(0, 50)
							),
							symbolSize: function (val) {
								return 10;
							},
							encode: {
								value: 2
							},
							showEffectOn: 'render',
							rippleEffect: {
								brushType: 'stroke'
							},
							label: {
								formatter: '{b}',
								position: 'bottom',
								show: true
							},
							itemStyle: {
								shadowBlur: 110,
								shadowColor: '#333'
							},
							emphasis: {
								scale: true
							},
							zlevel: 20
						}
					]
				};
				chartMapView.setOption(option);
			}
		}
	})

}

</script>