{extend name="../../base/view/common/base" /}
<!-- 主体 -->
{block name="body"}
<div class="p-3">
	<form class="layui-form gg-form-bar border-x border-t">
		<div class="layui-input-inline" style="width:300px;">
			<input type="text" class="layui-input" id="diff_time" placeholder="选择时间区间" readonly name="diff_time">
		</div>
		<div class="layui-input-inline" style="width:150px;">
			<button class="layui-btn layui-btn-normal" lay-submit="" lay-filter="webform"><i class="layui-icon layui-icon-search mr-1"></i>搜索</button>
			<button type="reset" class="layui-btn layui-btn-reset" lay-filter="reset">清空</button>
		</div>
	</form>
	<table class="layui-hide" id="test" lay-filter="test"></table>
</div>
{/block}
<!-- /主体 -->

<!-- 脚本 -->
{block name="script"}
<script>
	const moduleInit = ['tool','laydatePlus'];
	function gouguInit() {
		var form = layui.form,table = layui.table,tool=layui.tool, laydatePlus = layui.laydatePlus;
		//日期范围
		var diff_time = new laydatePlus({'target':'diff_time'});
		
		//监听搜索提交
		form.on('submit(webform)', function(data) {
			layui.pageTable.reload({
				where: data.field,
				page: {curr: 1}
			});
			return false;
		});

		layui.pageTable = table.render({
			elem: '#test',
			title: '发票列表',
			url: "/finance/invoice/copy", //数据接口		
			page: true, //开启分页
			cellMinWidth: 80,
			limit: 20,
			cols: [
				[
					{
						field: 'id',
						title: 'ID号',
						align: 'center',
						width: 80
					}, {
						field: 'invoice_title',
						title: '开票抬头',
						minWidth: 240,
						templet:function(d){
							var html='';
							if(d.type==1){
								html='<span class="layui-badge layui-bg-blue">企业</span> '+d.invoice_title;
							}
							else if(d.type==2){
								html='<span class="layui-badge layui-bg-green">个人</span> '+d.invoice_title;
							}
							return html;
						}
					},{
						field: 'amount',
						title: '开票金额(元)',
						align: 'right',
						width: 100,
					},{
						field: 'invoice_type',
						title: '开票类型',
						align: 'center',
						width: 120,
						templet:function(d){
							var html='-';
							if(d.invoice_type==1){
								html='<span class="green">增值税专用发票</span>';
							}
							else if(d.invoice_type==2){
								html='<span class="blue">普通发票</span>';
							}
							else if(d.invoice_type==3){
								html='<span class="red">专业发票</span>';
							}
							return html;
						}
					},{
						field: 'check_status',
						title: '发票状态',
						align: 'center',
						width: 120,
						templet:function(d){
							var html='<span class="black">待审核</span>';
							if(d.check_status==1){
								html='<span class="blue">审核中</span>';
							}
							else if(d.check_status==2){
								html='<span class="green">审核通过,待开具</span>';
							}
							else if(d.check_status==3){
								html='<span class="red">审核不通过</span>';
							}
							else if(d.check_status==4){
								html='<span class="red">已撤销</span>';
							}
							else if(d.check_status==5){
								html='<span class="green">已开具</span>';
							}
							else if(d.check_status==10){
								html='<span class="yellow">已作废</span>';
							}
							return html;
						}
					},{
						field: 'name',
						title: '申请人',
						align: 'center',
						width: 90
					},{
						field: 'department_name',
						title: '所属部门',
						align: 'center',
						width: 120
					},{
						field: 'create_time',
						title: '申请时间',
						align: 'center',
						width: 150
					},{
						field: 'open_name',
						title: '开票人',
						align: 'center',
						width: 90
					},{
						field: 'open_time',
						title: '开票时间',
						align: 'center',
						width: 100
					},{
						field: 'code',
						title: '发票号码',
						align: 'center',
						width: 120
					}, {
						field: 'right',
						fixed: 'right',
						title: '操作',
						width: 80,
						align: 'center',
						templet:function(d){
							//0待审、1审批中、2通过、3失败、4撤销、5已开具、10已作废
							var html='<span class="layui-btn layui-btn-normal layui-btn-xs" lay-event="view">详情</span>';
							return html;
						}
					}
				]
			]
		});
		//监听行工具事件
		table.on('tool(test)', function(obj) {
			var data = obj.data;
			if (obj.event === 'view') {
				tool.side("/finance/invoice/view?id="+data.id);
				return;
			}
		});
	}
</script>
{/block}
<!-- /脚本 -->
