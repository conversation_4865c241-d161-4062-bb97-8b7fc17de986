{extend name="../../base/view/common/base" /}

{block name="style"}
<style>
    /* 查看页面样式 - 参考balance/view.html的卡片布局 */
    .layui-card {
        border: 1px solid #e6e6e6;
        border-radius: 4px;
        margin-bottom: 15px;
    }

    .layui-card-header {
        background-color: #f8f8f8;
        padding: 10px 15px;
        border-bottom: 1px solid #e6e6e6;
        font-weight: bold;
    }

    .layui-card-body {
        padding: 15px;
    }

    /* 表格样式优化 */
    .layui-table {
        margin-bottom: 0;
    }

    .layui-table td {
        padding: 8px 12px;
        vertical-align: middle;
    }

    .layui-table th {
        background-color: #f8f8f8;
        font-weight: bold;
    }

    .layui-td-gray {
        background-color: #f5f5f5;
        font-weight: bold;
        text-align: left;
        vertical-align: middle;
        width: 140px;
        padding: 8px 12px;
    }

    /* 只读字段样式 */
    .readonly-value {
        background-color: #fff;
        color: #333;
        padding: 8px 12px;
        border: none;
        word-break: break-all;
    }

    /* 状态标签样式 */
    .status-enabled {
        color: #5FB878;
        font-weight: bold;
    }

    .status-disabled {
        color: #FF5722;
        font-weight: bold;
    }
</style>
{/block}

{block name="body"}
<div class="p-3">
    <!-- 基础信息卡片 -->
    <div class="layui-card">
        <div class="layui-card-header">基础信息</div>
        <div class="layui-card-body">
            <table class="layui-table" lay-skin="nob">
                <colgroup>
                    <col width="140">
                    <col width="200">
                    <col width="140">
                    <col>
                </colgroup>
                <tbody>
                    <tr>
                        <td class="layui-td-gray">门店</td>
                        <td class="readonly-value">{$detail.department_name|default=''}</td>
                        <td class="layui-td-gray">OA门店名称</td>
                        <td class="readonly-value">{$detail.oa_name|default=''}</td>
                    </tr>
                    <tr>
                        <td class="layui-td-gray">状态</td>
                        <td class="readonly-value">
                            {if condition="$detail.status == 1"}
                                <span class="status-enabled">启用</span>
                            {else/}
                                <span class="status-disabled">禁用</span>
                            {/if}
                        </td>
                        <td class="layui-td-gray">创建时间</td>
                        <td class="readonly-value">{$detail.create_time|date='Y-m-d H:i:s'}</td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>

    <!-- 对账相关信息卡片 -->
    <div class="layui-card">
        <div class="layui-card-header">对账相关信息</div>
        <div class="layui-card-body">
            <table class="layui-table" lay-skin="nob">
                <colgroup>
                    <col width="140">
                    <col width="200">
                    <col width="140">
                    <col>
                </colgroup>
                <tbody>
                    <tr>
                        <td class="layui-td-gray">对账表名称</td>
                        <td class="readonly-value">{$detail.reconciliation_table|default=''}</td>
                        <td class="layui-td-gray">对账表2名称</td>
                        <td class="readonly-value">{$detail.reconciliation_table2|default=''}</td>
                    </tr>
                    <tr>
                        <td class="layui-td-gray">跨店结算表名称</td>
                        <td class="readonly-value">{$detail.cross_store_settlement|default=''}</td>
                        <td class="layui-td-gray">门店分红表名称</td>
                        <td class="readonly-value">{$detail.store_dividend|default=''}</td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>

    <!-- 第三方平台信息卡片 -->
    <div class="layui-card">
        <div class="layui-card-header">第三方平台信息</div>
        <div class="layui-card-body">
            <table class="layui-table" lay-skin="nob">
                <colgroup>
                    <col width="140">
                    <col width="200">
                    <col width="140">
                    <col>
                </colgroup>
                <tbody>
                    <tr>
                        <td class="layui-td-gray">美团门店ID</td>
                        <td class="readonly-value">{$detail.meituan_id|default=''}</td>
                        <td class="layui-td-gray">美团门店名称</td>
                        <td class="readonly-value">{$detail.meituan_name|default=''}</td>
                    </tr>
                    <tr>
                        <td class="layui-td-gray">有赞门店名称</td>
                        <td class="readonly-value">{$detail.youzan_name|default=''}</td>
                        <td class="layui-td-gray">大众点评名称</td>
                        <td class="readonly-value">{$detail.dianping_name|default=''}</td>
                    </tr>
                    <tr>
                        <td class="layui-td-gray">抖音核销门店</td>
                        <td class="readonly-value">{$detail.douyin_verification|default=''}</td>
                        <td class="layui-td-gray">抖音广告名称</td>
                        <td class="readonly-value">{$detail.douyin_ad|default=''}</td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>

    <!-- 其他系统信息卡片 -->
    <div class="layui-card">
        <div class="layui-card-header">其他系统信息</div>
        <div class="layui-card-body">
            <table class="layui-table" lay-skin="nob">
                <colgroup>
                    <col width="140">
                    <col width="200">
                    <col width="140">
                    <col>
                </colgroup>
                <tbody>
                    <tr>
                        <td class="layui-td-gray">惠美云终端匹配</td>
                        <td class="readonly-value">{$detail.terminal_match|default=''}</td>
                        <td class="layui-td-gray">老板管账名称</td>
                        <td class="readonly-value">{$detail.boss_account|default=''}</td>
                    </tr>
                    <tr>
                        <td class="layui-td-gray">采购系统名称</td>
                        <td class="readonly-value">{$detail.purchase_system|default=''}</td>
                        <td class="layui-td-gray">数据框架名称</td>
                        <td class="readonly-value">{$detail.data_framework|default=''}</td>
                    </tr>
                    <tr>
                        <td class="layui-td-gray">访客收藏名称</td>
                        <td class="readonly-value" colspan="3">{$detail.visitor_collection|default=''}</td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</div>
{/block}

{block name="script"}
<script>
layui.use(['layer'], function(){
    var layer = layui.layer;
    
    // 页面加载完成后的处理
    $(document).ready(function(){
        // 可以在这里添加一些页面初始化逻辑
    });
});
</script>
{/block}