{extend name="../../base/view/common/base" /}

{block name="body"}
<div class="p-4">
    <h3 class="pb-3">{$store_name} - 风险金变化记录</h3>

    <!-- 风险金调整表单 -->
    <div class="layui-card" style="margin-bottom: 20px;">
        <div class="layui-card-header">
            <span class="layui-icon layui-icon-edit"></span> 风险金调整
        </div>
        <div class="layui-card-body">
            <form class="layui-form" lay-filter="adjustForm" id="adjustForm">
                <input type="hidden" name="store_id" value="{$store_id}">
                <div class="layui-row layui-col-space15">
                    <div class="layui-col-md3">
                        <label class="layui-form-label">调整类型<font style="color: red;">*</font></label>
                        <div class="layui-input-block">
                            <input type="radio" name="adjust_type" value="increase" title="增加" checked>
                            <input type="radio" name="adjust_type" value="decrease" title="减少">
                        </div>
                    </div>
                    <div class="layui-col-md3">
                        <label class="layui-form-label">调整金额<font style="color: red;">*</font></label>
                        <div class="layui-input-block">
                            <input type="number" name="adjust_amount" lay-verify="required|number"
                                   placeholder="请输入调整金额" class="layui-input" step="0.01" min="0.01"
                                   oninput="limitDecimalPlaces(this, 2)">
                        </div>
                    </div>
                    <div class="layui-col-md4">
                        <label class="layui-form-label">备注</label>
                        <div class="layui-input-block">
                            <input type="text" name="remark" placeholder="请输入备注"
                                   class="layui-input" maxlength="200">
                        </div>
                    </div>
                    <div class="layui-col-md2">
                        <div class="layui-input-block" style="margin-left: 0;">
                            <button type="submit" class="layui-btn" lay-submit lay-filter="adjustSubmit">
                                <i class="layui-icon layui-icon-ok"></i> 确认调整
                            </button>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- 数据表格 -->
    <div style="margin-top: 20px;">
        <table class="layui-hide" id="dataTable" lay-filter="dataTable"></table>
    </div>
</div>

<script type="text/html" id="changeTypeTpl">
    {{# if(d.change_type == 1) { }}
        <span class="layui-badge layui-bg-blue">计提</span>
    {{# } else if(d.change_type == 2) { }}
        <span class="layui-badge layui-bg-orange">调整</span>
    {{# } else if(d.change_type == 3) { }}
        <span class="layui-badge layui-bg-green">初始化</span>
    {{# } else { }}
        <span class="layui-badge layui-bg-gray">未知</span>
    {{# } }}
</script>

<script type="text/html" id="changeAmountTpl">
    {{# if(d.change_amount >= 0) { }}
        <span style="color: #5FB878;">{{d.change_amount_formatted}}</span>
    {{# } else { }}
        <span style="color: #FF5722;">{{d.change_amount_formatted}}</span>
    {{# } }}
</script>

<script type="text/html" id="adminNameTpl">
    {{# if(d.admin_id == 0 || !d.admin_name) { }}
        <span style="color: #999;">系统自动</span>
    {{# } else { }}
        {{d.admin_name}}
    {{# } }}
</script>

{/block}

{block name="script"}
<script>
    // 限制输入框小数位数的函数
    function limitDecimalPlaces(input, maxDecimalPlaces) {
        var value = input.value;
        if (value.indexOf('.') !== -1) {
            var parts = value.split('.');
            if (parts[1] && parts[1].length > maxDecimalPlaces) {
                input.value = parts[0] + '.' + parts[1].substring(0, maxDecimalPlaces);
            }
        }
    }

    const moduleInit = ['tool', 'tablePlus'];
    function gouguInit() {
        var table = layui.tablePlus, form = layui.form, tool = layui.tool;
        var layer = layui.layer;
        var $ = layui.$;

        // 初始化数据表格
        var tableIns = table.render({
            elem: '#dataTable',
            url: '/dividend/index/storeRiskReserveLog',
            method: 'post',
            where: {
                store_id: '{$store_id}'
            },
            page: false, // 不分页，显示所有数据
            height: 'full-250',
            loading: true,
            even: true,
            toolbar: true,
            cols: [[
                {field: 'create_time_formatted', title: '创建时间', width: 160, align: 'center', sort: true},
                {field: 'change_type', title: '变化类型', width: 100, align: 'center', templet: '#changeTypeTpl'},
                {field: 'before_amount_formatted', title: '变化前金额', width: 120, align: 'right'},
                {field: 'change_amount', title: '变化金额', width: 120, align: 'right', templet: '#changeAmountTpl'},
                {field: 'after_amount_formatted', title: '变化后金额', width: 120, align: 'right'},
                {field: 'admin_name', title: '操作人', width: 100, align: 'center', templet: '#adminNameTpl'},
                {field: 'remark', title: '备注', minWidth: 200}
            ]],
            done: function(res, curr, count){
                if (res.code !== 0) {
                    console.error('表格数据加载失败:', res.msg);
                }
            }
        });

        // 监听调整表单提交
        form.on('submit(adjustSubmit)', function(data) {
            var field = data.field;

            // 验证调整金额
            if (!field.adjust_amount || parseFloat(field.adjust_amount) <= 0) {
                layer.msg('请输入有效的调整金额', {icon: 2});
                return false;
            }

            // 验证调整金额最多两位小数
            var amountStr = field.adjust_amount.toString();
            if (amountStr.indexOf('.') !== -1) {
                var decimalPart = amountStr.split('.')[1];
                if (decimalPart && decimalPart.length > 2) {
                    layer.msg('调整金额最多支持两位小数', {icon: 2});
                    return false;
                }
            }

            // 确认调整操作
            var adjustTypeText = field.adjust_type === 'increase' ? '增加' : '减少';
            var confirmMsg = '确认' + adjustTypeText + '风险金 ' + field.adjust_amount + ' 元吗？';
            if (field.remark) {
                confirmMsg += '<br>备注：' + field.remark;
            }

            layer.confirm(confirmMsg, {
                icon: 3,
                title: '确认调整',
                btn: ['确认调整', '取消']
            }, function(index) {
                // 显示加载层
                var loadIndex = layer.load(2, {shade: [0.3, '#000']});

                // 提交调整请求
                $.ajax({
                    url: '/dividend/index/adjustRiskReserve',
                    type: 'POST',
                    data: field,
                    dataType: 'json',
                    success: function(res) {
                        layer.close(loadIndex);
                        layer.msg(res.msg, {icon: res.code == 0 ? 1 : 2});
                        if (res.code == 0) {
                            // 重置表单
                            document.getElementById('adjustForm').reset();
                            form.render();
                            // 刷新表格
                            tableIns.reload();
                        }
                    },
                    error: function(xhr, status, error) {
                        layer.close(loadIndex);
                        layer.msg('请求失败，请重试', {icon: 2});
                        console.error('Ajax请求失败:', error);
                    }
                });
                layer.close(index);
            });

            return false; // 阻止表单跳转
        });
    }
</script>
{/block}