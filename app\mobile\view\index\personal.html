{extend name="../../base/view/common/base" /}
{block name="style"}
<link rel="stylesheet" href="{__GOUGU__}/gougu/css/layout.css" media="all">
{/block}
<!-- 主体 -->
{block name="body"}
<style>

    body {
        background-color: #f5f6f7;
    }

    .uni-page {
        display: block;
        width: 100%;
        height: 100%;
    }

    .uni-page-head-main {
        display: block;
        box-sizing: border-box;
    }

    .uni-page-head .uni-page-head-hd {
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-align: center;
        -webkit-align-items: center;
        align-items: center;
        font-size: 16px;
    }

    .uni-page-head-main .uni-page-head {
        position: fixed;
        left: var(--window-left);
        right: var(--window-right);
        height: 44px;
        height: calc(44px + constant(safe-area-inset-top));
        height: calc(44px + env(safe-area-inset-top));
        padding: 7px 3px;
        padding-top: calc(7px + constant(safe-area-inset-top));
        padding-top: calc(7px + env(safe-area-inset-top));
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        overflow: hidden;
        -webkit-box-pack: justify;
        -webkit-justify-content: space-between;
        justify-content: space-between;
        box-sizing: border-box;
        z-index: 998;
        color: #fff;
        background-color: #000;
        -webkit-transition-property: all;
        transition-property: all;
        width: 100%;
    }

    .uni-page-head .uni-page-head-bd {
        position: absolute;
        left: 70px;
        right: 70px;
        min-width: 0;
    }

    .uni-page-head .uni-page-head__title {
        font-weight: 700;
        font-size: 16px;
        line-height: 30px;
        text-align: center;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
    }

    .uni-page-wrapper {
        height: calc(100% - 44px - env(safe-area-inset-top) - 50px - env(safe-area-inset-bottom));
    }

    .uni-page-body {
        background-color: #f5f6f7;
        display: block;
        box-sizing: border-box;
        width: 100%;
    }

    .mine-container .header-section[data-v-1716ef58] {
        padding: 15px 15px 45px 15px;
        background-color: #af1c22!important;
        color: #fff;
    }

    .uni-view {
        display: block;
        box-sizing: border-box;
    }

    .padding {
        padding: 15px;
    }

    .justify-between {
        justify-content: space-between;
    }

    .flex {
        display: flex;
    }

    .uni-page-head-main .uni-page-head ~ .uni-placeholder {
        width: 100%;
        height: 44px;
        height: calc(44px + constant(safe-area-inset-top));
        height: calc(44px + env(safe-area-inset-top));
    }

    .align-center {
        align-items: center;
    }

    .cu-avatar.xl {
        width: 64px;
        height: 64px;
        font-size: 2.5em;
    }

    .cu-avatar {
        font-variant: small-caps;
        margin: 0;
        padding: 0;
        display: inline-flex;
        text-align: center;
        justify-content: center;
        align-items: center;
        background-color: #fff;
        color: #fff;
        white-space: nowrap;
        position: relative;
        width: 32px;
        height: 32px;
        background-size: cover;
        background-position: 50%;
        vertical-align: middle;
        font-size: 1.5em;
    }

    .round {
        border-radius: 2500px;
    }

    .uni-image {
        max-width: 100%;
        display: inline-block;
        position: relative;
        z-index: 0;
    }

    .mine-container .header-section .cu-avatar[data-v-1716ef58] {
        border: 2px solid #eaeaea;
    }

    .uni-image > div, .uni-image > img {
        width: 100%;
        height: 100%;
    }

    .uni-image > img {
        -webkit-touch-callout: none;
        -webkit-user-select: none;
        -moz-user-select: none;
        display: block;
        position: absolute;
        top: 0;
        left: 0;
        opacity: 0;
    }

    .uni-resize-sensor {
        display: block;
        z-index: -1;
        visibility: hidden;
        -webkit-animation: once-show 1ms;
        animation: once-show 1ms;
    }

    .uni-resize-sensor, .uni-resize-sensor > div {
        position: absolute;
        left: 0;
        top: 0;
        right: 0;
        bottom: 0;
        overflow: hidden;
    }

    .mine-container .header-section .user-info[data-v-1716ef58] {
        margin-left: 15px;
    }

    .mine-container .header-section .user-info .u_title[data-v-1716ef58] {
        font-size: 18px;
        line-height: 30px;
    }

    .iconfont {
        font-family: iconfont !important;
        font-size: 16px;
        display: inline-block;
        font-style: normal;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
    }

    .icon-right:before {
        content: "\e7eb";
    }

    .menu-list {
        margin: 15px 15px;
    }

    .list-cell:first-child {
        border-radius: 4px 4px 0 0;
    }

    .list-cell {
        position: relative;
        width: 100%;
        box-sizing: border-box;
        background-color: #fff;
        color: #333;
        padding: 13px 15px;
    }

    .menu-list .menu-item-box {
        width: 100%;
        display: flex;
        align-items: center;
    }

    .menu-list .menu-item-box .menu-icon {
        color: #007aff;
        font-size: 16px;
        margin-right: 5px;
    }

    .mine-container .content-section[data-v-1716ef58] {
        position: relative;
        top: -50px;
    }

    .list-cell-arrow .right {
        color: silver;
        height: 10px;
        width: 10px;
        position: absolute;
        top: 50%;
        margin-top: -6px;
        right: 15px;
    }

</style>
<div class="layui-layout-body">
    <div id="GouguApp">
        <div class="layui-layout gg-layout layout-menu-{$web.menu_mode|default='classical'}">

            <div class="uni-page" data-page="pages/mine/index">
                <div class="uni-page-wrapper">
                    <div class="uni-page-body">
                        <div class="uni-view mine-container" data-v-1716ef58="" style="height: 523px;">
                            <div data-v-1716ef58="" class="header-section uni-view ">
                                <div data-v-1716ef58="" class="flex padding justify-between uni-view">
                                    <div data-v-1716ef58="" class="flex align-center uni-view"><!---->
                                        <div data-v-1716ef58="" class="cu-avatar xl round uni-image"
                                             style="height: 64px;">
                                            <div style="background-image: url({$login_admin.thumb}); background-size: 100% 100%; background-repeat: no-repeat;"></div>
                                            <div class="uni-resize-sensor">
                                                <div>
                                                    <div></div>
                                                </div>
                                                <div>
                                                    <div></div>
                                                </div>
                                            </div>
                                            <img src="{$login_admin.thumb}" draggable="false"/></div>
                                        <!---->
                                        <div data-v-1716ef58="" class="user-info">
                                            <div data-v-1716ef58="" class="u_title">用户名：{$login_admin.name}</div>
                                        </div>
                                    </div>
                                    <div data-v-1716ef58="" class="flex align-center">
                                        <div class="uni-text" data-v-1716ef58=""><span>个人信息</span></div>
                                        <i class="layui-icon layui-icon-right menu-icon uni-view" ></i>
                                    </div>
                                </div>
                            </div>

                            <div data-v-1716ef58="" class="content-section uni-view">
                                <div data-v-1716ef58="" class="menu-list uni-view">
                                    <div data-v-1716ef58="" class="list-cell list-cell-arrow uni-view">
                                        <i class="layui-icon layui-icon-right menu-icon uni-view right" ></i>
                                        <div data-v-1716ef58="" class="menu-item-box uni-view">
                                            <i class="layui-icon layui-icon-username menu-icon uni-view" ></i>
                                            <div class="uni-view" data-v-1716ef58="">编辑资料</div>
                                        </div>
                                    </div>
                                    <div data-v-1716ef58="" class="list-cell list-cell-arrow uni-view">
                                        <i class="layui-icon layui-icon-right menu-icon uni-view right" ></i>
                                        <div data-v-1716ef58="" class="menu-item-box uni-view">
                                            <i class="layui-icon layui-icon-question menu-icon uni-view" ></i>
                                            <div class="uni-view" data-v-1716ef58="">常见问题</div>
                                        </div>
                                    </div>
                                    <div data-v-1716ef58="" class="list-cell list-cell-arrow uni-view">
                                        <i class="layui-icon layui-icon-right menu-icon uni-view right" ></i>
                                        <div data-v-1716ef58="" class="menu-item-box uni-view">
                                            <i class="layui-icon layui-icon-heart menu-icon uni-view" ></i>
                                            <div class="uni-view" data-v-1716ef58="">关于我们</div>
                                        </div>
                                    </div>
                                    <div data-v-1716ef58="" class="list-cell list-cell-arrow uni-view">
                                        <i class="layui-icon layui-icon-right menu-icon uni-view right" ></i>
                                        <div data-v-1716ef58="" class="menu-item-box uni-view">
                                            <i class="layui-icon layui-icon-set menu-icon uni-view" ></i>
                                            <div class="uni-view" data-v-1716ef58="">应用设置</div>
                                        </div>
                                    </div>

                                    <div data-v-1716ef58="" class="list-cell list-cell-arrow uni-view">
                                        <button id="logout" type="button" class="layui-btn layui-bg-red" style="width: 100%;background-color: #af1c22!important;height: 45px;">退出登录</button>
                                    </div>


                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            {include file="../../mobile/view/index/tabbar" /}


        </div>
    </div>
</div>


<!-- /主体 -->
{/block}
<!-- 脚本 -->
{block name="script"}
<script src="{__GOUGU__}/daohang/vendor/jquery/jquery.min.js"></script>
<script>

    $('#GouguApp').on("click",'#logout',function () {
        layer.confirm('确认注销登录吗?', { icon: 7, title: '警告' }, function (index) {
            //注销
            $.ajax({
                url: "/home/<USER>/login_out",
                success: function (e) {
                    layer.msg(e.msg);
                    if (e.code == 0) {
                        setTimeout(function () {
                            location.href = "{:url('home/login/index?m=mobile')}"
                        }, 1000)
                    }
                }
            })
            layer.close(index);
        });
    });

</script>
{/block}
<!-- /脚本 -->