<?php

namespace app\contract\service;

use think\Db;

class StoreachievementService
{

    public function indexType()
    {
        Db::query("select
            sum(a.level_4) as level_4,
            sum(a.level_3) as level_3,
            sum(a.level_2) as level_2,
            sum(a.level_1) as level_1,
            a.did
            from
            (
            SELECT 
            case when `level` = 4  then 1 else 0 end level_4,
            case when `level` = 3  then 1 else 0 end level_3,
            case when `level` = 2  then 1 else 0 end level_2,
            case when `level` = 1  then 1 else 0 end level_1,
            did
            FROM `oa_store_business_t`
            where 
            sdate = '2025-01' 
            and did = 40
            ) a ");
    }



}