<?php
/**
 * @copyright Copyright (c) 2021 仲正堂工作室
 * @license https://opensource.org/licenses/GPL-3.0
 * @link https://www.gougucms.com
 */

namespace app\cross\model;

use think\Model;
use think\facade\Db;

class StoreNameMapping extends Model
{
    protected $name = 'store_name_mapping';

    // 设置字段信息
    protected $schema = [
        'id'                    => 'int',
        'department_id'         => 'int',
        'oa_name'              => 'string',
        'reconciliation_table'  => 'string',
        'terminal_match'        => 'string',
        'boss_account'          => 'string',
        'douyin_verification'   => 'string',
        'cross_store_settlement' => 'string',
        'reconciliation_table2' => 'string',
        'store_dividend'        => 'string',
        'data_framework'        => 'string',
        'meituan_id'           => 'string',
        'meituan_name'         => 'string',
        'youzan_name'          => 'string',
        'purchase_system'       => 'string',
        'dianping_name'        => 'string',
        'douyin_ad'            => 'string',
        'visitor_collection'    => 'string',
        'create_time'          => 'int',
        'update_time'          => 'int',
        'status'               => 'int',
    ];

    // 自动时间戳
    protected $autoWriteTimestamp = true;
    protected $createTime = 'create_time';
    protected $updateTime = 'update_time';

    /**
     * 关联门店信息
     */
    public function department()
    {
        return $this->belongsTo('app\base\model\Department', 'department_id', 'id');
    }

    /**
     * 获取列表数据
     * @param array $where 查询条件
     * @return array
     */
    public static function getList($where = [])
    {
        try {
            $list = self::alias('snm')
                ->leftJoin('department d', 'snm.department_id = d.id')
                ->field('snm.*, d.title as department_name')
                ->where($where)
                ->order('snm.id desc')
                ->select()
                ->toArray();

            $count = count($list);

            return [
                'data' => $list,
                'count' => $count,
                'total' => $count,
                'per_page' => $count,
                'current_page' => 1,
                'last_page' => 1
            ];
        } catch (\Exception $e) {
            return [
                'data' => [],
                'count' => 0,
                'total' => 0,
                'per_page' => 0,
                'current_page' => 1,
                'last_page' => 1
            ];
        }
    }

    /**
     * 获取详情数据
     * @param int $id
     * @return array
     */
    public static function getDetail($id)
    {
        try {
            $detail = self::alias('snm')
                ->leftJoin('department d', 'snm.department_id = d.id')
                ->field('snm.*, d.title as department_name')
                ->where('snm.id', $id)
                ->find();

            return $detail ? $detail->toArray() : [];
        } catch (\Exception $e) {
            return [];
        }
    }

    /**
     * 检查门店是否已存在映射
     * @param int $departmentId 门店ID
     * @param int $excludeId 排除的记录ID（编辑时使用）
     * @return bool
     */
    public static function checkDepartmentExists($departmentId, $excludeId = 0)
    {
        $where = [['department_id', '=', $departmentId]];
        if ($excludeId > 0) {
            $where[] = ['id', '<>', $excludeId];
        }
        
        return self::where($where)->count() > 0;
    }

    /**
     * 获取状态选项
     * @return array
     */
    public static function getStatusOptions()
    {
        return [
            1 => '启用',
            0 => '禁用'
        ];
    }

    /**
     * 获取状态文本
     * @param int $status
     * @return string
     */
    public static function getStatusText($status)
    {
        $options = self::getStatusOptions();
        return $options[$status] ?? '未知';
    }

    /**
     * 获取门店列表（用于下拉选择）
     * @return array
     */
    public static function getDepartmentList()
    {
        try {
            $list = Db::name('department')
                ->field('id, title')
                ->where('remark', '门店')
                ->where('status', 1)
                ->order('id asc')
                ->select()
                ->toArray();

            return $list;
        } catch (\Exception $e) {
            return [];
        }
    }

    /**
     * 获取未配置映射的门店列表（用于新增时的下拉选择）
     * @return array
     */
    public static function getAvailableDepartmentList()
    {
        try {
            // 获取已配置映射的门店ID列表
            $mappedDepartmentIds = self::column('department_id');

            // 获取所有门店，排除已配置映射的门店
            $where = [
                ['remark', '=', '门店'],
                ['status', '=', 1]
            ];
            
            if (!empty($mappedDepartmentIds)) {
                $where[] = ['id', 'not in', $mappedDepartmentIds];
            }

            $list = Db::name('department')
                ->field('id, title')
                ->where($where)
                ->order('id asc')
                ->select()
                ->toArray();

            return $list;
        } catch (\Exception $e) {
            return [];
        }
    }
}