<div class="layui-row layui-col-space16">
	<div class="layui-col-md12">
		<div class="layui-card">
			<div class="layui-card-title">知识列表</div>
			<div style="padding: 12px;">
				<table id="Article" lay-filter="Article" class="layui-hide" style="margin-top:0"></table>
			</div>
		</div>
	</div>
</div>
<script>
//公告
function layoutArticle(table){
	//文章知识
	table.render({
		elem: '#Article'
		, url: "/home/<USER>/get_article_list" //数据接口
		, page: false //开启分页
		, cols: [[ //表头
			{ field: 'cate_title', title: '知识分类', align: 'center','width': 90 },
			{ field: 'title', title: '知识标题',templet: '<div><a data-href="/article/index/view/id/{{d.id}}.html" class="side-a">{{d.title}}</a></div>'},
			{ field: 'create_time', title: '发布时间', align: 'center','width': 150}
		]]
	});
}
</script>