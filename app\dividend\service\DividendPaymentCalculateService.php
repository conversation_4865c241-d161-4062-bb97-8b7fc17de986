<?php
/**
 * @copyright Copyright (c) 2021 仲正堂工作室
 * @license https://opensource.org/licenses/GPL-3.0
 * @link https://www.gougucms.com
 */

namespace app\dividend\service;

use think\facade\Db;
use app\dividend\model\DividendPayment;
use app\dividend\model\DividendStoreDetailPerson;
use app\dividend\model\DividendCompanyDetailPerson;

class DividendPaymentCalculateService
{
    /**
     * 计算指定周期的分红人清单数据
     * @param string $period 统计周期（如：2025-04）
     * @param int $adminId 操作人ID（默认为0表示系统自动）
     * @return array
     */
    public static function calculatePaymentData($period, $adminId = 0)
    {
        $result = [
            'success' => true,
            'message' => '',
            'data' => []
        ];

        try {
            // 开启事务
            Db::startTrans();

            $calculatedData = [];

            // 1. 从门店分红人子表获取个人股东数据
            $personalShareholderData = self::getPersonalShareholderData($period);

            // 2. 从公司股东分红人子表获取公司股东数据
            $companyShareholderData = self::getCompanyShareholderData($period);

            // 3. 合并数据并按分红人姓名分组
            $shareholderGroups = self::groupShareholderData($personalShareholderData, $companyShareholderData);

            // 4. 为每个分红人计算并保存数据
            foreach ($shareholderGroups as $shareholderName => $shareholderInfo) {
                $paymentData = [
                    'shareholder_name' => $shareholderName,
                    'shareholder_ids' => implode(',', $shareholderInfo['shareholder_ids']),
                    'period' => $period,
                    'payable_amount' => $shareholderInfo['total_amount'],
                    'adjustment_amount' => 0.00, // 默认为0
                    'actual_payable_amount' => $shareholderInfo['total_amount'], // 默认等于应付金额
                    'paid_amount' => 0.00, // 默认为0
                    'unpaid_amount' => $shareholderInfo['total_amount'], // 默认等于实际应付金额
                    'remark' => '' // 默认为空
                ];

                // 保存或更新数据
                $model = new DividendPayment();
                if (!$model->savePaymentData($paymentData)) {
                    throw new \Exception("保存分红人 {$shareholderName} 的数据失败");
                }

                $calculatedData[] = [
                    'shareholder_name' => $shareholderName,
                    'period' => $period,
                    'payable_amount' => $shareholderInfo['total_amount'],
                    'store_count' => count($shareholderInfo['store_ids']),
                    'shareholder_count' => count($shareholderInfo['shareholder_ids'])
                ];
            }

            // 提交事务
            Db::commit();

            $result['data'] = $calculatedData;
            $result['message'] = '分红人清单数据计算完成，共处理 ' . count($calculatedData) . ' 个分红人';

        } catch (\Exception $e) {
            Db::rollback();
            $result['success'] = false;
            $result['message'] = '计算失败：' . $e->getMessage();
        }

        return $result;
    }

    /**
     * 从门店分红人子表获取个人股东数据
     * @param string $period 统计周期
     * @return array
     */
    private static function getPersonalShareholderData($period)
    {
        // 获取个人股东数据（shareholder_type = 2）
        $personalData = DividendStoreDetailPerson::alias('dsdp')
            ->join('oa_dividend_store_detail dsd', 'dsd.id = dsdp.dividend_store_detail_id', 'INNER')
            ->where([
                'dsd.period' => $period,
                'dsdp.shareholder_type' => 2, // 个人股东
                'dsd.is_delete' => 0,
                'dsdp.is_delete' => 0
            ])
            ->field([
                'dsdp.shareholder_id',
                'dsdp.shareholder_name',
                'dsdp.actual_payable_amount',
                'dsd.store_id'
            ])
            ->select()
            ->toArray();

        return $personalData;
    }

    /**
     * 从公司股东分红人子表获取公司股东数据
     * @param string $period 统计周期
     * @return array
     */
    private static function getCompanyShareholderData($period)
    {
        // 获取公司股东数据
        $companyData = DividendCompanyDetailPerson::alias('dcdp')
            ->join('oa_dividend_company_detail dcd', 'dcd.id = dcdp.company_detail_id', 'INNER')
            ->where([
                'dcd.period' => $period,
                'dcd.is_delete' => 0,
                'dcdp.is_delete' => 0
            ])
            ->field([
                'dcdp.shareholder_id',
                'dcdp.shareholder_name',
                'dcdp.payable_amount',
                'dcd.store_id'
            ])
            ->select()
            ->toArray();

        return $companyData;
    }

    /**
     * 合并数据并按分红人姓名分组
     * @param array $personalData 个人股东数据
     * @param array $companyData 公司股东数据
     * @return array
     */
    private static function groupShareholderData($personalData, $companyData)
    {
        $shareholderGroups = [];

        // 处理个人股东数据
        foreach ($personalData as $item) {
            $name = $item['shareholder_name'];
            if (!isset($shareholderGroups[$name])) {
                $shareholderGroups[$name] = [
                    'total_amount' => 0,
                    'shareholder_ids' => [],
                    'store_ids' => []
                ];
            }

            $shareholderGroups[$name]['total_amount'] += floatval($item['actual_payable_amount']);
            
            // 记录股东ID（避免重复）
            if (!in_array($item['shareholder_id'], $shareholderGroups[$name]['shareholder_ids'])) {
                $shareholderGroups[$name]['shareholder_ids'][] = $item['shareholder_id'];
            }
            
            // 记录门店ID（避免重复）
            if (!in_array($item['store_id'], $shareholderGroups[$name]['store_ids'])) {
                $shareholderGroups[$name]['store_ids'][] = $item['store_id'];
            }
        }

        // 处理公司股东数据
        foreach ($companyData as $item) {
            $name = $item['shareholder_name'];
            if (!isset($shareholderGroups[$name])) {
                $shareholderGroups[$name] = [
                    'total_amount' => 0,
                    'shareholder_ids' => [],
                    'store_ids' => []
                ];
            }

            $shareholderGroups[$name]['total_amount'] += floatval($item['payable_amount']);
            
            // 记录股东ID（避免重复）
            if (!in_array($item['shareholder_id'], $shareholderGroups[$name]['shareholder_ids'])) {
                $shareholderGroups[$name]['shareholder_ids'][] = $item['shareholder_id'];
            }
            
            // 记录门店ID（避免重复）
            if (!in_array($item['store_id'], $shareholderGroups[$name]['store_ids'])) {
                $shareholderGroups[$name]['store_ids'][] = $item['store_id'];
            }
        }

        return $shareholderGroups;
    }

    /**
     * 获取计算统计信息
     * @param string $period 统计周期
     * @return array
     */
    public static function getCalculateStatistics($period)
    {
        // 统计该周期的分红清单数据
        $statistics = DividendPayment::where('period', $period)
            ->where('is_delete', 0)
            ->field([
                'COUNT(id) as shareholder_count',
                'SUM(payable_amount) as total_payable_amount',
                'SUM(adjustment_amount) as total_adjustment_amount',
                'SUM(actual_payable_amount) as total_actual_payable_amount',
                'SUM(paid_amount) as total_paid_amount',
                'SUM(unpaid_amount) as total_unpaid_amount'
            ])
            ->find();

        if (!$statistics) {
            return [
                'shareholder_count' => 0,
                'total_payable_amount' => 0,
                'total_adjustment_amount' => 0,
                'total_actual_payable_amount' => 0,
                'total_paid_amount' => 0,
                'total_unpaid_amount' => 0
            ];
        }

        return $statistics->toArray();
    }

    /**
     * 批量计算多个周期的分红人清单数据
     * @param array $periods 统计周期数组
     * @param int $adminId 操作人ID
     * @return array
     */
    public static function batchCalculatePaymentData($periods, $adminId = 0)
    {
        $result = [
            'success' => true,
            'message' => '',
            'data' => []
        ];

        $successCount = 0;
        $failedPeriods = [];

        foreach ($periods as $period) {
            $periodResult = self::calculatePaymentData($period, $adminId);

            if ($periodResult['success']) {
                $successCount++;
                $result['data'][$period] = $periodResult['data'];
            } else {
                $failedPeriods[] = [
                    'period' => $period,
                    'error' => $periodResult['message']
                ];
            }
        }

        if (empty($failedPeriods)) {
            $result['message'] = "批量计算完成，成功处理 {$successCount} 个周期";
        } else {
            $result['success'] = false;
            $result['message'] = "批量计算完成，成功 {$successCount} 个，失败 " . count($failedPeriods) . " 个";
            $result['failed_periods'] = $failedPeriods;
        }

        return $result;
    }

    /**
     * 重新计算指定分红人的数据
     * @param string $shareholderName 分红人姓名
     * @param string $period 统计周期
     * @return array
     */
    public static function recalculateShareholderData($shareholderName, $period)
    {
        $result = [
            'success' => true,
            'message' => '',
            'data' => []
        ];

        try {
            // 开启事务
            Db::startTrans();

            // 1. 获取该分红人的个人股东数据
            $personalData = DividendStoreDetailPerson::alias('dsdp')
                ->join('oa_dividend_store_detail dsd', 'dsd.id = dsdp.dividend_store_detail_id', 'INNER')
                ->where([
                    'dsd.period' => $period,
                    'dsdp.shareholder_name' => $shareholderName,
                    'dsdp.shareholder_type' => 2, // 个人股东
                    'dsd.is_delete' => 0,
                    'dsdp.is_delete' => 0
                ])
                ->field([
                    'dsdp.shareholder_id',
                    'dsdp.shareholder_name',
                    'dsdp.actual_payable_amount',
                    'dsd.store_id'
                ])
                ->select()
                ->toArray();

            // 2. 获取该分红人的公司股东数据
            $companyData = DividendCompanyDetailPerson::alias('dcdp')
                ->join('oa_dividend_company_detail dcd', 'dcd.id = dcdp.company_detail_id', 'INNER')
                ->where([
                    'dcd.period' => $period,
                    'dcdp.shareholder_name' => $shareholderName,
                    'dcd.is_delete' => 0,
                    'dcdp.is_delete' => 0
                ])
                ->field([
                    'dcdp.shareholder_id',
                    'dcdp.shareholder_name',
                    'dcdp.payable_amount',
                    'dcd.store_id'
                ])
                ->select()
                ->toArray();

            // 3. 合并数据并计算总金额
            $shareholderGroups = self::groupShareholderData($personalData, $companyData);

            if (empty($shareholderGroups[$shareholderName])) {
                throw new \Exception("未找到分红人 {$shareholderName} 在周期 {$period} 的数据");
            }

            $shareholderInfo = $shareholderGroups[$shareholderName];

            // 4. 获取现有的调整金额和实付金额（保留用户手动输入的数据）
            $existingData = DividendPayment::where([
                'shareholder_name' => $shareholderName,
                'period' => $period,
                'is_delete' => 0
            ])->find();

            $adjustmentAmount = 0;
            $paidAmount = 0;
            if ($existingData) {
                $adjustmentAmount = $existingData['adjustment_amount'];
                $paidAmount = $existingData['paid_amount'];
            }

            // 5. 重新计算数据
            $paymentData = [
                'shareholder_name' => $shareholderName,
                'shareholder_ids' => implode(',', $shareholderInfo['shareholder_ids']),
                'period' => $period,
                'payable_amount' => $shareholderInfo['total_amount'],
                'adjustment_amount' => $adjustmentAmount, // 保留原有调整金额
                'actual_payable_amount' => $shareholderInfo['total_amount'] - $adjustmentAmount,
                'paid_amount' => $paidAmount, // 保留原有实付金额
                'unpaid_amount' => ($shareholderInfo['total_amount'] - $adjustmentAmount) - $paidAmount,
                'remark' => $existingData['remark'] ?? '' // 保留原有备注
            ];

            // 6. 保存数据
            $model = new DividendPayment();
            if (!$model->savePaymentData($paymentData)) {
                throw new \Exception("保存分红人 {$shareholderName} 的数据失败");
            }

            // 提交事务
            Db::commit();

            $result['data'] = $paymentData;
            $result['message'] = "分红人 {$shareholderName} 在周期 {$period} 的数据重新计算完成";

        } catch (\Exception $e) {
            Db::rollback();
            $result['success'] = false;
            $result['message'] = '重新计算失败：' . $e->getMessage();
        }

        return $result;
    }
}
