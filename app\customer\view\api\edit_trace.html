{extend name="../../base/view/common/base" /}
<!-- 主体 -->
{block name="body"}
<form class="layui-form p-4" lay-filter="demo-val-filter">
	<h3 class="pb-3">编辑跟进记录</h3>
    <table class="layui-table layui-table-form">
      <tr>
        <td class="layui-td-gray">客户名称</td>
        <td colspan="3">{$detail.customer}</td>
        <td class="layui-td-gray">联 系 人<font>*</font></td>
        <td>
          <select name="contact_id" lay-verify="required" lay-reqText="请选择联系人">
            <option value="">请选择</option>
            {volist name=":customer_contact($detail.cid)" id="v"}
            <option value="{$v.id}" {eq name="$v.id" value="$detail.contact_id"} selected{/eq}>{$v.name}</option>
            {/volist}
          </select>
        </td>
      </tr>
      <tr>
	    <td class="layui-td-gray">跟进方式<font>*</font></td>
        <td>
          <select name="type" lay-verify="required" lay-reqText="请选择跟进方式">
            <option value="">请选择</option>
            {volist name=":trace_type()" id="v"}
			<option value="{$key}" {eq name="$key" value="$detail.type"} selected{/eq}>{$v}</option>
            {/volist}
          </select>
        </td>
        <td class="layui-td-gray">跟进时间<font>*</font></td>
        <td>
          <input type="text" id="follow_time" name="follow_time" readonly value="{$detail.follow_time}" autocomplete="off" lay-verify="required" lay-reqText="请选择跟进时间" placeholder="请选择跟进时间" class="layui-input">
        </td>
        <td class="layui-td-gray-2">下次沟通时间<font>*</font></td>
        <td>
          <input type="text" id="next_time" name="next_time" readonly value="{$detail.next_time}" autocomplete="off" lay-verify="required" lay-reqText="请选择下次沟通时间" placeholder="请选择下次沟通时间" class="layui-input">
        </td>
	  </tr>
	  <tr>
		<td class="layui-td-gray" style="vertical-align:top">沟通内容<font>*</font></td>
        <td colspan="5">
          <textarea name="content" placeholder="请输入沟通内容" lay-verify="required" lay-reqText="请输入沟通内容" class="layui-textarea">{$detail.content}</textarea>
        </td>
      </tr>
      <tr>
	    <td class="layui-td-gray">销售机会</td>
        <td colspan="3">
          <select name="chance_id" lay-filter="chance">
            <option value="" title="0">请选择</option>
            {volist name=":customer_chance($detail.cid)" id="v"}
            <option value="{$v.id}" title="{$v.id}" {eq name="$v.id" value="$detail.chance_id"} selected{/eq}>{$v.title}</option>
            {/volist}
          </select>
        </td>
        <td class="layui-td-gray">当前阶段</td>
        <td>
          <select name="stage" id="stage" {eq name='$detail.stage' value='0'}disabled="true"{/eq}>
            <option value="">请选择</option>
            {volist name=":trace_stage()" id="v"}
			<option value="{$key}" {eq name="$key" value="$detail.stage"} selected{/eq}>{$v}</option>
            {/volist}
          </select>
        </td>
      </tr>
    </table>
    <div class="py-3">
	  <input type="hidden" name="id" value="{$detail.id}">
      <button class="layui-btn layui-btn-normal" lay-submit="" lay-filter="webform">立即提交</button>
      <button type="reset" class="layui-btn layui-btn-primary">重置</button>
    </div>
</form>
{/block}
<!-- /主体 -->

<!-- 脚本 -->
{block name="script"}
<script>
	const moduleInit = ['tool'];
	function gouguInit() {
		var form = layui.form,tool=layui.tool,laydate = layui.laydate;
		laydate.render({
			elem: '#follow_time'
			,fullPanel:true
			,type: 'datetime'
		});
		laydate.render({
			elem: '#next_time'
			,fullPanel:true
			,min: 0
			,type: 'datetime'
		});
		
		form.on('select(chance)', function(data){
			var check_type = data.elem[data.elem.selectedIndex].title;
			if(check_type == 0){
				form.val('demo-val-filter', {
					"stage": ""
				});
				$("#stage").attr("disabled",true);
			}
			else{
				$("#stage").removeAttr("disabled");
			}
			form.render();
		});
		//监听提交
		form.on('submit(webform)', function (data) {
			if(data.field.chance_id!='' && data.field.stage==''){
				layer.msg('请选择当前阶段');
				return false;
			}
			let callback = function (e) {
				layer.msg(e.msg);
				if (e.code == 0) {
					tool.sideClose();
					parent.layui.traceTable.reload();
				}
			}
			tool.post("/customer/api/add_trace", data.field, callback);
			return false;
		});

	}
</script>
{/block}
<!-- /脚本 -->