<?php
/**
 * @copyright Copyright (c) 2021 仲正堂工作室
 * @license https://opensource.org/licenses/GPL-3.0
 * @link https://www.gougucms.com
 */

namespace app\cross\validate;

use think\Validate;

class CrossStoreSettleOnlineDetailCheck extends Validate
{
    protected $rule = [
        'period' => 'require|regex:/^\d{4}-\d{2}$/',
        'settlement_type' => 'require|max:50',
        'verification_store_id' => 'require|integer|gt:0',
        'product_name' => 'require|max:255',
        'product_quantity' => 'require|integer|egt:0',
        'settlement_amount' => 'require|float|egt:0',
        'principal_payment_amount' => 'require|float|egt:0',
        'bonus_payment_amount' => 'require|float|egt:0',
        'cash_payment_amount' => 'require|float|egt:0',
        'card_payment_amount' => 'require|float|egt:0',
        'settlement_time' => 'dateFormat:Y-m-d H:i:s',
        'order_store_id' => 'require|integer|gt:0',
        'verification_order_number' => 'require|max:100',
        'purchase_order_number' => 'require|max:100',
        'customer_name' => 'require|max:100',
        'customer_mobile' => 'require|max:20',
        'customer_belong_store_id' => 'integer|egt:0',
    ];

    protected $message = [
        'period.require' => '快照月份不能为空',
        'period.regex' => '快照月份格式错误，应为YYYY-MM格式',
        'settlement_type.require' => '结算类型不能为空',
        'settlement_type.max' => '结算类型长度不能超过50个字符',
        'verification_store_id.require' => '核销门店不能为空',
        'verification_store_id.integer' => '核销门店ID必须为整数',
        'verification_store_id.gt' => '核销门店ID必须大于0',
        'product_name.require' => '商品名称不能为空',
        'product_name.max' => '商品名称长度不能超过255个字符',
        'product_quantity.require' => '商品数量不能为空',
        'product_quantity.integer' => '商品数量必须为整数',
        'product_quantity.egt' => '商品数量不能小于0',
        'settlement_amount.require' => '结算金额不能为空',
        'settlement_amount.float' => '结算金额必须为数字',
        'settlement_amount.egt' => '结算金额不能小于0',
        'principal_payment_amount.require' => '本金支付金额不能为空',
        'principal_payment_amount.float' => '本金支付金额必须为数字',
        'principal_payment_amount.egt' => '本金支付金额不能小于0',
        'bonus_payment_amount.require' => '赠金支付金额不能为空',
        'bonus_payment_amount.float' => '赠金支付金额必须为数字',
        'bonus_payment_amount.egt' => '赠金支付金额不能小于0',
        'cash_payment_amount.require' => '现金类支付金额不能为空',
        'cash_payment_amount.float' => '现金类支付金额必须为数字',
        'cash_payment_amount.egt' => '现金类支付金额不能小于0',
        'card_payment_amount.require' => '次卡支付金额不能为空',
        'card_payment_amount.float' => '次卡支付金额必须为数字',
        'card_payment_amount.egt' => '次卡支付金额不能小于0',
        'settlement_time.dateFormat' => '结算时间格式错误，应为YYYY-MM-DD HH:MM:SS格式',
        'order_store_id.require' => '下单门店不能为空',
        'order_store_id.integer' => '下单门店ID必须为整数',
        'order_store_id.gt' => '下单门店ID必须大于0',
        'verification_order_number.require' => '核销订单号不能为空',
        'verification_order_number.max' => '核销订单号长度不能超过100个字符',
        'purchase_order_number.require' => '购买订单号不能为空',
        'purchase_order_number.max' => '购买订单号长度不能超过100个字符',
        'customer_name.require' => '客户姓名不能为空',
        'customer_name.max' => '客户姓名长度不能超过100个字符',
        'customer_mobile.require' => '客户手机号不能为空',
        'customer_mobile.max' => '客户手机号长度不能超过20个字符',
        'customer_belong_store_id.integer' => '归属门店ID必须为整数',
        'customer_belong_store_id.egt' => '归属门店ID不能小于0',
    ];

    protected $scene = [
        'edit' => [
            'period', 'settlement_type', 'verification_store_id', 'product_name', 'product_quantity',
            'settlement_amount', 'principal_payment_amount', 'bonus_payment_amount', 
            'cash_payment_amount', 'card_payment_amount', 'settlement_time',
            'order_store_id', 'verification_order_number', 'purchase_order_number',
            'customer_name', 'customer_mobile', 'customer_belong_store_id'
        ],
    ];
}