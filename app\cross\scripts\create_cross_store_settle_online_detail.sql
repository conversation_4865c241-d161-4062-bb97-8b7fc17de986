-- 网店订单跨店核销明细表建表脚本
-- 创建时间：2025-07-24
-- 说明：用于存储网店订单跨店核销的详细记录

CREATE TABLE `oa_cross_store_settle_online_detail` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `settlement_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '结算类型',
  `verification_store_id` int unsigned NOT NULL DEFAULT 0 COMMENT '核销门店ID (关联 oa_department.id)',
  `product_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '商品名称',
  `product_quantity` int unsigned NOT NULL DEFAULT 0 COMMENT '商品数量',
  `settlement_amount` decimal(15,2) NOT NULL DEFAULT '0.00' COMMENT '结算金额',
  `principal_payment_amount` decimal(15,2) NOT NULL DEFAULT '0.00' COMMENT '本金支付金额',
  `bonus_payment_amount` decimal(15,2) NOT NULL DEFAULT '0.00' COMMENT '赠金支付金额',
  `cash_payment_amount` decimal(15,2) NOT NULL DEFAULT '0.00' COMMENT '现金类支付金额',
  `card_payment_amount` decimal(15,2) NOT NULL DEFAULT '0.00' COMMENT '次卡支付金额',
  `settlement_time` datetime DEFAULT NULL COMMENT '结算时间',
  `order_store_id` int unsigned NOT NULL DEFAULT 0 COMMENT '下单门店ID (关联 oa_department.id)',
  `verification_order_number` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '核销订单号',
  `purchase_order_number` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '购买订单号',
  `customer_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '客户姓名',
  `customer_mobile` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '客户手机号',
  `customer_belong_store_id` int unsigned NOT NULL DEFAULT 0 COMMENT '归属门店ID (关联 oa_department.id)',
  `period` varchar(7) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '快照月份 (格式: YYYY-MM)',
  `create_time` int NOT NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int NOT NULL DEFAULT 0 COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_verification_store_id` (`verification_store_id`) USING BTREE COMMENT '核销门店索引',
  KEY `idx_order_store_id` (`order_store_id`) USING BTREE COMMENT '下单门店索引',
  KEY `idx_customer_belong_store_id` (`customer_belong_store_id`) USING BTREE COMMENT '归属门店索引',
  KEY `idx_settlement_time` (`settlement_time`) USING BTREE COMMENT '结算时间索引',
  KEY `idx_verification_order_number` (`verification_order_number`) USING BTREE COMMENT '核销订单号索引',
  KEY `idx_purchase_order_number` (`purchase_order_number`) USING BTREE COMMENT '购买订单号索引',
  KEY `idx_customer_mobile` (`customer_mobile`) USING BTREE COMMENT '客户手机号索引',
  KEY `idx_period` (`period`) USING BTREE COMMENT '快照月份索引'
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='网店订单跨店核销明细表';