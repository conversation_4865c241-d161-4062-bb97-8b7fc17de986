{extend name="../../base/view/common/base" /}
<!-- 主体 -->
{block name="body"}
<form class="layui-form p-4">
	<h3 class="pb-3">知识关键字</h3>
	<table class="layui-table">
		<tr>
			<td class="layui-td-gray-2">关键字名称<font>*</font></td>
			<td>				
				<input type="text" name="title" lay-verify="required" lay-reqText="请输入关键字名称" autocomplete="off" placeholder="请输入关键字名称" class="layui-input" {notempty name="$keywords.title"} value="{$keywords.title}" {/notempty}>
			</td>
			<td class="layui-td-gray">排序</td>
			<td><input type="text" name="sort" placeholder="请输入排序，数字" autocomplete="off" class="layui-input" {notempty name="$keywords.sort" }value="{$keywords.sort}" {/notempty}>
			</td>
		</tr>
		<tr>
			<td class="layui-td-gray">状态<font>*</font></td>
			<td colspan = "3">
				{if condition="$id eq 0"}
				<input type="radio" name="status" value="1" title="正常" checked>
				<input type="radio" name="status" value="0" title="禁用">
				{else/}
				<input type="radio" name="status" value="1" title="正常" {eq name="$keywords.status" value="1"
					}checked{/eq}>
				<input type="radio" name="status" value="0" title="禁用" {eq name="$keywords.status" value="0"
					}checked{/eq}>
				{/if}
			</td>
		</tr>
	</table>
	<div style="padding: 10px 0">
		<input type="hidden" name="id" value="{$id}" />
		<button class="layui-btn layui-btn-normal" lay-submit="" lay-filter="webform">立即提交</button>
		<button type="reset" class="layui-btn layui-btn-primary">重置</button>
	</div>
</form>
{/block}
<!-- /主体 -->

<!-- 脚本 -->
{block name="script"}
<script>
	const moduleInit = ['tool'];
	function gouguInit() {
		var table = layui.table, tool = layui.tool, form = layui.form;
		//监听提交
		form.on('submit(webform)', function (data) {
			$.ajax({
				url: "/home/<USER>/add",
				type: 'post',
				data: data.field,
				success: function (e) {
					layer.msg(e.msg);
					if (e.code == 0) {
						layer.msg(e.msg);
						tool.sideClose(1000);
					}
				}
			})
			return false;
		});
	}

</script>
{/block}
<!-- /脚本 -->