<?php
/**
 * @copyright Copyright (c) 2021 仲正堂工作室
 * @license https://opensource.org/licenses/GPL-3.0
 * @link https://www.gougucms.com
 */

namespace app\cross\model;

use think\Model;
use think\facade\Db;

class CrossStoreSettleOnlineDetail extends Model
{
    protected $name = 'cross_store_settle_online_detail';

    // 设置字段信息
    protected $schema = [
        'id'                           => 'int',
        'settlement_type'              => 'string',
        'verification_store_id'        => 'int',
        'product_name'                 => 'string',
        'product_quantity'             => 'int',
        'settlement_amount'            => 'decimal',
        'principal_payment_amount'     => 'decimal',
        'bonus_payment_amount'         => 'decimal',
        'cash_payment_amount'          => 'decimal',
        'card_payment_amount'          => 'decimal',
        'settlement_time'              => 'datetime',
        'order_store_id'               => 'int',
        'verification_order_number'    => 'string',
        'purchase_order_number'        => 'string',
        'customer_name'                => 'string',
        'customer_mobile'              => 'string',
        'customer_belong_store_id'     => 'int',
        'period'                       => 'string',
        'create_time'                  => 'int',
        'update_time'                  => 'int',
    ];

    // 自动时间戳
    protected $autoWriteTimestamp = true;
    protected $createTime = 'create_time';
    protected $updateTime = 'update_time';

    /**
     * 关联核销门店信息
     */
    public function verificationStore()
    {
        return $this->belongsTo('app\user\model\Department', 'verification_store_id', 'id');
    }

    /**
     * 关联下单门店信息
     */
    public function orderStore()
    {
        return $this->belongsTo('app\user\model\Department', 'order_store_id', 'id');
    }

    /**
     * 关联客户归属门店信息
     */
    public function customerBelongStore()
    {
        return $this->belongsTo('app\user\model\Department', 'customer_belong_store_id', 'id');
    }

    /**
     * 获取网店核销明细列表
     * @param array $where 查询条件
     * @param int $page 页码
     * @param int $limit 每页数量
     * @return array
     */
    public static function getList($where = [], $page = 1, $limit = 20)
    {
        $query = self::alias('csod')
            ->join('oa_department vs', 'vs.id = csod.verification_store_id', 'LEFT')
            ->join('oa_department os', 'os.id = csod.order_store_id', 'LEFT')
            ->join('oa_department cbs', 'cbs.id = csod.customer_belong_store_id', 'LEFT')
            ->field('csod.*, vs.title as verification_store_name, os.title as order_store_name, 
                     cbs.title as customer_belong_store_name');

        if (!empty($where)) {
            $query->where($where);
        }

        $list = $query->order('csod.settlement_time desc, csod.id desc')
            ->paginate($limit, false, ['page' => $page])
            ->each(function ($item) {
                // 格式化金额显示
                $item['settlement_amount_formatted'] = number_format(floatval($item['settlement_amount']), 2);
                $item['principal_payment_amount_formatted'] = number_format(floatval($item['principal_payment_amount']), 2);
                $item['bonus_payment_amount_formatted'] = number_format(floatval($item['bonus_payment_amount']), 2);
                $item['cash_payment_amount_formatted'] = number_format(floatval($item['cash_payment_amount']), 2);
                $item['card_payment_amount_formatted'] = number_format(floatval($item['card_payment_amount']), 2);
                
                // 格式化时间
                $item['settlement_time_formatted'] = !empty($item['settlement_time']) ? date('Y-m-d H:i:s', strtotime($item['settlement_time'])) : '';
                $item['create_time_formatted'] = date('Y-m-d H:i:s', $item['create_time']);
                $item['update_time_formatted'] = date('Y-m-d H:i:s', $item['update_time']);
                
                return $item;
            });

        return $list;
    }

    /**
     * 获取网店核销明细详情
     * @param int $id 记录ID
     * @return array
     */
    public static function getDetail($id)
    {
        $detail = self::alias('csod')
            ->join('oa_department vs', 'vs.id = csod.verification_store_id', 'LEFT')
            ->join('oa_department os', 'os.id = csod.order_store_id', 'LEFT')
            ->join('oa_department cbs', 'cbs.id = csod.customer_belong_store_id', 'LEFT')
            ->field('csod.*, vs.title as verification_store_name, os.title as order_store_name, 
                     cbs.title as customer_belong_store_name')
            ->where('csod.id', $id)
            ->find();

        if (empty($detail)) {
            return [];
        }

        return $detail;
    }

    /**
     * 获取结算类型选项
     * @return array
     */
    public static function getSettlementTypeOptions()
    {
        return [
            '网店订单跨店核销' => '网店订单跨店核销',
            '网店订单跨店核销退款' => '网店订单跨店核销退款',
        ];
    }

    /**
     * 批量导入网店核销明细数据
     * @param array $data 数据数组
     * @return array 导入结果
     */
    public static function batchImport($data)
    {
        $successCount = 0;
        $failCount = 0;
        $errors = [];

        Db::startTrans();
        try {
            foreach ($data as $index => $row) {
                // 数据验证和处理
                $insertData = self::processImportData($row, $index + 1, $errors, $failCount);
                
                if ($insertData === false) {
                    continue;
                }

                // 插入数据
                $result = self::create($insertData);
                if ($result) {
                    $successCount++;
                } else {
                    $errors[] = "第" . ($index + 1) . "行：数据保存失败";
                    $failCount++;
                }
            }

            Db::commit();
        } catch (\Exception $e) {
            Db::rollback();
            $errors[] = "导入过程中发生错误：" . $e->getMessage();
            return [
                'success' => false,
                'message' => '导入失败',
                'success_count' => 0,
                'fail_count' => count($data),
                'errors' => $errors
            ];
        }

        return [
            'success' => $failCount === 0,
            'message' => $failCount === 0 ? '导入成功' : '部分导入失败',
            'success_count' => $successCount,
            'fail_count' => $failCount,
            'errors' => $errors
        ];
    }

    /**
     * 处理导入数据
     * @param array $row 单行数据
     * @param int $rowNumber 行号
     * @param array &$errors 错误信息数组
     * @param int &$failCount 失败计数
     * @return array|false
     */
    private static function processImportData($row, $rowNumber, &$errors, &$failCount)
    {
        // 基础字段验证
        $settlementType = trim($row['settlement_type'] ?? '');
        $verificationStoreName = trim($row['verification_store_name'] ?? '');
        $orderStoreName = trim($row['order_store_name'] ?? '');
        $customerBelongStoreName = trim($row['customer_belong_store_name'] ?? '');

        if (empty($settlementType)) {
            $errors[] = "第{$rowNumber}行：结算类型不能为空";
            $failCount++;
            return false;
        }

        // 门店名称转换为ID
        $storeIds = self::getStoreIdsByNames([
            'verification_store' => $verificationStoreName,
            'order_store' => $orderStoreName,
            'customer_belong_store' => $customerBelongStoreName
        ]);

        if (!$storeIds['verification_store_id']) {
            $errors[] = "第{$rowNumber}行：核销门店「{$verificationStoreName}」不存在或已停用";
            $failCount++;
            return false;
        }

        if (!$storeIds['order_store_id']) {
            $errors[] = "第{$rowNumber}行：下单门店「{$orderStoreName}」不存在或已停用";
            $failCount++;
            return false;
        }

        // 处理时间字段
        $settlementTime = null;
        if (!empty($row['settlement_time'])) {
            $settlementTime = date('Y-m-d H:i:s', strtotime($row['settlement_time']));
        }

        return [
            'settlement_type' => $settlementType,
            'verification_store_id' => $storeIds['verification_store_id'],
            'product_name' => trim($row['product_name'] ?? ''),
            'product_quantity' => intval($row['product_quantity'] ?? 0),
            'settlement_amount' => floatval($row['settlement_amount'] ?? 0),
            'principal_payment_amount' => floatval($row['principal_payment_amount'] ?? 0),
            'bonus_payment_amount' => floatval($row['bonus_payment_amount'] ?? 0),
            'cash_payment_amount' => floatval($row['cash_payment_amount'] ?? 0),
            'card_payment_amount' => floatval($row['card_payment_amount'] ?? 0),
            'settlement_time' => $settlementTime,
            'order_store_id' => $storeIds['order_store_id'],
            'verification_order_number' => trim($row['verification_order_number'] ?? ''),
            'purchase_order_number' => trim($row['purchase_order_number'] ?? ''),
            'customer_name' => trim($row['customer_name'] ?? ''),
            'customer_mobile' => trim($row['customer_mobile'] ?? ''),
            'customer_belong_store_id' => $storeIds['customer_belong_store_id'] ?: 0,
            'period' => trim($row['period'] ?? ''),
            'create_time' => time(),
            'update_time' => time()
        ];
    }

    /**
     * 根据门店名称获取门店ID
     * @param array $storeNames 门店名称数组
     * @return array
     */
    private static function getStoreIdsByNames($storeNames)
    {
        $result = [
            'verification_store_id' => 0,
            'order_store_id' => 0,
            'customer_belong_store_id' => 0
        ];

        $allStoreNames = array_filter(array_values($storeNames));
        if (empty($allStoreNames)) {
            return $result;
        }

        $stores = Db::name('Department')
            ->where('title', 'in', $allStoreNames)
            ->where('status', 1)
            ->select()
            ->toArray();

        $storeMap = [];
        foreach ($stores as $store) {
            $storeMap[$store['title']] = $store['id'];
        }

        foreach ($storeNames as $key => $storeName) {
            if (!empty($storeName) && isset($storeMap[$storeName])) {
                $result[$key . '_id'] = $storeMap[$storeName];
            }
        }

        return $result;
    }

    /**
     * 获取统计汇总数据
     * @param array $where 查询条件
     * @return array
     */
    public static function getSummaryData($where = [])
    {
        $query = self::where($where);

        $summaryData = $query->field([
            'SUM(settlement_amount) as total_settlement_amount',
            'SUM(principal_payment_amount) as total_principal_payment_amount',
            'SUM(bonus_payment_amount) as total_bonus_payment_amount',
            'SUM(cash_payment_amount) as total_cash_payment_amount',
            'SUM(card_payment_amount) as total_card_payment_amount',
            'SUM(product_quantity) as total_quantity',
            'COUNT(*) as total_count'
        ])->find();

        return [
            'total_settlement_amount' => number_format(floatval($summaryData['total_settlement_amount'] ?? 0), 2),
            'total_principal_payment_amount' => number_format(floatval($summaryData['total_principal_payment_amount'] ?? 0), 2),
            'total_bonus_payment_amount' => number_format(floatval($summaryData['total_bonus_payment_amount'] ?? 0), 2),
            'total_cash_payment_amount' => number_format(floatval($summaryData['total_cash_payment_amount'] ?? 0), 2),
            'total_card_payment_amount' => number_format(floatval($summaryData['total_card_payment_amount'] ?? 0), 2),
            'total_quantity' => intval($summaryData['total_quantity'] ?? 0),
            'total_count' => intval($summaryData['total_count'] ?? 0)
        ];
    }
}