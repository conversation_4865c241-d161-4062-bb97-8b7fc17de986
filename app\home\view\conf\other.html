{extend name="../../base/view/common/base" /}
<!-- 主体 -->
{block name="body"}
<form class="layui-form p-4">
	<h3 class="pb-3">其他配置</h3>
	<table class="layui-table layui-table-form">
		<tr>
			<td class="layui-td-gray">开发者</td>
			<td>
				<input type="hidden" value="{$id}" name="id">
				<input type="text" name="author" autocomplete="off" placeholder="请输入开发者" lay-reqText="请输入开发者" class="layui-input" value="{$config.author|default=''}">
			</td>
			<td class="layui-td-gray-2">开发版本号</td>
			<td><input type="text" name="version" autocomplete="off" placeholder="请输入版本号" lay-reqText="请输入版本号" class="layui-input" value="{$config.version|default=''}"></td>
		</tr>
	</table>

	<div class="py-3">
		<button class="layui-btn layui-btn-normal" lay-submit="" lay-filter="webform">立即提交</button>
		<button type="reset" class="layui-btn layui-btn-primary">重置</button>
	</div>
</form>
{/block}
<!-- /主体 -->

<!-- 脚本 -->
{block name="script"}
<script>
	const moduleInit = ['tool'];
	function gouguInit() {
		var form = layui.form, tool = layui.tool;
		//监听提交
		form.on('submit(webform)', function (data) {
			let callback = function (e) {
				layer.msg(e.msg);
				if (e.code == 0) {
					tool.sideClose(1000);
				}
			}
			tool.post("/home/<USER>/edit", data.field, callback);
			return false;
		});
	}
</script>
{/block}
<!-- /脚本 -->