{extend name="../../base/view/common/base" /}
<!-- 主体 -->
{block name="body"}
<div class="p-3">
	<form class="layui-form gg-form-bar border-x border-t" lay-filter="barsearchform">
		<input type="hidden" class="layui-input" readonly name="type" value="{$type}">
		<div class="layui-input-inline" style="width:300px;">
			<input type="text" class="layui-input" id="diff_time" placeholder="选择时间区间" readonly name="diff_time">
		</div>
<!--		<div class="layui-input-inline">-->
<!--			<select name="check_status">-->
<!--				<option value="">请选择状态</option>-->
<!--				<option value="1">审批中</option>-->
<!--				<option value="2">审批通过,待打款</option>-->
<!--				<option value="3">审批不通过</option>-->
<!--				<option value="4">撤销</option>-->
<!--				<option value="5">已打款</option>-->
<!--			</select>-->
<!--		</div>-->
		<div class="layui-input-inline">
			<select name="store"  lay-reqText="请选择申请部门" lay-search="">
				<option value="">请选择申请部门</option>
				{volist name="$dep" id="vo"}
				<option value="{$vo.id}" >{$vo.title}</option>
				{/volist}
			</select>
		</div>
		<div class="layui-input-inline">
			<select name="pay_type" lay-reqText="请选择费用类型" lay-search="">
				<option value="">请选择费用类型</option>
				{volist name="$pay_type" id="vo"}
				<option value="{$vo.id}" >{$vo.name}</option>
				{/volist}
			</select>
		</div>
		<div class="layui-input-inline">
			<select name="is_fapiao" lay-reqText="请选择是否有发票" lay-search="">
				<option value="">请选择是否有发票</option>
				<option value="是" >是</option>
				<option value="否" >否</option>
			</select>
		</div>
		<div class="layui-input-inline">
			<input type="text" name="pay_amount" autocomplete="off" placeholder="金额" class="layui-input" value="">
		</div>
		<div class="layui-input-inline">
			<input type="text" name="keywords" style="width: 500px" autocomplete="off" placeholder="可搜索：发起人；开户行；收款人；银行卡号；单据ID号" class="layui-input" value="">
		</div>
		<div class="layui-input-inline" style="width:150px;">
			<button class="layui-btn layui-btn-normal" lay-submit="" lay-filter="webform"><i class="layui-icon layui-icon-search mr-1"></i>搜索</button>
			<button type="reset" class="layui-btn layui-btn-reset" lay-filter="reset">清空</button>
		</div>
	</form>
	<div class="layui-col-md12">
		<div class="layui-card">
			<div class="body-table layui-tab layui-tab-brief" lay-filter="tab">
				<ul class="layui-tab-title" style="border:none;">
					<li class="layui-this">全部</li>
					<li>待我审批</li>
					<li>我已审批</li>
				</ul>
				<div class="layui-tab-content" style="padding:0">
					<table class="layui-hide" id="test" lay-filter="test"></table>
				</div>
			</div>
		</div>
	</div>
<!--	<table class="layui-hide" id="test" lay-filter="test"></table>-->
</div>
<script type="text/html" id="toolbarDemo">
<div class="layui-btn-container">
<!--    <span class="layui-btn layui-btn-sm" lay-event="add">+ 添加报销登记</span>-->
</div>
</script>
{/block}
<!-- /主体 -->

<!-- 脚本 -->
{block name="script"}
<script>
	const moduleInit = ['tool','laydatePlus'];
	function gouguInit() {
		var form = layui.form,table = layui.table,element=layui.element,tool=layui.tool, laydatePlus = layui.laydatePlus;
		//日期范围
		var diff_time = new laydatePlus({'target':'diff_time'});

		let status = 0;

		//监听搜索提交
		form.on('submit(webform)', function(data) {
			layui.pageTable.reload({
				where: {
					...data.field,
					status: status,
				},
				page: {curr: 1}
			});
			return false;
		});

		element.on('tab(tab)', function(data){
			status = data.index;
			console.log(status)
			layui.pageTable.reload({
				where:{
					status:data.index,
					type:$('[name="type"]').val()
				},page:{curr:1}});
			return false;
		});



		layui.pageTable = table.render({
			elem: '#test',
			title: $('[name="type"]').val() == 1 ? '付款管理列表' : '报销管理列表',
			toolbar: '#toolbarDemo',
			defaultToolbar:['filter', {title:'导出EXCEL',layEvent: 'LAYTABLE_EXCEL',icon: 'layui-icon-export'}],
			url: "/finance/expense/index_f", //数据接口
			where:{type:$('[name="type"]').val()},
			page: true, //开启分页
			cellMinWidth: 100,
			limit: 20,
			cols: [
				[
					{
						field: 'id',
						title: 'ID号',
						align: 'center',
						width: 80
					},
					{
						field: 'type_name',
						title: '审批类型',
						align: 'center',
						width: 80
					},
					// {
					// 	field: 'check_status',
					// 	title: '状态',
					// 	align: 'center',
					// 	width: 120,
					// 	templet:function(d){
					// 		var html='<span class="black">待审批</span>';
					// 		if(d.check_status==1){
					// 			html='<span class="blue">审批中</span>';
					// 		}
					// 		else if(d.check_status==2){
					// 			html='<span class="green">审批通过</span>';
					// 		}
					// 		else if(d.check_status==3){
					// 			html='<span class="red">审批失败</span>';
					// 		}
					// 		else if(d.check_status==4){
					// 			html='<span class="red">已撤销</span>';
					// 		}
					// 		else if(d.check_status==5){
					// 			html='<span class="yellow">已打款</span>';
					// 		}
					// 		return html;
					// 	}
					// },
					{
						field: 'aname',
						title: '发起人',
						align: 'center',
						width: 100
					},{
						field: 'dname',
						title: '所属部门',
						align: 'center',
						width: 150
					},
					{
						field: 'store_name',
						title: '申请部门',
						align: 'center',
						width: 150
					},{
						field: 'approve_id',
						title: '审批单据号',
						width: 150,
						align: 'center',
						templet:function(d) {
							return	`<span class="layui-btn layui-btn-normal layui-btn-xs" lay-event="viewApprove">${d.approve_id}</span>`
						}
					}, {
						field: 'check_status_name',
						title: '审批状态',
						align: 'center',
						width: 150,
						templet:function(d) {
							return	`<span style="color:${d.check_status_color}">${d.check_status_name}</span>`
						}
					},{
						field: 'pay_type_name',
						title: '费用类型',
						align: 'center',
						width: 120
						},{
							field: 'pay_amount',
							title: '费用金额',
							align: 'center',
							width: 150
						},{
							field: 'pay_text',
							title: '费用说明'
						}
						,{
							field: 'is_fapiao',
							title: '是否有发票',
							align: 'center',
							width: 150,
							templet:function(d) {
								if (d.is_fapiao == '否') {
									return `<span class= "layui-btn layui-btn-xs" style = "background-color:#535353" > 否 </span>`
								}else{
									return `<span class= "layui-btn layui-btn-success layui-btn-xs" > 是 </span>`
								}
							}
						},{
							field: 'payee_name',
							title: '收款人姓名',
							align: 'center',
							width: 150
						},{
							field: 'bank_number',
							title: '银行卡号',
							align: 'center',
							width: 150
						},{
							field: 'bank_name',
							title: '开户行',
							align: 'center',
							width: 150
						},{
							field: 'remark',
							title: '备注',
							align: 'center',
							width: 150
						},{
							field: 'create_time_date',
							title: '创建时间',
							align: 'center',
							width: 150
						},{
							field: 'finish_time_date',
							title: '完成时间',
							align: 'center',
							width: 150
						},
						{
							field: 'content',
							title: '审批意见',
							align: 'center',
							width: 50
						}
						// , {
						// 	field: 'right',
						// 	fixed: 'right',
						// 	title: '操作',
						// 	width: 130,
						// 	align: 'center',
						// 	templet:function(d){
						// 		var html='<div class="layui-btn-group">';
						// 		var btn1='<span class="layui-btn layui-btn-normal layui-btn-xs" lay-event="view">详情</span>';
						// 		var btn2='<span class="layui-btn layui-btn-xs" lay-event="edit">编辑</span>';
						// 		var btn3='<span class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">删除</span>';
						// 		html+=btn1;
						// 		if(d.check_status==4 && d.admin_id==login_admin){
						// 			html+=btn2+btn3;
						// 		}
						// 		html+='</div>';
						// 		return html;
						// 	}
						// }
				]
			]
		});

		//表头工具栏事件
		table.on('toolbar(test)', function(obj){
			if (obj.event === 'add') {
				tool.side("/finance/expense/add");
				return;
			}
			if(obj.event === 'LAYTABLE_EXCEL'){
				var formSelect = form.val('barsearchform');
				formSelect.limit=99999;
				$.ajax({
					url: '/finance/expense/index_f',
					data: {
						...formSelect,
						status:status,
						type:$('[name="type"]').val()
					},
					success:function(res){
						table.exportFile('test', res.data,'xls');
					}
				});
				return;
			}
		});
		//监听行工具事件
		table.on('tool(test)', function(obj) {
			var data = obj.data;
			console.log(obj)
			if (obj.event === 'viewApprove') {
				tool.side("/oa/approve/view?id="+data.approve_id);
				return;
			}
			if (obj.event === 'view') {
				tool.side("/finance/expense/view?id="+data.id);
				return;
			}
			if (obj.event === 'edit') {
				tool.side("/finance/expense/add?id="+data.id);
				return;
			}
			if (obj.event === 'del') {
				layer.confirm('确定删除该报销记录吗？', {
					icon: 3,
					title: '提示'
				}, function(index) {
					$.ajax({
						url: "/finance/expense/delete",
						type:'post',
						data: {
							id: data.id
						},
						success: function(res) {
							layer.msg(res.msg);
							if (res.code == 0) {
								obj.del();
							}
						}
					})
					layer.close(index);
				});
			}
		});
	}
</script>
{/block}
<!-- /脚本 -->
