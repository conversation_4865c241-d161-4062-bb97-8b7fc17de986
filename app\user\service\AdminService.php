<?php

namespace app\user\service;

use think\facade\Db;

class AdminService
{

    public function insertAdmin($data, $data_ex)
    {
        $admin_id = 0;

        if (!empty($data['workno'])) {
            $admin = Db::name('admin')->where(['workno' => $data['workno']])->find();
            if (!empty($admin)) {
                $admin_id = $admin['id'];
                $admin['update_time'] = time();
                Db::name('admin')->where(['id' => $admin['id']])->update($data);
            } else {
                return false;
            }
        } else {
            $data['pwd'] = "52dab9db1f7d480b20bca9559bf64c01";
            $data['salt'] = "svm5wu27kgdpet031z6x";
            $data['thumb'] = "/static/home/<USER>/icon.png";
            $data['theme'] = "white";
            $admin_id = Db::name('admin')->insertGetId($data);

            Db::name('admin')->where(['id' => $admin_id])->update([
                'workno' => numberStrPad($admin_id),
                'update_time' => time()
            ]);
        }

        if (!empty($admin_id)) {
            $admin_expand = Db::name('admin_expand')->where(['id' => $admin_id])->find();
            if (!empty($admin_expand)) {
                Db::name('admin_expand')->where(['id' => $admin_id])->update($data_ex);
            } else {
                $data_ex['id'] = $admin_id;
                Db::name('admin_expand')->insert($data_ex);
            }
        } else {
            return false;
        }
        return true;
    }

    public function insertAdminDep($id, $admin_dep, $admin_position)
    {
        $admin_dep_admin = array();

        $d_time = date("Y-m-d H:i:s");

        Db::name('admin_dep')
            ->where(['aid' => $id])
            ->update(['update_time' => $d_time, 'status' => -1]);

        foreach ($admin_dep as $k => $v) {
            if (empty($v)) continue;
            $admin_dep_admin[] = [
                'aid' => $id,
                'did' => $v,
                'position_id' => $admin_position[$k],
                'create_time' => $d_time,
                'update_time' => $d_time,
            ];
        }

        if (!empty($admin_dep_admin)){
            Db::name('admin_dep')->insertAll($admin_dep_admin);
        }

        return true;
    }

    public function syn_admin_openid($id)
    {
        $admin = Db::connect('mysql')->table('oa_admin')->where(['id' => $id])
            ->find();


        $staff_info = Db::connect('mt')->table('staff_info')
            ->where([['name', 'like', '%' . $admin['name'] . '%']])
            ->field("id,name,mobile,yz_open_id")
            ->find();

        if (!empty($staff_info)) {
            Db::connect('mysql')->table('oa_admin')
                ->where(['id' => $admin['id']])->update(['yz_uid' => $staff_info['yz_open_id']]);
        }
    }

    public function getParam()
    {
        $status_str = [
            ['title' => '待面试', 'color' => 'blue'],
            ['title' => '面试不通过', 'color' => 'red'],
            ['title' => '面试通过', 'color' => 'green'],
            ['title' => '合同签订完毕', 'color' => 'green'],
            ['title' => '薪资待确认', 'color' => 'orange'],
            ['title' => '薪资确认', 'color' => 'green'],
            ['title' => '已离职', 'color' => 'red'],
        ];
        $equ_background = ['初中及以下', '中专/中技', '高中', '大专', '本科', '硕士', '博士'];

        return ['status_str' => $status_str, 'equ_background' => $equ_background];
    }

    public function getPdepartment($main_did , $arr = [] , $key = 0)
    {
        $department  = Db::name("department")->field("id,title,pid")->where(['id' => $main_did])->find();

        $arr[$key] = $department['title'];

        if ($department['pid'] == 0){
            return $arr;
        }else{

            return $this->getPdepartment($department['pid'] , $arr , $key + 1);

        }

    }
}