{extend name="../../base/view/common/base" /}

{block name="style"}
<style>
    /* 查看页面样式 - 参考balance/view.html的卡片布局 */
    .layui-card {
        border: 1px solid #e6e6e6;
        border-radius: 4px;
        margin-bottom: 15px;
    }

    .layui-card-header {
        background-color: #f8f8f8;
        padding: 10px 15px;
        border-bottom: 1px solid #e6e6e6;
        font-weight: bold;
    }

    .layui-card-body {
        padding: 15px;
    }

    /* 表格样式优化 */
    .layui-table {
        margin-bottom: 0;
    }

    .layui-table td {
        padding: 8px 12px;
        vertical-align: middle;
    }

    .layui-table th {
        background-color: #f8f8f8;
        font-weight: bold;
    }

    .layui-td-gray {
        background-color: #f5f5f5;
        font-weight: bold;
        text-align: left;
        vertical-align: middle;
        width: 140px;
        padding: 8px 12px;
    }

    /* 只读字段样式 */
    .readonly-value {
        background-color: #fff;
        color: #333;
        padding: 8px 12px;
    }

    /* 金额显示样式 */
    .amount-display {
        font-weight: bold;
        color: #4CAF50;
    }

    /* 结算类型显示样式 */
    .settlement-type-display {
        font-weight: bold;
        color: #333;
    }

    /* 时间显示样式 */
    .time-display {
        color: #2196F3;
    }

    /* 页面内容区域 */
    .view-content {
        padding: 20px;
        height: 100%;
        box-sizing: border-box;
        overflow-y: auto;
    }
</style>
{/block}

{block name="body"}
<div class="view-content">
    <!-- 页面标题 -->
    <div style="margin-bottom: 20px;">
        <h3 style="margin: 0 0 15px 0; font-size: 18px; font-weight: bold; color: #333;">储值跨店结算明细详情</h3>
    </div>

    <!-- 基本信息 -->
    <div class="layui-card">
        <div class="layui-card-header">基本信息</div>
        <div class="layui-card-body">
            <table class="layui-table">
                <tr>
                    <td class="layui-td-gray">快照月份</td>
                    <td style="width: 200px;">
                        <span class="readonly-value" style="font-weight: bold;">{$detail.period|default='-'}</span>
                    </td>
                    <td class="layui-td-gray">结算类型</td>
                    <td style="width: 200px;">
                        <span class="readonly-value settlement-type-display">{$detail.settlement_type|default='-'}</span>
                    </td>
                </tr>
                <tr>
                    <td class="layui-td-gray">消费门店</td>
                    <td>
                        <span class="readonly-value" style="font-weight: bold;">{$detail.consume_store_name|default='-'}</span>
                    </td>
                    <td class="layui-td-gray">结算门店</td>
                    <td>
                        <span class="readonly-value" style="font-weight: bold;">{$detail.settlement_store_name|default='-'}</span>
                    </td>
                </tr>
                <tr>
                    <td class="layui-td-gray">结算时间</td>
                    <td colspan="3">
                        <span class="readonly-value time-display">{$detail.settlement_time|default='-'}</span>
                    </td>
                </tr>
            </table>
        </div>
    </div>

    <!-- 商品信息 -->
    <div class="layui-card">
        <div class="layui-card-header">商品信息</div>
        <div class="layui-card-body">
            <table class="layui-table">
                <tr>
                    <td class="layui-td-gray">商品名称</td>
                    <td style="width: 200px;">
                        <span class="readonly-value">{$detail.product_name|default='-'}</span>
                    </td>
                    <td class="layui-td-gray">商品数量</td>
                    <td style="width: 200px;">
                        <span class="readonly-value">{$detail.product_quantity|default=0}</span>
                    </td>
                </tr>
                <tr>
                    <td class="layui-td-gray">充值卡名称</td>
                    <td>
                        <span class="readonly-value">{$detail.card_name|default='-'}</span>
                    </td>
                    <td class="layui-td-gray">开卡门店</td>
                    <td>
                        <span class="readonly-value">{$detail.card_open_store_name|default='-'}</span>
                    </td>
                </tr>
            </table>
        </div>
    </div>

    <!-- 金额信息 -->
    <div class="layui-card">
        <div class="layui-card-header">金额信息</div>
        <div class="layui-card-body">
            <table class="layui-table">
                <tr>
                    <td class="layui-td-gray">结算金额</td>
                    <td style="width: 200px;">
                        <span class="readonly-value amount-display">¥{$detail.settlement_amount|number_format=2|default='0.00'}</span>
                    </td>
                    <td class="layui-td-gray">结算本金金额</td>
                    <td style="width: 200px;">
                        <span class="readonly-value amount-display">¥{$detail.settlement_principal_amount|number_format=2|default='0.00'}</span>
                    </td>
                </tr>
                <tr>
                    <td class="layui-td-gray">结算赠金金额</td>
                    <td colspan="3">
                        <span class="readonly-value amount-display">¥{$detail.settlement_bonus_amount|number_format=2|default='0.00'}</span>
                    </td>
                </tr>
            </table>
        </div>
    </div>

    <!-- 订单信息 -->
    <div class="layui-card">
        <div class="layui-card-header">订单信息</div>
        <div class="layui-card-body">
            <table class="layui-table">
                <tr>
                    <td class="layui-td-gray">发生订单号</td>
                    <td style="width: 200px;">
                        <span class="readonly-value">{$detail.order_number|default='-'}</span>
                    </td>
                    <td class="layui-td-gray">关联订单号</td>
                    <td style="width: 200px;">
                        <span class="readonly-value">{$detail.related_order_number|default='-'}</span>
                    </td>
                </tr>
            </table>
        </div>
    </div>

    <!-- 客户信息 -->
    <div class="layui-card">
        <div class="layui-card-header">客户信息</div>
        <div class="layui-card-body">
            <table class="layui-table">
                <tr>
                    <td class="layui-td-gray">客户姓名</td>
                    <td style="width: 200px;">
                        <span class="readonly-value">{$detail.customer_name|default='-'}</span>
                    </td>
                    <td class="layui-td-gray">客户手机号</td>
                    <td style="width: 200px;">
                        <span class="readonly-value">{$detail.customer_mobile|default='-'}</span>
                    </td>
                </tr>
                <tr>
                    <td class="layui-td-gray">客户归属门店</td>
                    <td colspan="3">
                        <span class="readonly-value">{$detail.customer_belong_store_name|default='-'}</span>
                    </td>
                </tr>
            </table>
        </div>
    </div>

</div>
{/block}

{block name="script"}
<script>
    const moduleInit = [];
    function gouguInit() {
        // 查看页面无需特殊初始化
    }
</script>
{/block}
