<?php
/**
 * 分红人清单数据批量计算脚本
 * 
 * 使用方法：
 * php batch_calculate_payment.php 2025-01,2025-02,2025-03
 * 或者：
 * php batch_calculate_payment.php 2025-01 2025-02 2025-03
 */

// 引入ThinkPHP框架
require_once __DIR__ . '/../../../vendor/autoload.php';

use think\facade\Db;
use think\facade\Config;
use app\dividend\service\DividendPaymentCalculateService;

// 初始化应用
$app = new think\App();
$app->initialize();

echo "=== 分红人清单数据批量计算脚本 ===\n";
echo "开始时间：" . date('Y-m-d H:i:s') . "\n";
echo "================================\n\n";

// 获取命令行参数
$periods = [];
if ($argc > 1) {
    // 检查是否是逗号分隔的格式
    if (strpos($argv[1], ',') !== false) {
        $periods = explode(',', $argv[1]);
    } else {
        // 多个参数格式
        for ($i = 1; $i < $argc; $i++) {
            $periods[] = $argv[$i];
        }
    }
} else {
    echo "❌ 请提供要计算的周期参数！\n";
    echo "使用方法：\n";
    echo "php batch_calculate_payment.php 2025-01,2025-02,2025-03\n";
    echo "或者：\n";
    echo "php batch_calculate_payment.php 2025-01 2025-02 2025-03\n";
    exit(1);
}

// 清理和验证周期格式
$validPeriods = [];
foreach ($periods as $period) {
    $period = trim($period);
    if (preg_match('/^\d{4}-\d{2}$/', $period)) {
        $validPeriods[] = $period;
    } else {
        echo "⚠️  跳过无效周期格式：{$period}\n";
    }
}

if (empty($validPeriods)) {
    echo "❌ 没有有效的周期格式！\n";
    exit(1);
}

echo "📅 计算周期：" . implode(', ', $validPeriods) . "\n\n";

try {
    // 执行批量计算
    $result = DividendPaymentCalculateService::batchCalculatePaymentData($validPeriods);
    
    if ($result['success']) {
        echo "✅ 批量计算成功！\n";
        echo "消息：{$result['message']}\n\n";
        
        if (!empty($result['data'])) {
            echo "📊 各周期计算结果详情：\n";
            echo str_repeat('=', 140) . "\n";
            
            foreach ($result['data'] as $period => $periodData) {
                echo "周期：{$period}\n";
                echo str_repeat('-', 120) . "\n";
                printf("%-20s %-15s %-15s %-10s %-10s\n", 
                    '分红人姓名', '统计周期', '应付金额(元)', '门店数量', '股东记录数');
                echo str_repeat('-', 120) . "\n";
                
                foreach ($periodData as $item) {
                    printf("%-20s %-15s %-15s %-10s %-10s\n",
                        mb_substr($item['shareholder_name'], 0, 18, 'UTF-8'),
                        $item['period'],
                        number_format($item['payable_amount'], 2),
                        $item['store_count'],
                        $item['shareholder_count']
                    );
                }
                echo str_repeat('-', 120) . "\n\n";
            }
        }
        
        // 获取各周期的统计信息
        echo "📈 各周期汇总统计：\n";
        echo str_repeat('=', 140) . "\n";
        printf("%-10s %-8s %-15s %-15s %-15s %-15s %-15s\n", 
            '周期', '分红人数', '总应付金额', '总调整金额', '总实际应付', '总实付金额', '总未付金额');
        echo str_repeat('-', 140) . "\n";
        
        foreach ($validPeriods as $period) {
            $statistics = DividendPaymentCalculateService::getCalculateStatistics($period);
            printf("%-10s %-8s %-15s %-15s %-15s %-15s %-15s\n",
                $period,
                $statistics['shareholder_count'],
                number_format($statistics['total_payable_amount'], 2),
                number_format($statistics['total_adjustment_amount'], 2),
                number_format($statistics['total_actual_payable_amount'], 2),
                number_format($statistics['total_paid_amount'], 2),
                number_format($statistics['total_unpaid_amount'], 2)
            );
        }
        echo str_repeat('-', 140) . "\n";
        
    } else {
        echo "❌ 批量计算失败！\n";
        echo "错误信息：{$result['message']}\n";
        
        if (!empty($result['failed_periods'])) {
            echo "\n失败的周期详情：\n";
            foreach ($result['failed_periods'] as $failed) {
                echo "周期：{$failed['period']} - 错误：{$failed['error']}\n";
            }
        }
    }
    
} catch (\Exception $e) {
    echo "❌ 脚本执行异常！\n";
    echo "异常信息：" . $e->getMessage() . "\n";
    echo "异常文件：" . $e->getFile() . " 第 " . $e->getLine() . " 行\n";
}

echo "\n================================\n";
echo "结束时间：" . date('Y-m-d H:i:s') . "\n";
echo "=== 分红人清单数据批量计算完成 ===\n";
