<?php
/**
 * @copyright Copyright (c) 2021 仲正堂工作室
 * @license https://opensource.org/licenses/GPL-3.0
 * @link https://www.gougucms.com
 */

namespace app\dividend\model;

use think\Model;

class DividendRiskReserveLog extends Model
{
    protected $name = 'dividend_risk_reserve_log';
    
    // 设置字段信息
    protected $schema = [
        'id'                          => 'int',
        'store_id'                    => 'int',
        'dividend_store_info_id'      => 'int',
        'change_type'                 => 'int',
        'before_amount'               => 'decimal',
        'change_amount'               => 'decimal',
        'after_amount'                => 'decimal',
        'dividend_detail_id'          => 'int',
        'period'                      => 'string',
        'remark'                      => 'string',
        'admin_id'                    => 'int',
        'create_time'                 => 'int',
        'update_time'                 => 'int',
    ];

    // 自动时间戳
    protected $autoWriteTimestamp = true;
    protected $createTime = 'create_time';
    protected $updateTime = 'update_time';

    // 变化类型常量
    const TYPE_PROVISION = 1;    // 计提
    const TYPE_ADJUSTMENT = 2;   // 调整
    const TYPE_INITIALIZE = 3;   // 初始化

    /**
     * 关联门店信息
     */
    public function store()
    {
        return $this->belongsTo('app\user\model\Department', 'store_id', 'id');
    }

    /**
     * 关联分红配置信息
     */
    public function dividendStoreInfo()
    {
        return $this->belongsTo('app\dividend\model\DividendStoreInfo', 'dividend_store_info_id', 'id');
    }

    /**
     * 关联分红明细信息
     */
    public function dividendStoreDetail()
    {
        return $this->belongsTo('app\dividend\model\DividendStoreDetail', 'dividend_detail_id', 'id');
    }

    /**
     * 关联操作人信息
     */
    public function admin()
    {
        return $this->belongsTo('app\user\model\Admin', 'admin_id', 'id');
    }

    /**
     * 获取变化类型文本
     * @param int $type 变化类型
     * @return string
     */
    public static function getChangeTypeText($type)
    {
        $types = [
            self::TYPE_PROVISION => '计提',
            self::TYPE_ADJUSTMENT => '调整',
            self::TYPE_INITIALIZE => '初始化'
        ];

        return $types[$type] ?? '未知类型';
    }

    /**
     * 从备注中提取周期信息（兼容旧数据）
     * @param string $remark
     * @return string
     */
    public static function extractPeriodFromRemark($remark)
    {
        if (preg_match('/计算周期：([^）]+)/', $remark, $matches)) {
            return $matches[1];
        }
        return '';
    }

    /**
     * 获取风险金变化记录列表
     * @param array $param 查询参数
     * @return array
     */
    public static function getList($param = [])
    {
        $where = [];
        
        // 门店筛选
        if (!empty($param['store_id'])) {
            $where[] = ['rrlog.store_id', '=', $param['store_id']];
        }
        
        // 变化类型筛选
        if (!empty($param['change_type'])) {
            $where[] = ['rrlog.change_type', '=', $param['change_type']];
        }
        
        // 时间范围筛选
        if (!empty($param['start_time']) && !empty($param['end_time'])) {
            $where[] = ['rrlog.create_time', 'between', [strtotime($param['start_time']), strtotime($param['end_time'] . ' 23:59:59')]];
        }

        $list = self::alias('rrlog')
            ->leftJoin('oa_department d', 'rrlog.store_id = d.id')
            ->leftJoin('oa_admin a', 'rrlog.admin_id = a.id')
            ->leftJoin('oa_dividend_store_detail dsd', 'rrlog.dividend_detail_id = dsd.id')
            ->field('rrlog.*, d.title as store_name, a.name as admin_name, dsd.period as dividend_period')
            ->where($where)
            ->order('rrlog.id desc')
            ->paginate($param['limit'] ?? 20, false, ['query' => $param])
            ->each(function ($item) {
                // 格式化变化类型
                $item['change_type_text'] = self::getChangeTypeText($item['change_type']);

                // 格式化金额
                $item['before_amount_formatted'] = number_format($item['before_amount'], 2);
                $item['after_amount_formatted'] = number_format($item['after_amount'], 2);
                $item['change_amount_formatted'] = number_format($item['change_amount'], 2);

                // 优先使用period字段，如果为空则从备注中提取（兼容旧数据）
                if (empty($item['period']) && !empty($item['remark']) && strpos($item['remark'], '计算周期：') !== false) {
                    $item['period'] = self::extractPeriodFromRemark($item['remark']);
                }
                
                // 格式化时间
                $item['create_time_formatted'] = date('Y-m-d H:i:s', $item['create_time']);
                
                // 操作人处理
                if (empty($item['admin_name'])) {
                    $item['admin_name'] = $item['admin_id'] == 0 ? '系统自动' : '未知用户';
                }
                
                return $item;
            });

        return $list;
    }

    /**
     * 获取门店风险金变化记录
     * @param int $storeId 门店ID
     * @param int $dividendStoreInfoId 分红配置ID
     * @return array
     */
    public static function getStoreLogList($storeId, $dividendStoreInfoId)
    {
        $list = self::alias('rrlog')
            ->leftJoin('oa_admin a', 'rrlog.admin_id = a.id')
            ->leftJoin('oa_dividend_store_detail dsd', 'rrlog.dividend_detail_id = dsd.id')
            ->field('rrlog.*, a.name as admin_name, dsd.period as dividend_period')
            ->where('rrlog.store_id', $storeId)
            ->where('rrlog.dividend_store_info_id', $dividendStoreInfoId)
            ->order('rrlog.id desc')
            ->select()
            ->each(function ($item) {
                // 格式化变化类型
                $item['change_type_text'] = self::getChangeTypeText($item['change_type']);

                // 优先使用period字段，如果为空则从备注中提取（兼容旧数据）
                if (empty($item['period']) && !empty($item['remark']) && strpos($item['remark'], '计算周期：') !== false) {
                    $item['period'] = self::extractPeriodFromRemark($item['remark']);
                }
                
                // 格式化金额
                $item['before_amount_formatted'] = number_format($item['before_amount'], 2);
                $item['after_amount_formatted'] = number_format($item['after_amount'], 2);
                $item['change_amount_formatted'] = number_format($item['change_amount'], 2);
                
                // 格式化时间
                $item['create_time_formatted'] = date('Y-m-d H:i:s', $item['create_time']);
                
                // 操作人处理
                if (empty($item['admin_name'])) {
                    $item['admin_name'] = $item['admin_id'] == 0 ? '系统自动' : '未知用户';
                }
                
                return $item;
            });

        return $list->toArray();
    }
}
