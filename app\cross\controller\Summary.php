<?php
/**
 * @copyright Copyright (c) 2021 仲正堂工作室
 * @license https://opensource.org/licenses/GPL-3.0
 * @link https://www.gougucms.com
 */

declare (strict_types=1);

namespace app\cross\controller;

use app\base\BaseController;
use app\cross\model\CrossStoreSettleSummary;
use app\cross\validate\CrossStoreSettleSummaryCheck;
use think\exception\ValidateException;
use think\facade\Db;
use think\facade\View;
use think\facade\Log;
use think\App;

class Summary extends BaseController
{
    public function __construct(App $app)
    {
        parent::__construct($app);
    }

    /**
     * 跨店结算汇总列表
     */
    public function index()
    {
        if (request()->isAjax()) {
            $param = get_params();

            // 构建查询条件
            $where = [];

            // 按月份筛选
            if (!empty($param['period'])) {
                $where[] = ['css.period', '=', $param['period']];
            }

            // 按门店筛选
            if (!empty($param['store_ids']) && is_array($param['store_ids'])) {
                $where[] = ['css.store_id', 'in', $param['store_ids']];
            }

            // 按他店筛选
            if (!empty($param['other_store_ids']) && is_array($param['other_store_ids'])) {
                $where[] = ['css.other_store_id', 'in', $param['other_store_ids']];
            }

            // 分页查询
            $rows = empty($param['limit']) ? 20 : intval($param['limit']);

            $query = Db::name('CrossStoreSettleSummary')
                ->alias('css')
                ->join('oa_department d1', 'd1.id = css.store_id', 'LEFT')
                ->join('oa_department d2', 'd2.id = css.other_store_id', 'LEFT');

            $list = $query->field('css.*, d1.title as store_name, d2.title as other_store_name')
                ->where($where)
                ->order('css.id desc')
                ->paginate($rows, false, ['query' => $param])
                ->each(function ($item) {
                    // 格式化总对账金额
                    $item['total_reconciliation_amount_formatted'] = number_format(floatval($item['total_reconciliation_amount']), 2);

                    // 格式化储值本金部分
                    $item['p_local_consume_foreign_formatted'] = number_format(floatval($item['p_local_consume_foreign']), 2);
                    $item['p_local_refund_foreign_formatted'] = number_format(floatval($item['p_local_refund_foreign']), 2);
                    $item['p_foreign_consume_local_formatted'] = number_format(floatval($item['p_foreign_consume_local']), 2);
                    $item['p_foreign_refund_local_formatted'] = number_format(floatval($item['p_foreign_refund_local']), 2);
                    $item['p_total_amount_formatted'] = number_format(floatval($item['p_total_amount']), 2);
                    $item['p_reconciliation_amount_formatted'] = number_format(floatval($item['p_reconciliation_amount']), 2);

                    // 格式化储值赠金部分
                    $item['b_local_consume_foreign_formatted'] = number_format(floatval($item['b_local_consume_foreign']), 2);
                    $item['b_local_refund_foreign_formatted'] = number_format(floatval($item['b_local_refund_foreign']), 2);
                    $item['b_foreign_consume_local_formatted'] = number_format(floatval($item['b_foreign_consume_local']), 2);
                    $item['b_foreign_refund_local_formatted'] = number_format(floatval($item['b_foreign_refund_local']), 2);
                    $item['b_total_amount_formatted'] = number_format(floatval($item['b_total_amount']), 2);
                    $item['b_reconciliation_amount_formatted'] = number_format(floatval($item['b_reconciliation_amount']), 2);

                    // 格式化次卡部分金额
                    $item['cc_local_upgrade_foreign_amount_formatted'] = number_format(floatval($item['cc_local_upgrade_foreign_amount']), 2);
                    $item['cc_foreign_upgrade_local_amount_formatted'] = number_format(floatval($item['cc_foreign_upgrade_local_amount']), 2);
                    $item['cc_total_amount_formatted'] = number_format(floatval($item['cc_total_amount']), 2);
                    $item['cc_reconciliation_amount_formatted'] = number_format(floatval($item['cc_reconciliation_amount']), 2);

                    // 格式化网店核销部分
                    $item['ol_total_amount_formatted'] = number_format(floatval($item['ol_total_amount']), 2);
                    $item['ol_reconciliation_amount_formatted'] = number_format(floatval($item['ol_reconciliation_amount']), 2);

                    // 确保count字段数据类型一致性，避免LayUI LAY_NUM错误
                    $item['cc_local_consume_foreign_count'] = intval($item['cc_local_consume_foreign_count'] ?? 0);
                    $item['cc_local_refund_foreign_count'] = intval($item['cc_local_refund_foreign_count'] ?? 0);
                    $item['cc_foreign_consume_local_count'] = intval($item['cc_foreign_consume_local_count'] ?? 0);
                    $item['cc_foreign_refund_local_count'] = intval($item['cc_foreign_refund_local_count'] ?? 0);
                    $item['ol_local_redeem_foreign_count'] = intval($item['ol_local_redeem_foreign_count'] ?? 0);
                    $item['ol_local_refund_foreign_count'] = intval($item['ol_local_refund_foreign_count'] ?? 0);
                    $item['ol_foreign_redeem_local_count'] = intval($item['ol_foreign_redeem_local_count'] ?? 0);
                    $item['ol_foreign_refund_local_count'] = intval($item['ol_foreign_refund_local_count'] ?? 0);
                    
                    // 重要：必须返回修改后的数据，否则each()会返回null
                    return $item;
                });

            // 计算合计数据
            $totalRowData = [];
            if (count($list) > 0) {
                $totalQuery = Db::name('CrossStoreSettleSummary')
                    ->alias('css')
                    ->where($where);

                $totalData = $totalQuery->field([
                    'SUM(total_reconciliation_amount) as total_reconciliation',
                    // 储值本金部分
                    'SUM(p_local_consume_foreign) as total_p_local_consume_foreign',
                    'SUM(p_local_refund_foreign) as total_p_local_refund_foreign',
                    'SUM(p_foreign_consume_local) as total_p_foreign_consume_local',
                    'SUM(p_foreign_refund_local) as total_p_foreign_refund_local',
                    'SUM(p_total_amount) as total_p_amount',
                    'SUM(p_reconciliation_amount) as total_p_reconciliation',
                    // 储值赠金部分
                    'SUM(b_local_consume_foreign) as total_b_local_consume_foreign',
                    'SUM(b_local_refund_foreign) as total_b_local_refund_foreign',
                    'SUM(b_foreign_consume_local) as total_b_foreign_consume_local',
                    'SUM(b_foreign_refund_local) as total_b_foreign_refund_local',
                    'SUM(b_total_amount) as total_b_amount',
                    'SUM(b_reconciliation_amount) as total_b_reconciliation',
                    // 次卡部分
                    'SUM(cc_local_consume_foreign_count) as total_cc_local_consume_foreign_count',
                    'SUM(cc_local_refund_foreign_count) as total_cc_local_refund_foreign_count',
                    'SUM(cc_foreign_consume_local_count) as total_cc_foreign_consume_local_count',
                    'SUM(cc_foreign_refund_local_count) as total_cc_foreign_refund_local_count',
                    'SUM(cc_local_upgrade_foreign_amount) as total_cc_local_upgrade_foreign_amount',
                    'SUM(cc_foreign_upgrade_local_amount) as total_cc_foreign_upgrade_local_amount',
                    'SUM(cc_total_amount) as total_cc_amount',
                    'SUM(cc_reconciliation_amount) as total_cc_reconciliation',
                    // 网店核销部分
                    'SUM(ol_local_redeem_foreign_count) as total_ol_local_redeem_foreign_count',
                    'SUM(ol_local_refund_foreign_count) as total_ol_local_refund_foreign_count',
                    'SUM(ol_foreign_redeem_local_count) as total_ol_foreign_redeem_local_count',
                    'SUM(ol_foreign_refund_local_count) as total_ol_foreign_refund_local_count',
                    'SUM(ol_total_amount) as total_ol_amount',
                    'SUM(ol_reconciliation_amount) as total_ol_reconciliation'
                ])->find();

                $totalRowData = [
                    // 基础字段（为了保持数据结构一致性）
                    'id' => '', // 确保id字段存在，避免LayUI LAY_NUM错误
                    'store_name' => '', // 合计行显示文本由前端 totalRowText 控制
                    'store_type' => '',
                    'other_store_name' => '',
                    'other_store_type' => '',
                    'total_reconciliation_amount_formatted' => number_format(floatval($totalData['total_reconciliation'] ?? 0), 2),
                    // 储值本金部分
                    'p_local_consume_foreign_formatted' => number_format(floatval($totalData['total_p_local_consume_foreign'] ?? 0), 2),
                    'p_local_refund_foreign_formatted' => number_format(floatval($totalData['total_p_local_refund_foreign'] ?? 0), 2),
                    'p_foreign_consume_local_formatted' => number_format(floatval($totalData['total_p_foreign_consume_local'] ?? 0), 2),
                    'p_foreign_refund_local_formatted' => number_format(floatval($totalData['total_p_foreign_refund_local'] ?? 0), 2),
                    'p_total_amount_formatted' => number_format(floatval($totalData['total_p_amount'] ?? 0), 2),
                    'p_reconciliation_amount_formatted' => number_format(floatval($totalData['total_p_reconciliation'] ?? 0), 2),
                    // 储值赠金部分
                    'b_local_consume_foreign_formatted' => number_format(floatval($totalData['total_b_local_consume_foreign'] ?? 0), 2),
                    'b_local_refund_foreign_formatted' => number_format(floatval($totalData['total_b_local_refund_foreign'] ?? 0), 2),
                    'b_foreign_consume_local_formatted' => number_format(floatval($totalData['total_b_foreign_consume_local'] ?? 0), 2),
                    'b_foreign_refund_local_formatted' => number_format(floatval($totalData['total_b_foreign_refund_local'] ?? 0), 2),
                    'b_total_amount_formatted' => number_format(floatval($totalData['total_b_amount'] ?? 0), 2),
                    'b_reconciliation_amount_formatted' => number_format(floatval($totalData['total_b_reconciliation'] ?? 0), 2),
                    // 次卡部分
                    'cc_local_consume_foreign_count' => intval($totalData['total_cc_local_consume_foreign_count'] ?? 0),
                    'cc_local_refund_foreign_count' => intval($totalData['total_cc_local_refund_foreign_count'] ?? 0),
                    'cc_foreign_consume_local_count' => intval($totalData['total_cc_foreign_consume_local_count'] ?? 0),
                    'cc_foreign_refund_local_count' => intval($totalData['total_cc_foreign_refund_local_count'] ?? 0),
                    'cc_local_upgrade_foreign_amount_formatted' => number_format(floatval($totalData['total_cc_local_upgrade_foreign_amount'] ?? 0), 2),
                    'cc_foreign_upgrade_local_amount_formatted' => number_format(floatval($totalData['total_cc_foreign_upgrade_local_amount'] ?? 0), 2),
                    'cc_total_amount_formatted' => number_format(floatval($totalData['total_cc_amount'] ?? 0), 2),
                    'cc_reconciliation_amount_formatted' => number_format(floatval($totalData['total_cc_reconciliation'] ?? 0), 2),
                    // 网店核销部分
                    'ol_local_redeem_foreign_count' => intval($totalData['total_ol_local_redeem_foreign_count'] ?? 0),
                    'ol_local_refund_foreign_count' => intval($totalData['total_ol_local_refund_foreign_count'] ?? 0),
                    'ol_foreign_redeem_local_count' => intval($totalData['total_ol_foreign_redeem_local_count'] ?? 0),
                    'ol_foreign_refund_local_count' => intval($totalData['total_ol_foreign_refund_local_count'] ?? 0),
                    'ol_total_amount_formatted' => number_format(floatval($totalData['total_ol_amount'] ?? 0), 2),
                    'ol_reconciliation_amount_formatted' => number_format(floatval($totalData['total_ol_reconciliation'] ?? 0), 2)
                ];
            }

            // 转换分页数据为数组格式
            $listArray = $list->toArray();

            return [
                'code' => 0,
                'msg' => '',
                'count' => $listArray['total'],
                'data' => $listArray['data'],
                'totalRow' => $totalRowData
            ];
        } else {
            // 记录查看跨店结算汇总列表页面日志
            add_log('view', 0, [], '[跨店结算]-[跨店结算汇总表]：查看跨店结算汇总列表页面');
            Log::info('[跨店结算]-[跨店结算汇总表]：用户查看跨店结算汇总列表页面，用户ID：' . $this->uid);
            return view();
        }
    }

    /**
     * 查看跨店结算汇总详情（只读模式）
     */
    public function view()
    {
        $param = get_params();
        $id = isset($param['id']) ? $param['id'] : 0;

        if (empty($id)) {
            return to_assign(1, '参数错误');
        }

        // 获取详情信息
        $detail = Db::name('CrossStoreSettleSummary')
            ->alias('css')
            ->join('oa_department d1', 'd1.id = css.store_id', 'LEFT')
            ->join('oa_department d2', 'd2.id = css.other_store_id', 'LEFT')
            ->field('css.*, d1.title as store_name, d2.title as other_store_name, css.store_type, css.other_store_type')
            ->where('css.id', $id)
            ->find();

        if (empty($detail)) {
            return to_assign(1, '数据不存在');
        }

        View::assign('detail', $detail);

        // 记录查看跨店结算汇总详情日志
        add_log('view', $id, ['period' => $detail['period'], 'store_name' => $detail['store_name'], 'other_store_name' => $detail['other_store_name']], '[跨店结算]-[跨店结算汇总表]：查看跨店结算汇总详情');
        Log::info('[跨店结算]-[跨店结算汇总表]：用户查看跨店结算汇总详情，ID：' . $id . '，用户ID：' . $this->uid);

        return view();
    }

    /**
     * 编辑跨店结算汇总信息
     */
    public function edit()
    {
        $param = get_params();

        if (request()->isPost()) {
            try {
                validate(CrossStoreSettleSummaryCheck::class)->scene('edit')->check($param);
            } catch (ValidateException $e) {
                return to_assign(1, $e->getError());
            }

            $id = intval($param['id']);
            if (empty($id)) {
                return to_assign(1, '参数错误');
            }

            // 检查记录是否存在
            $existRecord = Db::name('CrossStoreSettleSummary')->where('id', $id)->find();
            if (empty($existRecord)) {
                return to_assign(1, '记录不存在');
            }

            // 检查门店配对和月份是否重复（排除当前记录）
            if (CrossStoreSettleSummary::checkExists($param['period'], $param['store_id'], $param['other_store_id'], $id)) {
                return to_assign(1, '该门店配对在此月份已存在记录');
            }

            try {
                // 准备更新数据
                $updateData = [
                    'period' => $param['period'],
                    'store_id' => $param['store_id'],
                    'store_type' => $param['store_type'],
                    'other_store_id' => $param['other_store_id'],
                    'other_store_type' => $param['other_store_type'],
                    'total_reconciliation_amount' => $param['total_reconciliation_amount'] ?? 0,

                    // 储值本金部分
                    'p_local_consume_foreign' => $param['p_local_consume_foreign'] ?? 0,
                    'p_local_refund_foreign' => $param['p_local_refund_foreign'] ?? 0,
                    'p_foreign_consume_local' => $param['p_foreign_consume_local'] ?? 0,
                    'p_foreign_refund_local' => $param['p_foreign_refund_local'] ?? 0,
                    'p_total_amount' => $param['p_total_amount'] ?? 0,
                    'p_reconciliation_amount' => $param['p_reconciliation_amount'] ?? 0,

                    // 储值赠金部分
                    'b_local_consume_foreign' => $param['b_local_consume_foreign'] ?? 0,
                    'b_local_refund_foreign' => $param['b_local_refund_foreign'] ?? 0,
                    'b_foreign_consume_local' => $param['b_foreign_consume_local'] ?? 0,
                    'b_foreign_refund_local' => $param['b_foreign_refund_local'] ?? 0,
                    'b_total_amount' => $param['b_total_amount'] ?? 0,
                    'b_reconciliation_amount' => $param['b_reconciliation_amount'] ?? 0,

                    // 次卡部分
                    'cc_local_consume_foreign_count' => $param['cc_local_consume_foreign_count'] ?? 0,
                    'cc_local_refund_foreign_count' => $param['cc_local_refund_foreign_count'] ?? 0,
                    'cc_foreign_consume_local_count' => $param['cc_foreign_consume_local_count'] ?? 0,
                    'cc_foreign_refund_local_count' => $param['cc_foreign_refund_local_count'] ?? 0,
                    'cc_local_upgrade_foreign_amount' => $param['cc_local_upgrade_foreign_amount'] ?? 0,
                    'cc_foreign_upgrade_local_amount' => $param['cc_foreign_upgrade_local_amount'] ?? 0,
                    'cc_total_amount' => $param['cc_total_amount'] ?? 0,
                    'cc_reconciliation_amount' => $param['cc_reconciliation_amount'] ?? 0,

                    // 网店核销部分
                    'ol_local_redeem_foreign_count' => $param['ol_local_redeem_foreign_count'] ?? 0,
                    'ol_local_refund_foreign_count' => $param['ol_local_refund_foreign_count'] ?? 0,
                    'ol_foreign_redeem_local_count' => $param['ol_foreign_redeem_local_count'] ?? 0,
                    'ol_foreign_refund_local_count' => $param['ol_foreign_refund_local_count'] ?? 0,
                    'ol_total_amount' => $param['ol_total_amount'] ?? 0,
                    'ol_reconciliation_amount' => $param['ol_reconciliation_amount'] ?? 0,

                    'update_time' => time()
                ];

                $res = Db::name('CrossStoreSettleSummary')->where('id', $id)->update($updateData);

                if ($res) {
                    // 记录操作日志
                    add_log('edit', $id, $param, '[跨店结算]-[跨店结算汇总表]：编辑跨店结算汇总信息');
                    Log::info('[跨店结算]-[跨店结算汇总表]：编辑跨店结算汇总信息成功，ID：' . $id . '，用户ID：' . $this->uid);
                    return to_assign(0, '保存成功');
                } else {
                    return to_assign(1, '保存失败');
                }
            } catch (\think\exception\HttpResponseException $e) {
                // 重新抛出HttpResponseException，这是to_assign函数正常的响应机制
                throw $e;
            } catch (\Exception $e) {
                Log::error('[跨店结算]-[跨店结算汇总表]：编辑跨店结算汇总信息失败，错误信息：' . $e->getMessage() . '，用户ID：' . $this->uid);
                return to_assign(1, '保存失败：' . $e->getMessage());
            }
        } else {
            // GET请求，显示编辑表单
            $id = isset($param['id']) ? $param['id'] : 0;
            if (empty($id)) {
                return to_assign(1, '参数错误');
            }

            $detail = CrossStoreSettleSummary::getDetail($id);
            if (empty($detail)) {
                return to_assign(1, '数据不存在');
            }

            // 获取门店列表（只包含门店）
            $storeList = Db::name('Department')
                ->where('status', 1)
                ->where('remark', '门店')
                ->select()
                ->toArray();
            
            View::assign('detail', $detail);
            View::assign('store_list', $storeList);

            return view();
        }
    }
}
