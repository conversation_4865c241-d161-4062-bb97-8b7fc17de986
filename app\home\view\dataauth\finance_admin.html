{extend name="../../base/view/common/base" /}
<!-- 主体 -->
{block name="body"}
<form class="layui-form p-4">
	<h3 class="pb-1">权限配置</h3>
	<table class="layui-table layui-table-form">
		<tr>
			<td colspan="4" class="red" style="line-height:1.8">
				<p><strong>项目模块使用说明：</strong></p>
				<p>{$detail.desc}</p>
			</td>
		</tr>
		<tr>
			<td class="layui-td-gray">权限名称</td>
			<td>
				<input type="hidden" name="id" value="{$detail.id}" />
				{$detail.title}
			</td>
			<td class="layui-td-gray">权限标识</td>
			<td>{$detail.name}</td>
		</tr>
		<tr>
			<td class="layui-td-gray-2">报销管理员</td>
			<td colspan="3">
				<input type="text" name="unames" value="{$detail.unames}" readonly placeholder="请选择报销管理员" autocomplete="off" class="layui-input picker-more">
				<input type="hidden" id="uids" name="uids" value="{$detail.uids}">
			</td>
		</tr>
		<tr>
			<td class="layui-td-gray-2">发票管理员</td>
			<td colspan="3">
			<input type="text" name="conf_1_str" value="{$detail.conf_1_str}" readonly placeholder="请选择发票管理员" autocomplete="off" class="layui-input picker-more">
			<input type="hidden" name="conf_1" value="{$detail.conf_1}">
			</td>
		</tr>
		<tr>
			<td class="layui-td-gray-2">到账管理员</td>
			<td colspan="3">
			<input type="text" name="conf_2_str" value="{$detail.conf_2_str}" readonly placeholder="请选择到账管理员" autocomplete="off" class="layui-input picker-more">
			<input type="hidden" name="conf_2" value="{$detail.conf_2}">
			</td>
		</tr>
	</table>
	<div class="pt-1">
		<button class="layui-btn layui-btn-normal" lay-submit="" lay-filter="webform">立即提交</button>
		<button type="reset" class="layui-btn layui-btn-primary">重置</button>
	</div>
</form>
{/block}
<!-- /主体 -->

<!-- 脚本 -->
{block name="script"}
<script>
	const moduleInit = ['tool','employeepicker'];
	function gouguInit() {
		var form = layui.form, tool = layui.tool;
		
		//监听提交
		form.on('submit(webform)', function (data) {
			let callback = function (e) {
				layer.msg(e.msg);
				if(e.code==0){
					tool.sideClose(1000);
				}
			}
			tool.post("/home/<USER>/edit", data.field, callback);
			return false;
		});
	}
</script>
{/block}
<!-- /脚本 -->