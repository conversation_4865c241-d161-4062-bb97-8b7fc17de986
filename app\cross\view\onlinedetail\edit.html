{extend name="../../base/view/common/base" /}

{block name="style"}
<style>
    /* 编辑页面样式 - 参考balance/edit.html */
    .layui-card {
        border: 1px solid #e6e6e6;
        border-radius: 4px;
        margin-bottom: 15px;
    }

    .layui-card-header {
        background-color: #f8f8f8;
        padding: 10px 15px;
        border-bottom: 1px solid #e6e6e6;
        font-weight: bold;
    }

    .layui-card-body {
        padding: 15px;
    }

    .layui-td-gray {
        background-color: #f5f5f5;
        font-weight: bold;
        text-align: left;
        vertical-align: middle;
        width: 140px;
        padding: 8px 12px;
    }

    /* 表格样式优化 */
    .layui-table {
        margin-bottom: 0;
    }

    .layui-table td {
        padding: 8px 12px;
        vertical-align: middle;
    }

    .layui-table th {
        background-color: #f8f8f8;
        font-weight: bold;
    }

    /* 必填项红色星号 */
    .required-mark {
        color: #FF6347;
        margin-left: 3px;
    }

    /* 页面内容区域 */
    .edit-content {
        padding: 20px;
        height: 100%;
        box-sizing: border-box;
        overflow-y: auto;
    }

    /* 金额输入框样式 */
    .amount-input {
        text-align: right;
        font-weight: bold;
    }

    /* 所有数字输入框的通用样式 */
    input[type="number"] {
        font-weight: bold;
        text-align: right;
    }

    /* 表格中的数字显示样式 */
    .layui-table td span {
        font-weight: bold;
    }

    /* 系统提示信息样式 */
    .system-info {
        color: #999;
        font-size: 12px;
        font-style: italic;
        padding: 5px 0;
    }

    /* xmselect组件样式优化 */
    .xm-select {
        width: 100%;
    }
</style>
{/block}

{block name="body"}
<div class="edit-content">
    <!-- 页面标题 -->
    <div style="margin-bottom: 20px;">
        <h3 style="margin: 0 0 15px 0; font-size: 18px; font-weight: bold; color: #333;">编辑网店订单跨店核销明细</h3>
    </div>

    <form class="layui-form" lay-filter="editForm">
        <input type="hidden" name="id" value="{$detail.id}">

        <!-- 基础信息 -->
        <div class="layui-card">
            <div class="layui-card-header">基础信息</div>
            <div class="layui-card-body">
                <table class="layui-table">
                    <tr>
                        <td class="layui-td-gray">快照月份<span class="required-mark">*</span></td>
                        <td style="width: 200px;">
                            <input type="text" name="period" id="period" value="{$detail.period|default=''}"
                                   placeholder="请选择月份" autocomplete="off" class="layui-input" lay-verify="required">
                        </td>
                        <td class="layui-td-gray">结算类型<span class="required-mark">*</span></td>
                        <td style="width: 200px;">
                            <select name="settlement_type" lay-verify="required">
                                <option value="">请选择结算类型</option>
                                <option value="网店订单跨店核销" {if $detail.settlement_type == '网店订单跨店核销'}selected{/if}>网店订单跨店核销</option>
                                <option value="网店订单跨店核销退款" {if $detail.settlement_type == '网店订单跨店核销退款'}selected{/if}>网店订单跨店核销退款</option>
                            </select>
                        </td>
                    </tr>
                    <tr>
                        <td class="layui-td-gray">核销门店<span class="required-mark">*</span></td>
                        <td>
                            <div id="verification_store_select"></div>
                            <input type="hidden" name="verification_store_id" value="{$detail.verification_store_id}">
                        </td>
                        <td class="layui-td-gray">下单门店<span class="required-mark">*</span></td>
                        <td>
                            <div id="order_store_select"></div>
                            <input type="hidden" name="order_store_id" value="{$detail.order_store_id}">
                        </td>
                    </tr>
                    <tr>
                        <td class="layui-td-gray">结算时间</td>
                        <td colspan="3">
                            <input type="text" name="settlement_time" id="settlement_time"
                                   value="{$detail.settlement_time|default=''}"
                                   placeholder="请选择结算时间" autocomplete="off" class="layui-input">
                        </td>
                    </tr>
                </table>
            </div>
        </div>

        <!-- 商品信息 -->
        <div class="layui-card">
            <div class="layui-card-header">商品信息</div>
            <div class="layui-card-body">
                <table class="layui-table">
                    <tr>
                        <td class="layui-td-gray">商品名称<span class="required-mark">*</span></td>
                        <td style="width: 200px;">
                            <input type="text" name="product_name" value="{$detail.product_name|default=''}"
                                   lay-verify="required" placeholder="请输入商品名称" autocomplete="off" class="layui-input">
                        </td>
                        <td class="layui-td-gray">商品数量<span class="required-mark">*</span></td>
                        <td style="width: 200px;">
                            <input type="number" name="product_quantity" value="{$detail.product_quantity|default=0}"
                                   lay-verify="required|number" placeholder="请输入商品数量" autocomplete="off" class="layui-input" min="0">
                        </td>
                    </tr>
                </table>
            </div>
        </div>

        <!-- 金额信息 -->
        <div class="layui-card">
            <div class="layui-card-header">金额信息</div>
            <div class="layui-card-body">
                <table class="layui-table">
                    <tr>
                        <td class="layui-td-gray">结算金额<span class="required-mark">*</span></td>
                        <td style="width: 200px;">
                            <input type="number" name="settlement_amount" value="{$detail.settlement_amount|default=0}"
                                   lay-verify="required|number" placeholder="请输入结算金额" autocomplete="off"
                                   class="layui-input amount-input" step="0.01" min="0">
                        </td>
                        <td class="layui-td-gray">本金支付金额<span class="required-mark">*</span></td>
                        <td style="width: 200px;">
                            <input type="number" name="principal_payment_amount" value="{$detail.principal_payment_amount|default=0}"
                                   lay-verify="required|number" placeholder="请输入本金支付金额" autocomplete="off"
                                   class="layui-input amount-input" step="0.01" min="0">
                        </td>
                    </tr>
                    <tr>
                        <td class="layui-td-gray">赠金支付金额<span class="required-mark">*</span></td>
                        <td>
                            <input type="number" name="bonus_payment_amount" value="{$detail.bonus_payment_amount|default=0}"
                                   lay-verify="required|number" placeholder="请输入赠金支付金额" autocomplete="off"
                                   class="layui-input amount-input" step="0.01" min="0">
                        </td>
                        <td class="layui-td-gray">现金类支付金额<span class="required-mark">*</span></td>
                        <td>
                            <input type="number" name="cash_payment_amount" value="{$detail.cash_payment_amount|default=0}"
                                   lay-verify="required|number" placeholder="请输入现金类支付金额" autocomplete="off"
                                   class="layui-input amount-input" step="0.01" min="0">
                        </td>
                    </tr>
                    <tr>
                        <td class="layui-td-gray">次卡支付金额<span class="required-mark">*</span></td>
                        <td colspan="3">
                            <input type="number" name="card_payment_amount" value="{$detail.card_payment_amount|default=0}"
                                   lay-verify="required|number" placeholder="请输入次卡支付金额" autocomplete="off"
                                   class="layui-input amount-input" step="0.01" min="0">
                        </td>
                    </tr>
                </table>
            </div>
        </div>

        <!-- 订单信息 -->
        <div class="layui-card">
            <div class="layui-card-header">订单信息</div>
            <div class="layui-card-body">
                <table class="layui-table">
                    <tr>
                        <td class="layui-td-gray">核销订单号<span class="required-mark">*</span></td>
                        <td style="width: 200px;">
                            <input type="text" name="verification_order_number" value="{$detail.verification_order_number|default=''}"
                                   lay-verify="required" placeholder="请输入核销订单号" autocomplete="off" class="layui-input">
                        </td>
                        <td class="layui-td-gray">购买订单号<span class="required-mark">*</span></td>
                        <td style="width: 200px;">
                            <input type="text" name="purchase_order_number" value="{$detail.purchase_order_number|default=''}"
                                   lay-verify="required" placeholder="请输入购买订单号" autocomplete="off" class="layui-input">
                        </td>
                    </tr>
                </table>
            </div>
        </div>

        <!-- 客户信息 -->
        <div class="layui-card">
            <div class="layui-card-header">客户信息</div>
            <div class="layui-card-body">
                <table class="layui-table">
                    <tr>
                        <td class="layui-td-gray">客户姓名<span class="required-mark">*</span></td>
                        <td style="width: 200px;">
                            <input type="text" name="customer_name" value="{$detail.customer_name|default=''}"
                                   lay-verify="required" placeholder="请输入客户姓名" autocomplete="off" class="layui-input">
                        </td>
                        <td class="layui-td-gray">客户手机号<span class="required-mark">*</span></td>
                        <td style="width: 200px;">
                            <input type="text" name="customer_mobile" value="{$detail.customer_mobile|default=''}"
                                   lay-verify="required|phone" placeholder="请输入客户手机号" autocomplete="off" class="layui-input">
                        </td>
                    </tr>
                    <tr>
                        <td class="layui-td-gray">归属门店</td>
                        <td colspan="3">
                            <div id="customer_belong_store_select"></div>
                            <input type="hidden" name="customer_belong_store_id" value="{$detail.customer_belong_store_id}">
                        </td>
                    </tr>
                </table>
            </div>
        </div>

        <!-- 提交按钮 -->
        <div style="margin-top: 15px;">
            <button class="layui-btn layui-btn-normal" lay-submit lay-filter="editForm">保存修改</button>
            <button type="button" class="layui-btn layui-btn-primary" onclick="parent.layer.closeAll()">取消</button>
        </div>
    </form>
</div>
{/block}

{block name="script"}
<script src="/static/assets/xm-select.js"></script>
<script>
    const moduleInit = ['form'];
    function gouguInit() {
        var form = layui.form, laydate = layui.laydate;

        // 初始化日期时间选择器
        laydate.render({
            elem: '#settlement_time',
            type: 'datetime',
            format: 'yyyy-MM-dd HH:mm:ss'
        });

        // 初始化月份选择器
        laydate.render({
            elem: '#period',
            type: 'month',
            format: 'yyyy-MM'
        });

        // 门店数据
        var storeData = [
            {volist name="store_list" id="store"}
            {name: '{$store.title}', value: {$store.id}},
            {/volist}
        ];

        // 初始化核销门店选择器
        var verificationStoreSelect = xmSelect.render({
            el: '#verification_store_select',
            data: storeData,
            searchTips: '请搜索门店名称',
            empty: '暂无门店数据',
            radio: true,
            clickClose: true,
            filterable: true,
            on: function(data) {
                var value = data.arr.length > 0 ? data.arr[0].value : '';
                $('input[name="verification_store_id"]').val(value);
            }
        });

        // 初始化下单门店选择器
        var orderStoreSelect = xmSelect.render({
            el: '#order_store_select',
            data: storeData,
            searchTips: '请搜索门店名称',
            empty: '暂无门店数据',
            radio: true,
            clickClose: true,
            filterable: true,
            on: function(data) {
                var value = data.arr.length > 0 ? data.arr[0].value : '';
                $('input[name="order_store_id"]').val(value);
            }
        });

        // 初始化客户归属门店选择器
        var customerBelongStoreSelect = xmSelect.render({
            el: '#customer_belong_store_select',
            data: storeData,
            searchTips: '请搜索门店名称',
            empty: '暂无门店数据',
            radio: true,
            clickClose: true,
            filterable: true,
            on: function(data) {
                var value = data.arr.length > 0 ? data.arr[0].value : '';
                $('input[name="customer_belong_store_id"]').val(value);
            }
        });

        // 设置默认选中值
        var verificationStoreId = $('input[name="verification_store_id"]').val();
        if (verificationStoreId) {
            verificationStoreSelect.setValue([verificationStoreId]);
        }

        var orderStoreId = $('input[name="order_store_id"]').val();
        if (orderStoreId) {
            orderStoreSelect.setValue([orderStoreId]);
        }

        var customerBelongStoreId = $('input[name="customer_belong_store_id"]').val();
        if (customerBelongStoreId && customerBelongStoreId != '0') {
            customerBelongStoreSelect.setValue([customerBelongStoreId]);
        }

        // 表单提交
        form.on('submit(editForm)', function(data) {
            var loadIndex = layer.load(2, {shade: 0.3});

            $.post('/cross/onlinedetail/edit', data.field, function(res) {
                layer.close(loadIndex);
                if (res.code == 0) {
                    layer.msg(res.msg, {icon: 1, time: 2000}, function() {
                        parent.layer.closeAll();
                        // 触发父页面刷新
                        if (parent.layui && parent.layui.table) {
                            parent.layui.table.reload('dataTable');
                        }
                    });
                } else {
                    layer.msg(res.msg, {icon: 2, time: 3000});
                }
            }).fail(function() {
                layer.close(loadIndex);
                layer.msg('网络错误，请重试', {icon: 2});
            });

            return false;
        });

        // 表单渲染
        form.render();
    }
</script>
{/block}