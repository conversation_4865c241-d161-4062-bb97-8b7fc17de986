{extend name="../../base/view/common/base" /}
{block name="style"}
<style>
html{background-color:#fff;}
.log-timeline{ position: relative; min-height:600px; padding-left: 48px; background-color:#fff;}
.log-timeline:after {content: ""; position: absolute; top: 0; left: 24px; width: 1px; height: 100%; background: #e3e9ed;}
.log-timeline dl{padding-bottom: 8px; position: relative;}
.log-timeline dt{font-size: 16px; line-height: 2.4; color: #323232; font-weight:600}
.log-timeline dd{font-size: 14px; line-height: 1.6; padding:5px 0}
.log-timeline .date-second-point{width: 10px; height: 10px; display: block; border-radius: 50%; border: 3px solid #FBBC05; background: #fff; position: absolute; z-index: 99; left:-32px; top:9px}
.log-timeline .log-thumb{width: 24px; height: 24px; border-radius: 50%; margin-right:4px;}
.log-timeline .open-a{margin:0 4px;}
.log-item i{font-weight:800; color:#323232}
.log-content strong{margin:0 4px; color:#323232}
</style>
{/block}
<!-- 主体 -->
{block name="body"}
<form class="layui-form px-4 pt-2" lay-filter="contract">
	<div class="layui-tab" style="margin:0;" lay-filter="contract" id="contractTab">
	  <ul class="layui-tab-title">
		<li class="layui-this" data-load="true">合同详情</li>
		<li>操作记录</li>
	  </ul>
	  <div class="layui-tab-content">
		<div class="layui-tab-item layui-show">	
			<table class="layui-table layui-table-form" style="margin:0">
				{gt name="$detail.pid" value="0"}
				 <tr>
					<td class="layui-td-gray">母合同名称</td>
					<td colspan="7">{$detail.pname}</td>
				  </tr>
				{/gt}
			  <tr>
				<td class="layui-td-gray">合同名称</td>
				<td colspan="5">{$detail.name}</td>
				<td class="layui-td-gray">合同编号</td>
				<td>{$detail.code}</td>
			  </tr>
			  <tr>
				<td class="layui-td-gray">签约主体(乙方)</td>
				<td colspan="3">
					{volist name=":contract_subject()" id="v"}
					{eq name="$v.id" value="$detail.subject_id" }{$v.title}{/eq}
					{/volist}
				</td>
				<td class="layui-td-gray">合同性质</td>
				<td>
					{eq name="$detail.type" value="1" }普通合同{/eq}
					{eq name="$detail.type" value="2" }框架合同{/eq}
					{eq name="$detail.type" value="3" }补充协议{/eq}
					{eq name="$detail.type" value="4" }其他合同{/eq}
				</td>
				<td class="layui-td-gray">合同类别</td>
				<td>
					{volist name=":contract_cate()" id="v"}
					{eq name="$v.id" value="$detail.cate_id" }{$v.title}{/eq}
					{/volist}
				</td>
			  </tr>
			  <tr>
				<td class="layui-td-gray">客户名称(甲方)</td>
				<td colspan="3">{$detail.customer}</td>
				<td class="layui-td-gray">签约客户代表</td>
				<td>{$detail.customer_name}</td>
				<td class="layui-td-gray">客户联系电话</td>
				<td>{$detail.customer_mobile}</td>
			  </tr>
			   <tr>
				<td class="layui-td-gray-2">客户联系地址</td>
				<td  colspan="3">{$detail.customer_address}</td>
				<td class="layui-td-gray-2">合同开始日期</td>
				<td>{$detail.start_time}</td>
				<td class="layui-td-gray-2">合同结束日期</td>
				<td>{$detail.end_time}</td>
			  </tr>
			  {neq name="$detail.type" value="2"}
			  <tr>
				<td class="layui-td-gray">合同金额</td>
				<td>{$detail.cost}</td>
				{eq name="$detail.is_tax" value="1" }
				<td class="layui-td-gray">是否含税</td>
				<td>是</td>
				<td class="layui-td-gray">税点(百分比)</td>
				<td colspan="3">{$detail.tax}%</td>
				{/eq}
				{eq name="$detail.is_tax" value="0" }
				<td class="layui-td-gray">是否含税</td>
				<td colspan="5">否</td>
				{/eq}
			  </tr>
			  {/neq}
			  {notempty name="$detail.remark"}
			   <tr>
				<td class="layui-td-gray">备注信息</td>
				<td colspan="7">{$detail.remark|default=''}</td>
			  </tr>
			  {/notempty}
				{notempty name="$detail.file_ids"}
				<tr>
					<td class="layui-td-gray">相关附件</td>
					<td colspan="7" style="line-height:inherit">
						<div class="layui-row">
							{volist name="$detail.fileArray" id="vo"}
							<div class="layui-col-md4" id="uploadImg{$vo.id}">{:file_card($vo,'view')}</div>
							{/volist}
						</div>
					</td>
				</tr>
				{/notempty}			  
			  <tr>
				<td colspan="8"><strong>补充附件</strong></td>
			  </tr>
			  <tr>
				<td class="layui-td-gray">
					<button type="button" class="layui-btn layui-btn-sm" id="uploadBtn"><i class="layui-icon"></i>附件上传</button>
				</td>
				<td colspan="7" style="line-height:inherit">
					<div id="fileBox">
					{volist name="$detail.file_array_other" id="vo"}
					<div class="layui-col-md4" id="fileItem{$vo.id}">{:file_card($vo)}</div>
					{/volist}
					</div>
				</td>
			   </tr> 
			  <tr>
				<td colspan="8"><strong>签订信息</strong></td>
			  </tr>
			  <tr>
				<td class="layui-td-gray-2">合同制定人</td>
				<td>{$detail.prepared_name} </td>
				<td class="layui-td-gray-2">合同签订人</td>
				<td>{$detail.sign_name}</td>
				<td class="layui-td-gray-2">合同签订时间</td>
				<td>{$detail.sign_time}</td>
				<td class="layui-td-gray-2">合同签订部门</td>
				<td>{$detail.sign_department}</td>
			  </tr>
			  <tr>        
				<td class="layui-td-gray-2">合同保管人</td>
				<td>{$detail.keeper_name}{gt name="$auth" value="0"}<span id="keeper" data-ids="{$detail.keeper_uid}" data-names="{$detail.keeper_name}" class="layui-btn layui-btn-xs layui-btn-normal ml-1">更改</span>{/gt}</td>
				<td class="layui-td-gray">合同共享人员</td>
				<td colspan="5">{$detail.share_names}{gt name="$auth" value="0"}<span id="shares" data-ids="{$detail.share_ids}" data-names="{$detail.share_names}" class="layui-btn layui-btn-xs layui-btn-normal ml-1">更改</span>{/gt}</td>
			  </tr>	
			  <tr>
				<td colspan="8"><strong>审核信息</strong></td>
			  </tr>
			  <tr>
				<td class="layui-td-gray-2">合同状态</td>
				<td><span class="layui-color-{$detail.check_status}">{$detail.status_name}</span></td>
				<td class="layui-td-gray-2">录入人</td>
				<td>{$detail.admin_name|default=''} </td>
				<td class="layui-td-gray-2">录入时间</td>
				<td colspan="3">{$detail.create_time|default=''}</td>
			  </tr>		
			{notempty name="$check_record"}
			<tr>
				<td class="layui-td-gray">历史审批记录</td>
				<td colspan="7">
					<ul class="layui-timeline flow-record pt-2">
					{volist name="$check_record" id="vo"}		
						<li class="layui-timeline-item delete-{$vo.delete_time}">
							<i class="layui-icon layui-timeline-axis">&#xe63f;</i>
							<p style="padding-left:24px">{$vo.check_time_str}<span class="black ml-2">{$vo.name}</span><span class="mx-2 layui-color-{$vo.status}">{$vo.status_str}</span>了此申请。操作意见：<span class="green">{$vo.content}</span></p>
						</li>
					{/volist}
					</ul>
				</td>
			</tr>
			{/notempty}
		</table>
		
			{if ($detail.check_status == 1)}
				{include file="/index/view_step" /}			
			{elseif ($detail.check_status == 0) OR ($detail.check_status == 4)}
				{include file="/index/view_set" /}
			{else /}
			<table class="layui-table">
				{eq name="$detail.check_status" value="2" }
				  <tr>
					<td class="layui-td-gray-2">归档状态</td>
					{eq name="$detail.archive_status" value="0" }
					<td colspan="7"><span class="layui-color-{$detail.archive_status}">{$detail.archive_status_name}</span></td>
					{else/}
					<td><span class="layui-color-{$detail.archive_status}">{$detail.archive_status_name}</span></td>
					<td class="layui-td-gray-2">归档人</td>
					<td>{$detail.archive_name|default=''} </td>
					<td class="layui-td-gray-2">归档时间</td>
					<td colspan="3">{$detail.archive_time|default=''}</td>
					{/eq}
				  </tr>
				{/eq}
				
				{eq name="$detail.check_status" value="5" }
				  <tr>
					<td class="layui-td-gray-2">中止人</td>
					<td>{$detail.stop_name|default=''} </td>
					<td class="layui-td-gray-2">中止时间</td>
					<td colspan="5">{$detail.stop_time|default=''}</td>
				  </tr>
				  <tr>
					<td class="layui-td-gray-2">中止备注</td>
					<td colspan="7">{$detail.stop_remark|default=''}</td>
				  </tr>
				{/eq}
				
				{eq name="$detail.check_status" value="6" }
				  <tr>
					<td class="layui-td-gray-2">作废人</td>
					<td>{$detail.void_name|default=''} </td>
					<td class="layui-td-gray-2">作废时间</td>
					<td colspan="5">{$detail.void_time|default=''}</td>
				  </tr>
				  <tr>
					<td class="layui-td-gray-2">作废备注</td>
					<td colspan="7">{$detail.void_remark|default=''}</td>
				  </tr>
				{/eq}			
			</table>
			<div class="pt-2">
				{gt name="$auth" value="0"}				
					{eq name="$detail.check_status" value="2" }
						{eq name="$detail.archive_status" value="1" }
							<span class="layui-btn layui-btn-danger" data-event="archive" data-status="0">反确认归档</span>
						{else/}
						<span class="layui-btn layui-btn-normal" data-event="archive" data-status="1">合同归档</span>
						<span class="layui-btn layui-btn-danger" data-event="check" data-status="0">反确认审核</span>
						{/eq}
					{/eq}
					{eq name="$detail.check_status" value="5" }
					<div class="py-2">
						<span class="layui-btn layui-btn-danger" data-event="check" data-status="0">反中止合同</span>
					</div>
					{/eq}
					
					{eq name="$detail.check_status" value="6" }
					<div class="py-2">
						<span class="layui-btn layui-btn-danger" data-event="check" data-status="0">反作废合同</span>
					</div>
					{/eq}
				{/gt}	
				{if ( $detail.check_status eq 3) AND ( $is_create_admin eq 1) }
					<span class="layui-btn layui-btn-primary check_back"><i class="layui-icon layui-icon-reduce-circle"></i> 撤回</span>
				{/if}
			</div>
			{/if}
		
		</div>
		<div class="layui-tab-item">
			{include file="/index/view_log" /}
		</div>
	  </div>
	</div>
</form>
{/block}
<!-- /主体 -->

<!-- 脚本 -->
{block name="script"}
<script>
const contract_id = '{$detail.id}';
const contract_status = '{$detail.check_status}';
const moduleInit = ['tool','employeepicker','oaTool'];
	function gouguInit() {
		var form = layui.form,tool=layui.tool, element = layui.element,employeepicker = layui.employeepicker,oaTool = layui.oaTool;		
		element.on('tab(contract)', function(data){
			let index = data.index;
			if(index == 1){
				log(layui);	
			}
		});
		if(typeof init==='function'){
			init(form,tool);
		}

		if (typeof (flowStep) == "function") {
			flowStep();
		}
		
		//选择合同保管人弹窗	
		$('body').on('click','#keeper',function () {
			var ids=$(this).data('ids')+'',names = $(this).data('names')+'';
			employeepicker.init({
				ids:ids,
				names:names,
				type:0,
				department_url: "/api/index/get_department_tree",
				employee_url: "/api/index/get_employee",
				callback:function(ids,names,dids,departments){
					let callback = function (e) {
						layer.msg(e.msg);
						if (e.code == 0) {
							location.reload();
						}
					}
					tool.post("/contract/index/add", {'id':contract_id,'keeper_uid':ids,'scene':'change'}, callback);
				}
			});
		});
	
		//选择共享成员弹窗	
		$('body').on('click','#shares',function () {
			var  ids=$(this).data('ids')+'',names = $(this).data('names')+'',share_ids_array=[],share_names_array=[];
			if(ids.length>0){
				share_ids_array=ids.split(',');
				share_names_array=names.split(',');
			}
			employeepicker.init({
				ids:share_ids_array,
				names:share_names_array,
				type:1,
				department_url: "/api/index/get_department_tree",
				employee_url: "/api/index/get_employee",
				callback:function(ids,names,dids,departments){
					let callback = function (e) {
						layer.msg(e.msg);
						if (e.code == 0) {
							location.reload();
						}
					}
					tool.post("/contract/index/add", {'id':contract_id,'share_ids':ids.join(','),'scene':'change'}, callback);
				}
			});
		});
		
		
		$('body').on('click','[data-event="check"]',function(){
			let status = $(this).data('status');
			let action = '';
			let title = ''
			if(contract_status == 2 && status == 0){
				title = '确定要反确认该合同的审核?';
				action = 'check_refue';
			}
			if(contract_status == 5 && status == 0){
				title = '确定要反中止该合同?';
				action = 'stop_no';
			}
			if(contract_status == 6 && status == 0){
				title = '确定要反作废该合同?';
				action = 'void_no';
			}
			layer.confirm(title, {
				icon: 3,
				title: '提示'
			}, function(index) {
				let callback = function (e) {
					layer.msg(e.msg);
					parent.layui.pageTable.reload();
					setTimeout(function(){
						location.reload();
					},2000)					
				}
				tool.post("/contract/api/check", {id: contract_id,check_status:status,mark:''}, callback);			
				layer.close(index);
			});
		});
		
		$('body').on('click','[data-event="archive"]',function(){
			let status = $(this).data('status');
			layer.confirm('合同归档后将不能进行任何数据操作，确定要提交归档?', {
				icon: 3,
				title: '提示'
			}, function(index) {
				let callback = function (e) {
					layer.msg(e.msg);
					parent.layui.pageTable.reload();
					setTimeout(function(){
						location.reload();
					},2000)					
				}
				tool.post("/contract/api/archive", {id: contract_id,archive_status:status}, callback);
				layer.close(index);
			});
		})
		
		
		$('body').on('click','.check_back',function(){
			layer.prompt({
				formType: 2,
				title: '请输入撤回理由',
				area: ['500px', '120px'] //自定义文本域宽高
			}, function(value, index, elem){
				$.ajax({
					url: "/api/index/flow_check",
					type:'post',
					data:{
						id:contract_id,
						type:4,
						check:3,
						content:value
					},
					success: function (e) {
						layer.msg(e.msg);
						if (e.code == 0) {	
							parent.layui.pageTable.reload();
							location.reload();
						}
					}
				})
				layer.close(index);
			});
		})
		
		oaTool.addFile({
			type:1,
			isSave:true,
			ajaxDelete:function(file_id){
				let callback = function (e) {
					layer.msg(e.msg);
					if (e.code == 0) {						
						$('#fileItem' + file_id).remove();
					}
				}
				tool.delete("/contract/api/delete_file", {id: file_id}, callback);			
			},
			ajaxSave:function(res){
				let callback = function (e) {
					location.reload();
				}
				tool.post("/contract/api/add_file", {'contract_id':contract_id,'file_id':res.data.id,'file_name':res.data.name}, callback);
			}
		});
	}

</script>
{/block}
<!-- /脚本 -->