<?php

namespace app\user\controller;

use app\base\BaseController;
use app\message\service\QiWeiService;
use app\store\controller\Storebusiness;
use app\user\model\DepartmentChange;
use app\user\service\AdminService;
use app\user\service\AdminViewService;
use app\user\service\TrainService;
use think\App;
use think\facade\Db;
use think\facade\Session;
use think\facade\View;
use app\user\model\AdminView as AdminViewList;

class Train extends BaseController
{

    public function train_index()
    {
        $param = get_params();
        if (request()->isAjax()) {

            $where = array();
            $where[] = ['status', '>' , 0];

            if (isset($param['type']) && !empty($param['type'])){
                $where['type'] = $param['type'];
            }

            $rows = empty($param['limit']) ? get_config('app.page_size') : $param['limit'];

            $list = Db::name("train_programs")
                ->where($where)
                ->order("trainId desc")
                ->paginate($rows, false, ['query' => $param])
                ->each(function ($item, $key) {
                    if ($item['type'] == 1) {
                        $item['count'] = Db::name("train_progress")
                            ->where([
                                'trainId' => $item['trainId']
                            ])->count();
                    } else {
                        $item['count'] = Db::name("train_sign")
                            ->where([
                                'trainId' => $item['id'],
                                'status' => 1
                            ])->count();
                    }
                    return $item;
                });


            return table_assign(0, '', $list);
        } else {

            $type = isset($param['type']) ? $param['type'] : 1;

            View::assign("type" , $type);

            return view();
        }

    }

    public function train_progress()
    {
        $param = get_params();
        if (request()->isAjax()) {

            $where = array();
            $where['trainId'] = $param['trainId'];
            $where['status'] = 1;

            $rows = empty($param['limit']) ? get_config('app.page_size') : $param['limit'];

            $list = Db::name("train_progress")
                ->where($where)
                ->paginate($rows, false, ['query' => $param]);


            return table_assign(0, '', $list);
        } else {
            View::assign("trainId", $param['trainId']);
            return view();
        }

    }

    public function train_add()
    {
        $param = get_params();

        $id = isset($param['id']) ? $param['id'] : 0;

        if (request()->isAjax()) {

            $type = 1;
            if (strpos($param['categoryName'] , "大师傅" ) !== false) {
                $type = 2;
            }else if (strpos($param['categoryName'] , "技术指导") !== false) {
                $type = 3;
            }

            $data = [
                'trainName' => $param['trainName'],
                'categoryName' => $param['categoryName'],
                'startTime' => explode(" - ", $param['rangeTime'])[0],
                'endTime' => explode(" - ", $param['rangeTime'])[1],
                'createTime' => date("Y-m-d H:i:s"),
                'type' => $type,
                'creator' => $this->uid,
                'creatorName' => $this->name,
            ];

            if ($id > 0) {
                Db::name("train_programs")->where(['id' => $id])->update($data);
            } else {
                Db::name("train_programs")->insertGetId($data);
            }
            return to_assign(0, '操作成功');
        } else {

            if ($id > 0) {
                $detail = Db::name("train_programs")->where(['id' => $id])->find();
                $detail['startTime'] = date("Y-m-d" , strtotime($detail['startTime']));
                $detail['endTime'] = date("Y-m-d" , strtotime($detail['endTime']));
                View::assign("detail", $detail);
            }

            View::assign("id", $id);
            return view();
        }

    }

    public function train_del()
    {
        $param = get_params();

        $id = isset($param['id']) ? $param['id'] : 0;

        if (request()->isAjax()) {

            Db::name("train_programs")->where(['id' => $id])->update(['status' => -1]);
            Db::name("train_sign")->where(['trainId' => $id])->update(['status' => -1]);
            return to_assign(0, '删除成功');
        }
    }

    public function train_sign()
    {
        $param = get_params();
        $id = isset($param['id']) ? $param['id'] : 0;

        if (request()->isAjax()) {
            $where = array();

            $rows = empty($param['limit']) ? get_config('app.page_size') : $param['limit'];

            $where['ts.trainId'] = $id;
            $where['ts.status'] = 1;

            $list = Db::name("train_sign")
                ->field('ts.*,d.title as dname')
                ->alias('ts')
                ->join('department d', 'ts.did = d.id', 'left')
                ->where($where)
                ->order("trainId desc")
                ->paginate($rows, false, ['query' => $param]);

            return table_assign(0, '', $list);
        } else {

            $detail = Db::name("train_programs")->where([
                'id' => $id,
            ])->find();
            View::assign("detail", $detail);

            $HTTP_HOST = $_SERVER['HTTP_HOST'];
            View::assign('id', $id);
            View::assign('url', "http://$HTTP_HOST/mobile/index/train_sign_in?trainId=$id");
            return view();
        }

    }

    public function train_sign_result()
    {
        $param = get_params();
        $id = isset($param['id']) ? $param['id'] : 0;

        if (request()->isAjax()) {
            $where = array();

            $rows = empty($param['limit']) ? get_config('app.page_size') : $param['limit'];

            $where['trainId'] = $id;

            $data = Db::name("train_sign_result")
                ->where($where)
                ->select()->toArray();

            $list = array();

            foreach ($data as $k => $v){
                $list[$v['in_col']][$v['title']] = is_numeric($v['content']) ? floatval($v['content']) : $v['content'];
                $list[$v['in_col']][$v['aid']] = $v['aid'];
                $list[$v['in_col']][$v['aname']] = $v['aname'];
                $list[$v['in_col']][$v['did']] = $v['did'];
                $list[$v['in_col']][$v['dname']] = $v['dname'];

                $train_sign = Db::name('train_sign')->where(
                    [
                        'aid' => $v['aid'],
                        'trainId' => $v['trainId'],
                    ]
                )->find();

                if (!empty($train_sign)){
                    $list[$v['in_col']]['是否签到'] = 1;
                }else{
                    $list[$v['in_col']]['是否签到'] = 0;
                }

            }

            $list = array_values($list);

            return table_assign(0, '', ['data' => $list]);
        } else {

            $cols = Db::name("train_sign_result")
                ->where(['trainId' => $id])
                ->group("title")
                ->column('title');

            $cols[] = "是否签到";

            View::assign("cols" , $cols);
            View::assign("id" , $id);
            return view();
        }

    }

    public function train_sign_result_aid()
    {
        $param = get_params();
        $aid = isset($param['aid']) ? $param['aid'] : 0;

        if (request()->isAjax()) {
            $where = array();

            $train_sign_result = Db::name('train_sign_result')
                ->field("tsr.*,tp.trainName,tp.categoryName")
                ->alias('tsr')
                ->join('train_programs tp', 'tp.id = tsr.trainId', 'LEFT')
                ->where(['tsr.aid' => $aid])
                ->select()
                ->toArray();

            $list = array();

            foreach ($train_sign_result as $k => $v){
                $list_k = $v['trainId'] . '_' . $v['in_col'];

                $list[ $list_k ]['trainName'] = $v['trainName'];
                $list[ $list_k ]['categoryName'] = $v['categoryName'];

                if ($v['title'] == '姓名'){
                    $list[ $list_k ]['姓名'] = $v['content'];
                }else if ($v['title'] == '门店'){
                    $list[ $list_k ]['门店'] = $v['content'];
                }else if ($v['title'] == '培训时间'){
                    $list[ $list_k ]['培训时间'] = $v['content'];
                }else if ($v['title'] == '是否合格'){
                    $list[ $list_k ]['是否合格'] = $v['content'];
                }else{
                    if (!isset($list[ $list_k ]['其他'])){
                        $list[ $list_k ]['其他'] = "{$v['title']} ：{$v['content']}</br>";
                    }else{
                        $list[ $list_k ]['其他'] .= "{$v['title']} ：{$v['content']}</br>";
                    }
                }
            }

            $list = array_values($list);

            return table_assign(0, '', ['data' => $list]);
        }

    }

    public function train_sign_edit()
    {
        $param = get_params();
        $id = isset($param['id']) ? $param['id'] : 0;

        if (request()->isAjax()) {

            Db::name("train_sign")->where([
                'id' => $id
            ])->update(['status' => -1]);
            return to_assign(0, '删除成功');
        }
    }

    public function exam_list()
    {
        $param = get_params();
        if (request()->isAjax()) {

            $where = array();

            $rows = empty($param['limit']) ? get_config('app.page_size') : $param['limit'];

            $list = Db::name("train_exams")
                ->where($where)
                ->order("id desc")
                ->paginate($rows, false, ['query' => $param])
                ->each(function ($item, $key) {
                    $item['count'] = Db::name("train_exams_users")
                        ->where([
                            'examid' => $item['id']
                        ])->count();
                    return $item;
                });;


            return table_assign(0, '', $list);
        } else {

//            $TrainService = new TrainService();
//            $TrainService->syn_train_exam();

            return view();
        }

    }

    public function exams_result()
    {
        $param = get_params();
        if (request()->isAjax()) {

            $where = array();
            $where['teu.examid'] = $param['examid'];


            $rows = empty($param['limit']) ? get_config('app.page_size') : $param['limit'];

            $list = Db::name("train_exams_users")
                ->field('teu.* , ter.examName , ter.totalScore , ter.passScore')
                ->alias("teu")
                ->join('train_exams_result ter', 'teu.examid = ter.examid', 'left')
                ->where($where)
                ->paginate($rows, false, ['query' => $param]);

            return table_assign(0, '', $list);
        } else {

            View::assign("examid", $param['examid']);
            return view();
        }

    }

    public function syn_train()
    {
        $TrainService = new TrainService();
        //syn_train_exam
        $TrainService->syn_train();

        return view();

    }

}


