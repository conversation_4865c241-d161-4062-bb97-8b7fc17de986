<?php
/**
 * @copyright Copyright (c) 2021 仲正堂工作室
 * @license https://opensource.org/licenses/GPL-3.0
 * @link https://www.gougucms.com
 */

namespace app\cross\validate;

use think\Validate;

class CrossStoreSettleSummaryCheck extends Validate
{
    protected $rule = [
        'id' => 'require|number',
        'period' => 'require|regex:/^\d{4}-\d{2}$/',
        'store_id' => 'require|number|gt:0',
        'store_type' => 'require|in:新店,老店',
        'other_store_id' => 'require|number|gt:0|different:store_id',
        'other_store_type' => 'require|in:新店,老店',
        'total_reconciliation_amount' => 'float|egt:0|checkDecimalPlaces',
        
        // 储值本金部分
        'p_local_consume_foreign' => 'float|egt:0|checkDecimalPlaces',
        'p_local_refund_foreign' => 'float|egt:0|checkDecimalPlaces',
        'p_foreign_consume_local' => 'float|egt:0|checkDecimalPlaces',
        'p_foreign_refund_local' => 'float|egt:0|checkDecimalPlaces',
        'p_total_amount' => 'float|egt:0|checkDecimalPlaces',
        'p_reconciliation_amount' => 'float|checkDecimalPlaces',
        
        // 储值赠金部分
        'b_local_consume_foreign' => 'float|egt:0|checkDecimalPlaces',
        'b_local_refund_foreign' => 'float|egt:0|checkDecimalPlaces',
        'b_foreign_consume_local' => 'float|egt:0|checkDecimalPlaces',
        'b_foreign_refund_local' => 'float|egt:0|checkDecimalPlaces',
        'b_total_amount' => 'float|egt:0|checkDecimalPlaces',
        'b_reconciliation_amount' => 'float|checkDecimalPlaces',
        
        // 次卡部分
        'cc_local_consume_foreign_count' => 'number|egt:0',
        'cc_local_refund_foreign_count' => 'number|egt:0',
        'cc_foreign_consume_local_count' => 'number|egt:0',
        'cc_foreign_refund_local_count' => 'number|egt:0',
        'cc_local_upgrade_foreign_amount' => 'float|egt:0|checkDecimalPlaces',
        'cc_foreign_upgrade_local_amount' => 'float|egt:0|checkDecimalPlaces',
        'cc_total_amount' => 'float|egt:0|checkDecimalPlaces',
        'cc_reconciliation_amount' => 'float|checkDecimalPlaces',
        
        // 网店核销部分
        'ol_local_redeem_foreign_count' => 'number|egt:0',
        'ol_local_refund_foreign_count' => 'number|egt:0',
        'ol_foreign_redeem_local_count' => 'number|egt:0',
        'ol_foreign_refund_local_count' => 'number|egt:0',
        'ol_total_amount' => 'float|egt:0|checkDecimalPlaces',
        'ol_reconciliation_amount' => 'float|checkDecimalPlaces',
    ];

    protected $message = [
        'id.require' => '缺少更新条件',
        'id.number' => 'ID必须为数字',
        'period.require' => '请选择汇总月份',
        'period.regex' => '汇总月份格式错误，应为YYYY-MM格式',
        'store_id.require' => '请选择门店',
        'store_id.number' => '门店ID必须为数字',
        'store_id.gt' => '请选择有效的门店',
        'store_type.require' => '请选择门店类型',
        'store_type.in' => '门店类型只能是新店或老店',
        'other_store_id.require' => '请选择他店',
        'other_store_id.number' => '他店ID必须为数字',
        'other_store_id.gt' => '请选择有效的他店',
        'other_store_id.different' => '他店不能与门店相同',
        'other_store_type.require' => '请选择他店类型',
        'other_store_type.in' => '他店类型只能是新店或老店',
        'total_reconciliation_amount.float' => '总对账金额必须为数字',
        'total_reconciliation_amount.egt' => '总对账金额不能为负数',
        'total_reconciliation_amount.checkDecimalPlaces' => '总对账金额最多支持两位小数',
        
        // 储值本金部分错误信息
        'p_local_consume_foreign.float' => '本店耗他店本金必须为数字',
        'p_local_consume_foreign.egt' => '本店耗他店本金不能为负数',
        'p_local_consume_foreign.checkDecimalPlaces' => '本店耗他店本金最多支持两位小数',
        'p_local_refund_foreign.float' => '本店回退他店本金必须为数字',
        'p_local_refund_foreign.egt' => '本店回退他店本金不能为负数',
        'p_local_refund_foreign.checkDecimalPlaces' => '本店回退他店本金最多支持两位小数',
        'p_foreign_consume_local.float' => '他店耗本店本金必须为数字',
        'p_foreign_consume_local.egt' => '他店耗本店本金不能为负数',
        'p_foreign_consume_local.checkDecimalPlaces' => '他店耗本店本金最多支持两位小数',
        'p_foreign_refund_local.float' => '他店回退本店本金必须为数字',
        'p_foreign_refund_local.egt' => '他店回退本店本金不能为负数',
        'p_foreign_refund_local.checkDecimalPlaces' => '他店回退本店本金最多支持两位小数',
        'p_total_amount.float' => '本金合计必须为数字',
        'p_total_amount.egt' => '本金合计不能为负数',
        'p_total_amount.checkDecimalPlaces' => '本金合计最多支持两位小数',
        'p_reconciliation_amount.float' => '本金对账金额必须为数字',
        'p_reconciliation_amount.checkDecimalPlaces' => '本金对账金额最多支持两位小数',
        
        // 储值赠金部分错误信息
        'b_local_consume_foreign.float' => '本店耗他店赠金必须为数字',
        'b_local_consume_foreign.egt' => '本店耗他店赠金不能为负数',
        'b_local_consume_foreign.checkDecimalPlaces' => '本店耗他店赠金最多支持两位小数',
        'b_local_refund_foreign.float' => '本店回退他店赠金必须为数字',
        'b_local_refund_foreign.egt' => '本店回退他店赠金不能为负数',
        'b_local_refund_foreign.checkDecimalPlaces' => '本店回退他店赠金最多支持两位小数',
        'b_foreign_consume_local.float' => '他店耗本店赠金必须为数字',
        'b_foreign_consume_local.egt' => '他店耗本店赠金不能为负数',
        'b_foreign_consume_local.checkDecimalPlaces' => '他店耗本店赠金最多支持两位小数',
        'b_foreign_refund_local.float' => '他店回退本店赠金必须为数字',
        'b_foreign_refund_local.egt' => '他店回退本店赠金不能为负数',
        'b_foreign_refund_local.checkDecimalPlaces' => '他店回退本店赠金最多支持两位小数',
        'b_total_amount.float' => '赠金合计必须为数字',
        'b_total_amount.egt' => '赠金合计不能为负数',
        'b_total_amount.checkDecimalPlaces' => '赠金合计最多支持两位小数',
        'b_reconciliation_amount.float' => '赠金对账金额必须为数字',
        'b_reconciliation_amount.checkDecimalPlaces' => '赠金对账金额最多支持两位小数',
        
        // 次卡部分错误信息
        'cc_local_consume_foreign_count.number' => '本店耗他店卡次数必须为数字',
        'cc_local_consume_foreign_count.egt' => '本店耗他店卡次数不能为负数',
        'cc_local_refund_foreign_count.number' => '本店回退他店卡次数必须为数字',
        'cc_local_refund_foreign_count.egt' => '本店回退他店卡次数不能为负数',
        'cc_foreign_consume_local_count.number' => '他店耗本店卡次数必须为数字',
        'cc_foreign_consume_local_count.egt' => '他店耗本店卡次数不能为负数',
        'cc_foreign_refund_local_count.number' => '他店回退本店卡次数必须为数字',
        'cc_foreign_refund_local_count.egt' => '他店回退本店卡次数不能为负数',
        'cc_local_upgrade_foreign_amount.float' => '本店升他店卡金额必须为数字',
        'cc_local_upgrade_foreign_amount.egt' => '本店升他店卡金额不能为负数',
        'cc_local_upgrade_foreign_amount.checkDecimalPlaces' => '本店升他店卡金额最多支持两位小数',
        'cc_foreign_upgrade_local_amount.float' => '他店升本店卡金额必须为数字',
        'cc_foreign_upgrade_local_amount.egt' => '他店升本店卡金额不能为负数',
        'cc_foreign_upgrade_local_amount.checkDecimalPlaces' => '他店升本店卡金额最多支持两位小数',
        'cc_total_amount.float' => '次卡跨店金额合计必须为数字',
        'cc_total_amount.egt' => '次卡跨店金额合计不能为负数',
        'cc_total_amount.checkDecimalPlaces' => '次卡跨店金额合计最多支持两位小数',
        'cc_reconciliation_amount.float' => '次卡对账金额必须为数字',
        'cc_reconciliation_amount.checkDecimalPlaces' => '次卡对账金额最多支持两位小数',
        
        // 网店核销部分错误信息
        'ol_local_redeem_foreign_count.number' => '本店核销他店码次数必须为数字',
        'ol_local_redeem_foreign_count.egt' => '本店核销他店码次数不能为负数',
        'ol_local_refund_foreign_count.number' => '本店回退他店码次数必须为数字',
        'ol_local_refund_foreign_count.egt' => '本店回退他店码次数不能为负数',
        'ol_foreign_redeem_local_count.number' => '他店核销本店码次数必须为数字',
        'ol_foreign_redeem_local_count.egt' => '他店核销本店码次数不能为负数',
        'ol_foreign_refund_local_count.number' => '他店回退本店码次数必须为数字',
        'ol_foreign_refund_local_count.egt' => '他店回退本店码次数不能为负数',
        'ol_total_amount.float' => '网店核销金额合计必须为数字',
        'ol_total_amount.egt' => '网店核销金额合计不能为负数',
        'ol_total_amount.checkDecimalPlaces' => '网店核销金额合计最多支持两位小数',
        'ol_reconciliation_amount.float' => '网店核销对账金额必须为数字',
        'ol_reconciliation_amount.checkDecimalPlaces' => '网店核销对账金额最多支持两位小数',
    ];

    protected $scene = [
        'edit' => [
            'id', 'period', 'store_id', 'store_type', 'other_store_id', 'other_store_type',
            'total_reconciliation_amount',
            'p_local_consume_foreign', 'p_local_refund_foreign', 'p_foreign_consume_local', 'p_foreign_refund_local',
            'p_total_amount', 'p_reconciliation_amount',
            'b_local_consume_foreign', 'b_local_refund_foreign', 'b_foreign_consume_local', 'b_foreign_refund_local',
            'b_total_amount', 'b_reconciliation_amount',
            'cc_local_consume_foreign_count', 'cc_local_refund_foreign_count', 'cc_foreign_consume_local_count', 'cc_foreign_refund_local_count',
            'cc_local_upgrade_foreign_amount', 'cc_foreign_upgrade_local_amount', 'cc_total_amount', 'cc_reconciliation_amount',
            'ol_local_redeem_foreign_count', 'ol_local_refund_foreign_count', 'ol_foreign_redeem_local_count', 'ol_foreign_refund_local_count',
            'ol_total_amount', 'ol_reconciliation_amount'
        ],
    ];

    /**
     * 自定义验证规则：检查小数位数（金额保持2位小数）
     */
    protected function checkDecimalPlaces($value, $rule, $data)
    {
        if (empty($value) || $value == 0) {
            return true;
        }
        
        $valueStr = (string)$value;
        if (strpos($valueStr, '.') !== false) {
            $decimalPart = explode('.', $valueStr)[1];
            if (strlen($decimalPart) > 2) {
                return false;
            }
        }
        return true;
    }
}
