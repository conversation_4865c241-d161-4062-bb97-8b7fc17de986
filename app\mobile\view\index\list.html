{extend name="../../base/view/common/base" /}
{block name="style"}
<link rel="stylesheet" href="{__GOUGU__}/gougu/css/layout.css" media="all">
{/block}
<!-- 主体 -->
{block name="body"}
<style>

    body {
        background-color: #f8f9fa;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
        line-height: 1.5;
    }

    .uni-page {
        display: block;
        width: 100%;
        height: 100%;
    }

    .uni-page-head-main {
        display: block;
        box-sizing: border-box;
    }

    .uni-page-head .uni-page-head-hd {
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-align: center;
        -webkit-align-items: center;
        align-items: center;
        font-size: 16px;
    }

    .uni-page-head-main .uni-page-head {
        position: fixed;
        left: var(--window-left);
        right: var(--window-right);
        height: 44px;
        height: calc(44px + constant(safe-area-inset-top));
        height: calc(44px + env(safe-area-inset-top));
        padding: 7px 3px;
        padding-top: calc(7px + constant(safe-area-inset-top));
        padding-top: calc(7px + env(safe-area-inset-top));
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        overflow: hidden;
        -webkit-box-pack: justify;
        -webkit-justify-content: space-between;
        justify-content: space-between;
        box-sizing: border-box;
        z-index: 998;
        color: #fff;
        background-color: #000;
        -webkit-transition-property: all;
        transition-property: all;
        width: 100%;
    }

    .uni-page-head .uni-page-head-bd {
        position: absolute;
        left: 70px;
        right: 70px;
        min-width: 0;
    }

    .uni-page-head .uni-page-head__title {
        font-weight: 700;
        font-size: 16px;
        line-height: 30px;
        text-align: center;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
    }

    .mine-container .header-section[data-v-1716ef58] {
        padding: 15px 15px 45px 15px;
        background-color: #3c96f3;
        color: #fff;
    }

    .uni-page-head-main .uni-page-head ~ .uni-placeholder {
        width: 100%;
        height: 44px;
        height: calc(44px + constant(safe-area-inset-top));
        height: calc(44px + env(safe-area-inset-top));
    }

    .mine-container .header-section .cu-avatar[data-v-1716ef58] {
        border: 2px solid #eaeaea;
    }

    /* 优化审批卡片主体样式 */
    .voice {
        position: relative;
        padding: 12px; /* 减少内边距 */
        margin: 4px 8px; /* 减少外边距 */
        background-color: #ffffff !important;
        color: #333;
        border-radius: 10px; /* 减少圆角 */
        box-shadow: 0 1px 6px rgba(0, 0, 0, 0.05); /* 减少阴影 */
        border: 1px solid #e8eaed;
        transition: all 0.3s ease;
        /* 确保卡片有明确的边界，防止子元素溢出 */
        overflow: hidden;
    }

    .voice:active {
        transform: scale(0.98);
        box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
    }

    /* 优化卡片标题行样式 */
    .voice_title {
        display: flex;
        align-items: center;
        justify-content: space-between;
        border-bottom: 1px solid #f0f2f5;
        padding: 0 0 8px 0; /* 减少底部内边距 */
        margin-bottom: 8px; /* 减少底部外边距 */
        position: relative;
    }

    .voice_title:last-child {
        border-bottom: none;
        margin-bottom: 0;
        padding-bottom: 0;
    }

    /* 优化第一行标题布局，为状态标签预留空间 */
    .voice_title:first-child {
        padding-right: 70px; /* 为状态标签预留空间 */
        margin-bottom: 10px; /* 减少与下一行的间距 */
        flex-direction: column; /* 改为垂直布局 */
        align-items: flex-start; /* 左对齐 */
    }

    /* 申请人姓名行样式 */
    .voice_title:first-child .name {
        width: 100%;
        margin-bottom: 4px; /* 减少姓名与时间的间距 */
    }

    /* 时间独立行样式 */
    .voice_title:first-child .create_time {
        margin-left: 0;
        font-size: 12px;
        color: #8b949e;
        align-self: flex-start;
    }

    .read_time_on {
        background-color: #52c41a;
    }

    .read_time_off {
        background-color: #ff4d4f;
    }

    /* 优化申请人姓名样式 */
    .name {
        font-size: 16px;
        font-weight: 600;
        color: #1f2937;
        flex: 1;
        min-width: 0; /* 允许文字截断 */
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        /* 移除最大宽度限制，因为时间已经独立显示 */
    }

    /* 优化时间显示样式 */
    .create_time {
        display: flex;
        flex-direction: row;
        align-items: center;
        font-size: 12px;
        color: #8b949e;
        font-weight: 400;
        white-space: nowrap; /* 防止时间换行 */
        /* 移除margin-left和flex-shrink，因为已经独立成行 */
    }

    /* 优化重点文字样式 */
    .b_color {
        color: #1f2937;
        font-weight: 600;
    }

    /* 优化流程类型样式 */
    .flow_type {
        color: #1890ff !important;
        font-weight: 600;
        margin-left: 4px;
    }

    /* 优化备注区域样式 */
    .remark {
        padding: 12px;
        margin-top: 12px;
        background-color: #f8f9fa;
        color: #4b5563;
        border-radius: 8px;
        font-size: 14px;
        line-height: 1.5;
        border-left: 3px solid #e5e7eb;
    }

    /* 优化状态标签样式 - 改为标签风格而非按钮风格 */
    .read_time {
        position: absolute;
        z-index: 100;
        top: 12px;
        right: 12px;
        padding: 3px 6px; /* 减少padding，更像标签 */
        color: #fff;
        border-radius: 4px; /* 减少圆角，更像标签 */
        font-size: 10px; /* 减小字体，更精致 */
        font-weight: 500; /* 减少字体粗细 */
        min-width: 40px;
        max-width: 56px;
        text-align: center;
        box-shadow: none; /* 移除阴影，减少按钮感 */
        line-height: 1.2;
        /* 确保标签完全在卡片内 */
        transform: translateX(-2px) translateY(0px);
        /* 使用flexbox确保文字垂直居中 */
        display: flex;
        align-items: center;
        justify-content: center;
        min-height: 18px; /* 减少最小高度 */
        white-space: nowrap; /* 防止文字换行 */
        border: 1px solid rgba(255, 255, 255, 0.3); /* 添加细边框增强标签感 */
    }

    /* 状态标签颜色 - 使用纯色而非渐变，更像标签 */
    .status_1 {
        background: #6b7280; /* 待审批 - 灰色 */
    }

    .status_2 {
        background: #3b82f6; /* 审批中 - 蓝色 */
    }

    .status_3 {
        background: #10b981; /* 已通过 - 绿色 */
    }

    .status_4 {
        background: #f59e0b; /* 已撤销 - 橙色 */
    }

    .status_5 {
        background: #ef4444; /* 已拒绝 - 红色 */
    }

    /* 优化标签页样式 - 移除所有指示器 */
    .layui-tab-title .layui-this:after {
        display: none; /* 隐藏默认的指示器 */
    }

    .layui-tab-title li {
        font-weight: 500;
        color: #6b7280;
        transition: all 0.3s ease;
        position: relative;
        border-bottom: none; /* 移除边框指示器 */
    }

    .layui-tab-title .layui-this {
        color: #1890ff !important;
        font-weight: 600;
        border-bottom: none; /* 移除边框指示器 */
    }

    .demo-tab-header .layui-btn.layui-this{
        border-color: #1890ff;
        color: #1890ff;
    }

    .demo-tab-body>div{
        display: none;
    }

    .layui-btn-container .layui-btn {
        margin-right: 0;
        margin-bottom: 0;
        word-spacing: normal;
        line-height: 50px;
        height: 50px;
        border: 0;
        z-index: 10001;
    }

    .demo-tab-header .layui-btn.layui-this {
        border-color: #1890ff;
        border-bottom: 1px solid;
        color: #1890ff;
    }

    /* 优化催审按钮样式 - 改为独立行显示 */
    .urge-btn-container {
        margin-top: 12px;
        padding-top: 12px;
        border-top: 1px solid #f0f2f5;
        display: flex;
        justify-content: flex-end;
    }

    /* 催审按钮在卡片内的独立显示 */
    .voice .urge-btn {
        position: relative;
        display: inline-flex; /* 使用inline-flex更适合按钮 */
        align-items: center; /* 垂直居中 */
        justify-content: center; /* 水平居中 */
        margin: 8px 0 0 auto; /* 右对齐 */
        background: linear-gradient(135deg, #ff6b35, #f7931e);
        color: white;
        border: none;
        padding: 10px 12px !important; /* 调整padding确保垂直居中 */
        border-radius: 12px;
        font-size: 12px;
        font-weight: 600;
        cursor: pointer;
        min-width: 48px;
        height: 28px; /* 固定高度确保一致性 */
        box-shadow: 0 2px 6px rgba(255, 107, 53, 0.3);
        transition: all 0.2s ease;
        letter-spacing: 0.2px;
        border-top: 1px solid #f0f2f5;
        padding-top: 6px; /* 统一padding */
        margin-top: 8px;
        /* 确保文字完美垂直居中 */
        line-height: 1.2; /* 调整行高 */
        vertical-align: middle; /* 添加垂直对齐 */
    }

    .urge-btn:hover {
        background: linear-gradient(135deg, #f7931e, #ff6b35);
        box-shadow: 0 4px 12px rgba(255, 107, 53, 0.4);
    }

    .urge-btn:active {
        background: linear-gradient(135deg, #e8851c, #f55a2c);
        box-shadow: 0 1px 4px rgba(255, 107, 53, 0.3);
    }

    /* 优化数据为空时的显示 */
    .data-none {
        display: flex;
        align-items: center;
        justify-content: center;
        flex-direction: column;
        width: 100%;
        height: 200px;
        color: #9ca3af;
    }

    .data-none::before {
        content: "📋";
        font-size: 48px;
        margin-bottom: 16px;
        opacity: 0.5;
    }

    /* 优化卡片容器间距 */
    .layui-col-md12 {
        margin-bottom: 0;
    }

    /* 优化整体页面布局 */
    .uni-page[data-page="pages/mine/index"] {
        padding: 4px 0 0 0;
        margin-bottom: 50px;
        margin-top: 100px;
    }

    /* 优化第一张卡片的顶部间距 - 与其他卡片保持一致 */
    .layui-col-md12:first-child .voice {
        margin-top: 4px; /* 减少第一张卡片的顶部间距 */
    }

    /* 优化固定头部样式 */
    .layui-card.border-x.border-t {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
        border-bottom: 1px solid #e5e7eb;
    }

    /* 优化标签页标题样式 */
    .layui-tab-title {
        background-color: transparent;
        border-bottom: 1px solid #e5e7eb;
    }

    .layui-tab-title li {
        position: relative;
        transition: all 0.3s ease;
    }

    /* 移除自定义指示器，使用边框替代 */

    /* 响应式优化 */
    @media (max-width: 375px) {
        .voice {
            margin: 3px 6px; /* 进一步减少小屏幕间距 */
            padding: 10px; /* 减少小屏幕内边距 */
        }

        .voice_title:first-child {
            padding-right: 60px; /* 小屏幕减少预留空间 */
        }

        .name {
            font-size: 15px;
            /* 移除最大宽度限制 */
        }

        .create_time {
            font-size: 11px;
            /* 移除margin-left */
        }

        .read_time {
            top: 14px;
            right: 14px;
            padding: 3px 6px; /* 保持标签风格的padding */
            font-size: 9px;
            min-width: 38px; /* 减少最小宽度 */
            max-width: 50px; /* 减少最大宽度 */
            min-height: 18px; /* 减少最小高度 */
            /* 小屏幕下确保不溢出 */
            transform: translateX(-3px) translateY(0px);
        }

        .voice .urge-btn {
            padding: 5px 10px; /* 小屏幕进一步减少padding */
            font-size: 11px;
            min-width: 44px;
            min-height: 26px; /* 减少最小高度 */
            border-radius: 14px; /* 减少圆角 */
            padding-top: 10px; /* 减少顶部内边距 */
            margin-top: 8px; /* 减少顶部外边距 */
            /* 确保小屏幕下文字也居中 */
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
            line-height: 1;
        }

        .urge-btn-container {
            margin-top: 8px;
            padding-top: 8px;
        }
    }

    @media (min-width: 414px) {
        .voice {
            margin: 6px 10px; /* 减少大屏幕间距 */
            padding: 14px; /* 减少大屏幕内边距 */
        }

        .voice_title:first-child {
            padding-right: 75px; /* 大屏幕预留空间 */
        }

        .name {
            font-size: 17px;
            /* 移除最大宽度限制 */
        }

        .create_time {
            font-size: 13px;
            /* 移除margin-left */
        }

        .read_time {
            top: 18px;
            right: 18px;
            padding: 4px 8px; /* 保持标签风格的padding */
            font-size: 10px;
            min-width: 44px; /* 减少最小宽度 */
            max-width: 60px; /* 减少最大宽度 */
            min-height: 20px; /* 减少最小高度 */
            /* 大屏幕下确保不溢出 */
            transform: translateX(-4px) translateY(0px);
        }

        .voice .urge-btn {
            padding: 6px 14px; /* 大屏幕适中的padding */
            font-size: 12px;
            min-width: 52px;
            min-height: 28px;
            border-radius: 16px;
            padding-top: 12px; /* 适中的顶部内边距 */
            margin-top: 10px; /* 适中的顶部外边距 */
            /* 确保大屏幕下文字也居中 */
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
            line-height: 1;
        }

        .urge-btn-container {
            margin-top: 12px;
            padding-top: 12px;
        }
    }

    /* 优化触摸反馈 */
    .voice:active,
    .layui-tab-title li:active {
        background-color: #f3f4f6;
    }

    /* 优化文字选择 */
    .voice * {
        -webkit-user-select: none;
        -moz-user-select: none;
        -ms-user-select: none;
        user-select: none;
    }

</style>
<div class="layui-layout-body">
    <div id="GouguApp">
        <div class="layui-layout gg-layout layout-menu-{$web.menu_mode|default='classical'}">

            <div class="layui-card border-x border-t"
                 style="margin-bottom:0; box-shadow:0 0 0 0 rgb(5 32 96 / 0%);
                    position: fixed;
                    top:0;left: 0;z-index: 1000;width: 100%">
                <div class="body-table layui-tab layui-tab-brief" lay-filter="tab_main">

                    <ul class="layui-tab-title" style="display: flex;height: 50px;line-height: 50px">
                        <li class="layui-this" style="flex: 1">我发起的</li>
                        <li style="flex: 1">经我审批</li>
                    </ul>
                    <div class="layui-tab-content">
                        <div class="layui-tab-item layui-show">
                            <div class="layui-card border-x border-t"
                                 style="margin-bottom:0; box-shadow:0 0 0 0 rgb(5 32 96 / 0%);
                                    position: fixed;margin-top: 50px;
                                    top:0;left: 0;z-index: 100;width: 100%">
                                <div class="body-table layui-tab layui-tab-brief" lay-filter="tab-1">
                                    <ul class="layui-tab-title" style="display: flex;height: 50px">
                                        <li class="layui-this" style="flex: 1;line-height: 50px">全部</li>
                                        <li style="flex: 1;line-height: 50px">待审批</li>
                                        <li style="flex: 1;line-height: 50px">审批通过</li>
                                        <li style="flex: 1;line-height: 50px">审批拒绝</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        <div class="layui-tab-item" style="display: block">
                            <div class="layui-card border-x border-t"
                                 style="margin-bottom:0; box-shadow:0 0 0 0 rgb(5 32 96 / 0%);
                                    position: fixed;margin-top: 50px;width: 100%;
                                    top:0;left: 0;z-index: 99">
                                <div class="body-table layui-tab layui-tab-brief" lay-filter="tab-2">
                                    <ul class="layui-tab-title" style="height: 50px;display: flex">
                                        <li class="layui-this" style="flex:1;line-height: 50px">全部</li>
                                        <li style="flex:1;line-height: 50px">待我审批{if condition="isset($pending_count) && $pending_count > 0"}<span style="color:#ff5722; margin-left:3px; font-size:12px;">({$pending_count})</span>{/if}</li>
                                        <li style="flex:1;line-height: 50px">我已审批</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>


            <div class="uni-page" data-page="pages/mine/index" style="margin-bottom: 50px;margin-top: 100px">
                <div class="layui-row" id="toolbarDemo">
                    <!--                    <div class="flow-demo" id="ID-flow-demo"></div>-->

                </div>
            </div>

            {include file="../../mobile/view/index/tabbar" /}


        </div>
    </div>
</div>


<!-- /主体 -->
{/block}
<!-- 脚本 -->
{block name="script"}
<script src="{__GOUGU__}/layui/layui.js"></script>
<script src="{__GOUGU__}/gougu/gouguInit.js"></script>
<script>
    const moduleInit = ['tool', 'employeepicker', 'laydatePlus'];

    function gouguInit() {
        var form = layui.form, table = layui.table, tool = layui.tool, element = layui.element,
            laydatePlus = layui.laydatePlus;

        var index = 0;
        var url = "/oa/approve/index";
        var currentUserId = {$uid}; // 当前用户ID

        element.on('tab(tab_main)', function(data){

            if (data.index == 0){
                url = "/oa/approve/index";
            }else{
                url = "/oa/approve/list";
            }
            getdata()
        });

        element.on('tab(tab-1)', function (data) {
            index = data.index;
            getdata()
        });
        element.on('tab(tab-2)', function (data) {
            index = data.index;
            getdata()
        });

        getdata()
        function getdata() {
            $.ajax({
                url: url,
                type: 'post',
                data: {"status": index},
                success: function (e) {
                    if (e.code == 0) {
                        let data = e.data;

                        var voice_html = "";
                        for (let i = 0; i < data.length; i++) {

                            var remark = "";
                            if (data[i].remark != null && data[i].remark != "") {
                                remark = `<div class="remark">${data[i].remark}</div>`;
                            }

                            var className = "status_1";
                            var read_text = "待审批";
                            if (data[i].check_status == 1) {
                                className = "status_2"
                                read_text = "审批中";
                            } else if (data[i].check_status == 2) {
                                className = "status_3"
                                read_text = "已通过";
                            }else if (data[i].check_status == 4) {
                                className = "status_4"
                                read_text = "已撤销";
                            }else if (data[i].check_status == 3) {
                                className = "status_5"
                                read_text = "已拒绝";
                            }

                            // 如果有催审且状态为待审批或审批中，且当前用户是被催审的审批人，添加催审标识
                            var urgeTag = '';
                            if (data[i].urge_count > 0 && (data[i].check_status == 0 || data[i].check_status == 1)) {
                                // 检查当前用户是否是当前节点的审批人
                                var checkAdminIds = data[i].check_admin_ids ? data[i].check_admin_ids.split(',') : [];
                                if (checkAdminIds.includes(currentUserId.toString())) {
                                    urgeTag = `<span style="background-color:#ff4444; color:white; padding:2px 6px; border-radius:8px; font-size:11px; display:inline-block;">催审中(${data[i].urge_count})</span>`;
                                }
                            }

                            var node_title = data[i].node_tit == null ? '' : data[i].node_tit

                            // 催审按钮逻辑：待审批(0)或审批中(1)状态且当前用户是申请人才显示催审按钮
                            var urgeBtn = '';
                            if ((data[i].check_status == 0 || data[i].check_status == 1) && data[i].admin_id == currentUserId) {
                                urgeBtn = `<button class="urge-btn" onclick="urgeApprove(${data[i].id}, event)">催审</button>`;
                            }

                            let voice = `<div class="layui-col-md12">
                        <div class="layui-panel voice" onclick="clickvoice(${data[i].id})">
                            <div class="voice_title">
                                <div class="name">${data[i].name} [${data[i].department_name}]</div>
                                <div class="create_time">${data[i].create_time}</div>
                            </div>
                            <div class="voice_title">
                                提交了<div class="flow_type b_color" style="color: #0000cc">『${data[i].flow_type}』</div>
                            </div>
                            <div class="voice_title">
                                申请人 <span class="b_color" style="color: #0000cc">『${data[i].aname}』</span>
                            </div>
                            <div class="voice_title">
                                当前 <span class="b_color" style="color: #0000cc">『${data[i].check_user}』</span>  正在处理
                                <span class="b_color" style="display:inline-block;width: 50%">${node_title}</span>
                            </div>
                            ${remark}
                            ${urgeBtn}
                            ${urgeTag ? '<div style="text-align: right; margin-top: 8px;">' + urgeTag + '</div>' : ''}
                        </div>
                        <div class="read_time ${className}">${read_text}</div>
                    </div>`;
                            voice_html += voice;
                        }

                        if (data.length == 0){
                            voice_html = `<div class="data-none" style="width:100%; height:200px;"></div>
                                <div style="color: #dbdbdb;text-align: center;margin-top: -30px">暂无数据</div>`;
                        }

                        $("#toolbarDemo").html(voice_html);
                    }
                }
            })
        }

    }

    function onclickbtn(e){

    }

    function clickvoice(id) {
        window.location.pathname = `/oa/approve/view/${id}`;
    }

    // 催审功能
    function urgeApprove(id, event) {
        // 阻止事件冒泡，防止触发卡片点击事件
        event.stopPropagation();

        if (confirm('确定要催审吗？')) {
            $.ajax({
                url: '/oa/approve/urge',
                type: 'post',
                data: {id: id},
                success: function(res) {
                    if (res.code == 0) {
                        alert('催审通知已发送');
                    } else {
                        alert(res.msg);
                    }
                },
                error: function() {
                    alert('请求失败，请重试');
                }
            });
        }
    }

</script>
{/block}
<!-- /脚本 -->