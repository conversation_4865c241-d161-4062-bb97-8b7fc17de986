{extend name="../../base/view/common/base" /}

{block name="style"}
<style>
    /* 编辑页面样式 */
    .layui-card {
        border: 1px solid #e6e6e6;
        border-radius: 4px;
        margin-bottom: 15px;
    }

    .layui-card-header {
        background-color: #f8f8f8;
        padding: 10px 15px;
        border-bottom: 1px solid #e6e6e6;
        font-weight: bold;
    }

    .layui-card-body {
        padding: 15px;
    }

    .layui-td-gray {
        background-color: #f5f5f5;
        font-weight: bold;
        text-align: left;
        vertical-align: middle;
        width: 140px;
        padding: 8px 12px;
    }

    /* 表格样式优化 */
    .layui-table {
        margin-bottom: 0;
    }

    .layui-table td {
        padding: 8px 12px;
        vertical-align: middle;
    }

    .layui-table th {
        background-color: #f8f8f8;
        font-weight: bold;
    }

    /* 必填项红色星号 */
    .required-mark {
        color: #FF6347;
        margin-left: 3px;
    }

    /* 页面内容区域 */
    .edit-content {
        padding: 20px;
        height: 100%;
        box-sizing: border-box;
        overflow-y: auto;
    }

    /* 金额输入框样式 - 参考门店分红明细表 */
    .amount-input {
        text-align: right;
        font-weight: bold;
    }

    /* 次数输入框样式 - 参考门店分红明细表 */
    .count-input {
        text-align: right;
        font-weight: bold;
    }

    /* 数字显示样式优化 - 参考门店分红明细表 */
    .layui-table td span {
        font-weight: bold;
    }

    /* 金额显示样式 */
    .amount-display {
        font-weight: bold;
        text-align: right;
    }

    /* 所有数字输入框的通用样式 */
    input[type="number"] {
        font-weight: bold;
    }

    /* 按钮组样式 */
    .button-group {
        text-align: center;
        padding: 20px 0;
        border-top: 1px solid #e9ecef;
        margin-top: 30px;
    }

    .button-group .layui-btn {
        margin: 0 10px;
        min-width: 100px;
    }
</style>
{/block}

{block name="body"}
<div class="edit-content">
    <!-- 页面标题 -->
    <div style="margin-bottom: 20px;">
        <h3 style="margin: 0 0 15px 0; font-size: 18px; font-weight: bold; color: #333;">编辑跨店结算汇总</h3>
    </div>

    <form class="layui-form" lay-filter="editForm">
        <input type="hidden" name="id" value="{$detail.id}">

        <!-- 基础信息 -->
        <div class="layui-card">
            <div class="layui-card-header">基础信息</div>
            <div class="layui-card-body">
                <table class="layui-table">
                    <tr>
                        <td class="layui-td-gray">汇总月份</td>
                        <td style="width: 200px;">
                            <span style="font-weight: bold;">{$detail.period}</span>
                            <input type="hidden" name="period" value="{$detail.period}">
                        </td>
                        <td class="layui-td-gray">总对账金额</td>
                        <td style="width: 200px;">
                            <input type="number" name="total_reconciliation_amount" value="{$detail.total_reconciliation_amount}" placeholder="请输入总对账金额" autocomplete="off" class="layui-input amount-input" step="0.01" min="0">
                        </td>
                    </tr>
                    <tr>
                        <td class="layui-td-gray">门店</td>
                        <td>
                            <span style="font-weight: bold;">{$detail.store_name|default='未知门店'}</span>
                            <input type="hidden" name="store_id" value="{$detail.store_id}">
                        </td>
                        <td class="layui-td-gray">门店类型<span class="required-mark">*</span></td>
                        <td>
                            <select name="store_type" lay-verify="required">
                                <option value="">请选择门店类型</option>
                                <option value="新店" {if $detail.store_type == '新店'}selected{/if}>新店</option>
                                <option value="老店" {if $detail.store_type == '老店'}selected{/if}>老店</option>
                            </select>
                        </td>
                    </tr>
                    <tr>
                        <td class="layui-td-gray">他店</td>
                        <td>
                            <span style="font-weight: bold;">{$detail.other_store_name|default='未知门店'}</span>
                            <input type="hidden" name="other_store_id" value="{$detail.other_store_id}">
                        </td>
                        <td class="layui-td-gray">他店类型<span class="required-mark">*</span></td>
                        <td>
                            <select name="other_store_type" lay-verify="required">
                                <option value="">请选择他店类型</option>
                                <option value="新店" {if $detail.other_store_type == '新店'}selected{/if}>新店</option>
                                <option value="老店" {if $detail.other_store_type == '老店'}selected{/if}>老店</option>
                            </select>
                        </td>
                    </tr>
                </table>
            </div>
        </div>

        <!-- 储值本金部分 -->
        <div class="layui-card">
            <div class="layui-card-header">储值本金部分</div>
            <div class="layui-card-body">
                <table class="layui-table">
                    <tr>
                        <td class="layui-td-gray">本店耗他店本金</td>
                        <td style="width: 200px;">
                            <input type="number" name="p_local_consume_foreign" value="{$detail.p_local_consume_foreign}" placeholder="0.00" autocomplete="off" class="layui-input amount-input" step="0.01" min="0">
                        </td>
                        <td class="layui-td-gray">本店回退他店本金</td>
                        <td style="width: 200px;">
                            <input type="number" name="p_local_refund_foreign" value="{$detail.p_local_refund_foreign}" placeholder="0.00" autocomplete="off" class="layui-input amount-input" step="0.01" min="0">
                        </td>
                    </tr>
                    <tr>
                        <td class="layui-td-gray">他店耗本店本金</td>
                        <td>
                            <input type="number" name="p_foreign_consume_local" value="{$detail.p_foreign_consume_local}" placeholder="0.00" autocomplete="off" class="layui-input amount-input" step="0.01" min="0">
                        </td>
                        <td class="layui-td-gray">他店回退本店本金</td>
                        <td>
                            <input type="number" name="p_foreign_refund_local" value="{$detail.p_foreign_refund_local}" placeholder="0.00" autocomplete="off" class="layui-input amount-input" step="0.01" min="0">
                        </td>
                    </tr>
                    <tr>
                        <td class="layui-td-gray">本金合计</td>
                        <td>
                            <input type="number" name="p_total_amount" value="{$detail.p_total_amount}" placeholder="0.00" autocomplete="off" class="layui-input amount-input" step="0.01" min="0">
                        </td>
                        <td class="layui-td-gray">本金对账金额</td>
                        <td>
                            <input type="number" name="p_reconciliation_amount" value="{$detail.p_reconciliation_amount}" placeholder="0.00" autocomplete="off" class="layui-input amount-input" step="0.01">
                        </td>
                    </tr>
                </table>
            </div>
        </div>

        <!-- 储值赠金部分 -->
        <div class="layui-card">
            <div class="layui-card-header">储值赠金部分</div>
            <div class="layui-card-body">
                <table class="layui-table">
                    <tr>
                        <td class="layui-td-gray">本店耗他店赠金</td>
                        <td style="width: 200px;">
                            <input type="number" name="b_local_consume_foreign" value="{$detail.b_local_consume_foreign}" placeholder="0.00" autocomplete="off" class="layui-input amount-input" step="0.01" min="0">
                        </td>
                        <td class="layui-td-gray">本店回退他店赠金</td>
                        <td style="width: 200px;">
                            <input type="number" name="b_local_refund_foreign" value="{$detail.b_local_refund_foreign}" placeholder="0.00" autocomplete="off" class="layui-input amount-input" step="0.01" min="0">
                        </td>
                    </tr>
                    <tr>
                        <td class="layui-td-gray">他店耗本店赠金</td>
                        <td>
                            <input type="number" name="b_foreign_consume_local" value="{$detail.b_foreign_consume_local}" placeholder="0.00" autocomplete="off" class="layui-input amount-input" step="0.01" min="0">
                        </td>
                        <td class="layui-td-gray">他店回退本店赠金</td>
                        <td>
                            <input type="number" name="b_foreign_refund_local" value="{$detail.b_foreign_refund_local}" placeholder="0.00" autocomplete="off" class="layui-input amount-input" step="0.01" min="0">
                        </td>
                    </tr>
                    <tr>
                        <td class="layui-td-gray">赠金合计</td>
                        <td>
                            <input type="number" name="b_total_amount" value="{$detail.b_total_amount}" placeholder="0.00" autocomplete="off" class="layui-input amount-input" step="0.01" min="0">
                        </td>
                        <td class="layui-td-gray">赠金对账金额</td>
                        <td>
                            <input type="number" name="b_reconciliation_amount" value="{$detail.b_reconciliation_amount}" placeholder="0.00" autocomplete="off" class="layui-input amount-input" step="0.01">
                        </td>
                    </tr>
                </table>
            </div>
        </div>

        <!-- 次卡部分 -->
        <div class="layui-card">
            <div class="layui-card-header">次卡部分</div>
            <div class="layui-card-body">
                <table class="layui-table">
                    <tr>
                        <td class="layui-td-gray">本店耗他店卡(次数)</td>
                        <td style="width: 200px;">
                            <input type="number" name="cc_local_consume_foreign_count" value="{$detail.cc_local_consume_foreign_count}" placeholder="0" autocomplete="off" class="layui-input count-input" min="0" step="1">
                        </td>
                        <td class="layui-td-gray">本店回退他店卡(次数)</td>
                        <td style="width: 200px;">
                            <input type="number" name="cc_local_refund_foreign_count" value="{$detail.cc_local_refund_foreign_count}" placeholder="0" autocomplete="off" class="layui-input count-input" min="0" step="1">
                        </td>
                    </tr>
                    <tr>
                        <td class="layui-td-gray">他店耗本店卡(次数)</td>
                        <td>
                            <input type="number" name="cc_foreign_consume_local_count" value="{$detail.cc_foreign_consume_local_count}" placeholder="0" autocomplete="off" class="layui-input count-input" min="0" step="1">
                        </td>
                        <td class="layui-td-gray">他店回退本店卡(次数)</td>
                        <td>
                            <input type="number" name="cc_foreign_refund_local_count" value="{$detail.cc_foreign_refund_local_count}" placeholder="0" autocomplete="off" class="layui-input count-input" min="0" step="1">
                        </td>
                    </tr>
                    <tr>
                        <td class="layui-td-gray">本店升他店卡(金额)</td>
                        <td>
                            <input type="number" name="cc_local_upgrade_foreign_amount" value="{$detail.cc_local_upgrade_foreign_amount}" placeholder="0.00" autocomplete="off" class="layui-input amount-input" step="0.01" min="0">
                        </td>
                        <td class="layui-td-gray">他店升本店卡(金额)</td>
                        <td>
                            <input type="number" name="cc_foreign_upgrade_local_amount" value="{$detail.cc_foreign_upgrade_local_amount}" placeholder="0.00" autocomplete="off" class="layui-input amount-input" step="0.01" min="0">
                        </td>
                    </tr>
                    <tr>
                        <td class="layui-td-gray">次卡跨店金额合计</td>
                        <td>
                            <input type="number" name="cc_total_amount" value="{$detail.cc_total_amount}" placeholder="0.00" autocomplete="off" class="layui-input amount-input" step="0.01" min="0">
                        </td>
                        <td class="layui-td-gray">次卡对账金额</td>
                        <td>
                            <input type="number" name="cc_reconciliation_amount" value="{$detail.cc_reconciliation_amount}" placeholder="0.00" autocomplete="off" class="layui-input amount-input" step="0.01">
                        </td>
                    </tr>
                </table>
            </div>
        </div>

        <!-- 网店核销部分 -->
        <div class="layui-card">
            <div class="layui-card-header">网店核销部分</div>
            <div class="layui-card-body">
                <table class="layui-table">
                    <tr>
                        <td class="layui-td-gray">本店核销他店码(次数)</td>
                        <td style="width: 200px;">
                            <input type="number" name="ol_local_redeem_foreign_count" value="{$detail.ol_local_redeem_foreign_count}" placeholder="0" autocomplete="off" class="layui-input count-input" min="0" step="1">
                        </td>
                        <td class="layui-td-gray">本店回退他店码(次数)</td>
                        <td style="width: 200px;">
                            <input type="number" name="ol_local_refund_foreign_count" value="{$detail.ol_local_refund_foreign_count}" placeholder="0" autocomplete="off" class="layui-input count-input" min="0" step="1">
                        </td>
                    </tr>
                    <tr>
                        <td class="layui-td-gray">他店核销本店码(次数)</td>
                        <td>
                            <input type="number" name="ol_foreign_redeem_local_count" value="{$detail.ol_foreign_redeem_local_count}" placeholder="0" autocomplete="off" class="layui-input count-input" min="0" step="1">
                        </td>
                        <td class="layui-td-gray">他店回退本店码(次数)</td>
                        <td>
                            <input type="number" name="ol_foreign_refund_local_count" value="{$detail.ol_foreign_refund_local_count}" placeholder="0" autocomplete="off" class="layui-input count-input" min="0" step="1">
                        </td>
                    </tr>
                    <tr>
                        <td class="layui-td-gray">网店核销金额合计</td>
                        <td>
                            <input type="number" name="ol_total_amount" value="{$detail.ol_total_amount}" placeholder="0.00" autocomplete="off" class="layui-input amount-input" step="0.01" min="0">
                        </td>
                        <td class="layui-td-gray">网店核销对账金额</td>
                        <td>
                            <input type="number" name="ol_reconciliation_amount" value="{$detail.ol_reconciliation_amount}" placeholder="0.00" autocomplete="off" class="layui-input amount-input" step="0.01">
                        </td>
                    </tr>
                </table>
            </div>
        </div>

        <!-- 提交按钮 -->
        <div style="margin-top: 15px;">
            <button class="layui-btn layui-btn-normal" lay-submit lay-filter="submitForm">保存修改</button>
            <button type="button" class="layui-btn layui-btn-primary" onclick="parent.layer.closeAll()">取消</button>
        </div>
    </form>
</div>
{/block}

{block name="script"}
<script>
    const moduleInit = ['form'];
    function gouguInit() {
        var form = layui.form;

        // 表单提交
        form.on('submit(submitForm)', function(data) {
            var loadIndex = layer.load(2, {shade: 0.3});
            
            $.post('/cross/summary/edit', data.field, function(res) {
                layer.close(loadIndex);
                if (res.code == 0) {
                    layer.msg(res.msg, {icon: 1, time: 2000}, function() {
                        parent.layer.closeAll();
                        // 触发父页面刷新
                        if (parent.layui && parent.layui.pageTable) {
                            parent.layui.pageTable.reload();
                        }
                    });
                } else {
                    layer.msg(res.msg, {icon: 2, time: 3000});
                }
            }).fail(function() {
                layer.close(loadIndex);
                layer.msg('网络错误，请重试', {icon: 2});
            });
            
            return false;
        });

        // 表单渲染
        form.render();
    }
</script>
{/block}
