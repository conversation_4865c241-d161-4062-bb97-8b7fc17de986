<?php

namespace app\contract\controller;

use app\base\BaseController;
use app\contract\model\Contract as ContractList;
use app\contract\service\ContractrentService;
use app\contract\validate\ContractCheck;
use app\user\model\DepartmentChange;
use think\console\command\make\Model;
use think\exception\ValidateException;
use think\facade\Db;
use think\facade\View;
use think\facade\Session;

class Contractrent extends BaseController
{
    public function index()
    {

        $DepartmentChange = new DepartmentChange();
        $dep_fenguan = $DepartmentChange->get_dep_fenguan(0, $this->uid);

        $dids = array();

        foreach ($dep_fenguan as $k => $v) {
            $dids[] = $v['id'];
        }
        if (count($dep_fenguan) > 30){
            $dids[] = 1;
        }

        if (request()->isAjax()) {
            $param = get_params();

            $rows = empty($param['limit']) ? get_config('app.page_size') : $param['limit'];

            $where = '';

            if (isset($param['status']) && $param['status'] != 0) {
                $where = "cr.status = {$param['status']}";
            } else if (isset($param['status']) && $param['status'] == 0) {
                $where = "cr.status is not null";
            }else {
                $where = 'cr.status = 1';
            }

            if (isset($param['cate_id']) && !empty($param['cate_id'])) {
                $where .= " AND cr.cate_id = {$param['cate_id']} ";
            }

            if (isset($param['keywords']) && !empty($param['keywords'])) {
                $where .= " AND cr.code LIKE '%{$param['keywords']}%' ";
            }

            if (isset($param['did']) && !empty($param['did'])) {
                $where .= " AND cr.did = {$param['did']} ";
            } else {
                $implode = implode(",", $dids);
                $where .= " AND cr.did in ($implode) ";
            }

            if (isset($param['is_invoice']) && !empty($param['is_invoice'])) {
                $where .= " AND cr.is_invoice = {$param['is_invoice']} ";
            }

            $sql = "
            SELECT `cr`.*,d.title as dname,a.name as create_aname,cc.name as cate_name ,
                   s.pay_date as recent_pay,s.check_status as recent_check_status,s.is_pay as recent_is_pay,
                   s.ei_amount
FROM `oa_contract_rent` `cr` 
LEFT JOIN `oa_type_pay` `cc` ON `cc`.`id`=`cr`.`cate_id` 
LEFT JOIN `oa_department` `d` ON `d`.`id`=`cr`.`did` 
LEFT JOIN `oa_admin` `a` ON `a`.`id`=`cr`.`create_aid` 
left join (
	select cr_id,MIN(pay_date) as pay_date,check_status,is_pay,ei_amount from oa_rent 
	where 
	pay_date is not null
	and is_pay = 0
	GROUP BY cr_id
 ) s on cr.id = s.cr_id 
WHERE  
    $where 
ORDER BY s.pay_date asc 
            ";

            $list = Db::query($sql);

            $data['data'] = $list;
            $data['total'] = count($list);

//            return ['code' => 0 , 'data' => $data];
            return table_assign(0, '', $data);
        } else {
            $uid = $this->uid;
            $auth = isAuth($uid, 'contract_admin');
            View::assign('auth', $auth);

            $store = Db::name("store")->where([ 'sdate' => date("Y-m") , 'holder_id' => $uid])->group("did")->select();

            $dids = array();
            foreach ($store as $k => $v){
                $dids[] = [
                    'id' => $v['did'],
                    'title' => $v['dname'],
                ];
            }

            foreach ($dep_fenguan as $k => $v){
                $dids[] = [
                    'id' => $v['id'],
                    'title' => $v['title'],
                ];
            }

            View::assign('dep', $dids);
            View::assign('did', $this->storeid);
            return view();
        }
    }

    //添加&&编辑
    public function add()
    {
        $param = get_params();
        if (request()->isAjax()) {

            if (isset($param['id']) && !empty($param['id'])) {
                Db::name('contract_rent')->where(['id' => $param['id']])
                    ->update([
                        'did' => $param['did'],
                        'dz_aid' => $param['dz_aid'],
                        'dz_name' => $param['dz_name'],
                        'dz_mobile' => $param['dz_mobile'],
                        'files' => $param['files'],
                        'is_invoice' => $param['is_invoice'],

                        'account_name' => $param['account_name'],
                        'account_number' => $param['account_number'],
                        'account_bank' => $param['account_bank'],
                        'address' => $param['address'],

                    ]);

                Db::name('rent')->where(['cr_id' => $param['id']])
                    ->update([
                        'did' => $param['did']
                    ]);

                return to_assign();
            }

            $type_pay = Db::name('type_pay')->where(['id' => $param['cate_id']])->find();

            if (!empty($type_pay) && !empty($type_pay['prefix'])) {
                $param['code'] = $type_pay['prefix'] . $param['code'];
            }

            $param['create_time'] = date("Y-m-d H:i:s");
            $param['create_aid'] = $this->uid;

            $id = Db::name('contract_rent')->strict(false)->field(true)->insertGetId($param);
            $param['id'] = $id;

            $rent_growth = [];
            if (isset($param['rent_gc']) && !empty($param['rent_gc'])) {
                foreach ($param['rent_gc'] as $k => $v) {
                    if ($v['month'] == null) {
                        unset($param['rent_gc'][$k]);
                    } else {
                        $rent_growth[] = [
                            'cr_id' => $id,
                            'gc_month' => $v['month'],
                            'gc_amount' => $v['amount'],
                            'status' => 1,
                            'sort' => count($rent_growth) + 1
                        ];
                    }
                }
                $param['rent_gc'] = array_values($param['rent_gc']);
            }

            if (isset($param['wyf_gc']) && !empty($param['wyf_gc'])) {
                $param['wyf_gc'] = array_values($param['wyf_gc']);
                foreach ($param['wyf_gc'] as $k => $v) {
                    if (empty($v['month'])) {
                        unset($param['wyf_gc'][$k]);
                    } else {
                        $rent_growth[] = [
                            'cr_id' => $id,
                            'gc_month' => $v['month'],
                            'gc_amount' => $v['amount'],
                            'gc_per' => $v['per'],
                            'status' => 2,
                            'sort' => count($rent_growth) + 1
                        ];
                    }
                }
            }
            Db::name('rent_growth')->strict(false)->field(true)->insertAll($rent_growth);

            if (isset($param['i_count']) && $param['i_count'] == 0) {
                $ContractrentService = new ContractrentService();
                $ContractrentService->insertRent($param);
            }


            return to_assign();
        } else {
            $id = 0;
            if (isset($param['id']) && !empty($param['id'])) {
                $id = $param['id'];
                $contract_rent = Db::name('contract_rent')
                    ->field('cr.*,cc.name as cate_name')
                    ->alias('cr')
                    ->join('type_pay cc', 'cc.id = cr.cate_id', 'LEFT')
                    ->where(['cr.id' => $id])
                    ->find();

                if (!empty($contract_rent['files'])) {
                    $file_array = Db::name('File')->order('create_time desc')->where([['id', 'in', $contract_rent['files']]])->select()->toArray();
                    $contract_rent['file_array'] = $file_array;
                }

                if (!empty($contract_rent['fcz_files'])) {
                    $file_fcz_array = Db::name('File')->order('create_time desc')->where([['id', 'in', $contract_rent['fcz_files']]])->select()->toArray();
                    $contract_rent['file_fcz_array'] = $file_fcz_array;
                }

                if (!empty($contract_rent['fd_files'])) {
                    $file_fd_array = Db::name('File')->order('create_time desc')->where([['id', 'in', $contract_rent['fd_files']]])->select()->toArray();
                    $contract_rent['file_fd_array'] = $file_fd_array;
                }

                if (!empty($contract_rent['yyzz_files'])) {
                    $file_yyzz_array = Db::name('File')->order('create_time desc')->where([['id', 'in', $contract_rent['fd_files']]])->select()->toArray();
                    $contract_rent['file_yyzz_array'] = $file_yyzz_array;
                }

                $contract_rent['rent_gc'] = Db::name('rent_growth')->where(['cr_id' => $id, 'status' => 1])->order('sort asc')->select()->toArray();
                $contract_rent['wyf_gc'] = Db::name('rent_growth')->where(['cr_id' => $id, 'status' => 2])->order('sort asc')->select()->toArray();

                if (isset($param['type'])) {
                    $id = 0;
                }

                $additional = [];
                if (!empty($contract_rent['additional'])){
                    $additional = unserialize($contract_rent['additional']);
                }
                $contract_rent['additional'] = $additional;

                View::assign('contract_rent', $contract_rent);
            }

            $stores = Db::name('Department')
                ->field(" dep.id , dep.leader_id,dep.title,a.name as dzname,a.id as dzid,a.mobile as dzmobile")
                ->alias('dep')
                ->join('admin a', 'dep.leader_id = a.id and a.status = 1 ', 'left')
                ->where([
                    ['dep.remark', 'in', '门店']
                ])->select()->toArray();


            View::assign('codeno', date('YmdHis') . rand(10, 99));
            View::assign('stores', $stores);
            View::assign('id', $id);
            return view();
        }
    }


    //查看合同详情
    public function view()
    {
        $param = get_params();
        $id = $param['id'] ?? 0;

        if (empty($id)) {
            return '合同ID不能为空';
        }

        $contract_rent = Db::name('contract_rent')
            ->field('cr.*,cc.name as cate_name,d.title as dname')
            ->alias('cr')
            ->join('type_pay cc', 'cc.id = cr.cate_id', 'LEFT')
            ->join('department d', 'd.id = cr.did', 'LEFT')
            ->where(['cr.id' => $id])
            ->find();

        if (empty($contract_rent)) {
            return '合同不存在';
        }

        // 获取附件信息
        if (!empty($contract_rent['files'])) {
            $file_array = Db::name('File')->order('create_time desc')->where([['id', 'in', $contract_rent['files']]])->select()->toArray();
            $contract_rent['file_array'] = $file_array;
        }

        if (!empty($contract_rent['fcz_files'])) {
            $file_fcz_array = Db::name('File')->order('create_time desc')->where([['id', 'in', $contract_rent['fcz_files']]])->select()->toArray();
            $contract_rent['file_fcz_array'] = $file_fcz_array;
        }

        if (!empty($contract_rent['fd_files'])) {
            $file_fd_array = Db::name('File')->order('create_time desc')->where([['id', 'in', $contract_rent['fd_files']]])->select()->toArray();
            $contract_rent['file_fd_array'] = $file_fd_array;
        }

        if (!empty($contract_rent['yyzz_files'])) {
            $file_yyzz_array = Db::name('File')->order('create_time desc')->where([['id', 'in', $contract_rent['yyzz_files']]])->select()->toArray();
            $contract_rent['file_yyzz_array'] = $file_yyzz_array;
        }

        // 获取增长周期信息
        $contract_rent['rent_gc'] = Db::name('rent_growth')->where(['cr_id' => $id, 'status' => 1])->order('sort asc')->select()->toArray();
        $contract_rent['wyf_gc'] = Db::name('rent_growth')->where(['cr_id' => $id, 'status' => 2])->order('sort asc')->select()->toArray();

        // 获取续签/改签明细
        $additional = [];
        if (!empty($contract_rent['additional'])){
            $additional = unserialize($contract_rent['additional']);
        }
        $contract_rent['additional'] = $additional;

        // 检测是否为移动端访问
        $param = get_params();
        if (isset($param['m']) && $param['m'] == 'mobile') {
            Session::set("is_mobile", "is_mobile");
        }
        $is_mobile = Session::get("is_mobile");

        View::assign('contract_rent', $contract_rent);

        if (!empty($is_mobile)) {
            // 移动端返回专用模板
            return view('mobile_view');
        } else {
            // PC端返回原有模板
            return view();
        }
    }

    public function delete()
    {
        if (request()->isAjax()) {
            $param = get_params();

            Db::name('contract_rent')->where([['id', 'in', $param['id']]])->update(['status' => $param['status']]);
            Db::name('rent')->where([['cr_id', 'in', $param['id']]])->update(['status' => $param['status']]);

            return to_assign();
        }
    }

    //移动端合同选择器页面
    public function mobile_picker()
    {
        if (request()->isAjax()) {
            // Ajax请求返回合同数据
            $param = get_params();

            // 获取用户分管部门权限，与租聘合同列表保持一致的权限控制
            $DepartmentChange = new DepartmentChange();
            $dep_fenguan = $DepartmentChange->get_dep_fenguan(0, $this->uid);

            $dids = array();
            foreach ($dep_fenguan as $k => $v) {
                $dids[] = $v['id'];
            }
            if (count($dep_fenguan) > 30){
                $dids[] = 1;
            }

            $where = array();
            if (!empty($param['keywords'])) {
                $where[] = ['code|party_a_name|party_a_person|address', 'like', '%' . $param['keywords'] . '%'];
            }
            $where[] = ['status', '=', 1]; // 只查询启用状态的合同

            // 应用部门权限控制
            if (!empty($dids)) {
                $where[] = ['did', 'in', $dids];
            }

            $rows = empty($param['limit']) ? 20 : $param['limit'];
            $list = Db::name('contract_rent')
                ->field('id,code,party_a_name,party_a_person,party_a_mobile,address,rent_amount,deposit_amount')
                ->where($where)
                ->order('id desc')
                ->paginate($rows, false);

            $data = [];
            foreach ($list->items() as $item) {
                // 格式化显示信息
                $item['display_name'] = $item['party_a_name'];
                if (!empty($item['party_a_person']) && $item['party_a_person'] != $item['party_a_name']) {
                    $item['display_name'] .= '(' . $item['party_a_person'] . ')';
                }

                // 确保金额是数字类型再格式化
                $rent_amount = floatval($item['rent_amount'] ?? 0);
                $deposit_amount = floatval($item['deposit_amount'] ?? 0);
                $item['rent_amount_formatted'] = number_format($rent_amount, 2);
                $item['deposit_amount_formatted'] = number_format($deposit_amount, 2);
                $item['rent_amount_raw'] = $rent_amount;
                $item['deposit_amount_raw'] = $deposit_amount;

                $data[] = $item;
            }

            return json([
                'code' => 0,
                'msg' => '',
                'count' => $list->total(),
                'data' => $data
            ]);
        } else {
            // 返回移动端选择页面
            return view('mobile_picker');
        }
    }

}