<?php

namespace app\mobile\controller;

use app\base\BaseController;
use app\message\service\QiWeiService;
use app\user\controller\AdminView;
use app\user\service\AdminService;
use think\facade\Db;
use think\facade\Request;
use think\facade\Session;
use think\facade\View;

class Index
{

    /**
     * @return \think\response\View
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * 面试登记页面
     */
    public function interview()
    {
        $admin = Db::name('Admin')->where(['id' => 1305])->find();
        $data = [
            'is_lock' => 0,
            'last_login_time' => time(),
            'last_login_ip' => request()->ip(),
            'login_num' => $admin['login_num'] + 1,
        ];
        Db::name('admin')->where(['id' => $admin['id']])->update($data);
        $session_admin = get_config('app.session_admin');
        Session::set($session_admin, $admin['id']);
        $token = make_token();
        set_cache($token, $admin, 7200);
        $admin['token'] = $token;
        $logdata = [
            'uid' => $admin['id'],
            'type' => 'login',
            'action' => '登录',
            'subject' => '系统',
            'param_id' => $admin['id'],
            'param' => '[]',
            'ip' => request()->ip(),
            'create_time' => time()
        ];
        Db::name('AdminLog')->strict(false)->field(true)->insert($logdata);

        $adminService = new AdminService();
        $param = $adminService->getParam();
        $status_str = $param['status_str'];
        $edu_background = $param['equ_background'];

        View::assign('login_admin', $admin);
        View::assign('did', 0);
        View::assign('status_str', $status_str);
        View::assign('department', getmd(['门店']));
        View::assign('edu_background', $edu_background);
        View::assign('id', 0);
        return View("../../user/view/adminview/add");
    }

    public function test()
    {
        $store_records = Db::name("store_record")
            ->where(['status' => 2])->select()->toArray();

        foreach ($store_records as $k => $v) {
            $t = strtotime(date("Y-m-d 12:00:00", $v['create_time']));
            $create_time = $v['create_time'];

            if ($create_time > $t) {
                continue;
            }
            $sdate = strtotime(date("Y-m-d 12:00:00", $v['sdate']) . ' +1 day');
            if ($sdate == $t) {
                var_dump($v['id']);
                var_dump("</br>");
                Db::name("store_record")
                    ->where(['id' => $v['id']])->delete();
            }

        }


    }

    public function test2()
    {
//        $admin = Db::name("admin")->where(
//            [ ['position_id','=','85'],['name','like','%店%'] ]
//        )->column('id,name,did');

//        foreach ($admin as $k => $v){
//            Db::name("admin")->where(['id'=>$v['id']])->update(
//                ['did'=>"{$v['did']},94"]
//            );
//        }

        $admin = Db::name("StoreRankCalculate")->order("id asc")->select();
        foreach ($admin as $k => $v) {
            unset($v['id']);
            $v['sdate'] = '2024-06';
            Db::name("StoreRankCalculate")->strict(false)->field(true)
                ->insertGetId($v);
        }

    }

    public function qiweitest()
    {

    }

    public function qiweinoteview()
    {
        var_dump("qiweinoteview");
        //qdeM1546H-9Cs1QuvU9uI04auG0sVzlzoLP5tMF-MRg
        $param = get_params();

        $BASE_URL = "/?m=mobile";

        if (isset($param['code']) && !empty($param['code'])){
            $qiweiservice = new QiWeiService();
            $url = "https://qyapi.weixin.qq.com/cgi-bin/auth/getuserinfo?access_token=".$qiweiservice->getOAtoken()."&code={$param['code']}";
            $re = $qiweiservice->ajax($url, [], 'GET');

            if (!empty($re)){
                $resposen = json_decode($re);
                $admin = Db::name('Admin')->where(['wx_account' => $resposen->userid])->find();
                if (!empty($admin)){
                    $re_f = $this->autoLogin($admin['id']);

                    if ($re_f && !empty($param['state'])){
                        $BASE_URL = urldecode($param['state']);
                    }
                }
            }

        }

        return redirect($BASE_URL);

    }


    public function autoLogin($aid)
    {
        if (empty($aid)) return false;

        $admin = Db::name('Admin')->where(['id' => $aid])->find();

        $data = [
            'is_lock' => 0,
            'last_login_time' => time(),
            'last_login_ip' => request()->ip(),
            'login_num' => $admin['login_num'] + 1,
        ];
        Db::name('admin')->where(['id' => $admin['id']])->update($data);
        $session_admin = get_config('app.session_admin');
        Session::set($session_admin, $admin['id']);
        $token = make_token();
        set_cache($token, $admin, 7200);
        $admin['token'] = $token;
        $logdata = [
            'uid' => $admin['id'],
            'type' => 'login',
            'action' => '登录',
            'subject' => '系统',
            'param_id' => $admin['id'],
            'param' => '[]',
            'ip' => request()->ip(),
            'create_time' => time()
        ];
        Db::name('AdminLog')->strict(false)->field(true)->insert($logdata);
        return true;
    }

    public function view()
    {
        return view();
    }


    public function train_sign_in()
    {
        $param = get_params();

        $id = isset($param['trainId']) ? $param['trainId'] : 0;

        if (request()->isAjax()) {

            $ip = request()->ip();

            $train_sign = Db::name("train_sign")->where([
                'aid' => $param['aid'],
                'did' => $param['did'],
                'ip' => $ip,
                'status' => 1,
                ['create_time', '>=' , date("Y-m-d 00:00:00")],
                ['create_time', '<=' , date("Y-m-d 23:59:59")]
            ])->find();

            if (!empty($train_sign)){
                return to_assign(1, '请勿重复签到');
            }

            $param['ip'] = $ip;
            $param['create_time'] = date("Y-m-d H:i:s");
            Db::name("train_sign")->insertGetId($param);

            return to_assign(0, '签到成功');

        } else{
            $admin = Db::name('Admin')->where(['id' => 1305])->find();
            $data = [
                'is_lock' => 0,
                'last_login_time' => time(),
                'last_login_ip' => request()->ip(),
                'login_num' => $admin['login_num'] + 1,
            ];
            Db::name('admin')->where(['id' => $admin['id']])->update($data);
            $session_admin = get_config('app.session_admin');
            Session::set($session_admin, $admin['id']);
            $token = make_token();
            set_cache($token, $admin, 7200);
            $admin['token'] = $token;
            $logdata = [
                'uid' => $admin['id'],
                'type' => 'login',
                'action' => '登录',
                'subject' => '系统',
                'param_id' => $admin['id'],
                'param' => '[]',
                'ip' => request()->ip(),
                'create_time' => time()
            ];
            Db::name('AdminLog')->strict(false)->field(true)->insert($logdata);
            View::assign('login_admin', $admin);

            if ($id > 0){
                $detail = Db::name("train_programs")->where(['id' => $id])->find();
                View::assign("detail",$detail);
            }

            View::assign("id",$id);
            return view();
        }
    }

}