<?php
declare (strict_types=1);
/**
 * @copyright Copyright (c) 2021 仲正堂工作室
 * @license https://opensource.org/licenses/GPL-3.0
 * @link https://www.gougucms.com
 */

namespace app\api\controller;

use app\api\BaseController;
use app\oa\service\YouzanService;
use app\user\model\Admin as AdminList;
use app\user\model\DepartmentChange;
use PhpOffice\PhpSpreadsheet\Cell\DataType;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Style\Fill;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use think\facade\Db;
use app\user\model\Admin;
use app\customer\model\Customer;
use avatars\MDAvatars;
use Overtrue\Pinyin\Pinyin;
use PhpOffice\PhpSpreadsheet\IOFactory;
use PhpOffice\PhpSpreadsheet\Shared\Date as Shared;
use PhpOffice\PhpSpreadsheet\Cell\Coordinate;
use app\user\service\AdminService;

class Export
{
    public function index()
    {
        var_dump(111111);
    }

    public function exportstoreachievement()
    {

        $param = get_params();

        $newExcel = new Spreadsheet();  //创建一个新的excel文档
        $objSheet = $newExcel->getActiveSheet();  //获取当前操作sheet的对象
        $objSheet->setTitle('OA区域业绩模板');  //设置当前sheet的标题

        //设置宽度为true,不然太窄了
        $newExcel->getActiveSheet()->getColumnDimension('A')->setAutoSize(true);
        $newExcel->getActiveSheet()->getColumnDimension('B')->setAutoSize(true);
        $newExcel->getActiveSheet()->getColumnDimension('C')->setAutoSize(true);
        $newExcel->getActiveSheet()->getColumnDimension('D')->setAutoSize(true);

        //设置第一栏的标题
        $objSheet->setCellValue('A1', '年份')
            ->setCellValue('B1', '季度')
            ->setCellValue('C1', '门店名称')
            ->setCellValue('D1', '季度目标');

        //默认数据
        $Department = Db::name("Department")->where(['remark' => '门店'])->select();

        //第二行起，每一行的值,setCellValueExplicit是用来导出文本格式的。
        //->setCellValueExplicit('C' . $k, $val['admin_password']PHPExcel_Cell_DataType::TYPE_STRING),可以用来导出数字不变格式
        $baseRow = 2; //数据从N-1行开始往下输出 这里是避免头信息被覆盖
        $months = getSeason();
        foreach ($Department as $k => $val) {
            $i = $k + $baseRow;
            $objSheet->setCellValue('A' . $i, date("Y"))
                ->setCellValue('B' . $i, $months['index'])
                ->setCellValue('C' . $i, $val['title']);

        }

        $this->downloadExcel($newExcel, 'OA区域业绩模板', 'Xls');
    }

    public function export_user()
    {
        set_time_limit(0);
        $param = get_params();
        $where = array();
        if (!empty($param['keywords'])) {
            $where[] = ['id|username|name|nickname|mobile|desc', 'like', '%' . $param['keywords'] . '%'];
        }

        if (isset($param['status']) && $param['status'] != '') {
            $where[] = ['status', '=', $param['status']];
        }

        if (!empty($param['type'])) {
            $where[] = ['type', '=', $param['type']];
        }
        if (isset($param['position_id']) && !empty($param['position_id'])) {
//                $where[] = ['position_id', 'find_in_set', $param['position_id']];
            $where[] = ['', 'exp', Db::raw("FIND_IN_SET('{$param['position_id']}',position_id)")];
        }

        $whereor = [];
        if (!empty($param['did'])) {
            $dep_son = get_department_son($param['did']);
            foreach ($dep_son as $k => $v) {
                $whereor[] = ['did', 'find in set', $v];
            }

            if ($param['did'] != 94) {
                $where[] = ['did', 'not like', "%94%"];
            }
        }
        $where['is_virtually'] = 0;
        $rows = 3000;

        $admin = AdminList::where($where)
            ->where(function ($query) use ($whereor) {
                $query->whereOr($whereor);
            })
            ->order('id asc')
            ->paginate($rows, false, ['query' => $param])
            ->each(function ($item, $key) {
                $type = ['未设置', '正式', '试用', '实习'];
                $item->type = $type[$item->type];
                $sex = ['未设置', '男', '女'];
                $item->sex = $sex[$item->sex];
                $status = ['禁止登录', '正常', '离职'];
                $item->status = $status[$item->status];

                $admin_service = new AdminService();
                $lo_dep = $admin_service->getPdepartment($item->main_did);
                $lo_dep = array_reverse($lo_dep);

                $item->d0 = isset($lo_dep[0]) ? $lo_dep[0] : '';
                $item->d1 = isset($lo_dep[1]) ? $lo_dep[1] : '';
                $item->d2 = isset($lo_dep[2]) ? $lo_dep[2] : '';
                $item->d3 = isset($lo_dep[3]) ? $lo_dep[3] : '';

                $item->rank_name = "";
                if (stripos($item->d1, "分管") !== false ){
                    $groupId = Db::name('PositionGroup')->where(['pid' => $item->main_position_id])->column('group_id');
                    $groupName = Db::name('AdminGroup')->where('id', 'in', $groupId)->column('title');
                    $item->rank_name = implode(",",$groupName);
                }

                $position = Db::name('Position')
                    ->where([['id', 'in', $item->main_position_id]])
                    ->field("GROUP_CONCAT(title ORDER BY title SEPARATOR ',') as title")
                    ->find();
                $item->position = $position['title'];

                $admin_dep = Db::name("admin_dep")
                    ->alias("a")
                    ->join('department d', 'd.id = a.did', 'LEFT')
                    ->join('position p', 'p.id = a.position_id', 'LEFT')
                    ->where(['a.aid' => $item->id,'a.status' => 1])
                    ->field("d.title as dname , p.title as position_name")
                    ->select()
                    ->toArray();

                $item->o_admin_dep = "";

                foreach ($admin_dep as $k => $v) {
                    if (!empty($item->o_admin_dep)) {
                        $item->o_admin_dep .= " / {$v['dname']}-{$v['position_name']}";
                    } else {
                        $item->o_admin_dep = "{$v['dname']}-{$v['position_name']}";
                    }
                }


                $label_name = Db::name('StoreLabel')->where([['id', '=', $item->label_id]])->field('name')->find();
                if (!empty($label_name)){
                    $item->label_name = $label_name['name'];
                }else{
                    $item->label_name = "";
                }

                $item->entry_time = empty($item->entry_time) ? '-' : date('Y-m-d', $item->entry_time);
                $item->last_login_time = empty($item->last_login_time) ? '-' : date('Y-m-d H:i', $item->last_login_time);
                $item->last_login_ip = empty($item->last_login_ip) ? '-' : $item->last_login_ip;
            })->toArray();

        $col_s = array('A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z',
            'AA', 'AB', 'AC' , 'AD' , 'AE' , 'AF' , 'AG' , 'AH' , 'AI' , 'AJ'
        );// 0 - 25个字母

        $newExcel = new Spreadsheet();  //创建一个新的excel文档
        $objSheet = $newExcel->getActiveSheet();  //获取当前操作sheet的对象
        $objSheet->setTitle('OA区域业绩模板');  //设置当前sheet的标题

        $data_head = [
            ["field" => 'workno', "field_name" => '工号'],
            ["field" => 'status', "field_name" => '状态'],
            ["field" => 'type', "field_name" => '员工类型'],
            ["field" => 'name', "field_name" => '姓名'],
            ["field" => 'mobile', "field_name" => '手机号码'],
            ["field" => 'email', "field_name" => '邮箱'],
            ["field" => 'sex', "field_name" => '性别'],
            ["field" => 'd0', "field_name" => '一级部门'],
            ["field" => 'd1', "field_name" => '二级部门'],
            ["field" => 'd2', "field_name" => '三级部门'],
            ["field" => 'd3', "field_name" => '四级部门'],
            ["field" => 'position', "field_name" => '岗位职称'],
            ["field" => 'o_admin_dep', "field_name" => '兼岗'],
            ["field" => 'rank_name', "field_name" => '职级'],
            ["field" => 'label_name', "field_name" => '标签'],
            ["field" => 'entry_time', "field_name" => '入职日期'],
            ["field" => 'c_age', "field_name" => '司龄'],

            ["field" => 'id_card_number', "field_name" => '身份证号'],
            ["field" => 'birthday', "field_name" => '出生日期'],
            ["field" => 'age', "field_name" => '年龄'],
            ["field" => 'salary_card_number', "field_name" => '银行卡号'],
            ["field" => 'bank_name', "field_name" => '开户行'],
            ["field" => 'education', "field_name" => '最高学历'],
            ["field" => 'graduation_school', "field_name" => '毕业院校'],
            ["field" => 'major', "field_name" => '专业'],
            ["field" => 'certificates', "field_name" => '证书'],
            ["field" => 'emergency_contact', "field_name" => '紧急联系人'],
            ["field" => 'contact_mobile', "field_name" => '紧急联系人电话'],
            ["field" => 'position_salary', "field_name" => '岗位工资'],
            ["field" => 'base_salary', "field_name" => '基本工资'],
            ["field" => 'performance_salary', "field_name" => '绩效工资'],
            ["field" => 'trial_salary', "field_name" => '试用期工资'],
            ["field" => 'res_date', "field_name" => '离职日期'],

        ];

        foreach ($data_head as $k => $v) {
            $newExcel->getActiveSheet()->getColumnDimension("{$col_s[$k]}")->setAutoSize(true);
            $objSheet->setCellValueExplicit("{$col_s[$k]}1", $v['field_name'], DataType::TYPE_STRING);
        }

        //第二行起，每一行的值,setCellValueExplicit是用来导出文本格式的。
        $baseRow = 2; //数据从N-1行开始往下输出 这里是避免头信息被覆盖
        foreach ($admin['data'] as $k => $val) {
            $i = $k + $baseRow;
            $expand = Db::name("admin_expand")->where('id', $val['id'])->find();

            if (!empty($expand)) {
                $val['id_card_number'] = $expand['id_card_number'];
                $val['birthday'] = $expand['birthday'];
                $val['age'] = $expand['age'];
                $val['salary_card_number'] = $expand['salary_card_number'];
                $val['bank_name'] = $expand['bank_name'];
                $val['education'] = $expand['education'];
                $val['graduation_school'] = $expand['graduation_school'];
                $val['major'] = $expand['major'];
                $val['certificates'] = $expand['certificates'];
                $val['emergency_contact'] = $expand['emergency_contact'];
                $val['contact_mobile'] = $expand['contact_mobile'];
                $val['position_salary'] = $expand['position_salary'];
                $val['base_salary'] = $expand['base_salary'];
                $val['performance_salary'] = $expand['performance_salary'];
                $val['trial_salary'] = $expand['trial_salary'];
            }

            foreach ($data_head as $kk => $v) {

                if (isset($val[$v['field']])) {
                    $objSheet->setCellValueExplicit("{$col_s[$kk]}{$i}", $val[$v['field']], DataType::TYPE_STRING);
                } else {
                    $objSheet->setCellValueExplicit("{$col_s[$kk]}{$i}", '', DataType::TYPE_STRING);
                }

            }
        }
        $this->downloadExcel($newExcel, 'OA员工列表', 'Xls');
    }

    public function export_store_data()
    {
        //默认数据
        $Department = Db::name("Department")->where(['remark' => '门店'])->select();

        $col_s = array('A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z');// 0 - 25个字母

        $newExcel = new Spreadsheet();  //创建一个新的excel文档
        $objSheet = $newExcel->getActiveSheet();  //获取当前操作sheet的对象
        $objSheet->setTitle('OA门店数据模板');  //设置当前sheet的标题

        $data_head = [
            ["field_name" => '门店ID'],
            ["field_name" => '门店名称'],
            ["field_name" => '日期'],
            ["field_name" => '店铺标准4.9'],
            ["field_name" => '牌级标准金牌'],
            ["field_name" => '榜单商圈前三'],
            ["field_name" => '30秒回复率'],
            ["field_name" => '好评目标'],
            ["field_name" => '大众好评目标占比80%'],
            ["field_name" => '大众好评实际完成'],
            ["field_name" => '美团'],
            ["field_name" => '大众差额'],
            ["field_name" => '差评'],
            ["field_name" => '交易标准'],
            ["field_name" => '实际完成'],
            ["field_name" => '交易差额'],
        ];
        foreach ($data_head as $k => $v) {
            $newExcel->getActiveSheet()->getColumnDimension("{$col_s[$k]}")->setAutoSize(true);
            $objSheet->setCellValue("{$col_s[$k]}1", $v['field_name']);
        }

        //第二行起，每一行的值,setCellValueExplicit是用来导出文本格式的。
        //->setCellValueExplicit('C' . $k, $val['admin_password']PHPExcel_Cell_DataType::TYPE_STRING),可以用来导出数字不变格式
        $baseRow = 2; //数据从N-1行开始往下输出 这里是避免头信息被覆盖
        $months = getSeason();
        foreach ($Department as $k => $val) {
            $i = $k + $baseRow;
            $objSheet->setCellValue('A' . $i, $val['id'])
                ->setCellValue('B' . $i, $val['title']);
        }

        $this->downloadExcel($newExcel, 'OA门店数据模板', 'Xls');
    }


    public function export_store_record()
    {

        set_time_limit(0);
        $param = get_params();
        $where = array();
        if (!empty($param['status'])) {
            $where[] = ['status', '=', $param['status']];
        }

        if (!empty($param['did'])) {
            $where[] = ['did', '=', $param['did']];
        }

        if (!empty($param['did'])) {
            $where[] = ['did', '=', $param['did']];
        }

        if (isset($param['date_range']) && !empty($param['date_range'])) {
            $date_range = explode(" - ", $param['date_range']);
            $where[] = ['create_time', 'between', [strtotime($date_range[0]), strtotime('+1 day', strtotime($date_range[1]))]];
        }

        //默认数据
        $Department = Db::name("store_record")->where($where)->select()->toArray();

        $col_s = array('A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z');// 0 - 25个字母

        $newExcel = new Spreadsheet();  //创建一个新的excel文档
        $objSheet = $newExcel->getActiveSheet();  //获取当前操作sheet的对象
        $objSheet->setTitle('OA每日数据操作记录');  //设置当前sheet的标题

        $data_head = [
            ["field_name" => 'ID'],
            ["field_name" => '状态'],
            ["field_name" => '门店名称'],
            ["field_name" => '操作人'],
            ["field_name" => '报表日期'],
            ["field_name" => '创建时间']
        ];
        foreach ($data_head as $k => $v) {
            $newExcel->getActiveSheet()->getColumnDimension("{$col_s[$k]}")->setAutoSize(true);
            $objSheet->setCellValue("{$col_s[$k]}1", $v['field_name']);
        }

        //第二行起，每一行的值,setCellValueExplicit是用来导出文本格式的。
        //->setCellValueExplicit('C' . $k, $val['admin_password']PHPExcel_Cell_DataType::TYPE_STRING),可以用来导出数字不变格式
        $baseRow = 2; //数据从N-1行开始往下输出 这里是避免头信息被覆盖

        foreach ($Department as $k => $val) {
            $i = $k + $baseRow;

            if ($val['status'] == 1) {
                $val['status_n'] = '补交';
            }
            if ($val['status'] == 2) {
                $val['status_n'] = '编辑';
            }

            $objSheet->setCellValue('A' . $i, $val['id'])
                ->setCellValue('B' . $i, $val['status_n'])
                ->setCellValue('C' . $i, $val['dname'])
                ->setCellValue('D' . $i, $val['aname'])
                ->setCellValue('E' . $i, date("Y-m-d", $val['sdate']))
                ->setCellValue('F' . $i, date("Y-m-d H:i:s", $val['create_time']));
        }

        $this->downloadExcel($newExcel, 'OA每日数据操作记录', 'Xls');
    }


    public function export_storebill()
    {
        $param = get_params();

        $where = array();
        $where[] = ['sdate', '=', $param['sdate']];

        if ($param['did'] !== '') {
            $where[] = ['did', '=', $param['did']];
        }

        if (isset($param['title_1']) && !empty($param['title_1'])) {
            $where[] = ['title_1', '=', $param['title_1']];
        }
        if (isset($param['title_2']) && $param['title_2'] !== '') {
            $where[] = ['title_2', '=', $param['title_2']];
        }

        $list = Db::name("store_bill_s")
            ->where($where)
            ->order('id asc')->select()->toArray();

        $col_s = array('A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z');// 0 - 25个字母

        $newExcel = new Spreadsheet();  //创建一个新的excel文档
        $objSheet = $newExcel->getActiveSheet();  //获取当前操作sheet的对象
        $objSheet->setTitle('OA门店数据模板');  //设置当前sheet的标题

        $data_head = [
            ["field_name" => '序号'],
            ["field_name" => '付款日期'],
            ["field_name" => '摘要'],
            ["field_name" => '科目'],
            ["field_name" => '部门/门店'],
            ["field_name" => '金额'],
            ["field_name" => '备注'],
            ["field_name" => '一级科目']
        ];
        foreach ($data_head as $k => $v) {
            $newExcel->getActiveSheet()->getColumnDimension("{$col_s[$k]}")->setAutoSize(true);
            $objSheet->setCellValue("{$col_s[$k]}1", $v['field_name']);
        }

        //第二行起，每一行的值,setCellValueExplicit是用来导出文本格式的。
        //->setCellValueExplicit('C' . $k, $val['admin_password']PHPExcel_Cell_DataType::TYPE_STRING),可以用来导出数字不变格式
        $baseRow = 2; //数据从N-1行开始往下输出 这里是避免头信息被覆盖

        foreach ($list as $k => $val) {

            $aname = '';
            if (!empty($val['aid'])){
                $admin = Db::name("admin")->where(['id' => $val['aid']])->find();
                $aname = $admin['name'];
            }

            $i = $k + $baseRow;
            $objSheet->setCellValue('A' . $i, $val['id'])
                ->setCellValue('B' . $i, $val['sdate'])
                ->setCellValue('C' . $i, $val['abstract'])
                ->setCellValue('D' . $i, $val['title_2'])
                ->setCellValue('E' . $i, $val['dname'])
                ->setCellValue('F' . $i, $val['amount'])
                ->setCellValue('G' . $i, $val['remark'])
                ->setCellValue('H' . $i, $val['title_1'])
                ->setCellValue('I' . $i, $aname);
        }

        $this->downloadExcel($newExcel, '对账明细', 'Xls');

    }

    //导出门店薪资
    public function export_storesalary()
    {
        set_time_limit(0);
        $param = get_params();

        $where = array();
        if (!isset($param['sdate']) || $param['sdate'] == '') {
            $param['sdate'] = date("Y-m");
        }
        $where['a.date_time'] = strtotime($param['sdate']);

        if ($param['did'] !== '') {
            $where[] = ['a.did', '=', $param['did']];
        }

        $list = Db::name("store_salary")
            ->field(" a.* , ifnull(b.income_tax , 0) as income_tax , ifnull(b.real_salary , 0) as real_salary")
            ->alias('a')
            ->join('store_salary_external b', 'a.did = b.did and a.aid = b.aid and a.date_time=b.sdate and b.status = 1 ', 'left')
            ->where($where)
            ->order('a.did asc')
            ->select()
            ->toArray();

        $col_s = array('A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z',
            'AA', 'AB', 'AC','AD','AE','AF','AG','AH','AI','AJ');// 0 - 25个字母

        $newExcel = new Spreadsheet();  //创建一个新的excel文档
        $objSheet = $newExcel->getActiveSheet();  //获取当前操作sheet的对象
        $objSheet->setTitle('OA门店数据模板');  //设置当前sheet的标题

        $data_head = [
            ["field_name" => '门店名称', 'filed' => 'dname'],
            ["field_name" => '老师名称', 'filed' => 'aname'],
            ["field_name" => '职级', 'filed' => 'rank_name'],
            ["field_name" => '基本工资', 'filed' => 'base_amount'],
            ["field_name" => '应出勤天数', 'filed' => 'attender_required'],
            ["field_name" => '实际出勤天数', 'filed' => 'attender_actual'],
            ["field_name" => '出勤薪资', 'filed' => 'attender_amount'],
            ["field_name" => '劳动提成', 'filed' => 'labor_amount'],
            ["field_name" => '绩效', 'filed' => 'perf_amount'],
            ["field_name" => '卡提', 'filed' => 'kat_amount'],
            ["field_name" => '店长业绩提成', 'filed' => 'manager_amount'],
            ["field_name" => '加班', 'filed' => 'overtime_amount'],
            ["field_name" => '超额业绩奖', 'filed' => 'excess_amount'],
            ["field_name" => '考核奖', 'filed' => 'examine_amount'],
            ["field_name" => '考核罚', 'filed' => 'punish_amount'],
            ["field_name" => '补贴', 'filed' => 'subsidy_amount'],
            ["field_name" => '保底补差', 'filed' => 'differ_amount'],
            ["field_name" => '其他补发', 'filed' => 'onther_amount'],
            ["field_name" => '退卡', 'filed' => 'tuika_amount'],
            ["field_name" => '宿舍分摊费', 'filed' => 'sushe_amount'],
            ["field_name" => '社保扣款', 'filed' => 'shebao_amount'],
            ["field_name" => '公积金扣款', 'filed' => 'fund_amount'],
            ["field_name" => '工资合计', 'filed' => 'total_amount'],
            ["field_name" => '个税', 'filed' => 'income_tax'],
            ["field_name" => '对公实发工资', 'filed' => 'real_salary'],
            ["field_name" => '门店发放工资', 'filed' => 'store_salary'],
            ["field_name" => '收款人', 'filed' => 'employee_name'],
            ["field_name" => '银行卡号', 'filed' => 'bank_number'],
            ["field_name" => '开户行', 'filed' => 'bank_name'],
            ["field_name" => '身份证', 'filed' => 'id_card_number'],
            ["field_name" => '手机号', 'filed' => 'mobile'],
            ["field_name" => '备注', 'filed' => 'remark'],
        ];

        $newExcel->getDefaultStyle()->getFont()->setName('微软雅黑');
        // 将字体大小设置为12
        $newExcel->getDefaultStyle()->getFont()->setSize(14);

        $baseRow = 2;
        foreach ($data_head as $k => $v) {
            $newExcel->getActiveSheet()->getColumnDimension("{$col_s[$k]}")->setAutoSize(true);
            $objSheet->setCellValue("{$col_s[$k]}1",  $v['field_name']);

            $did = '';
            $sum = 0;

            $i = $baseRow;

            foreach ($list as $key => $val) {

                //个税
                //对公实发工资
                //门店发放工资=工资合计-个税-对公实发工资
//                $store_salary_external = Db::name("store_salary_external")->where([
//                    'aid' => $val['aid'],
//                    'did' => $val['did'],
//                    'sdate' => $val['date_time'],
//                    'status' => 1
//                ])->find();

//                $list[$key]['income_tax'] = '';
//                $list[$key]['real_salary'] = '';
//                $list[$key]['store_salary'] = '';
//
//                if (!empty($store_salary_external)) {
//                    $income_tax = $store_salary_external['income_tax'];
//                    $real_salary = $store_salary_external['real_salary'];
//                    $list[$key]['income_tax'] = $income_tax;
//                    $list[$key]['real_salary'] = $real_salary;
//                    $list[$key]['store_salary'] = round($list[$key]['total_amount'] - $income_tax - $real_salary, 2);
//                }

                if ($list[$key]['real_salary'] !== '0.00') {
                    $list[$key]['store_salary'] = round($list[$key]['total_amount'] - $list[$key]['income_tax'] - $list[$key]['real_salary'], 2);
                } else {
                    $list[$key]['store_salary'] = $list[$key]['total_amount'];
                }

                if (is_numeric($val[$v['filed']])) {
                    if (!empty($did) && $did != $val['did']) {
                        $dname = Db::name("department")->where(['id' => $did])->value('title');
                        $objSheet->setCellValue('A' . $i, $dname . '-合计');
                        $objSheet->setCellValue($col_s[$k] . $i, $sum);

                        $newExcel->getActiveSheet()->getStyle($col_s[$k] . $i)->getFont()->getColor()->setARGB(\PhpOffice\PhpSpreadsheet\Style\Color::COLOR_BLUE);

                        $sum = $val[$v['filed']];
                        $i++;
                    } else {
                        $sum += $val[$v['filed']];
                    }
                } else {
                    if (!empty($did) && $did != $val['did']) {
                        $i++;
                    }
                }

//                if (is_numeric($val[$v['filed']])) {
//                    if ($val[$v['filed']] != 0) {
//                        $objSheet->setCellValue($col_s[$k] . $i, $val[$v['filed']]);
//                    }
//                } else {
//                    $objSheet->setCellValue($col_s[$k] . $i, $val[$v['filed']]);
//                }

                $vs = $val[$v['filed']];

//                $objSheet->setCellValue($col_s[$k] . $i, $vs );
                $objSheet->setCellValue($col_s[$k] . $i, $vs."\t");

                if (is_numeric($val[$v['filed']]) && $val[$v['filed']] == 0) {
                    $objSheet->setCellValue($col_s[$k] . $i, "");
                }

                $did = $val['did'];
                $i++;

                if (is_numeric($val[$v['filed']]) && ($key + 1 == count($list))) {
                    $dname = Db::name("department")->where(['id' => $did])->value('title');
                    $objSheet->setCellValue('A' . $i, $dname . '-合计');
                    $objSheet->setCellValue($col_s[$k] . $i, $sum);
                    $newExcel->getActiveSheet()->getStyle($col_s[$k] . $i)->getFont()->getColor()->setARGB(\PhpOffice\PhpSpreadsheet\Style\Color::COLOR_BLUE);
                }
            }
        }
        $this->downloadExcel($newExcel, "门店薪资_{$param['sdate']}", 'Xls');

    }

    public function export_store()
    {
        $param = get_params();

        $where = array();
        if (!isset($param['sdate']) || $param['sdate'] == '') {
            $param['sdate'] = date("Y-m");
        }
        $where['sdate'] = $param['sdate'];

        $list = Db::name("store")->where($where)->select()->toArray();

        $col_s = array('A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z');// 0 - 25个字母

        $newExcel = new Spreadsheet();  //创建一个新的excel文档
        $objSheet = $newExcel->getActiveSheet();  //获取当前操作sheet的对象
        $objSheet->setTitle('OA门店数据模板');  //设置当前sheet的标题

        $data_head = [
            ["field_name" => '日期', 'filed' => 'sdate'],
            ["field_name" => '门店ID', 'filed' => 'did'],
            ["field_name" => '门店名称', 'filed' => 'dname'],
            ["field_name" => '店长姓名', 'filed' => 'aname'],
            ["field_name" => '业绩指标', 'filed' => 'target_amount'],
            ["field_name" => '新店', 'filed' => 'nstatus'],
            ["field_name" => '参与改革', 'filed' => 'rstatus'],

            ["field_name" => '分级', 'filed' => 'grade'],
            ["field_name" => '分级金额', 'filed' => 'grade_amount'],
            ["field_name" => '系数', 'filed' => 'ratio']
        ];

        $newExcel->getDefaultStyle()->getFont()->setName('微软雅黑');
        // 将字体大小设置为12
        $newExcel->getDefaultStyle()->getFont()->setSize(14);

        $baseRow = 2;
        foreach ($data_head as $k => $v) {
            $newExcel->getActiveSheet()->getColumnDimension("{$col_s[$k]}")->setAutoSize(true);
            $objSheet->setCellValue("{$col_s[$k]}1", $v['field_name']);

            foreach ($list as $key => $val) {
                $i = $key + $baseRow;

                $value = $val[$v['filed']];
                if ($v['filed'] == 'nstatus' || $v['filed'] == 'rstatus') {
                    $value = $value == 1 ? '是' : '否';
                }
                $objSheet->setCellValue("{$col_s[$k]}{$i}", $value);
            }
        }
        $this->downloadExcel($newExcel, '门店详情', 'Xls');
    }

    public function export_rent_store()
    {
        $param = get_params();

        $where = array();
        if (!isset($param['sdate']) || $param['sdate'] == '') {
            $param['sdate'] = date("Y-m");
        }
        $where['sdate'] = $param['sdate'];

        $list = Db::name("store")->where($where)->select()->toArray();

        $col_s = array('A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z');// 0 - 25个字母

        $newExcel = new Spreadsheet();  //创建一个新的excel文档
        $objSheet = $newExcel->getActiveSheet();  //获取当前操作sheet的对象
        $objSheet->setTitle('OA门店数据模板');  //设置当前sheet的标题

        $data_head = [
            ["field_name" => '日期', 'filed' => 'sdate'],
            ["field_name" => '门店ID', 'filed' => 'did'],
            ["field_name" => '门店名称', 'filed' => 'dname'],
            ["field_name" => '门店租金', 'filed' => 'rent_amount'],
            ["field_name" => '物业费', 'filed' => 'wuye_amount'],
            ["field_name" => '宿舍租金', 'filed' => 'sushe_amount'],
        ];

        $newExcel->getDefaultStyle()->getFont()->setName('微软雅黑');
        // 将字体大小设置为12
        $newExcel->getDefaultStyle()->getFont()->setSize(14);

        $baseRow = 2;
        foreach ($data_head as $k => $v) {
            $newExcel->getActiveSheet()->getColumnDimension("{$col_s[$k]}")->setAutoSize(true);
            $objSheet->setCellValue("{$col_s[$k]}1", $v['field_name']);

            foreach ($list as $key => $val) {
                $i = $key + $baseRow;

                $value = $val[$v['filed']];
                if ($v['filed'] == 'nstatus' || $v['filed'] == 'rstatus') {
                    $value = $value == 1 ? '是' : '否';
                }
                $objSheet->setCellValue("{$col_s[$k]}{$i}", $value);
            }
        }
        $this->downloadExcel($newExcel, '门店详情', 'Xls');
    }

    //公共文件，用来传入xls并下载
    public function downloadExcel($newExcel, $filename, $format)
    {
        // $format只能为 Xlsx 或 Xls
        if ($format == 'Xlsx') {
            header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        } elseif ($format == 'Xls') {
            header('Content-Type: application/vnd.ms-excel');
        }

        header("Content-Disposition: attachment;filename="
            . $filename . date('Y-m-d') . '.' . strtolower($format));
        header('Cache-Control: max-age=0');
        $objWriter = IOFactory::createWriter($newExcel, $format);

        $objWriter->save('php://output');

        //通过php保存在本地的时候需要用到
        //$objWriter->save($dir.'/demo.xlsx');

        //以下为需要用到IE时候设置
        // If you're serving to IE 9, then the following may be needed
        //header('Cache-Control: max-age=1');
        // If you're serving to IE over SSL, then the following may be needed
        //header('Expires: Mon, 26 Jul 1997 05:00:00 GMT'); // Date in the past
        //header('Last-Modified: ' . gmdate('D, d M Y H:i:s') . ' GMT'); // always modified
        //header('Cache-Control: cache, must-revalidate'); // HTTP/1.1
        //header('Pragma: public'); // HTTP/1.0
        exit;
    }


    public function export_approve()
    {
        set_time_limit(0);

        $param = get_params();

        $where = array();

        $whereor = array();
        if ($param['uid'] != 2202 && $param['uid'] != 2311 && $param['uid'] != 2357 && $param['uid'] != 1){
            //导出经我审批
            $where[] = ['', 'exp', Db::raw("FIND_IN_SET('{$param['uid']}',flow_admin_ids) or FIND_IN_SET('{$param['uid']}',check_admin_ids)")];
        }


        if (isset($param['type']) && !empty($param['type']) ){
            if (isset($param['flow_cate']) && !empty($param['flow_cate']) ){
                $where[] = ['type', '=', $param['flow_cate']];
            }else{
                $ids = Db::name("flow")->where(['type' => $param['type'] , 'status' => 1 ])->column(['id']);
                $where[] = ['type', 'in', $ids];
            }
        }


        if (isset($param['sdate']) && !empty($param['sdate'])) {
            $sdate = explode(" - ",$param['sdate']);
            $where[] = ['create_time', 'between', [strtotime($sdate[0]) , strtotime($sdate[1])] ];
        }

        //按时间检索
        if (!empty($param['finish_time'])) {
            $finish_time = explode('~', $param['finish_time']);
            $where[] = ['finish_time', 'between', [strtotime(urldecode($finish_time[0])), strtotime(urldecode($finish_time[1]))]];
        }

        //按时间检索
        if (!empty($param['finish_date'])) {
            $finish_time = explode(' - ', $param['finish_date']);
            $where[] = ['finish_time', 'between', [strtotime(urldecode($finish_time[0])), strtotime(urldecode($finish_time[1]))]];
        }

        if (!empty($param['id'])){
            $where[] = ['id', '=', $param['id']];
        }

        if (isset($param['status']) && !empty($param['status']) ){
            if ($param['status'] == 1) {
                $where[] = ['check_status', '<', 2];
            }
            if ($param['status'] == 2) {
                $where[] = ['check_status', '=', 2];
            }
            if ($param['status'] == 3) {
                $where[] = ['check_status', '>', 2];
            }
        }

        $approve = Db::name("approve")
            ->where($where)
            ->whereOr(function ($query) use ($whereor) {
                $query->whereOr($whereor);
            })
            ->order("create_time desc")->select()->toArray();


        $newExcel = new Spreadsheet();  //创建一个新的excel文档
        $objSheet = $newExcel->getActiveSheet();  //获取当前操作sheet的对象
        $objSheet->setTitle('OA门店数据模板');  //设置当前sheet的标题

        $htmlArray = ['待审批', '审批中', '已通过', '已拒绝', '已撤销'];
        $data_head = [
            ["field_name" => 'ID号', 'filed' => 'id'],
            ["field_name" => '发起人', 'filed' => 'name'],
            ["field_name" => '申请人', 'filed' => 'aname'],
            ["field_name" => '申请时间', 'filed' => 'create_time'],
            ["field_name" => '审批类型', 'filed' => 'flow_type'],
            ["field_name" => '所属部门', 'filed' => 'department_name'],
            ["field_name" => '当前审批人', 'filed' => 'check_user'],
            ["field_name" => '当前节点', 'filed' => 'node_tit'],
            ["field_name" => '备注', 'filed' => 'remark'],
            ["field_name" => '审批状态', 'filed' => 'check_status'],

            ["field_name" => '所在部门', 'filed' => 'department_type'],
            ["field_name" => '大众点评称呼', 'filed' => 'dz_name'],
            ["field_name" => '职位', 'filed' => 'position'],
            ["field_name" => '申请离职日期', 'filed' => 'start_time'],
            ["field_name" => '离职（生效）日期', 'filed' => 'end_time'],
            ["field_name" => '是否有股份', 'filed' => 'is_gufen'],
            ["field_name" => '申请离职原因', 'filed' => 'content'],
        ];
        $newExcel->getDefaultStyle()->getFont()->setName('微软雅黑');
        // 将字体大小设置为12
        $newExcel->getDefaultStyle()->getFont()->setSize(14);
        $col_s = array('A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z');// 0 - 25个字母

        $baseRow = 2;



        $objSheet->setCellValue('A1', 'ID号')
            ->setCellValue('B1', '发起人')
            ->setCellValue('C1', '申请人')
            ->setCellValue('D1', '申请时间')
            ->setCellValue('E1', '审批类型')
            ->setCellValue('F1', '所属部门')
            ->setCellValue('G1', '当前审批人')
            ->setCellValue('H1', '当前节点')
            ->setCellValue('I1', '备注')
            ->setCellValue('J1', '审批状态')
            ->setCellValue('K1', '所在部门')
            ->setCellValue('L1', '大众点评称呼')
            ->setCellValue('M1', '职位')
            ->setCellValue('N1', '申请离职日期')
            ->setCellValue('O1', '离职（生效）日期')
            ->setCellValue('P1', '是否有股份')
            ->setCellValue('Q1', '申请离职原因')
            ->setCellValue('R1', '原岗位')
            ->setCellValue('S1', '现岗位')
            ->setCellValue('T1', '转岗（生效）日期')
            ->setCellValue('U1', '入职日期')
            ->setCellValue('V1', '原岗位')
            ->setCellValue('W1', '晋升岗位')
            ->setCellValue('X1', '晋升（生效）日期')
            ->setCellValue('Y1', '原门店')
            ->setCellValue('Z1', '调入门店')
            ->setCellValue('AA1', '调动日期')
            ->setCellValue('AB1', '担任岗位')

            ->setCellValue('AC1', '付款类型')
            ->setCellValue('AD1', '付款金额')
            ->setCellValue('AE1', '收款人姓名')
            ->setCellValue('AF1', '开户行')
            ->setCellValue('AG1', '银行账号')
            ->setCellValue('AH1', '介绍人收款人姓名')
            ->setCellValue('AI1', '完成时间');


        foreach ($approve as $k => $val) {
            $val['name'] = Db::name("admin")->where(['id' => $val['admin_id']])->value("name");

            if (!empty($val['check_admin_ids'])) {
                $val['check_user'] = Db::name("admin")->where(['id' => $val['check_admin_ids']])->value("name");
            } else {
                $val['check_user'] = '';
            }

            $val['create_time'] = date("Y-m-d H:i:s", $val['create_time']);
            $val['finish_time'] = empty($val['finish_time']) ? '' : date("Y-m-d H:i:s", $val['finish_time']);
            $val['flow_type'] = Db::name("flow_type")->where(['id' => $val['type']])->value("title");
            $val['department_name'] = Db::name("department")->where(['id' => $val['department_id'], 'remark' => '门店'])->value("title");
            $val['check_status'] = $htmlArray[$val['check_status']];

            if (!empty($val['jsoncode'])) {
                $jsoncode = unserialize($val['jsoncode']);

                $val['aname'] = "";
                if (isset($jsoncode['aid'])){
                    $val['aname'] = Db::name("admin")->where(['id' => $jsoncode['aid']])->value("name");
                }

                foreach ($jsoncode as $kk => $vv) {
                    if ($kk == 'id') continue;
                    $val[$kk] = $vv;
                }

                if ($val['type'] == 37) {
                    $val['department_type'] = Db::name("department")->where(['id' => $jsoncode['o_did']])->value("title");
                } else {
                    if (isset($jsoncode['department_type'])) {
                        $val['department_type'] = Db::name("department")->where(['id' => $jsoncode['department_type']])->value("title");
                    } elseif (isset($jsoncode['did'])) {
                        $val['department_type'] = Db::name("department")->where(['id' => $jsoncode['did']])->value("title");
                    }elseif(isset($jsoncode['department_id'])){
                        $val['department_type'] = Db::name("department")->where(['id' => $jsoncode['department_id']])->value("title");
                    }
                }

                if (isset($jsoncode['is_gufen'])) {
                    $val['is_gufen'] = $jsoncode['is_gufen'] == 1 ? '是' : '否';
                }

                if ($val['type'] == 20 || $val['type'] == 38) {
                    $in_po = Db::name("position")->where([['id', 'in', $jsoncode['select']]])->column("title");
                    $val['in_position'] = implode(",", $in_po);
                }

                if (isset($jsoncode['o_did']) || isset($jsoncode['n_did'])) {
                    $val['o_dname'] = Db::name("department")->where(['id' => $jsoncode['o_did']])->value("title");
                    $val['n_dname'] = Db::name("department")->where(['id' => $jsoncode['n_did']])->value("title");
                }


                if ($val['type'] == 23) {
                    $val['pay_amount'] = $val['pay_total_amount'];
                }

                if ($val['type'] == 41) {
                    $val['pay_amount'] = $val['salary'];
                }
            }

            if (isset( $val['pay_type'] )){
                $val['pay_type_name'] = Db::name("type_pay")->where(['id' => $val['pay_type']])->value("name");
            }


            $i = $k + $baseRow;
            $objSheet->setCellValue('A' . $i, $val['id'])
                ->setCellValue('B' . $i, $val['name'])
                ->setCellValue('C' . $i, isset($val['aname']) ? $val['aname'] : '' )
                ->setCellValue('D' . $i, $val['create_time'])
                ->setCellValue('E' . $i, $val['flow_type'])
//                ->setCellValue('F' . $i, $val['department_name'])
                ->setCellValue('F' . $i, '')
                ->setCellValue('G' . $i, $val['check_user'])
                ->setCellValue('H' . $i, $val['node_tit'])
                ->setCellValue('I' . $i, $val['remark'])
                ->setCellValue('J' . $i, $val['check_status'])
                ->setCellValue('K' . $i, $val['department_type'])
                ->setCellValue('L' . $i, isset($val['dz_name']) ? $val['dz_name'] : '')
                ->setCellValue('M' . $i, isset($val['position']) ? $val['position'] : '')
                ->setCellValue('N' . $i, isset($val['start_time']) ? $val['start_time'] : '')
                ->setCellValue('O' . $i, isset($val['end_time']) ? $val['end_time'] : '')
                ->setCellValue('P' . $i, isset($val['is_gufen']) ? $val['is_gufen'] : '')
                ->setCellValue('Q' . $i, isset($val['content']) ? $val['content'] : '')
                ->setCellValue('R' . $i, isset($val['position']) ? $val['position'] : '')
                ->setCellValue('S' . $i, isset($val['in_position']) ? $val['in_position'] : '')
                ->setCellValue('T' . $i, isset($val['detail_time']) ? $val['detail_time'] : '')
                ->setCellValue('U' . $i, isset($val['detail_time']) ? $val['detail_time'] : '')
                ->setCellValue('V' . $i, isset($val['position']) ? $val['position'] : '')
                ->setCellValue('W' . $i, isset($val['in_position']) ? $val['in_position'] : '')
                ->setCellValue('X' . $i, isset($val['promotion_time']) ? $val['promotion_time'] : '')
                ->setCellValue('Y' . $i, isset($val['o_dname']) ? $val['o_dname'] : '')
                ->setCellValue('Z' . $i, isset($val['n_dname']) ? $val['n_dname'] : '')
                ->setCellValue('AA' . $i, isset($val['detail_time']) ? $val['detail_time'] : '')
                ->setCellValue('AB' . $i, isset($val['position']) ? $val['position'] : '')


                ->setCellValue('AC' . $i, isset($val['pay_type_name']) ? $val['pay_type_name'] . "\t" : '')
                ->setCellValue('AD' . $i, isset($val['pay_amount']) ? $val['pay_amount'] . "\t" : '')
                ->setCellValue('AE' . $i, isset($val['payee']) ? $val['payee'] . "\t" : (isset($val['payee_name']) ? $val['payee_name'] . "\t" : ''))
                ->setCellValue('AF' . $i, isset($val['bank_name']) ? $val['bank_name'] . "\t" : (isset($val['open_bank']) ? $val['open_bank'] . "\t" : ''))
                ->setCellValue('AG' . $i, isset($val['bank_number']) ? $val['bank_number'] . "\t" : '')
                ->setCellValue('AH' . $i, isset($val['l_salary']) ? $val['l_salary'] . "\t" : '')
                ->setCellValue('AI' . $i, isset($val['finish_time']) ? $val['finish_time'] . "\t" : '');
            ;

        }


        $this->downloadExcel($newExcel, '审批列表', 'Xls');
    }

    public function exportAnnualLeave()
    {
        set_time_limit(0);

        $list = Db::name("admin")->where(['status' => 1, 'is_virtually' => 0])->order("id asc")->select()->toArray();

        $newExcel = new Spreadsheet();  //创建一个新的excel文档
        $objSheet = $newExcel->getActiveSheet();  //获取当前操作sheet的对象
        $objSheet->setTitle('OA员工年假模板');  //设置当前sheet的标题

        $data_head = [
            ["field_name" => 'ID号', 'filed' => 'id'],
            ["field_name" => '可用年假', 'filed' => 'annual_leave'],
            ["field_name" => '已用年假', 'filed' => 'is_annual_leave'],
        ];
        $newExcel->getDefaultStyle()->getFont()->setName('微软雅黑');
        // 将字体大小设置为12
        $newExcel->getDefaultStyle()->getFont()->setSize(14);
        $col_s = array('A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z');// 0 - 25个字母

        $baseRow = 2;

//        foreach ($data_head as $k => $v) {
//            $newExcel->getActiveSheet()->getColumnDimension("{$col_s[$k]}")->setAutoSize(true);
//            $objSheet->setCellValue("{$col_s[$k]}1", $v['field_name']);
//
//            foreach ($approve as $key => $val) {
//                dump($val);
//
//                $i = $key + $baseRow;
//
//                $val['name'] = Db::name("admin")->where(['id' => $val['admin_id']])->value("name");
//
//                if (!empty($val['check_admin_ids'])) {
//                    $val['check_user'] = Db::name("admin")->where(['id' => $val['check_admin_ids']])->value("name");
//                }
//
//                $val['create_time'] = date("Y-m-d H:i:s", $val['create_time']);
//                $val['flow_type'] = Db::name("flow_type")->where(['id' => $val['type']])->value("title");
//                $val['department_name'] = Db::name("department")->where(['id' => $val['department_id'], 'remark' => '门店'])->value("title");
//                $val['check_status'] = $htmlArray[$val['check_status']];
//
//                if (!empty($val['jsoncode'])) {
//                    $jsoncode = unserialize($val['jsoncode']);
//                    $val['aname'] = Db::name("admin")->where(['id' => $jsoncode['aid']])->value("name");
//                    if (isset($jsoncode[$v['filed']])) {
//                        $val[$v['filed']] = $jsoncode[$v['filed']];
//                    }
//
//                    if (isset($jsoncode['department_type'])) {
//                        $val['department_type'] = Db::name("department")->where(['id' => $jsoncode['department_type']])->value("title");
//                    }
//                }
//
//                $value = isset($val[$v['filed']]) ? $val[$v['filed']] : '';
//                $objSheet->setCellValue("{$col_s[$k]}{$i}", $value);
//            }
//        }

        $objSheet->setCellValue('A1', 'ID号')
            ->setCellValue('B1', '姓名')
            ->setCellValue('C1', '可用年假')
            ->setCellValue('D1', '已用年假');


        foreach ($list as $k => $val) {

//            $newExcel->getActiveSheet()->getColumnDimension("{$col_s[$k]}")->setAutoSize(true);
            $i = $k + $baseRow;
            $objSheet->setCellValue('A' . $i, $val['id'])
                ->setCellValue('B' . $i, $val['name'])
                ->setCellValue('C' . $i, $val['annual_leave'])
                ->setCellValue('D' . $i, $val['is_annual_leave']);


        }


        $this->downloadExcel($newExcel, 'OA员工年假模板', 'Xls');
    }

    public function exportStorecontranct()
    {
        set_time_limit(0);

        $list = getmd("门店");

        $newExcel = new Spreadsheet();  //创建一个新的excel文档
        $objSheet = $newExcel->getActiveSheet();  //获取当前操作sheet的对象
        $objSheet->setTitle('数据比对导入模板');  //设置当前sheet的标题

        $data_head = [
            ["field_name" => '日期', 'filed' => 'sdate'],
            ["field_name" => '门店ID', 'filed' => 'did'],
            ["field_name" => '门店', 'filed' => 'dname'],
            ["field_name" => '好评统计', 'filed' => 'hp_num'],
        ];
        $newExcel->getDefaultStyle()->getFont()->setName('微软雅黑');
        // 将字体大小设置为12
        $newExcel->getDefaultStyle()->getFont()->setSize(14);
        $col_s = array('A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z');// 0 - 25个字母

        $baseRow = 2;

//        foreach ($data_head as $k => $v) {
//            $newExcel->getActiveSheet()->getColumnDimension("{$col_s[$k]}")->setAutoSize(true);
//            $objSheet->setCellValue("{$col_s[$k]}1", $v['field_name']);
//
//            foreach ($approve as $key => $val) {
//                dump($val);
//
//                $i = $key + $baseRow;
//
//                $val['name'] = Db::name("admin")->where(['id' => $val['admin_id']])->value("name");
//
//                if (!empty($val['check_admin_ids'])) {
//                    $val['check_user'] = Db::name("admin")->where(['id' => $val['check_admin_ids']])->value("name");
//                }
//
//                $val['create_time'] = date("Y-m-d H:i:s", $val['create_time']);
//                $val['flow_type'] = Db::name("flow_type")->where(['id' => $val['type']])->value("title");
//                $val['department_name'] = Db::name("department")->where(['id' => $val['department_id'], 'remark' => '门店'])->value("title");
//                $val['check_status'] = $htmlArray[$val['check_status']];
//
//                if (!empty($val['jsoncode'])) {
//                    $jsoncode = unserialize($val['jsoncode']);
//                    $val['aname'] = Db::name("admin")->where(['id' => $jsoncode['aid']])->value("name");
//                    if (isset($jsoncode[$v['filed']])) {
//                        $val[$v['filed']] = $jsoncode[$v['filed']];
//                    }
//
//                    if (isset($jsoncode['department_type'])) {
//                        $val['department_type'] = Db::name("department")->where(['id' => $jsoncode['department_type']])->value("title");
//                    }
//                }
//
//                $value = isset($val[$v['filed']]) ? $val[$v['filed']] : '';
//                $objSheet->setCellValue("{$col_s[$k]}{$i}", $value);
//            }
//        }

        $objSheet->setCellValue('A1', '日期')
            ->setCellValue('B1', '门店ID')
            ->setCellValue('C1', '门店')
            ->setCellValue('D1', '好评统计');


        foreach ($list as $k => $val) {

//            $newExcel->getActiveSheet()->getColumnDimension("{$col_s[$k]}")->setAutoSize(true);
            $i = $k + $baseRow;
            $objSheet->setCellValue('A' . $i, date("Y-m"))
                ->setCellValue('B' . $i, $val['id'])
                ->setCellValue('C' . $i, $val['title'])
                ->setCellValue('D' . $i, '');
        }


        $this->downloadExcel($newExcel, '数据比对导入模板', 'Xls');
    }


    public function test3()
    {
        $approve = Db::name('approve')->where([
            ['type', 'in', '26,29,34'],
            ['check_status', '<>', '4']
        ])->select()->toArray();


        $list = array();

        foreach ($approve as $k => $v) {

            $list[] = [
                'id' => $v['id'],
                'aname' => Db::name('admin')->where(['id' => $v['admin_id']])->value("name"),
                'anames' => '',
                'create_time' => date('Y-m-d H:i', $v['create_time']),
                'mdname' => $v['mdname'],
                'fz_name' => '',

                's_anames' => '',
                's_check_time' => '',
                's_status_str' => '',
            ];

            $check_record = Db::name('FlowRecord')->field('f.*,a.name,a.thumb')
                ->alias('f')
                ->join('Admin a', 'a.id = f.check_user_id', 'left')
                ->where(['f.action_id' => $v['id']])
                ->order('check_time asc')
                ->select()->toArray();

            foreach ($check_record as $kk => $vv) {

                $vv['check_time_str'] = date('Y-m-d H:i', $vv['check_time']);
                $vv['status_str'] = '提交';
                if ($vv['status'] == 1) {
                    $vv['status_str'] = '审核通过';
                } else if ($vv['status'] == 2) {
                    $vv['status_str'] = '审核拒绝';
                }
                if ($vv['status'] == 3) {
                    $vv['status_str'] = '撤销';
                }

                $list[] = [
                    'id' => '-',
                    'aname' => '-',
                    'anames' => '-',
                    'create_time' => '-',
                    'mdname' => '',
                    'fz_name' => $v['fz_name'],

                    's_anames' => Db::name('admin')->where(['id' => $vv['check_user_id']])->value("name"),
                    's_check_time' => date('Y-m-d H:i', $vv['check_time']),
                    's_status_str' => $vv['status_str'],
                ];

            }
        }

        set_time_limit(0);

        $newExcel = new Spreadsheet();  //创建一个新的excel文档
        $objSheet = $newExcel->getActiveSheet();  //获取当前操作sheet的对象
        $objSheet->setTitle('数据比对导入模板');  //设置当前sheet的标题

        $newExcel->getDefaultStyle()->getFont()->setName('微软雅黑');
        // 将字体大小设置为12
        $newExcel->getDefaultStyle()->getFont()->setSize(14);
        $col_s = array('A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z');// 0 - 25个字母

        $baseRow = 2;

        $objSheet->setCellValue('A1', 'ID')
            ->setCellValue('B1', '发起人')
            ->setCellValue('C1', '申请人')
            ->setCellValue('D1', '申请时间')
            ->setCellValue('E1', '门店名称')
            ->setCellValue('F1', '分支名称')
            ->setCellValue('G1', '审批人')
            ->setCellValue('H1', '审批时间')
            ->setCellValue('I1', '审批状态');


        foreach ($list as $k => $val) {
            $i = $k + $baseRow;
            $objSheet->setCellValue('A' . $i, $val['id'])
                ->setCellValue('B' . $i, $val['aname'])
                ->setCellValue('C' . $i, $val['anames'])
                ->setCellValue('D' . $i, $val['create_time'])
                ->setCellValue('E' . $i, $val['mdname'])
                ->setCellValue('F' . $i, $val['fz_name'])
                ->setCellValue('G' . $i, $val['s_anames'])
                ->setCellValue('H' . $i, $val['s_check_time'])
                ->setCellValue('I' . $i, $val['s_status_str']);
        }

        $this->downloadExcel($newExcel, '数据比对导入模板', 'Xls');

    }


    public function export_flow()
    {
        set_time_limit(0);

        $param = get_params();

        $where = array();
        //导出经我审批

        $list = Db::name("flow")
            ->field('f.*,ft.title as title')
            ->alias('f')
            ->join('flow_type ft', 'f.flow_cate = ft.id')
            ->where([
                ['f.flow_list', '<>', ''],
                ['f.status', '=', '1'],
            ])
            ->select()
            ->toArray();

        $newExcel = new Spreadsheet();  //创建一个新的excel文档
        $objSheet = $newExcel->getActiveSheet();  //获取当前操作sheet的对象
        $objSheet->setTitle('OA门店数据模板');  //设置当前sheet的标题

        $htmlArray = ['待审批', '审批中', '已通过', '已拒绝', '已撤销'];

        $newExcel->getDefaultStyle()->getFont()->setName('微软雅黑');
        // 将字体大小设置为12
        $newExcel->getDefaultStyle()->getFont()->setSize(14);
        $col_s = array('A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z');// 0 - 25个字母

        $baseRow = 2;
        $type = get_config('approve.type');

        $array = array();
        foreach ($list as $k => $val) {

            $jsonencode = unserialize($val['flow_list']);


            $array[] = [
                'name' => $val['name'],
                'employee_names' => $val['employee_names'],
                'pay_amount' => $val['pay_amount'],
                'department_remark' => $val['department_remark'],
                'copy_uids' => Db::name("admin")->where([['id', 'in', $val['copy_uids']]])->value('name'),
            ];

            foreach ($jsonencode as $kk => $vv) {
                $flow_type_name = '';
                switch ($vv['flow_type']) {
                    case 1:
                        $flow_type_name = '部门负责人';
                        break;
                    case 2:
                        $flow_type_name = '上级部门负责人';
                        break;
                    case 3:
                        $flow_type_name = '指定人员(多人或签)';
                        break;
                    case 4:
                        $flow_type_name = '指定人员(多人会签)';
                        break;
                    case 5:
                        $flow_type_name = '指定部门(多人会签)';
                        break;
                    case 6:
                        $flow_type_name = '指定部门(多人或签)';
                        break;
                    case 7:
                        $flow_type_name = '门店负责人';
                        break;
                }

                if ($vv['flow_type'] == 6 || $vv['flow_type'] == 5) {
                    if (isset($vv['flow_dep'])){
                        $admin = Db::name("admin")
                            ->order("sort asc")
                            ->where([['status','=' , 1],['id','<>','878'],  ['', 'exp', Db::raw("FIND_IN_SET('{$vv['flow_dep']}',did)")]])->column('name');

                        $array[] = [
                            'flow_type_name' => $flow_type_name,
                            'flow_title' => isset($vv['flow_title']) ? $vv['flow_title'] : '',
                            'flow_uids' => implode(",", $admin)
                        ];
                    }
                } else {
                    $admin = Db::name("admin")->where([ ['status','=' , 1],  ['id', 'in', $vv['flow_uids']]])->column('name');

                    $array[] = [
                        'flow_type_name' => $flow_type_name,
                        'flow_title' => isset($vv['flow_title']) ? $vv['flow_title'] : '',
                        'flow_uids' => !empty($admin) ? implode(",", $admin) : ''
                    ];
                }
            };
        }
        $objSheet->setCellValue('A1', '审批流名称')
            ->setCellValue('B1', '抄送人')
            ->setCellValue('C1', '节点类型')
            ->setCellValue('D1', '节点名称')
            ->setCellValue('E1', '审批人')
            ->setCellValue('F1', '申请人')
            ->setCellValue('G1', '金额')
            ->setCellValue('H1', '部门属性')
        ;

        foreach ($array as $k => $v) {
            $i = $baseRow + $k;
            $objSheet->setCellValue('A' . $i, isset($v['name']) ? $v['name'] : '')
                ->setCellValue('B' . $i, isset($v['copy_uids']) ? $v['copy_uids'] : '')
                ->setCellValue('C' . $i, isset($v['flow_type_name']) ? $v['flow_type_name'] : '')
                ->setCellValue('D' . $i, isset($v['flow_title']) ? $v['flow_title'] : '')
                ->setCellValue('E' . $i, isset($v['flow_uids']) ? $v['flow_uids'] : '')
                ->setCellValue('F' . $i, isset($v['employee_names']) ? $v['employee_names'] : '')
                ->setCellValue('G' . $i, isset($v['pay_amount']) ? $v['pay_amount'] : '')
                ->setCellValue('H' . $i, isset($v['department_remark']) ? $v['department_remark'] : '')
            ;
        }

        $this->downloadExcel($newExcel, '审批节点', 'Xls');
    }

    public function export_rent()
    {
        set_time_limit(0);

        $newExcel = new Spreadsheet();  //创建一个新的excel文档
        $objSheet = $newExcel->getActiveSheet();  //获取当前操作sheet的对象
        $objSheet->setTitle('OA房租明细导入模板');  //设置当前sheet的标题

        $newExcel->getDefaultStyle()->getFont()->setName('微软雅黑');
        // 将字体大小设置为12
        $newExcel->getDefaultStyle()->getFont()->setSize(14);

        $objSheet->setCellValue('A1', '付款日期')
            ->setCellValue('B1', '月租金')
            ->setCellValue('C1', '物业费')
            ->setCellValue('D1', '其他费用')
            ->setCellValue('E1', '每期金额')
            ->setCellValue('F1', '周期：起')
            ->setCellValue('G1', '止')
            ->setCellValue('H1', '付款情况（已付填写已付款）')
            ->setCellValue('I1', '备注');

        $this->downloadExcel($newExcel, 'OA房租明细导入模板', 'Xls');
    }

    public function export_store_achievement_r()
    {
        set_time_limit(0);

        $param = get_params();
        $uid = $param['uid'];

        $dep_model = new DepartmentChange();
        $dep_fenguan = $dep_model->get_dep_fenguan(0, $uid);

        $newExcel = new Spreadsheet();  //创建一个新的excel文档
        $objSheet = $newExcel->getActiveSheet();  //获取当前操作sheet的对象
        $objSheet->setTitle('OA人才培养目标模板');  //设置当前sheet的标题

        $newExcel->getDefaultStyle()->getFont()->setName('微软雅黑');
        // 将字体大小设置为12
        $newExcel->getDefaultStyle()->getFont()->setSize(14);

        $objSheet->setCellValue('A1', '日期')
            ->setCellValue('B1', '门店')
            ->setCellValue('C1', '等级A')
            ->setCellValue('D1', '等级B')
            ->setCellValue('E1', '等级C');



        $i = 2;
        foreach ($dep_fenguan as $k => $v){
            $objSheet->setCellValue('A' . $i, date("Y-m"))
                ->setCellValue('B' . $i, $v['title'])
                ->setCellValue('C' . $i, '')
                ->setCellValue('D' . $i, '')
                ->setCellValue('E' . $i, '')
                ->setCellValue('F' . $i, '');
            $i++;
        }

        $this->downloadExcel($newExcel, 'OA人才培养目标模板', 'Xls');
    }
}
