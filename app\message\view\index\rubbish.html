{extend name="../../base/view/common/base" /}
<!-- 主体 -->
{block name="body"}
<div class="p-3">
	{include file="index/msgmenu" /}
	<div style="margin-left:172px;">
		<form class="layui-form gg-form-bar border-t border-x">
			<div class="layui-input-inline" style="width:240px">
				<input type="text" name="keywords" placeholder="关键字" class="layui-input" autocomplete="off"/>
			</div>
			<div class="layui-input-inline" id="date">
				<div class="layui-input-inline" style="width:112px; margin-bottom:0;">
				  <input type="text" autocomplete="off" id="start_time" name="start_time" class="layui-input" placeholder="开始日期">
				</div>
				~
				<div class="layui-input-inline" style="width:112px; margin-bottom:0">
				  <input type="text" autocomplete="off" id="end_time" name="end_time" class="layui-input" placeholder="结束日期">
				</div>
			</div>
			<div class="layui-input-inline" style="width:150px;">
				<button class="layui-btn layui-btn-normal" lay-submit="" lay-filter="webform"><i class="layui-icon layui-icon-search mr-1"></i>搜索</button>
				<button type="reset" class="layui-btn layui-btn-reset" lay-filter="reset">清空</button>
			</div>
		</form> 
		<table class="layui-hide" id="test" lay-filter="test"></table>
	</div>
</div>

<script type="text/html" id="mail_type">
	<span class="layui-type{{d.delete_source}}">{{d.delete_source_title}}</span>
</script>
<script type="text/html" id="is_read">
	<span class="layui-type{{d.is_read}}">{{d.is_read_title}}</span>
</script>

<script type="text/html" id="toolbarDemo">
	<div class="layui-btn-container">
		<button class="layui-btn layui-btn-normal layui-btn-sm" lay-event="add"><i class="layui-icon">&#xe61f;</i>新建消息</button>
		<button class="layui-btn layui-btn-danger layui-btn-sm" lay-event="del"><i class="layui-icon">&#xe640;</i>彻底删除</button>
		<button class="layui-btn layui-btn-sm" lay-event="recover"><i class="layui-icon">&#xe609;</i>移出垃圾箱</button>
	</div>
</script>

<script type="text/html" id="barDemo">
<div class="layui-btn-group"><button class="layui-btn layui-btn-xs layui-btn-normal" lay-event="view">查看</button><button class="layui-btn layui-btn-xs" lay-event="recover">还原</button><button class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">彻底删除</button></div>
</script>
{/block}
<!-- /主体 -->

<!-- 脚本 -->
{block name="script"}
<script>
	const moduleInit = ['tool'];
	function gouguInit() {
		var table = layui.table, tool = layui.tool ,form = layui.form,laydate = layui.laydate;
	  
		//日期范围
		laydate.render({
			elem: '#date',
			range: ['#start_time', '#end_time'],
			rangeLinked:true
		});

		layui.pageTable = table.render({
			elem: '#test',
			toolbar: '#toolbarDemo',
			title:'垃圾箱',
			url: "/message/index/rubbish", //数据接口
			page: true ,//开启分页
			limit: 20,
			cellMinWidth: 80, //全局定义常规单元格的最小宽度，layui 2.2.1 新增
			cols: [[ //表头
			  {type:'checkbox',fixed:'left'},
			  {field: 'delete_source_title', title: '来源',toolbar: '#mail_type',width:90,align:'center'},
			  {field: 'from_name', title: '发件人', width:100,align:'center'},
			  {field: 'title', title: '消息主题'},
			  {field: 'send_time', title: '发送时间', align:'center',width:160},
			  {field: 'right', title: '操作',fixed:'right', toolbar: '#barDemo', width:160, align:'center'}
			]],
			where:{type:1}
		});

		//监听行工具事件
		table.on('tool(test)', function(obj){
			var data = obj.data;
			if(obj.event === 'view'){
				tool.side('/message/index/read?id='+data.id);
				return;
			}
			if(obj.event === 'del'){
				layer.confirm('确定把该信息彻底删除吗?', {icon: 3, title:'提示'}, function(index){
					$.ajax({
						url: "/message/index/check",
						data:{ids:data.id,type:4},
						success:function(e){
							layer.msg(e.msg);
							if(e.code==0){
								layui.pageTable.reload();
							}		
						}
					})
					layer.close(index);
				})
			}
			if(obj.event === 'recover'){
				layer.confirm('确定把该信息移出垃圾箱吗?', {icon: 3, title:'提示'}, function(index){
					$.ajax({
						url: "/message/index/check",
						data:{ids:data.id,type:3},
						success:function(e){
							layer.msg(e.msg);
							if(e.code==0){
								layui.pageTable.reload();
							}		
						}
					})
					layer.close(index);
				})
			}
		});
		  
		//表头工具栏事件
		table.on('toolbar(test)', function(obj){
			var checkStatus = table.checkStatus(obj.config.id); //获取选中行状态
			var data = checkStatus.data;
			if (obj.event === 'add') {
				tool.side("/message/index/add");
				return;
			}
			if(data.length==0){
				layer.msg('请选择要操作的消息');
				return false;
			}
			var idArray=[],msg='是否执行该操作？',type=0;
			for(var i=0;i<data.length;i++){
				idArray.push(data[i].id);
			}
			switch(obj.event){
				case 'recover':
					msg = '确定把选中的信息移出垃圾箱吗?';
					type = 3;
				break;
				case 'del':
					msg = '确定把选中的信息彻底删除吗?';
					type = 4;
				break;
			};
			
			layer.confirm(msg, {
				icon: 3,
				title: '提示'
			}, function (index) {
				let callback = function (e) {
					layer.msg(e.msg);
					if(e.code==0){
						layui.pageTable.reload();
					}
				}
				tool.delete("/message/index/check", {
					ids: idArray.join(','),
					type:type
				}, callback);
				layer.close(index);
			});
		});
		  
		//监听搜索提交
		form.on('submit(webform)', function(data){
			let f=data.field;
			layui.pageTable.reload({where:{keywords:f.keywords,start_time:f.start_time,end_time:f.end_time},page:{curr:1}});
			return false;
		});
	}		
</script>
{/block}
<!-- /脚本 -->