<?php

namespace app\home\service;

use think\facade\Db;

class YouzanService
{

    //根据人员获取开卡售卡金额
    public function getKaj($sdate, $yz_uid, $sale_kdt_id)
    {
        $order_item_staff = Db::name("order_item_staff")
            ->field('ois.*,oi.*,o.buyer_new_customer,o.pay_real_pay,o.item_index,o.pay_pay_channel')
            ->alias('ois')
            ->join('order o', 'o.id = ois.order_id')
            ->join('order_item oi', 'oi.id = ois.order_item_id')
            ->where([
                ['ois.yz_open_id', '=', $yz_uid],
                ['o.sale_kdt_id', '=', $sale_kdt_id],
                ['o.finish_time', 'between', [strtotime("$sdate-01") * 1000, strtotime('+1 month', strtotime("$sdate-01")) * 1000]],
                ['o.order_state', '=', 40],
                ['o.is_reverse', '=', 0],
                ['ois.type', '=', '2'],
            ])->select()->toArray();

        if (empty($order_item_staff)) {
            return 0;
        }

        $ka_j = 0;
        foreach ($order_item_staff as $k => $v) {
            $server_num = Db::name("order_item_staff")->where(['item_no' => $v['item_no']])->count();
            $ka_j += $v['pay_real_pay'] / $server_num;
        }

        return round($ka_j / 100, 2);
    }

}