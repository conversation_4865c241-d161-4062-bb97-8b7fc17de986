<?php
/**
 * @copyright Copyright (c) 2021 仲正堂工作室
 * @license https://opensource.org/licenses/GPL-3.0
 * @link https://www.gougucms.com
 */
declare (strict_types=1);

namespace app\api\controller;

use app\api\BaseController;
use think\facade\Db;
use app\customer\model\Customer;
use avatars\MDAvatars;
use PhpOffice\PhpSpreadsheet\IOFactory;
use PhpOffice\PhpSpreadsheet\Shared\Date as Shared;
use PhpOffice\PhpSpreadsheet\Cell\Coordinate;
use app\user\service\AdminService;
use think\facade\Log;

class Import extends BaseController
{
    //生成头像
    public function to_avatars($char)
    {
        $defaultData = array('A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N',
            'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'S', 'Y', 'Z',
            '0', '1', '2', '3', '4', '5', '6', '7', '8', '9',
            '零', '壹', '贰', '叁', '肆', '伍', '陆', '柒', '捌', '玖', '拾',
            '一', '二', '三', '四', '五', '六', '七', '八', '九', '十');
        if (isset($char)) {
            $Char = $char;
        } else {
            $Char = $defaultData[mt_rand(0, count($defaultData) - 1)];
        }
        $OutputSize = min(512, empty($_GET['size']) ? 36 : intval($_GET['size']));

        $Avatar = new MDAvatars($Char, 256, 1);
        $avatar_name = '/avatars/avatar_256_' . set_salt(10) . time() . '.png';
        $path = get_config('filesystem.disks.public.url') . $avatar_name;
        $res = $Avatar->Save('.' . $path, 256);
        $Avatar->Free();
        return $path;
    }

    //登录名校验
    public function check_name($name, $arr)
    {
        if (in_array($name, $arr)) {
            $name = $this->check_name($name . '1', $arr);
        }
        return $name;
    }

    //导入员工
    public function import_admin()
    {

        header('Content-Type: text/html; charset=UTF-8');

        // 获取表单上传文件
        $file[] = request()->file('file');
        if ($this->uid > 1 && $this->uid != 880) {
            return to_assign(1, '该操作只能是超级管理员有权限操作');
        }
        try {
            // 验证文件大小，名称等是否正确
            validate(['file' => 'filesize:51200|fileExt:xls,xlsx'])->check($file);
            // 日期前綴
            $dataPath = date('Ym');
            $md5 = $file[0]->hash('md5');
            $savename = \think\facade\Filesystem::disk('public')->putFile($dataPath, $file[0], function () use ($md5) {
                return $md5;
            });
            $fileExtendName = substr(strrchr($savename, '.'), 1);
            // 有Xls和Xlsx格式两种
            if ($fileExtendName == 'xlsx') {
                $objReader = IOFactory::createReader('Xlsx');
            } else {
                $objReader = IOFactory::createReader('Xls');
            }
            $objReader->setReadDataOnly(TRUE);
            $path = get_config('filesystem.disks.public.url');
            // 读取文件，tp6默认上传的文件，在runtime的相应目录下，可根据实际情况自己更改
            $objPHPExcel = $objReader->load('.' . $path . '/' . $savename);
            $sheet = $objPHPExcel->getSheet(0);   //excel中的第一张sheet
            $highestRow = $sheet->getHighestRow();       // 取得总行数
            $highestColumn = $sheet->getHighestColumn();   // 取得总列数
            Coordinate::columnIndexFromString($highestColumn);
            $lines = $highestRow - 1;
            if ($lines <= 0) {
                return to_assign(1, '数据不能为空');
                exit();
            }
            $sex_array = ['未知', '男', '女'];
            $type_array = ['未知', '正式', '试用', '实习'];


            $mobile_array = Db::name('Admin')->where([['status', '>=', 0]])->column('mobile');
            //循环读取excel表格，整合成数组。如果是不指定key的二维，就用$data[i][j]表示。
            $data_index = 3;
            $update_time = time();
            for ($j = $data_index; $j <= $highestRow; $j++) {
                $workno = $objPHPExcel->getActiveSheet()->getCell("A" . $j)->getValue();
                $name = $objPHPExcel->getActiveSheet()->getCell("B" . $j)->getValue();
                if (empty($name)) {
                    continue;
                }
                $char = mb_substr($name, 0, 1, 'utf-8');
                //手机号
                $mobile = $objPHPExcel->getActiveSheet()->getCell("C" . $j)->getValue();
                //邮箱
                $email = $objPHPExcel->getActiveSheet()->getCell("D" . $j)->getValue();
                //性别
                $sex = arraySearch($sex_array, $objPHPExcel->getActiveSheet()->getCell("E" . $j)->getValue());
                //部门
                $excel_dep = $objPHPExcel->getActiveSheet()->getCell("F" . $j)->getValue();
                $department = array();
                $did_flag = false;
                if (!empty($excel_dep)) {
                    $excel_dep = explode("/", $excel_dep);
                    foreach ($excel_dep as $ek => $ev) {
                        $re_dep = Db::name('department')->where([['title', '=', $ev]])->field('id,remark')->find();
                        if (!empty($re_dep)) {
                            if ($re_dep['remark'] == '门店') {
                                $did_flag = true;
                            }
                            $department[] = $re_dep['id'];
                        }
                    }
                }
                $department = implode(",", $department);
                //职位
                $excel_pos = $objPHPExcel->getActiveSheet()->getCell("G" . $j)->getValue();
                $position = array();
                if (!empty($excel_pos)) {
                    $excel_pos = explode("/", $excel_pos);
                    foreach ($excel_pos as $ek => $ev) {
                        $re_pos = Db::name('position')->where([['title', '=', $ev]])->value('id');
                        if (!empty($re_pos)) {
                            $position[] = $re_pos;
                        }
                    }
                }
                $position = implode(",", $position);

                $file_check['mobile'] = $mobile;
                $validate_mobile = \think\facade\Validate::rule([
                    'mobile' => 'require|mobile',
                ]);

                if (!$validate_mobile->check($file_check)) {
                    return to_assign(1, '第' . ($j - $data_index + 1) . '行的手机号码:' . $mobile . '的手机号码的格式错误');
                } else {
                    if (in_array($mobile, $mobile_array)) {
                        $admin = Db::name('admin')->where(['mobile' => $mobile])->find();
                        if ($admin['name'] != $name) {
                            return to_assign(1, '第' . ($j - $data_index + 1) . '行的手机号码:' . $mobile . '已存在或者重复');
                        }
                    } else {
                        array_push($mobile_array, $mobile);
                    }
                }

                $data[$j - $data_index] = [
                    'workno' => $workno,
                    'name' => $name,
                    'mobile' => $mobile,
                    'email' => !empty($email) ? $email : '',
                    'sex' => $sex,
                    'username' => $mobile,
                    'did' => $department,
                    'position_id' => $position,
                    'update_time' => $update_time
                ];

                $entry_time = $objPHPExcel->getActiveSheet()->getCell("H" . $j)->getValue();
                if (!empty($entry_time)) {
                    if (is_numeric($entry_time)) {
                        $entry_time = date("Y-m-d", Shared::excelToTimestamp($objPHPExcel->getActiveSheet()->getCell("H" . $j)->getValue(), 'Asia/Shanghai'));
                    }
                    $data[$j - $data_index]['entry_time'] = strtotime($entry_time);
                } else {
                    $data[$j - $data_index]['entry_time'] = time();
                }
                //用户拓展信息
                //身份证号
                $id_card_number = $objPHPExcel->getActiveSheet()->getCell("I" . $j)->getValue();
                $data_ex[$j - $data_index]['id_card_number'] = $id_card_number;

                if (!empty($id_card_number)) {
                    $age_birthday = getAgeFromIdCard($id_card_number);
                    $data_ex[$j - $data_index]['birthday'] = $age_birthday['birthday'];
                    $data_ex[$j - $data_index]['age'] = $age_birthday['age'];
                } else {
                    $birthday = $objPHPExcel->getActiveSheet()->getCell("J" . $j)->getValue();
                    if (!empty($birthday)) {
                        $data_ex[$j - $data_index]['birthday'] = $birthday;
                    }
                    $data_ex[$j - $data_index]['age'] = $objPHPExcel->getActiveSheet()->getCell("K" . $j)->getValue();
                }

                $data_ex[$j - $data_index]['salary_card_number'] = $objPHPExcel->getActiveSheet()->getCell("L" . $j)->getValue();
                $bank_name = $objPHPExcel->getActiveSheet()->getCell("M" . $j)->getValue();
                $data_ex[$j - $data_index]['bank_name'] = preg_replace('/\s+/', '', $bank_name);
                $data_ex[$j - $data_index]['education'] = $objPHPExcel->getActiveSheet()->getCell("N" . $j)->getValue();
                $data_ex[$j - $data_index]['graduation_school'] = $objPHPExcel->getActiveSheet()->getCell("O" . $j)->getValue();
                $data_ex[$j - $data_index]['major'] = $objPHPExcel->getActiveSheet()->getCell("P" . $j)->getValue();
                $data_ex[$j - $data_index]['certificates'] = $objPHPExcel->getActiveSheet()->getCell("Q" . $j)->getValue();
                $data_ex[$j - $data_index]['emergency_contact'] = $objPHPExcel->getActiveSheet()->getCell("R" . $j)->getValue();
                $data_ex[$j - $data_index]['contact_mobile'] = $objPHPExcel->getActiveSheet()->getCell("S" . $j)->getValue();
                $c_age = $objPHPExcel->getActiveSheet()->getCell("T" . $j)->getValue();
                $data_ex[$j - $data_index]['c_age'] = !empty($c_age) ? $c_age : 0;

                if ($did_flag) {
                    $re_bol = isFullTwoYears($data[$j - $data_index]['entry_time']);
                    if ($re_bol) {
                        $data_ex[$j - $data_index]['position_salary'] = 1700;
                    } else {
                        $data_ex[$j - $data_index]['position_salary'] = 1500;
                    }
                } else {
                    $data_ex[$j - $data_index]['position_salary'] = 0;
                }
                $data_ex[$j - $data_index]['base_salary'] = 0;
                $data_ex[$j - $data_index]['performance_salary'] = 0;
                $data_ex[$j - $data_index]['trial_salary'] = 0;
            }
            //dd($data);exit;
            // 批量添加数据

            $re = false;
            $admin_service = new AdminService();
            $count = 0;
            $err_admin = array();
            foreach ($data as $k => $v) {
                $re = $admin_service->insertAdmin($v, $data_ex[$k]);
                if ($re) {
                    $count++;
                } else {
                    $err_admin[] = $v['mobile'];
                }
            }
            $err_text = implode(",", $err_admin);

            if ($count > 0) {
                if (empty($err_admin)) {
                    return to_assign(0, "导入成功，{$count}条数据;");
                } else {
                    return to_assign(1, "导入成功{$count}条数据;" . "未导入：" . $err_text);
                }
            } else {
                return to_assign(1, '导入失败，请检查excel文件再试');
            }

            // if ((new Admin())->saveAll($data)) {
            //     return to_assign(0, '导入成功');
            // }
            // else{
            // 	return to_assign(1, '导入失败，请检查excel文件再试');
            // }
        } catch (\think\exception\ValidateException $e) {
            return to_assign(1, $e->getMessage());
        }
    }

    //导入客户
    public function import_customer()
    {
        // 获取表单上传文件
        $file[] = request()->file('file');

        $param = get_params();
        $type = 'sea';
        if (isset($param['type'])) {
            $type = $param['type'];
        }
        try {
            // 验证文件大小，名称等是否正确
            validate(['file' => 'filesize:51200|fileExt:xls,xlsx'])->check($file);
            // 日期前綴
            $dataPath = date('Ym');
            $md5 = $file[0]->hash('md5');
            $savename = \think\facade\Filesystem::disk('public')->putFile($dataPath, $file[0], function () use ($md5) {
                return $md5;
            });
            $fileExtendName = substr(strrchr($savename, '.'), 1);
            // 有Xls和Xlsx格式两种
            if ($fileExtendName == 'xlsx') {
                $objReader = IOFactory::createReader('Xlsx');
            } else {
                $objReader = IOFactory::createReader('Xls');
            }
            $objReader->setReadDataOnly(TRUE);
            $path = get_config('filesystem.disks.public.url');
            // 读取文件，tp6默认上传的文件，在runtime的相应目录下，可根据实际情况自己更改
            $objPHPExcel = $objReader->load('.' . $path . '/' . $savename);
            //$objPHPExcel = $objReader->load('./storage/202209/d11544d20b3ca1c1a5f8ce799c3b2433.xlsx');
            $sheet = $objPHPExcel->getSheet(0);   //excel中的第一张sheet
            $highestRow = $sheet->getHighestRow();       // 取得总行数
            $highestColumn = $sheet->getHighestColumn();   // 取得总列数
            Coordinate::columnIndexFromString($highestColumn);
            $lines = $highestRow - 1;
            if ($lines <= 0) {
                return to_assign(1, '数据不能为空');
                exit();
            }
            $name_array = [];
            $source_array = Db::name('CustomerSource')->where(['status' => 1])->column('title', 'id');
            $grade_array = Db::name('CustomerGrade')->where(['status' => 1])->column('title', 'id');
            $industry_array = Db::name('Industry')->where(['status' => 1])->column('title', 'id');

            //循环读取excel表格，整合成数组。如果是不指定key的二维，就用$data[i][j]表示。
            for ($j = 3; $j <= $highestRow; $j++) {
                $file_check = [];
                $name = $objPHPExcel->getActiveSheet()->getCell("A" . $j)->getValue();
                if (empty($name)) {
                    continue;
                }
                $count_name = Db::name('Customer')->where('name', $name)->count();
                if ($count_name > 0) {
                    return to_assign(1, '第' . ($j - 2) . '行的客户名称已经存在');
                }
                if (in_array($name, $name_array)) {
                    return to_assign(1, '上传的文件存在相同的客户名称，请删除再操作');
                }
                array_push($name_array, $name);
                $source_id = arraySearch($source_array, $objPHPExcel->getActiveSheet()->getCell("B" . $j)->getValue());
                $grade_id = arraySearch($grade_array, $objPHPExcel->getActiveSheet()->getCell("C" . $j)->getValue());
                $industry_id = arraySearch($industry_array, $objPHPExcel->getActiveSheet()->getCell("D" . $j)->getValue());

                $c_name = $objPHPExcel->getActiveSheet()->getCell("E" . $j)->getValue();
                $c_mobile = $objPHPExcel->getActiveSheet()->getCell("F" . $j)->getValue();
                $file_check['c_mobile'] = $c_mobile;
                $tax_num = $objPHPExcel->getActiveSheet()->getCell("G" . $j)->getValue();
                $bank = $objPHPExcel->getActiveSheet()->getCell("H" . $j)->getValue();
                $bank_sn = $objPHPExcel->getActiveSheet()->getCell("I" . $j)->getValue();
                $file_check['bank_sn'] = $bank_sn;
                $bank_no = $objPHPExcel->getActiveSheet()->getCell("K" . $j)->getValue();
                $cperson_mobile = $objPHPExcel->getActiveSheet()->getCell("K" . $j)->getValue();
                $address = $objPHPExcel->getActiveSheet()->getCell("L" . $j)->getValue();
                $content = $objPHPExcel->getActiveSheet()->getCell("M" . $j)->getValue();
                $market = $objPHPExcel->getActiveSheet()->getCell("N" . $j)->getValue();
                if (empty($c_name)) {
                    return to_assign(1, '第' . ($j - 2) . '行的客户联系人姓名没完善');
                }
                if (empty($c_mobile)) {
                    return to_assign(1, '第' . ($j - 2) . '行的客户联系人手机号码没完善');
                }
                $validate_mobile = \think\facade\Validate::rule([
                    'c_mobile' => 'mobile',
                ]);
                if (!$validate_mobile->check($file_check)) {
                    return to_assign(1, '第' . ($j - 2) . '行的客户联系人手机号码格式错误');
                }
                if (empty($source_id)) {
                    return to_assign(1, '第' . ($j - 2) . '行的客户来源错误');
                }
                if (empty($grade_id)) {
                    return to_assign(1, '第' . ($j - 2) . '行的客户等级错误');
                }
                if (empty($industry_id)) {
                    return to_assign(1, '第' . ($j - 2) . '行的所属行业错误');
                }
                if (empty($tax_num)) {
                    $tax_num = '';
                }
                if (empty($bank)) {
                    $bank = '';
                }
                $validate_bank = \think\facade\Validate::rule([
                    'bank_sn' => 'number',
                ]);
                if (!empty($bank_sn)) {
                    if (!$validate_bank->check($file_check)) {
                        return to_assign(1, '第' . ($j - 2) . '行的银行卡账号格式错误');
                    }
                } else {
                    $bank_sn = '';
                }
                if (empty($bank_no)) {
                    $bank_no = '';
                }
                if (empty($cperson_mobile)) {
                    $cperson_mobile = '';
                }
                if (empty($address)) {
                    $address = '';
                }
                if (empty($content)) {
                    $content = '';
                }
                if (empty($market)) {
                    $market = '';
                }
                $belong_uid = 0;
                $belong_did = 0;
                if ($type != 'sea') {
                    $belong_uid = $this->uid;
                    $belong_did = $this->did;
                }
                $data[$j - 3] = [
                    'name' => $name,
                    'source_id' => $source_id,
                    'grade_id' => $grade_id,
                    'industry_id' => $industry_id,
                    'tax_num' => $tax_num,
                    'bank' => $bank,
                    'bank_sn' => $bank_sn,
                    'bank_no' => $bank_no,
                    'cperson_mobile' => $cperson_mobile,
                    'address' => $address,
                    'content' => $content,
                    'market' => $market,
                    'admin_id' => $this->uid,
                    'belong_uid' => $belong_uid,
                    'belong_did' => $belong_did,
                    'c_mobile' => $c_mobile,
                    'c_name' => $c_name,
                    'create_time' => time()
                ];
            }
            //dd($data);exit;
            // 批量添加数据
            $count = 0;
            foreach ($data as $a => $aa) {
                $cid = Customer::strict(false)->field(true)->insertGetId($aa);
                if ($cid > 0) {
                    $contact = [
                        'name' => $aa['c_name'],
                        'mobile' => $aa['c_mobile'],
                        'sex' => 1,
                        'cid' => $cid,
                        'is_default' => 1,
                        'create_time' => time(),
                        'admin_id' => $this->uid
                    ];
                    Db::name('CustomerContact')->strict(false)->field(true)->insert($contact);
                    $count++;
                }
            }
            return to_assign(0, '共成功导入了' . $count . '条客户数据');
        } catch (\think\exception\ValidateException $e) {
            return to_assign(1, $e->getMessage());
        }
    }

    public function import_store_bill()
    {
        set_time_limit(0);
        header('Content-Type: text/html; charset=UTF-8');

        // 获取表单上传文件
        $file[] = request()->file('file');
        try {
            // 验证文件大小，名称等是否正确
            validate(['file' => 'filesize:51200|fileExt:xls,xlsx'])->check($file);
            // 日期前綴
            $dataPath = date('Ym');
            $md5 = $file[0]->hash('md5');
            $savename = \think\facade\Filesystem::disk('public')->putFile($dataPath, $file[0], function () use ($md5) {
                return $md5;
            });
            $fileExtendName = substr(strrchr($savename, '.'), 1);
            // 有Xls和Xlsx格式两种
            if ($fileExtendName == 'xlsx') {
                $objReader = IOFactory::createReader('Xlsx');
            } else {
                $objReader = IOFactory::createReader('Xls');
            }
            $objReader->setReadDataOnly(TRUE);
            $path = get_config('filesystem.disks.public.url');
            // 读取文件，tp6默认上传的文件，在runtime的相应目录下，可根据实际情况自己更改
            $objPHPExcel = $objReader->load('.' . $path . '/' . $savename);
            $sheet = $objPHPExcel->getSheet(0);   //excel中的第一张sheet
            $highestRow = $sheet->getHighestRow();       // 取得总行数
            $highestColumn = $sheet->getHighestColumn();   // 取得总列数
            Coordinate::columnIndexFromString($highestColumn);
            $lines = $highestRow - 1;
            if ($lines <= 0) {
                return to_assign(1, '数据不能为空');
                exit();
            }

            $param = get_params();


            $type_son = Db::name("type")->where(
                [['controller', '=', 'store_bill'], ['pid', '>', '0']]
            )->select()->toArray();

            $re = array();

            $count = 0;
            //循环读取excel表格，整合成数组。如果是不指定key的二维，就用$data[i][j]表示。
            for ($j = 2; $j <= $highestRow; $j++) {
                $count++;
                $sdate = date("Y-m", Shared::excelToTimestamp($objPHPExcel->getActiveSheet()->getCell("B" . $j)->getValue(), 'Asia/Shanghai'));
                $abstract = $objPHPExcel->getActiveSheet()->getCell("C" . $j)->getValue();
                $title_2 = $objPHPExcel->getActiveSheet()->getCell("D" . $j)->getValue();
                $dname = $objPHPExcel->getActiveSheet()->getCell("E" . $j)->getValue();
                $amount = $objPHPExcel->getActiveSheet()->getCell("F" . $j)->getValue();
                $remark = $objPHPExcel->getActiveSheet()->getCell("G" . $j)->getValue();
                $title_1 = $objPHPExcel->getActiveSheet()->getCell("H" . $j)->getValue();

                if (empty($dname)) {
                    continue;
                }

                //删除未锁定的数据
                if ($param['type'] == 1 && $j == 2) {
                    Db::name("store_bill")->where(['is_lock' => 0])->delete();
                    Db::name("store_bill_s")->where(['is_lock' => 0, 'aid' => $this->uid])->delete();
                }

                $store_bill_data = [
                    'create_time' => date('Y-m-d'),
                    'sdate_time' => strtotime(date("Y-m", strtotime($sdate))),
                    'sdate_date' => $sdate,
                ];

                if ($dname == '总部') {
                    $store_bill_data['did'] = 0;
                    $store_bill_data['dname'] = '总部';
                } else {
                    $dname = str_replace('路', "", $dname);
                    $dname = str_replace('店', "", $dname);
                    $store = Db::name("department")->where([['title', 'like', "%$dname%"]])->find();
                    if (!empty($store)) {
                        $store_bill_data['did'] = $store['id'];
                        $store_bill_data['dname'] = $store['title'];
                    } else {
                        continue;
                    }
                }

                $store_bill = Db::name("store_bill")->where([
                    'did' => $store_bill_data['did'],
                    'sdate_date' => $store_bill_data['sdate_date'],
                ])->find();

                if (empty($store_bill)) {
                    $store_bill_id = Db::name("store_bill")->insertGetId($store_bill_data);
                } else {
                    //跳过已锁定的数据
                    if ($store_bill['is_lock'] == 1) {
                        continue;
                    }
                    $store_bill_id = $store_bill['id'];
                }

                $field = '';
                foreach ($type_son as $tsk => $tsv) {
                    if ($tsv['name'] == $title_2) {
                        $field = $tsv['title'];
                        break;
                    }
                }

//                $re[] = Db::name("store_bill_s")->insertGetId();
                $re[] = [
                    'store_id' => $store_bill_id,
                    'sdate' => $sdate,
                    'abstract' => $abstract,
                    'title_1' => $title_1,
                    'title_2' => $title_2,
                    'amount' => $amount,
                    'remark' => $remark,
                    'did' => $store_bill_data['did'],
                    'dname' => $store_bill_data['dname'],
                    'field' => $field,
                    'aid' => $this->uid,
                    'create_time' => date("Y-m-d H:i:s"),
                    'type' => $param['type'],
                ];

                //$type = Db::name("type")->where(['controller' => 'store_bill', 'name' => $title_2])->find();
//                if (!empty($type)) {
//                    $re = Db::name("store_bill")
//                        ->where('id', $store_bill_id)
//                        ->inc($type['title'], $amount)
//                        ->update();
//                }
//                if ($re) {
//                    $count++;
//                }
            }

            Db::name("store_bill_s")->insertAll($re);

            $re_count = count($re);

            // 导入完成后，直接基于已更新的汇总数据同步分红数据
            Log::info("开始同步门店分红数据 - 导入数据量: {$re_count}");
            $syncService = new \app\store\service\StoreBillImportSyncService();
            $syncService->syncDividendDataAfterImport($re);
            Log::info("门店分红数据同步完成");

            return to_assign(0, "导入成功，共{$count}条数据，已导入{$re_count}条数据");

        } catch (\think\exception\ValidateException $e) {
            return to_assign(1, $e->getMessage());
        }
    }

    public function import_Storeachievement()
    {
        // 获取表单上传文件
        $file[] = request()->file('file');

        try {
            // 验证文件大小，名称等是否正确
            validate(['file' => 'filesize:51200|fileExt:xls,xlsx'])->check($file);
            // 日期前綴
            $dataPath = date('Ym');
            $md5 = $file[0]->hash('md5');
            $savename = \think\facade\Filesystem::disk('public')->putFile($dataPath, $file[0], function () use ($md5) {
                return $md5;
            });
            $fileExtendName = substr(strrchr($savename, '.'), 1);
            // 有Xls和Xlsx格式两种
            if ($fileExtendName == 'xlsx') {
                $objReader = IOFactory::createReader('Xlsx');
            } else {
                $objReader = IOFactory::createReader('Xls');
            }
            $objReader->setReadDataOnly(TRUE);
            $path = get_config('filesystem.disks.public.url');
            // 读取文件，tp6默认上传的文件，在runtime的相应目录下，可根据实际情况自己更改
            $objPHPExcel = $objReader->load('.' . $path . '/' . $savename);
            //$objPHPExcel = $objReader->load('./storage/202209/d11544d20b3ca1c1a5f8ce799c3b2433.xlsx');
            $sheet = $objPHPExcel->getSheet(0);   //excel中的第一张sheet
            $highestRow = $sheet->getHighestRow();       // 取得总行数
            $highestColumn = $sheet->getHighestColumn();   // 取得总列数
            Coordinate::columnIndexFromString($highestColumn);
            $lines = $highestRow - 1;
            if ($lines <= 0) {
                return to_assign(1, '数据不能为空');
                exit();
            }

            //循环读取excel表格，整合成数组。如果是不指定key的二维，就用$data[i][j]表示。
            for ($j = 2; $j <= $highestRow; $j++) {
                $year = $objPHPExcel->getActiveSheet()->getCell("A" . $j)->getValue();
                $index = $objPHPExcel->getActiveSheet()->getCell("B" . $j)->getValue();
                $dname = $objPHPExcel->getActiveSheet()->getCell("C" . $j)->getValue();
                if (empty($dname) || empty($year) || empty($index)) {
                    continue;
                }
                $target_amount = $objPHPExcel->getActiveSheet()->getCell("D" . $j)->getValue();

                if (empty($target_amount)) {
                    $target_amount = 0;
                }

                $Department = Db::name("Department")->where(['title' => $dname])->find();
                if (empty($Department)) {
                    continue;
                }

                $data[$j - 2] = [
                    'target_amount' => $target_amount,
                    'did' => $Department['id'],
                    'pid' => 0,
                    'dname' => $Department['title'],
                    'create_time' => time(),
                    'create_date' => date("Y-m-d"),
                    'year' => $year,
                    'season' => $index,
                ];
            }
            //dd($data);exit;
            // 批量添加数据
            $count = 0;
            foreach ($data as $a => $aa) {
                //$cid = Db::->insertGetId($aa);
                $s_achievement_s = Db::name("store_achievement_s")->where([
                    'year' => $aa['year'], 'season' => $aa['season'], 'did' => $aa['did']
                ])->find();
                if (!empty($s_achievement_s)) {
                    Db::name("store_achievement_s")->where(['id' => $s_achievement_s['id']])->update(['target_amount' => $aa['target_amount']]);
                } else {
                    Db::name("store_achievement_s")->insertGetId($aa);
                }
                $count++;
            }
            return to_assign(0, '共成功导入了' . $count . '条数据');
        } catch (\think\exception\ValidateException $e) {
            return to_assign(1, $e->getMessage());
        }
    }

    public function import_store()
    {
        // 获取表单上传文件
        $file[] = request()->file('file');

        try {
            // 验证文件大小，名称等是否正确
            validate(['file' => 'filesize:51200|fileExt:xls,xlsx'])->check($file);
            // 日期前綴
            $dataPath = date('Ym');
            $md5 = $file[0]->hash('md5');
            $savename = \think\facade\Filesystem::disk('public')->putFile($dataPath, $file[0], function () use ($md5) {
                return $md5;
            });
            $fileExtendName = substr(strrchr($savename, '.'), 1);
            // 有Xls和Xlsx格式两种
            if ($fileExtendName == 'xlsx') {
                $objReader = IOFactory::createReader('Xlsx');
            } else {
                $objReader = IOFactory::createReader('Xls');
            }
            $objReader->setReadDataOnly(TRUE);
            $path = get_config('filesystem.disks.public.url');
            // 读取文件，tp6默认上传的文件，在runtime的相应目录下，可根据实际情况自己更改
            $objPHPExcel = $objReader->load('.' . $path . '/' . $savename);
            //$objPHPExcel = $objReader->load('./storage/202209/d11544d20b3ca1c1a5f8ce799c3b2433.xlsx');
            $sheet = $objPHPExcel->getSheet(0);   //excel中的第一张sheet
            $highestRow = $sheet->getHighestRow();       // 取得总行数
            $highestColumn = $sheet->getHighestColumn();   // 取得总列数
            Coordinate::columnIndexFromString($highestColumn);
            $lines = $highestRow - 1;
            if ($lines <= 0) {
                return to_assign(1, '数据不能为空');
                exit();
            }

            //循环读取excel表格，整合成数组。如果是不指定key的二维，就用$data[i][j]表示。
            for ($j = 2; $j <= $highestRow; $j++) {
                $sdate = $objPHPExcel->getActiveSheet()->getCell("A" . $j)->getValue();

                $did = $objPHPExcel->getActiveSheet()->getCell("B" . $j)->getValue();

                $aname = $objPHPExcel->getActiveSheet()->getCell("D" . $j)->getValue();
                $aid = 0;
                if (!empty($aname)) {
                    $admin = Db::name("admin")->where(['name' => $aname])->find();
                    if (!empty($admin)) {
                        $aid = $admin['id'];
                    }
                } else {
                    $aname = '';
                }

                $target_amount = $objPHPExcel->getActiveSheet()->getCell("E" . $j)->getValue(); //业绩指标

                $nstatus = $objPHPExcel->getActiveSheet()->getCell("F" . $j)->getValue(); //新店
                $rstatus = $objPHPExcel->getActiveSheet()->getCell("G" . $j)->getValue(); //参与改革

                $grade = $objPHPExcel->getActiveSheet()->getCell("H" . $j)->getValue(); //参与改革
                $grade_amount = $objPHPExcel->getActiveSheet()->getCell("I" . $j)->getValue(); //参与改革
                $ratio = $objPHPExcel->getActiveSheet()->getCell("J" . $j)->getValue(); //参与改革


                $data[$j - 2] = [
                    'sdate' => $sdate,

                    'did' => $did,
                    'aid' => $aid,
                    'aname' => $aname,

                    'target_amount' => $target_amount,

                    'nstatus' => $nstatus == '是' ? 1 : 0,
                    'rstatus' => $rstatus == '是' ? 1 : 0,

                    'grade' => $grade,
                    'grade_amount' => $grade_amount,
                    'ratio' => $ratio,

                    'update_time' => time()

                ];
            }
            //dd($data);exit;
            // 批量添加数据
            $count = 0;
            foreach ($data as $a => $aa) {
                //$cid = Db::->insertGetId($aa);
                $store_data = Db::name("store")->where(['sdate' => $aa['sdate'], 'did' => $aa['did']])->find();
                if (!empty($store_data)) {
                    Db::name("store")->where(['id' => $store_data['id']])->update($aa);
                    $count++;
                }
            }
            return to_assign(0, '共成功导入了' . $count . '条数据');
        } catch (\think\exception\ValidateException $e) {
            return to_assign(1, $e->getMessage());
        }
    }

    public function import_rent_store()
    {
        // 获取表单上传文件
        $file[] = request()->file('file');

        try {
            // 验证文件大小，名称等是否正确
            validate(['file' => 'filesize:51200|fileExt:xls,xlsx'])->check($file);
            // 日期前綴
            $dataPath = date('Ym');
            $md5 = $file[0]->hash('md5');
            $savename = \think\facade\Filesystem::disk('public')->putFile($dataPath, $file[0], function () use ($md5) {
                return $md5;
            });
            $fileExtendName = substr(strrchr($savename, '.'), 1);
            // 有Xls和Xlsx格式两种
            if ($fileExtendName == 'xlsx') {
                $objReader = IOFactory::createReader('Xlsx');
            } else {
                $objReader = IOFactory::createReader('Xls');
            }
            $objReader->setReadDataOnly(TRUE);
            $path = get_config('filesystem.disks.public.url');
            // 读取文件，tp6默认上传的文件，在runtime的相应目录下，可根据实际情况自己更改
            $objPHPExcel = $objReader->load('.' . $path . '/' . $savename);
            //$objPHPExcel = $objReader->load('./storage/202209/d11544d20b3ca1c1a5f8ce799c3b2433.xlsx');
            $sheet = $objPHPExcel->getSheet(0);   //excel中的第一张sheet
            $highestRow = $sheet->getHighestRow();       // 取得总行数
            $highestColumn = $sheet->getHighestColumn();   // 取得总列数
            Coordinate::columnIndexFromString($highestColumn);
            $lines = $highestRow - 1;
            if ($lines <= 0) {
                return to_assign(1, '数据不能为空');
                exit();
            }

            //循环读取excel表格，整合成数组。如果是不指定key的二维，就用$data[i][j]表示。
            for ($j = 2; $j <= $highestRow; $j++) {
                $sdate = $objPHPExcel->getActiveSheet()->getCell("A" . $j)->getValue();
                $did = $objPHPExcel->getActiveSheet()->getCell("B" . $j)->getValue();

                $rent_amount = $objPHPExcel->getActiveSheet()->getCell("D" . $j)->getValue(); //门店租金
                $wuye_amount = $objPHPExcel->getActiveSheet()->getCell("E" . $j)->getValue(); //物业费
                $sushe_amount = $objPHPExcel->getActiveSheet()->getCell("F" . $j)->getValue(); //宿舍租金

                $data[$j - 2] = [
                    'sdate' => $sdate,
                    'did' => $did,

                    'rent_amount' => !empty($rent_amount) ? $rent_amount : 0,
                    'wuye_amount' => !empty($wuye_amount) ? $wuye_amount : 0,
                    'sushe_amount' => !empty($sushe_amount) ? $sushe_amount : 0,
                    'update_time' => time()
                ];

                $data[$j - 2]['amount'] = $data[$j - 2]['rent_amount'] + $data[$j - 2]['wuye_amount'];
            }
            //dd($data);exit;
            // 批量添加数据
            $count = 0;
            foreach ($data as $a => $aa) {
                //$cid = Db::->insertGetId($aa);
                $store_data = Db::name("store")->where(['sdate' => $aa['sdate'], 'did' => $aa['did']])->find();
//                getLastSql("store");
                if (!empty($store_data)) {
                    Db::name("store")->where(['id' => $store_data['id']])->update($aa);
                    $count++;
                }
            }
            return to_assign(0, '共成功导入了' . $count . '条数据');
        } catch (\think\exception\ValidateException $e) {
            return to_assign(1, $e->getMessage());
        }
    }


    public function import_annual_leave()
    {
        // 获取表单上传文件
        $file[] = request()->file('file');

        try {
            // 验证文件大小，名称等是否正确
            validate(['file' => 'filesize:51200|fileExt:xls,xlsx'])->check($file);
            // 日期前綴
            $dataPath = date('Ym');
            $md5 = $file[0]->hash('md5');
            $savename = \think\facade\Filesystem::disk('public')->putFile($dataPath, $file[0], function () use ($md5) {
                return $md5;
            });
            $fileExtendName = substr(strrchr($savename, '.'), 1);
            // 有Xls和Xlsx格式两种
            if ($fileExtendName == 'xlsx') {
                $objReader = IOFactory::createReader('Xlsx');
            } else {
                $objReader = IOFactory::createReader('Xls');
            }
            $objReader->setReadDataOnly(TRUE);
            $path = get_config('filesystem.disks.public.url');
            // 读取文件，tp6默认上传的文件，在runtime的相应目录下，可根据实际情况自己更改
            $objPHPExcel = $objReader->load('.' . $path . '/' . $savename);
            //$objPHPExcel = $objReader->load('./storage/202209/d11544d20b3ca1c1a5f8ce799c3b2433.xlsx');
            $sheet = $objPHPExcel->getSheet(0);   //excel中的第一张sheet
            $highestRow = $sheet->getHighestRow();       // 取得总行数
            $highestColumn = $sheet->getHighestColumn();   // 取得总列数
            Coordinate::columnIndexFromString($highestColumn);
            $lines = $highestRow - 1;
            if ($lines <= 0) {
                return to_assign(1, '数据不能为空');
                exit();
            }

            //循环读取excel表格，整合成数组。如果是不指定key的二维，就用$data[i][j]表示。
            for ($j = 2; $j <= $highestRow; $j++) {
                $id = $objPHPExcel->getActiveSheet()->getCell("A" . $j)->getValue();
                $annual_leave = $objPHPExcel->getActiveSheet()->getCell("C" . $j)->getValue(); //门店租金
                $is_annual_leave = $objPHPExcel->getActiveSheet()->getCell("D" . $j)->getValue(); //物业费


                $data[$j - 2] = [
                    'id' => $id,
                    'annual_leave' => !empty($annual_leave) ? $annual_leave : 0,
                    'is_annual_leave' => !empty($is_annual_leave) ? $is_annual_leave : 0
                ];
            }
            //dd($data);exit;
            // 批量添加数据
            $count = 0;
            foreach ($data as $a => $aa) {
                $store_data = Db::name("admin")->where(['id' => $aa['id']])->find();
                if (!empty($store_data)) {
                    Db::name("admin")->where(['id' => $aa['id']])->update($aa);
                    $count++;
                }
            }
            return to_assign(0, '共成功导入了' . $count . '条数据');
        } catch (\think\exception\ValidateException $e) {
            return to_assign(1, $e->getMessage());
        }
    }

    public function importStorecontranct()
    {
        // 获取表单上传文件
        $file[] = request()->file('file');

        try {
            // 验证文件大小，名称等是否正确
            validate(['file' => 'filesize:51200|fileExt:xls,xlsx'])->check($file);
            // 日期前綴
            $dataPath = date('Ym');
            $md5 = $file[0]->hash('md5');
            $savename = \think\facade\Filesystem::disk('public')->putFile($dataPath, $file[0], function () use ($md5) {
                return $md5;
            });
            $fileExtendName = substr(strrchr($savename, '.'), 1);
            // 有Xls和Xlsx格式两种
            if ($fileExtendName == 'xlsx') {
                $objReader = IOFactory::createReader('Xlsx');
            } else {
                $objReader = IOFactory::createReader('Xls');
            }
            $objReader->setReadDataOnly(TRUE);
            $path = get_config('filesystem.disks.public.url');
            // 读取文件，tp6默认上传的文件，在runtime的相应目录下，可根据实际情况自己更改
            $objPHPExcel = $objReader->load('.' . $path . '/' . $savename);
            //$objPHPExcel = $objReader->load('./storage/202209/d11544d20b3ca1c1a5f8ce799c3b2433.xlsx');
            $sheet = $objPHPExcel->getSheet(0);   //excel中的第一张sheet
            $highestRow = $sheet->getHighestRow();       // 取得总行数
            $highestColumn = $sheet->getHighestColumn();   // 取得总列数
            Coordinate::columnIndexFromString($highestColumn);
            $lines = $highestRow - 1;
            if ($lines <= 0) {
                return to_assign(1, '数据不能为空');
                exit();
            }

            //循环读取excel表格，整合成数组。如果是不指定key的二维，就用$data[i][j]表示。
            for ($j = 2; $j <= $highestRow; $j++) {
                $sdate = $objPHPExcel->getActiveSheet()->getCell("A" . $j)->getValue();
                $did = $objPHPExcel->getActiveSheet()->getCell("B" . $j)->getValue(); //门店租金
                $dname = $objPHPExcel->getActiveSheet()->getCell("C" . $j)->getValue(); //物业费
                $hp_num = $objPHPExcel->getActiveSheet()->getCell("D" . $j)->getValue(); //物业费

                $data[$j - 2] = [
                    'sdate' => $sdate,
                    'did' => $did,
                    'dname' => $dname,
                    'hp_num' => !empty($hp_num) ? $hp_num : 0,
                ];
            }

            // 批量添加数据
            $count = 0;
            foreach ($data as $a => $aa) {
                $store_data = Db::name("store_contrast")->where([
                    'sdate' => $aa['sdate'],
                    'did' => $aa['did']
                ])->find();
                if (!empty($store_data)) {
                    Db::name("store_contrast")->where(['id' => $store_data['id']])->update($aa);
                    $count++;
                } else {
                    Db::name("store_contrast")->insertGetId($aa);
                    $count++;
                }
            }
            return to_assign(0, '共成功导入了' . $count . '条数据');
        } catch (\think\exception\ValidateException $e) {
            return to_assign(1, $e->getMessage());
        }
    }

    public function importSalaryExternal()
    {

        // 获取表单上传文件
        $file[] = request()->file('file');

        try {
            // 验证文件大小，名称等是否正确
            validate(['file' => 'filesize:51200|fileExt:xls,xlsx'])->check($file);
            // 日期前綴
            $dataPath = date('Ym');
            $md5 = $file[0]->hash('md5');
            $savename = \think\facade\Filesystem::disk('public')->putFile($dataPath, $file[0], function () use ($md5) {
                return $md5;
            });
            $fileExtendName = substr(strrchr($savename, '.'), 1);
            // 有Xls和Xlsx格式两种
            if ($fileExtendName == 'xlsx') {
                $objReader = IOFactory::createReader('Xlsx');
            } else {
                $objReader = IOFactory::createReader('Xls');
            }
            $objReader->setReadDataOnly(TRUE);
            $path = get_config('filesystem.disks.public.url');
            // 读取文件，tp6默认上传的文件，在runtime的相应目录下，可根据实际情况自己更改
            $objPHPExcel = $objReader->load('.' . $path . '/' . $savename);
            //$objPHPExcel = $objReader->load('./storage/202209/d11544d20b3ca1c1a5f8ce799c3b2433.xlsx');
            $sheet = $objPHPExcel->getSheet(0);   //excel中的第一张sheet
            $highestRow = $sheet->getHighestRow();       // 取得总行数
            $highestColumn = $sheet->getHighestColumn();   // 取得总列数
            Coordinate::columnIndexFromString($highestColumn);
            $lines = $highestRow - 1;
            if ($lines <= 0) {
                return to_assign(1, '数据不能为空');
                exit();
            }

            $data = array();

            //循环读取excel表格，整合成数组。如果是不指定key的二维，就用$data[i][j]表示。
            for ($j = 3; $j <= $highestRow; $j++) {
                $sdate = $objPHPExcel->getActiveSheet()->getCell("A" . $j)->getValue(); //物业费

                $dname = $objPHPExcel->getActiveSheet()->getCell("B" . $j)->getValue(); //物业费
                $aname = $objPHPExcel->getActiveSheet()->getCell("C" . $j)->getValue(); //物业费

                $payable_salary = $objPHPExcel->getActiveSheet()->getCell("D" . $j)->getValue(); //物业费

                $wh_pension = $objPHPExcel->getActiveSheet()->getCell("F" . $j)->getValue(); //物业费
                $wh_medical = $objPHPExcel->getActiveSheet()->getCell("G" . $j)->getValue(); //物业费
                $wh_unemployed = $objPHPExcel->getActiveSheet()->getCell("H" . $j)->getValue(); //物业费

                $wh_accumulation_fund = $objPHPExcel->getActiveSheet()->getCell("J" . $j)->getValue(); //物业费
                $income_tax = $objPHPExcel->getActiveSheet()->getCell("K" . $j)->getValue(); //物业费
                $real_salary = $objPHPExcel->getActiveSheet()->getCell("L" . $j)->getValue(); //物业费

                $admin = Db::name("admin")->where([['name', 'like', "%{$aname}%"]])->find();

                if (empty($admin)) {
                    continue;
                }

                $dname = str_replace('路', "", $dname);
                $dname = str_replace('店', "", $dname);
                $dname = str_replace('东', "", $dname);
                $store = Db::name("department")->where([['title', 'like', "%$dname%"]])->find();

                if (empty($store)) {
                    continue;
                }
                if (empty($sdate)) continue;
                $data[] = [
                    'aid' => $admin['id'],
                    'aname' => $admin['name'],

                    'did' => $store['id'],
                    'dname' => $store['title'],

                    'payable_salary' => $payable_salary, //应发工资
                    'wh_pension' => $wh_pension, //代扣养老
                    'wh_medical' => $wh_medical, //代扣医疗
                    'wh_unemployed' => $wh_unemployed, //代扣失业

                    'wh_accumulation_fund' => empty($wh_accumulation_fund) ? 0 : $wh_accumulation_fund, //代扣公积金
                    'income_tax' => empty($income_tax) ? 0 : $income_tax, //代扣个人所得税

                    'real_salary' => $real_salary, //实发工资
                    'sdate' => strtotime($sdate),
                    'sdate_date' => "$sdate-01 00:00:00",
                ];

            }

            Db::name("store_salary_external")->insertAll($data);

            return to_assign(0, "共{$highestRow}数据，成功导入了" . count($data) . '条数据');
        } catch (\think\exception\ValidateException $e) {
            return to_assign(1, $e->getMessage());
        }
    }

    public function importRent()
    {

        // 获取表单上传文件
        $file[] = request()->file('file');

        $param = get_params();

        if (!isset($param['in_cr_id']) && empty($param['in_cr_id'])) {
            return to_assign(1, "合同不存在");
        }

        try {
            // 验证文件大小，名称等是否正确
            validate(['file' => 'filesize:51200|fileExt:xls,xlsx'])->check($file);
            // 日期前綴
            $dataPath = date('Ym');
            $md5 = $file[0]->hash('md5');
            $savename = \think\facade\Filesystem::disk('public')->putFile($dataPath, $file[0], function () use ($md5) {
                return $md5;
            });
            $fileExtendName = substr(strrchr($savename, '.'), 1);
            // 有Xls和Xlsx格式两种
            if ($fileExtendName == 'xlsx') {
                $objReader = IOFactory::createReader('Xlsx');
            } else {
                $objReader = IOFactory::createReader('Xls');
            }
            $objReader->setReadDataOnly(TRUE);
            $path = get_config('filesystem.disks.public.url');
            // 读取文件，tp6默认上传的文件，在runtime的相应目录下，可根据实际情况自己更改
            $objPHPExcel = $objReader->load('.' . $path . '/' . $savename);
            //$objPHPExcel = $objReader->load('./storage/202209/d11544d20b3ca1c1a5f8ce799c3b2433.xlsx');
            $sheet = $objPHPExcel->getSheet(0);   //excel中的第一张sheet
            $highestRow = $sheet->getHighestRow();       // 取得总行数
            $highestColumn = $sheet->getHighestColumn();   // 取得总列数
            Coordinate::columnIndexFromString($highestColumn);
            $lines = $highestRow - 1;
            if ($lines <= 0) {
                return to_assign(1, '数据不能为空');
                exit();
            }

            $data = array();

            $contract_rent = Db::name("contract_rent")->find(['id' => $param['in_cr_id']]);

            if (empty($contract_rent)) {
                return to_assign(1, "合同不存在");
            }

            //循环读取excel表格，整合成数组。如果是不指定key的二维，就用$data[i][j]表示。
            for ($j = 3; $j <= $highestRow; $j++) {
                $pay_date = date("Y-m-d", Shared::excelToTimestamp($objPHPExcel->getActiveSheet()->getCell("A" . $j)->getValue(), 'Asia/Shanghai'));  //付款日期
//                $pay_date = $objPHPExcel->getActiveSheet()->getCell("A" . $j)->getValue(); //付款日期

                $m_amount = $objPHPExcel->getActiveSheet()->getCell("B" . $j)->getValue(); //月租金
                $wyf_amount = $objPHPExcel->getActiveSheet()->getCell("C" . $j)->getValue(); //物业费

                $o_amount = $objPHPExcel->getActiveSheet()->getCell("D" . $j)->getValue(); //其他费用

                $ei_amount = $objPHPExcel->getActiveSheet()->getCell("E" . $j)->getValue(); //每期金额
                $ei_start_date = date("Y-m-d", Shared::excelToTimestamp($objPHPExcel->getActiveSheet()->getCell("F" . $j)->getValue(), 'Asia/Shanghai'));  //付款日期
//                $ei_start_date = $objPHPExcel->getActiveSheet()->getCell("F" . $j)->getValue(); //周期起
                $ei_end_date = date("Y-m-d", Shared::excelToTimestamp($objPHPExcel->getActiveSheet()->getCell("G" . $j)->getValue(), 'Asia/Shanghai'));  //付款日期
//                $ei_end_date = $objPHPExcel->getActiveSheet()->getCell("G" . $j)->getValue(); //周期止

                $is_pay = $objPHPExcel->getActiveSheet()->getCell("H" . $j)->getValue(); //付款情况
                $remark = $objPHPExcel->getActiveSheet()->getCell("I" . $j)->getValue(); //备注

                $data[] = [
                    'pay_date' => $pay_date,
                    'status' => !empty($pay_date) ? 1 : 0,
                    'm_amount' => $m_amount,
                    'wyf_amount' => $wyf_amount,
                    'o_amount' => $o_amount,
                    'ei_amount' => $ei_amount,
                    'ei_start_date' => $ei_start_date,
                    'ei_end_date' => $ei_end_date,
                    'is_pay' => !empty($is_pay) ? 1 : 0,
                    'remark' => $remark,
                    'cr_id' => $contract_rent['id'],
                    'did' => $contract_rent['did'],
                ];

            }

            Db::name("rent")->insertAll($data);

            return to_assign(0, "共{$highestRow}数据，成功导入了" . count($data) . '条数据');
        } catch (\think\exception\ValidateException $e) {
            return to_assign(1, $e->getMessage());
        }
    }

    public function importStoreAchievementR()
    {

        // 获取表单上传文件
        $file[] = request()->file('file');

        try {
            // 验证文件大小，名称等是否正确
            validate(['file' => 'filesize:51200|fileExt:xls,xlsx'])->check($file);
            // 日期前綴
            $dataPath = date('Ym');
            $md5 = $file[0]->hash('md5');
            $savename = \think\facade\Filesystem::disk('public')->putFile($dataPath, $file[0], function () use ($md5) {
                return $md5;
            });
            $fileExtendName = substr(strrchr($savename, '.'), 1);
            // 有Xls和Xlsx格式两种
            if ($fileExtendName == 'xlsx') {
                $objReader = IOFactory::createReader('Xlsx');
            } else {
                $objReader = IOFactory::createReader('Xls');
            }
            $objReader->setReadDataOnly(TRUE);
            $path = get_config('filesystem.disks.public.url');
            // 读取文件，tp6默认上传的文件，在runtime的相应目录下，可根据实际情况自己更改
            $objPHPExcel = $objReader->load('.' . $path . '/' . $savename);
            //$objPHPExcel = $objReader->load('./storage/202209/d11544d20b3ca1c1a5f8ce799c3b2433.xlsx');
            $sheet = $objPHPExcel->getSheet(0);   //excel中的第一张sheet
            $highestRow = $sheet->getHighestRow();       // 取得总行数
            $highestColumn = $sheet->getHighestColumn();   // 取得总列数
            Coordinate::columnIndexFromString($highestColumn);
            $lines = $highestRow - 1;
            if ($lines <= 0) {
                return to_assign(1, '数据不能为空');
                exit();
            }

            $data = array();

            //循环读取excel表格，整合成数组。如果是不指定key的二维，就用$data[i][j]表示。
            $t_sdate = "";
            for ($j = 2; $j <= $highestRow; $j++) {

                $sdate = $objPHPExcel->getActiveSheet()->getCell("A" . $j)->getValue();  //付款日期
                $sdate_date = date("Y-m-01", strtotime($objPHPExcel->getActiveSheet()->getCell("A" . $j)->getValue()));  //付款日期

                $dname = $objPHPExcel->getActiveSheet()->getCell("B" . $j)->getValue(); //月租金

                $dname = str_replace('仲正堂', "", $dname);
                $dname = str_replace('路', "", $dname);
                $dname = str_replace('店', "", $dname);
                $department = DB::name("department")->where([['title', 'like', '%' . $dname . '%']])->find();

                if (empty($department)) {
                    continue;
                }

                $re = Db::name("store_achievement_r")->where([
                    'sdate' => $sdate, 'did' => $department['id']
                ])->delete();

                $level_4 = $objPHPExcel->getActiveSheet()->getCell("C" . $j)->getValue(); //等级A
                $level_3 = $objPHPExcel->getActiveSheet()->getCell("D" . $j)->getValue(); //等级B
                $level_2 = $objPHPExcel->getActiveSheet()->getCell("E" . $j)->getValue(); //等级C
                $level_1 = 0; //等级D

                $data[] = [
                    'sdate' => $sdate,
                    'sdate_date' => $sdate_date,
                    'did' => $department['id'],
                    'level_4' => !empty($level_4) ? $level_4 : 0,
                    'level_3' => !empty($level_3) ? $level_3 : 0,
                    'level_2' => !empty($level_2) ? $level_2 : 0,
                    'level_1' => !empty($level_1) ? $level_1 : 0
                ];
            }

            Db::name("store_achievement_r")->insertAll($data);

            return to_assign(0, "共{$highestRow}数据，成功导入了" . count($data) . '条数据');
        } catch (\think\exception\ValidateException $e) {
            return to_assign(1, $e->getMessage());
        }
    }


    public function importUserContractLink()
    {

        // 获取表单上传文件
        $file[] = request()->file('file');

        try {
            // 验证文件大小，名称等是否正确
            validate(['file' => 'filesize:51200|fileExt:xls,xlsx'])->check($file);
            // 日期前綴
            $dataPath = date('Ym');
            $md5 = $file[0]->hash('md5');
            $savename = \think\facade\Filesystem::disk('public')->putFile($dataPath, $file[0], function () use ($md5) {
                return $md5;
            });
            $fileExtendName = substr(strrchr($savename, '.'), 1);
            // 有Xls和Xlsx格式两种
            if ($fileExtendName == 'xlsx') {
                $objReader = IOFactory::createReader('Xlsx');
            } else {
                $objReader = IOFactory::createReader('Xls');
            }
            $objReader->setReadDataOnly(TRUE);
            $path = get_config('filesystem.disks.public.url');
            // 读取文件，tp6默认上传的文件，在runtime的相应目录下，可根据实际情况自己更改
            $objPHPExcel = $objReader->load('.' . $path . '/' . $savename);
            //$objPHPExcel = $objReader->load('./storage/202209/d11544d20b3ca1c1a5f8ce799c3b2433.xlsx');
            $sheet = $objPHPExcel->getSheet(0);   //excel中的第一张sheet
            $highestRow = $sheet->getHighestRow();       // 取得总行数
            $highestColumn = $sheet->getHighestColumn();   // 取得总列数
            Coordinate::columnIndexFromString($highestColumn);
            $lines = $highestRow - 1;
            if ($lines <= 0) {
                return to_assign(1, '数据不能为空');
                exit();
            }

            $data = array();

            //循环读取excel表格，整合成数组。如果是不指定key的二维，就用$data[i][j]表示。
            $t_sdate = "";
            for ($j = 2; $j <= $highestRow; $j++) {

                $sdate = $objPHPExcel->getActiveSheet()->getCell("A" . $j)->getValue();  //付款日期
                $sdate_date = date("Y-m-01", strtotime($objPHPExcel->getActiveSheet()->getCell("A" . $j)->getValue()));  //付款日期


                $dname = $objPHPExcel->getActiveSheet()->getCell("B" . $j)->getValue(); //月租金

                $dname = str_replace('仲正堂', "", $dname);
                $dname = str_replace('路', "", $dname);
                $dname = str_replace('店', "", $dname);
                $department = DB::name("department")->where([['title', 'like', '%' . $dname . '%']])->find();

                if (empty($department)) {
                    continue;
                }

                if ($t_sdate != $sdate) {
                    Db::name("store_achievement_r")->where([
                        'sdate' => $sdate, 'did' => $department['id']
                    ])->delete();
                    $t_sdate = $sdate;
                }

                $level_4 = $objPHPExcel->getActiveSheet()->getCell("C" . $j)->getValue(); //等级A
                $level_3 = $objPHPExcel->getActiveSheet()->getCell("D" . $j)->getValue(); //等级B
                $level_2 = $objPHPExcel->getActiveSheet()->getCell("E" . $j)->getValue(); //等级C
                $level_1 = 0; //等级D

                $data[] = [
                    'sdate' => $sdate,
                    'sdate_date' => $sdate_date,
                    'did' => $department['id'],
                    'level_4' => !empty($level_4) ? $level_4 : 0,
                    'level_3' => !empty($level_3) ? $level_3 : 0,
                    'level_2' => !empty($level_2) ? $level_2 : 0,
                    'level_1' => !empty($level_1) ? $level_1 : 0
                ];
            }

            Db::name("store_achievement_r")->insertAll($data);

            return to_assign(0, "共{$highestRow}数据，成功导入了" . count($data) . '条数据');
        } catch (\think\exception\ValidateException $e) {
            return to_assign(1, $e->getMessage());
        }
    }


    /***
     *导入 线下 培训后的成绩
     */

    public function importTrainExamsResult()
    {

        // 获取表单上传文件
        $file[] = request()->file('file');

        $param = get_params();


        try {
            // 验证文件大小，名称等是否正确
            validate(['file' => 'filesize:51200|fileExt:xls,xlsx'])->check($file);
            // 日期前綴
            $dataPath = date('Ym');
            $md5 = $file[0]->hash('md5');
            $savename = \think\facade\Filesystem::disk('public')->putFile($dataPath, $file[0], function () use ($md5) {
                return $md5;
            });
            $fileExtendName = substr(strrchr($savename, '.'), 1);
            // 有Xls和Xlsx格式两种
            if ($fileExtendName == 'xlsx') {
                $objReader = IOFactory::createReader('Xlsx');
            } else {
                $objReader = IOFactory::createReader('Xls');
            }
            $objReader->setReadDataOnly(TRUE);
            $path = get_config('filesystem.disks.public.url');
            // 读取文件，tp6默认上传的文件，在runtime的相应目录下，可根据实际情况自己更改
            $objPHPExcel = $objReader->load('.' . $path . '/' . $savename);
            //$objPHPExcel = $objReader->load('./storage/202209/d11544d20b3ca1c1a5f8ce799c3b2433.xlsx');
            $sheet = $objPHPExcel->getSheet(0);   //excel中的第一张sheet
            $highestRow = $sheet->getHighestRow();       // 取得总行数
            $highestColumn = $sheet->getHighestColumn();   // 取得总列数
            Coordinate::columnIndexFromString($highestColumn);
            $lines = $highestRow - 1;
            if ($lines <= 0) {
                return to_assign(1, '数据不能为空');
            }

            $title = [];
            for ($k = 'A'; $k <= $highestColumn; $k++) {
                $title[$k] = $objPHPExcel->getActiveSheet()->getCell($k . "1")->getValue();
            }
            var_dump($title);

            $ChangeDatas = [];
            $count = 0;
            for ($j = 2; $j <= $highestRow; $j++) {

                $aname = $objPHPExcel->getActiveSheet()->getCell("A$j")->getValue();
                $d_name = $objPHPExcel->getActiveSheet()->getCell("B$j")->getValue();

                $admin = DB::name("admin")->where([['name', 'like', '%' . $aname . '%']])->find();
                if (empty($admin) || empty($d_name)) continue;
                $dname = str_replace('路', "", $d_name);
                $dname = str_replace('店', "", $dname);
                $department = DB::name("department")->where([['title', 'like', '%' . $dname . '%']])->find();

                for ($k = 'A'; $k <= $highestColumn; $k++) {

                    $v = $objPHPExcel->getActiveSheet()->getCell("$k$j")->getValue();

                    $d = [
                        'trainId' => $param['id'],
                        'aid' => !empty($admin) ? $admin['id'] : 0,
                        'aname' => !empty($admin) ? $admin['name'] : $aname,
                        'create_time' => date("Y-m-d H:i:s"),
                        'did' => !empty($department) ? $department['id'] : 0,
                        'dname' => !empty($department) ? $department['title'] : $d_name,
                        'status' => 1,
                        'title' => $title[$k],
                        'content' => $v,
                        'type' => is_numeric($v) ? 'number' : 'text',
                        'in_col' => $j,
                    ];
                    $ChangeDatas[] = $d;
                }
                $count++;
            }

            Db::name("train_sign_result")->insertAll($ChangeDatas);

            return to_assign(0, "共{$highestRow}数据，成功导入了" . $count . '条数据');
        } catch (\think\exception\ValidateException $e) {
            return to_assign(1, $e->getMessage());
        }
    }

}


