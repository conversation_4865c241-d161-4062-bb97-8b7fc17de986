{extend name="../../base/view/common/base" /}
{block name="style"}
<style>
/* 移动端消息详情页面样式 */
body { 
    background-color: #f2f2f2; 
    font-size: 14px;
}

.mobile-message-container {
    padding: 10px;
}

.layui-layout-body {
    overflow-x: visible;
}

.mobile-message-header {
    background-color: #fff;
    border-radius: 8px;
    margin-bottom: 10px;
    padding: 15px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.mobile-message-title {
    font-size: 18px;
    font-weight: bold;
    color: #333;
    margin-bottom: 15px;
    line-height: 1.4;
    word-break: break-all;
}

.mobile-message-info {
    background-color: #fff;
    border-radius: 8px;
    margin-bottom: 10px;
    padding: 15px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.mobile-info-row {
    display: flex;
    margin-bottom: 10px;
    align-items: flex-start;
}

.mobile-info-row:last-child {
    margin-bottom: 0;
}

.mobile-info-label {
    color: #666;
    font-size: 13px;
    min-width: 70px;
    margin-right: 10px;
    flex-shrink: 0;
}

.mobile-info-value {
    color: #333;
    font-size: 14px;
    flex: 1;
    word-break: break-all;
}

.mobile-message-content {
    background-color: #fff;
    border-radius: 8px;
    margin-bottom: 10px;
    padding: 15px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.mobile-content-label {
    color: #666;
    font-size: 13px;
    margin-bottom: 10px;
}

.mobile-content-text {
    color: #333;
    font-size: 14px;
    line-height: 1.6;
    word-break: break-all;
}

.mobile-quoted-message {
    background-color: #f8f9fa;
    border-left: 3px solid #007bff;
    margin-top: 15px;
    padding: 10px;
    border-radius: 0 5px 5px 0;
}

.mobile-quoted-label {
    color: #666;
    font-size: 12px;
    margin-bottom: 8px;
}

.mobile-quoted-text {
    color: #666;
    font-size: 13px;
    line-height: 1.5;
}

.mobile-attachments {
    background-color: #fff;
    border-radius: 8px;
    margin-bottom: 10px;
    padding: 15px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.mobile-attachment-item {
    margin-bottom: 10px;
}

.mobile-attachment-item:last-child {
    margin-bottom: 0;
}

.mobile-read-receipt {
    background-color: #fff;
    border-radius: 8px;
    margin-bottom: 10px;
    padding: 15px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.mobile-reply-section {
    background-color: #fff;
    border-radius: 8px;
    padding: 15px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    text-align: center;
}

.mobile-reply-btn {
    background-color: #007bff;
    color: #fff;
    border: none;
    border-radius: 6px;
    padding: 12px 30px;
    font-size: 16px;
    text-decoration: none;
    display: inline-block;
    transition: background-color 0.3s;
}

.mobile-reply-btn:hover {
    background-color: #0056b3;
    color: #fff;
    text-decoration: none;
}

.mobile-type-badge {
    display: inline-block;
    background-color: #e9ecef;
    color: #495057;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 12px;
}

/* 文件卡片移动端优化 */
.mobile-file-card {
    width: 100% !important;
    margin-bottom: 8px !important;
}

/* 响应式调整 */
@media (max-width: 480px) {
    .mobile-message-container {
        padding: 8px;
    }
    
    .mobile-message-header,
    .mobile-message-info,
    .mobile-message-content,
    .mobile-attachments,
    .mobile-read-receipt,
    .mobile-reply-section {
        padding: 12px;
    }
    
    .mobile-message-title {
        font-size: 16px;
    }
    
    .mobile-info-label {
        min-width: 60px;
        font-size: 12px;
    }
    
    .mobile-info-value {
        font-size: 13px;
    }
}

/* 隐藏桌面端的表格样式 */
.layui-table {
    display: none !important;
}

.layui-form {
    padding: 0 !important;
}
</style>
{/block}

{block name="body"}
<div class="layui-layout-body">
    <div id="GouguApp">
        <div class="layui-layout gg-layout layout-menu-{$web.menu_mode|default='classical'}">
            <div class="uni-page" data-page="pages/message/read" style="padding-bottom: 50px">
                <!-- 移动端顶部导航 -->
                <div style="background-color: #fff; padding: 10px 15px; border-bottom: 1px solid #eee; position: sticky; top: 0; z-index: 100;">
                    <div style="display: flex; align-items: center;">
                        <a href="javascript:history.back()" style="color: #007bff; text-decoration: none; font-size: 16px; margin-right: 15px;">
                            ← 返回
                        </a>
                        <span style="font-size: 16px; font-weight: bold; color: #333;">消息详情</span>
                    </div>
                </div>

                <div class="mobile-message-container">
    <!-- 消息标题 -->
    <div class="mobile-message-header">
        <div class="mobile-message-title">{$detail.title}</div>
    </div>
    
    <!-- 消息基本信息 -->
    <div class="mobile-message-info">
        <div class="mobile-info-row">
            <div class="mobile-info-label">发送人</div>
            <div class="mobile-info-value">{$detail.person_name}</div>
        </div>
        <div class="mobile-info-row">
            <div class="mobile-info-label">接收类别</div>
            <div class="mobile-info-value">
                <span class="mobile-type-badge">
                    {eq name="$detail.type" value="0"}-{/eq}
                    {eq name="$detail.type" value="1"}同事{/eq}
                    {eq name="$detail.type" value="2"}部门{/eq}
                    {eq name="$detail.type" value="3"}岗位{/eq}
                    {eq name="$detail.type" value="4"}全部{/eq}
                </span>
            </div>
        </div>
        <div class="mobile-info-row">
            <div class="mobile-info-label">发送时间</div>
            <div class="mobile-info-value">{$detail.send_time}</div>
        </div>
        <div class="mobile-info-row">
            <div class="mobile-info-label">收件人</div>
            <div class="mobile-info-value">{$detail.users}</div>
        </div>
    </div>
    
    <!-- 消息内容 -->
    <div class="mobile-message-content">
        <div class="mobile-content-label">消息内容</div>
        <div class="mobile-content-text">
            {if condition="($detail.template == 0)"}
                {$detail.content}
                {if condition="($detail.fid > 0)"}
                    <div class="mobile-quoted-message">
                        <div class="mobile-quoted-label">引用消息内容</div>
                        <div class="mobile-quoted-text">{$detail.from_content}</div>
                        {notempty name="$detail.from_file_array"}
                        <div style="margin-top: 10px;">
                            <div class="mobile-quoted-label">引用消息附件</div>
                            {volist name="$detail.from_file_array" id="vo"}
                                <div class="mobile-file-card">{:file_card($vo,'view')}</div>
                            {/volist}
                        </div>
                        {/notempty}
                    </div>
                {/if}
            {else/}
                {$detail.content}{:getMessageLink($detail.template,$detail.action_id)}
            {/if}
        </div>
    </div>
    
    <!-- 相关附件 -->
    {notempty name="$detail.file_array"}
    <div class="mobile-attachments">
        <div class="mobile-content-label">相关附件</div>
        {volist name="$detail.file_array" id="vo"}
            <div class="mobile-file-card">{:file_card($vo,'view')}</div>
        {/volist}
    </div>
    {/notempty}
    
    <!-- 已读回执 -->
    {notempty name="$detail.read_users"}
    <div class="mobile-read-receipt">
        <div class="mobile-content-label">收件人已读回执</div>
        <div class="mobile-content-text">{$detail.read_users}</div>
    </div>
    {/notempty}
    
    <!-- 回复按钮 -->
    {if condition="($detail.template == 0) AND ($detail.pid != 0)"}
    <div class="mobile-reply-section">
        <a class="mobile-reply-btn" href="/message/index/reply?id={$detail.id}&type=1">回复消息</a>
    </div>
    {/if}
                </div>
            </div>

            {include file="../../mobile/view/index/tabbar" /}

        </div>
    </div>
</div>

<!-- 保留原始表格结构（隐藏），以防某些功能依赖 -->
<form class="layui-form p-4" style="display: none;">
    <h3 class="pb-3">消息详情</h3>
    <table class="layui-table">
        <tr>
            <td class="layui-td-gray">信息主题</td>
            <td colspan="5">{$detail.title}</td>
        </tr>
        <tr>
            <td class="layui-td-gray">发送人</td>
            <td>{$detail.person_name}</td>
            <td class="layui-td-gray-2">接收人类别</td>
            <td>
                {eq name="$detail.type" value="0"}-{/eq}
                {eq name="$detail.type" value="1"}同事{/eq}
                {eq name="$detail.type" value="2"}部门{/eq}
                {eq name="$detail.type" value="3"}岗位{/eq}
                {eq name="$detail.type" value="4"}全部{/eq}
            </td>
            <td class="layui-td-gray">发送时间</td>
            <td>{$detail.send_time}</td>
        </tr>
        <tr>
            <td class="layui-td-gray">收件人</td>
            <td colspan="5">{$detail.users}</td>
        </tr>
        <tr>
            <td class="layui-td-gray" style="vertical-align:top;">消息内容</td>
            <td colspan="5">
                {if condition="($detail.template == 0)"}
                    {$detail.content}
                    {if condition="($detail.fid > 0)"}		
                    <table class="layui-table" style="margin-top:10px;">
                    <tr>
                        <td class="layui-td-gray-2">引用消息内容</td>
                        <td style="color:#999">{$detail.from_content}</td>
                    </tr>
                    {notempty name="$detail.from_file_array"}
                    <tr>
                        <td class="layui-td-gray-2" style="vertical-align:top">引用消息附件</td>
                        <td style="line-height:inherit">						
                            {volist name="$detail.from_file_array" id="vo"}
                                <div class="layui-col-md4" id="uploadImg{$vo.id}">{:file_card($vo,'view')}</div>
                            {/volist}
                        </td>
                    </tr>
                    {/notempty}
                    </table>
                    {/if}
                {else/}
                    {$detail.content}{:getMessageLink($detail.template,$detail.action_id)}
                {/if}
            </td>
        </tr>
        {notempty name="$detail.file_array"}
        <tr>
            <td class="layui-td-gray" style="vertical-align:top">相关附件</td>
            <td colspan="5" style="line-height:inherit">
                {volist name="$detail.file_array" id="vo"}
                    <div class="layui-col-md4" id="uploadImg{$vo.id}">{:file_card($vo,'view')}</div>
                {/volist}
            </td>
        </tr>
        {/notempty}
        {notempty name="$detail.read_users"}
        <tr>
            <td class="layui-td-gray-2" style="vertical-align:top">收件人已读回执</td>
            <td colspan="5">{$detail.read_users}</td>
        </tr>
        {/notempty}
    </table>
    {if condition="($detail.template == 0) AND ($detail.pid != 0)"}
    <div class="py-3">
        <a class="layui-btn" href="/message/index/reply?id={$detail.id}&type=1">回复</a>
    </div>
    {/if}
</form>
{/block}

{block name="script"}
<script>
const moduleInit = ['tool','oaTool'];
function gouguInit() {
    // 移动端不需要刷新父页面表格
    // if (parent.layui.pageTable) {
    //     parent.layui.pageTable.reload();
    // }

    // 移动端优化：处理文件卡片
    $('.mobile-file-card .layui-col-md4').addClass('mobile-file-card');

    // 移动端优化：处理长文本
    $('.mobile-content-text').each(function() {
        var $this = $(this);
        var text = $this.html();
        // 处理长URL，添加换行
        text = text.replace(/(https?:\/\/[^\s]+)/g, '<span style="word-break: break-all;">$1</span>');
        $this.html(text);
    });

    // 移动端优化：处理链接点击
    $('.mobile-content-text a').on('click', function(e) {
        var href = $(this).attr('href');
        if (href && href.indexOf('data-href') === -1) {
            // 外部链接在新窗口打开
            if (href.indexOf('http') === 0) {
                window.open(href, '_blank');
                e.preventDefault();
            }
        }
    });

    // 移动端优化：返回按钮功能
    if (window.history.length > 1) {
        // 可以添加返回按钮的逻辑
    }
}
</script>
{/block}
