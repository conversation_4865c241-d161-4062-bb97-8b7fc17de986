<?php

namespace app\finance\service;

use think\facade\Db;

class ExpenseService
{

    public function getList($param, $uid)
    {
        $where = [];

        if (!empty($param['diff_time'])) {
            $diff_time = explode('~', $param['diff_time']);
            $where[] = ['e.pay_time', 'between', [$diff_time[0], $diff_time[1]]];
        }

        if (!empty($param['type'])) {
            $where[] = ['e.type', '=', $param['type']];
        }

        if (!empty($param['store'])) {
            $where[] = ['e.did|e.store', '=', $param['store']];
        }

        if (!empty($param['pay_type'])) {
            $where[] = ['e.pay_type', '=', $param['pay_type']];
        }

        if (!empty($param['is_fapiao'])) {
            $where[] = ['e.is_fapiao', '=', $param['is_fapiao']];
        }

        if (!empty($param['pay_amount'])) {
            $where[] = ['e.pay_amount', '=', $param['pay_amount']];
        }


        if (!empty($param['keywords'])) {
            $where[] = ['e.admin_name|e.payee_name|e.bank_number|e.bank_name|e.approve_id', 'like', '%' . $param['keywords'] . '%'];
        }

        if (isset($param['status'])) {
            if ($param['status'] == 1) {
                $where[] = ['approve.check_admin_ids', '=', $uid];
            }
            if ($param['status'] == 2) {
                $where[] = ['', 'exp', Db::raw("FIND_IN_SET('{$uid}',approve.flow_admin_ids)")];
            }
        }


        $rows = empty($param['limit']) ? get_config('app.page_size') : $param['limit'];
        $list = Db::name('expense')
            ->field("
                    e.*,
                    (e.pay_amount + IFNULL(e.o_amount,0)) as pay_amount,
                    IFNULL(dd.title,'总部') AS store_name,
                    FROM_UNIXTIME(e.create_time, '%Y-%m-%d %H:%i:%s') AS create_time_date,
                    d.title as dname,a.name as aname,t.name as pay_type_name,
                    approve.check_status,approve.finish_time,approve.jsoncode,approve.type as a_type,
                    ft.title as type_name
                ")
            ->alias('e')
            ->join('type_pay t', 'e.pay_type = t.id', "left")
            ->join('Admin a', 'e.admin_id = a.id')
            ->join('Department d', 'e.did = d.id')
            ->join('Department dd', 'e.store = dd.id', 'left')
            ->join('Approve approve', 'e.approve_id = approve.id', 'left')
            ->join('flow_type ft', 'ft.id = approve.type', 'left')
            ->where($where)
            ->order('e.id desc')
            ->paginate($rows, false, ['query' => $param])
            ->each(function ($item, $key) {
                $check_status = [
                    '0' => ['title' => '待审批' , 'color' => '#666666'],
                    '1' => ['title' => '审批中' , 'color' => '#4285f4'],
                    '2' => ['title' => '已通过' , 'color' => '#34a853'],
                    '3' => ['title' => '已拒绝' , 'color' => '#FF5722'],
                    '4' => ['title' => '已撤销' , 'color' => '#fbbc05']
                ];

//                $type_name = "";
//                switch ($item['type']){
//                    case 1: $type_name = '付款';break;
//                    case 2: $type_name = '报销';break;
//                    case 3: $type_name = '其他';break;
//                }
//                $item['type_name'] = $type_name;

                if ($item['a_type'] == 53){
                    $jsoncode = unserialize($item['jsoncode']);
                    $item['pay_type_name'] = $jsoncode['bx_type'];
                }

                if ($item['check_status'] !== null){
                    $item['check_status_name'] = $check_status[$item['check_status']]['title'];
                    $item['check_status_color'] = $check_status[$item['check_status']]['color'];
                    $item['finish_time_date'] = !empty($item['finish_time']) ? date("Y-m-d H:i:s", $item['finish_time']) : '';
                }

                $content = "";
                $flow_record = Db::name("flow_record")
                    ->field("fr.content , a.name")
                    ->alias('fr')
                    ->join('admin a', 'fr.check_user_id = a.id', "left")
                    ->where([
                        'fr.action_id' => $item['approve_id'],
                        ['fr.step_id', '<>', 0],
                        ['fr.content', '<>', '']
                    ])->select()->toArray();
                foreach ($flow_record as $k => $v){
                    $content .= "{$v['name']}：{$v['content']}；";
                }
                $item['content'] = $content;

                return $item;
            });

        return $list;
    }

    public function addAll($data)
    {

        $this->account(
            $data[0]['payee_name'],
            $data[0]['bank_number'],
            $data[0]['bank_name'],
        );

        $expense = Db::name("expense")->where(['approve_id' => $data[0]['approve_id']])->find();
        if (!empty($expense)) {
            Db::name("expense")->where(['approve_id' => $data[0]['approve_id']])->delete();
        }
        return Db::name("expense")->insertAll($data);

    }

    //保存 付款账户
    public function account($payee_name , $bank_number , $bank_name)
    {
        $account = Db::name("account")->where([
            'payee_name' => $payee_name,
            'bank_number' => $bank_number,
            'bank_name' => $bank_name
        ])->find();

        if (!empty($account)){
            Db::name("account")
                ->where(['id' => $account['id']])
                ->inc('times' , 1)
                ->update([
                    'update_time' => date("Y-m-d H:i:s")
                ]);
        }else{
            Db::name("account")->insertGetId([
                'create_time' =>  date("Y-m-d H:i:s"),
                'update_time' =>  date("Y-m-d H:i:s"),
                'payee_name' => $payee_name,
                'bank_number' => $bank_number,
                'bank_name' => $bank_name
            ]);
        }

    }


}