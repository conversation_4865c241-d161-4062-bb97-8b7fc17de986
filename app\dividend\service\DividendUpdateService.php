<?php
/**
 * @copyright Copyright (c) 2021 仲正堂工作室
 * @license https://opensource.org/licenses/GPL-3.0
 * @link https://www.gougucms.com
 */

namespace app\dividend\service;

use think\facade\Db;
use think\facade\Log;
use app\dividend\model\DividendStoreInfo;
use app\dividend\model\DividendStoreDetail;
use app\dividend\model\DividendStoreDetailPerson;
use app\dividend\model\DividendCompanyDetail;
use app\dividend\model\DividendCompanyDetailPerson;
use app\dividend\model\DividendPayment;

class DividendUpdateService
{
    /**
     * 根据门店账单数据更新分红相关数据
     * @param int $storeId 门店ID
     * @param string $period 周期（如：2024-05）
     * @param float $newIncome 新的收入金额
     * @param float $newExpense 新的支出金额
     * @return array
     */
    public function updateDividendDataByStoreBill($storeId, $period, $newIncome, $newExpense)
    {
        $result = [
            'success' => true,
            'message' => '',
            'data' => []
        ];

        try {
            // 开启事务
            Db::startTrans();

            // 1. 获取门店分红配置信息
            $storeInfo = DividendStoreInfo::where('store_id', $storeId)
                ->where('is_delete', 0)
                ->find();

            if (empty($storeInfo)) {
                throw new \Exception("门店ID {$storeId} 未配置分红信息");
            }

            // 2. 获取现有的门店分红明细数据
            $storeDetail = DividendStoreDetail::where('store_id', $storeId)
                ->where('period', $period)
                ->where('is_delete', 0)
                ->find();

            if (empty($storeDetail)) {
                throw new \Exception("门店ID {$storeId} 在周期 {$period} 无分红明细数据");
            }

            // 3. 检查收入和支出是否真的发生了变化
            $oldIncome = floatval($storeDetail['income']);
            $oldExpense = floatval($storeDetail['expense']);
            $newIncome = floatval($newIncome);
            $newExpense = floatval($newExpense);

            if ($oldIncome == $newIncome && $oldExpense == $newExpense) {
                $result['message'] = "门店收入和支出未发生变化，无需更新分红数据";
                Db::commit();
                return $result;
            }

            Log::info("门店分红数据更新开始 - 门店ID: {$storeId}, 周期: {$period}");
            Log::info("收入变化: {$oldIncome} -> {$newIncome}");
            Log::info("支出变化: {$oldExpense} -> {$newExpense}");

            // 4. 重新计算分红相关数据
            $updateResult = $this->recalculateDividendData($storeInfo, $storeDetail, $newIncome, $newExpense, $period);

            if (!$updateResult['success']) {
                throw new \Exception($updateResult['message']);
            }

            // 提交事务
            Db::commit();

            $result['data'] = $updateResult['data'];
            $result['message'] = "门店分红数据更新成功";

        } catch (\Exception $e) {
            Db::rollback();
            $result['success'] = false;
            $result['message'] = '更新失败：' . $e->getMessage();
            Log::error('门店分红数据更新失败: ' . $e->getMessage());
        }

        return $result;
    }

    /**
     * 重新计算分红数据
     * @param array $storeInfo 门店分红配置信息
     * @param array $storeDetail 门店分红明细数据
     * @param float $newIncome 新收入
     * @param float $newExpense 新支出
     * @param string $period 周期
     * @return array
     */
    private function recalculateDividendData($storeInfo, $storeDetail, $newIncome, $newExpense, $period)
    {
        $result = [
            'success' => true,
            'message' => '',
            'data' => []
        ];

        try {
            $storeId = $storeInfo['store_id'];
            $storeDetailId = $storeDetail['id'];

            // 1. 计算新的净利润
            $oldNetProfit = floatval($storeDetail['income']) - floatval($storeDetail['expense']);
            $newNetProfit = $newIncome - $newExpense;

            // 2. 重新计算风险金计提
            $oldRiskReserveCurrent = floatval($storeDetail['risk_reserve_current']);
            $currentTotalRiskReserve = floatval($storeInfo['risk_reserve']);

            // 从风险金日志表查询当前周期的计提记录
            $currentPeriodLog = $this->getCurrentPeriodRiskReserveLog($storeId, $storeInfo['id'], $period);

            // 计算除当前周期外的历史累计风险金
            $historicalRiskReserve = $currentTotalRiskReserve - $oldRiskReserveCurrent;

            // 基于新净利润重新计算当前周期应该计提的风险金
            $newRiskReserveCurrent = $this->calculateRiskReserveForPeriod(
                $newNetProfit,
                $historicalRiskReserve,
                $storeId,
                $period
            );

            // 3. 重新计算分红利润
            $dividendProfit = $newIncome - $newExpense - $newRiskReserveCurrent;

            // 4. 计算风险金变化
            $riskReserveDiff = $newRiskReserveCurrent - $oldRiskReserveCurrent;
            $newRiskReserveTotal = $historicalRiskReserve + $newRiskReserveCurrent;

            // 5. 更新门店分红明细表
            $updateData = [
                'income' => $newIncome,
                'expense' => $newExpense,
                'risk_reserve_current' => $newRiskReserveCurrent,
                'dividend_profit' => $dividendProfit,
                'risk_reserve_total' => $newRiskReserveTotal,
                'update_time' => time()
            ];

            DividendStoreDetail::where('id', $storeDetailId)->update($updateData);

            // 6. 更新门店分红信息表的风险金总额
            DividendStoreInfo::where('id', $storeInfo['id'])->update([
                'risk_reserve' => $newRiskReserveTotal,
                'update_time' => time()
            ]);

            // 7. 记录风险金变化日志（如果风险金发生了变化）
            if ($riskReserveDiff != 0) {
                $this->addRiskReserveChangeLog(
                    $storeId,
                    $storeInfo['id'],
                    $currentTotalRiskReserve,
                    $riskReserveDiff,
                    $newRiskReserveTotal,
                    $storeDetailId,
                    $period,
                    $oldNetProfit,
                    $newNetProfit,
                    $currentPeriodLog
                );
            }

            // 6. 重新计算门店分红人数据
            $this->recalculateStorePersonDividend($storeInfo['id'], $dividendProfit, $storeDetailId);

            // 7. 重新计算公司分红数据
            $this->recalculateCompanyDividend($storeId, $period, $dividendProfit, $storeDetail['company_shareholding_ratio']);

            // 8. 重新计算分红支付数据
            $this->recalculatePaymentData($period);

            $result['data'] = [
                'store_id' => $storeId,
                'period' => $period,
                'old_income' => floatval($storeDetail['income']),
                'new_income' => $newIncome,
                'old_expense' => floatval($storeDetail['expense']),
                'new_expense' => $newExpense,
                'old_dividend_profit' => floatval($storeDetail['dividend_profit']),
                'new_dividend_profit' => $dividendProfit,
                'old_risk_reserve_current' => $oldRiskReserveCurrent,
                'new_risk_reserve_current' => $newRiskReserveCurrent,
                'old_risk_reserve_total' => floatval($storeDetail['risk_reserve_total']),
                'new_risk_reserve_total' => $newRiskReserveTotal
            ];

        } catch (\Exception $e) {
            $result['success'] = false;
            $result['message'] = $e->getMessage();
        }

        return $result;
    }

    /**
     * 计算风险金计提（原有方法，用于初始计算）
     * @param float $netProfit 净利润
     * @param float $currentRiskReserve 当前已计提风险金
     * @param int $storeId 门店ID
     * @param string $period 统计周期
     * @return float
     */
    private function calculateRiskReserve($netProfit, $currentRiskReserve, $storeId, $period)
    {
        // 1. 检查是否为新开门店前3个月
        if ($this->isNewStoreWithinThreeMonths($storeId, $period)) {
            return 0;
        }

        // 2. 净利润小于1万，不计提
        if ($netProfit < 10000) {
            return 0;
        }

        // 3. 风险金累计金额已达到10万，不再计提
        if ($currentRiskReserve >= 100000) {
            return 0;
        }

        // 4. 净利润≥1万，计提1万元，但不能超过总额10万
        $riskReserveAmount = 10000;

        // 确保计提后不超过10万总额
        if ($currentRiskReserve + $riskReserveAmount > 100000) {
            $riskReserveAmount = 100000 - $currentRiskReserve;
        }

        return $riskReserveAmount;
    }

    /**
     * 重新计算指定周期的风险金计提（用于数据变化后的重新计算）
     * @param float $netProfit 新的净利润
     * @param float $historicalRiskReserve 历史累计风险金（不包含当前周期）
     * @param int $storeId 门店ID
     * @param string $period 统计周期
     * @return float
     */
    private function calculateRiskReserveForPeriod($netProfit, $historicalRiskReserve, $storeId, $period)
    {
        // 1. 检查是否为新开门店前3个月
        if ($this->isNewStoreWithinThreeMonths($storeId, $period)) {
            return 0;
        }

        // 2. 净利润小于1万，当前周期不计提风险金
        if ($netProfit < 10000) {
            return 0;
        }

        // 3. 历史累计风险金已达到10万，不再计提
        if ($historicalRiskReserve >= 100000) {
            return 0;
        }

        // 4. 净利润≥1万，当前周期计提1万元，但总额不能超过10万
        $riskReserveAmount = 10000;

        // 确保历史累计+当前计提不超过10万总额
        if ($historicalRiskReserve + $riskReserveAmount > 100000) {
            $riskReserveAmount = 100000 - $historicalRiskReserve;
        }

        return $riskReserveAmount;
    }

    /**
     * 检查是否为新开门店前3个月
     * @param int $storeId 门店ID
     * @param string $period 统计周期
     * @return bool
     */
    private function isNewStoreWithinThreeMonths($storeId, $period)
    {
        // 这里需要根据实际业务逻辑来判断
        // 暂时返回false，表示不是新开门店
        return false;
    }

    /**
     * 查询当前周期的风险金计提日志
     * @param int $storeId 门店ID
     * @param int $dividendStoreInfoId 门店分红信息ID
     * @param string $period 周期
     * @return array|null
     */
    private function getCurrentPeriodRiskReserveLog($storeId, $dividendStoreInfoId, $period)
    {
        try {
            // 优先使用period字段查询，如果没有则使用备注查询（兼容旧数据）
            $log = Db::name('dividend_risk_reserve_log')
                ->where('store_id', $storeId)
                ->where('dividend_store_info_id', $dividendStoreInfoId)
                ->where('change_type', 1) // 1=计提
                ->where(function($query) use ($period) {
                    $query->where('period', $period)
                          ->whereOr('remark', 'like', "%计算周期：{$period}%");
                })
                ->order('create_time desc')
                ->find();

            return $log;
        } catch (\Exception $e) {
            Log::error("查询风险金计提日志失败: " . $e->getMessage());
            return null;
        }
    }

    /**
     * 重新计算门店分红人数据
     * @param int $storeInfoId 门店分红信息ID
     * @param float $dividendProfit 分红利润
     * @param int $storeDetailId 门店分红明细ID
     */
    private function recalculateStorePersonDividend($storeInfoId, $dividendProfit, $storeDetailId)
    {
        // 获取门店分红明细信息
        $storeDetail = DividendStoreDetail::where('id', $storeDetailId)->find();
        if (empty($storeDetail)) {
            throw new \Exception("门店分红明细数据不存在");
        }

        // 从门店分红配置信息获取公司持股比例
        $storeInfo = DividendStoreInfo::where('id', $storeInfoId)->find();
        if (empty($storeInfo)) {
            throw new \Exception("门店分红配置信息不存在");
        }

        $companyShareholdingRatio = floatval($storeInfo['company_shareholding_ratio']);

        // 软删除原有的分红人记录
        DividendStoreDetailPerson::where('dividend_store_detail_id', $storeDetailId)
            ->where('is_delete', 0)
            ->update(['is_delete' => 1, 'delete_time' => time()]);

        // 重新生成分红人数据
        $personData = DividendStoreDetailPerson::generatePersonsFromShareholders(
            $storeDetail['store_id'],
            $dividendProfit,
            $companyShareholdingRatio
        );

        // 批量插入新的分红人数据
        if (!empty($personData)) {
            foreach ($personData as &$person) {
                $person['dividend_store_detail_id'] = $storeDetailId;
                $person['create_time'] = time();
                $person['update_time'] = time();
            }
            DividendStoreDetailPerson::insertAll($personData);
        }
    }

    /**
     * 重新计算公司分红数据
     * @param int $storeId 门店ID
     * @param string $period 周期
     * @param float $dividendProfit 分红利润
     * @param float $companyShareholdingRatio 公司持股比例
     */
    private function recalculateCompanyDividend($storeId, $period, $dividendProfit, $companyShareholdingRatio)
    {
        try {
            // 1. 获取门店分红配置信息
            $storeInfo = DividendStoreInfo::where('store_id', $storeId)
                ->where('is_delete', 0)
                ->find();

            if (empty($storeInfo)) {
                Log::warning("门店ID {$storeId} 未配置分红信息，跳过公司分红计算");
                return;
            }

            // 2. 从门店分红人子表获取公司股东的分红数据
            $companyPersonData = Db::name('dividend_store_detail_person')
                ->alias('dsdp')
                ->join('oa_dividend_store_detail dsd', 'dsd.id = dsdp.dividend_store_detail_id', 'INNER')
                ->where([
                    'dsd.store_id' => $storeId,
                    'dsd.period' => $period,
                    'dsdp.shareholder_name' => '公司',
                    'dsd.is_delete' => 0,
                    'dsdp.is_delete' => 0
                ])
                ->field('dsdp.actual_payable_amount as dividend_profit, dsdp.store_shareholding_ratio as company_shareholding_ratio')
                ->find();

            if (empty($companyPersonData)) {
                Log::info("门店ID {$storeId} 在周期 {$period} 无公司股东分红数据，跳过公司分红计算");
                return;
            }

            // 3. 检查是否已存在公司分红明细数据
            $existingCompanyDetail = DividendCompanyDetail::where([
                'store_id' => $storeId,
                'period' => $period,
                'is_delete' => 0
            ])->find();

            $companyDetailData = [
                'store_id' => $storeId,
                'period' => $period,
                'dividend_profit' => floatval($companyPersonData['dividend_profit']),
                'company_shareholding_ratio' => floatval($companyPersonData['company_shareholding_ratio']),
                'remark' => ''
            ];

            if ($existingCompanyDetail) {
                // 更新现有记录
                $companyDetailData['update_time'] = time();
                DividendCompanyDetail::where('id', $existingCompanyDetail['id'])->update($companyDetailData);
                $companyDetailId = $existingCompanyDetail['id'];

                // 软删除原有的分红人记录
                DividendCompanyDetailPerson::softDeleteByCompanyDetailId($companyDetailId);
            } else {
                // 创建新记录
                $companyDetailData['create_time'] = time();
                $companyDetailData['update_time'] = time();
                $companyDetailId = DividendCompanyDetail::insertGetId($companyDetailData);
            }

            // 4. 重新计算公司股东分红人数据
            $this->recalculateCompanyPersonDividend(
                $storeInfo['id'],
                floatval($companyPersonData['dividend_profit']),
                $companyDetailId
            );

        } catch (\Exception $e) {
            Log::error("重新计算公司分红数据失败: " . $e->getMessage());
        }
    }

    /**
     * 重新计算公司股东分红人数据
     * @param int $storeInfoId 门店分红信息ID
     * @param float $dividendProfit 分红利润
     * @param int $companyDetailId 公司分红明细表ID
     */
    private function recalculateCompanyPersonDividend($storeInfoId, $dividendProfit, $companyDetailId)
    {
        // 获取公司股东信息（股东类型为1的股东）
        $companyShareholders = Db::name('dividend_shareholder')
            ->where([
                'dividend_store_info_id' => $storeInfoId,
                'shareholder_type' => 1, // 公司股东
                'is_delete' => 0
            ])
            ->order('sort_order asc, id asc')
            ->select()
            ->toArray();

        if (empty($companyShareholders)) {
            return;
        }

        foreach ($companyShareholders as $shareholder) {
            // 计算金额：分红利润 * 持股比例
            $amount = $dividendProfit * ($shareholder['company_shareholding_ratio'] / 100);

            $personRecord = [
                'company_detail_id' => $companyDetailId,
                'shareholder_id' => $shareholder['id'],
                'shareholder_name' => $shareholder['shareholder_name'],
                'shareholding_ratio' => $shareholder['company_shareholding_ratio'],
                'actual_shareholding' => $shareholder['store_shareholding_ratio'],
                'amount' => $amount,
                'adjustment_amount' => 0, // 默认为0
                'payable_amount' => $amount, // 默认等于金额
                'remark' => '',
                'create_time' => time(),
                'update_time' => time()
            ];

            DividendCompanyDetailPerson::insert($personRecord);
        }
    }

    /**
     * 重新计算分红支付数据
     * @param string $period 周期
     */
    private function recalculatePaymentData($period)
    {
        // 调用分红支付计算服务重新计算该周期的支付数据
        $paymentCalculateService = new DividendPaymentCalculateService();
        $paymentCalculateService->calculatePaymentData($period);
    }

    /**
     * 添加风险金变化日志
     * @param int $storeId 门店ID
     * @param int $dividendStoreInfoId 门店分红信息ID
     * @param float $beforeAmount 变化前金额
     * @param float $changeAmount 变化金额（正数为增加，负数为减少）
     * @param float $afterAmount 变化后金额
     * @param int $dividendDetailId 分红明细ID
     * @param string $period 周期
     * @param float $oldNetProfit 原净利润
     * @param float $newNetProfit 新净利润
     * @param array|null $currentPeriodLog 当前周期的计提日志
     */
    private function addRiskReserveChangeLog($storeId, $dividendStoreInfoId, $beforeAmount, $changeAmount, $afterAmount, $dividendDetailId, $period, $oldNetProfit, $newNetProfit, $currentPeriodLog = null)
    {
        try {
            // 构建详细的备注信息
            $remark = "门店账单数据变化导致风险金调整 - 周期: {$period}";
            $remark .= ", 净利润变化: " . number_format($oldNetProfit, 2) . " -> " . number_format($newNetProfit, 2);

            // 根据变化类型添加具体说明
            if ($changeAmount > 0) {
                $remark .= ", 新增计提: " . number_format($changeAmount, 2) . "元";
                if ($oldNetProfit < 10000 && $newNetProfit >= 10000) {
                    $remark .= " (净利润达到计提条件)";
                }
            } elseif ($changeAmount < 0) {
                $remark .= ", 撤回计提: " . number_format(abs($changeAmount), 2) . "元";
                if ($oldNetProfit >= 10000 && $newNetProfit < 10000) {
                    $remark .= " (净利润不再符合计提条件)";
                }
                // 如果有原计提日志，添加引用信息
                if ($currentPeriodLog) {
                    $remark .= ", 撤回原计提记录ID: " . $currentPeriodLog['id'];
                }
            }

            $logData = [
                'store_id' => $storeId,
                'dividend_store_info_id' => $dividendStoreInfoId,
                'change_type' => 2, // 2=调整
                'before_amount' => $beforeAmount,
                'change_amount' => $changeAmount,
                'after_amount' => $afterAmount,
                'dividend_detail_id' => $dividendDetailId,
                'remark' => $remark,
                'admin_id' => 0, // 系统自动调整
                'create_time' => time(),
                'update_time' => time()
            ];

            Db::name('dividend_risk_reserve_log')->insert($logData);

            Log::info("风险金变化日志记录成功 - 门店ID: {$storeId}, 变化金额: {$changeAmount}");

        } catch (\Exception $e) {
            Log::error("记录风险金变化日志失败: " . $e->getMessage());
        }
    }
}
