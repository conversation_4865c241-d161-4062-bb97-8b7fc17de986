<?php
/**
 * @copyright Copyright (c) 2021 仲正堂工作室
 * @license https://opensource.org/licenses/GPL-3.0
 * @link https://www.gougucms.com
 */

namespace app\dividend\service;

use think\facade\Db;
use app\dividend\model\DividendStoreInfo;
use app\dividend\model\DividendShareholder;
use app\dividend\model\DividendStoreDetailPerson;
use app\dividend\model\DividendCompanyDetail;
use app\dividend\model\DividendCompanyDetailPerson;

class DividendCompanyCalculateService
{
    /**
     * 计算指定周期的公司股东分红表
     * @param string $period 统计周期（如：2025-04）
     * @param int $adminId 操作人ID（默认为0表示系统自动）
     * @return array
     */
    public static function calculateCompanyDividend($period, $adminId = 0)
    {
        $result = [
            'success' => true,
            'message' => '',
            'data' => []
        ];

        try {
            // 开启事务
            Db::startTrans();

            $calculatedData = [];

            // 1. 获取所有门店分红配置表信息
            $storeInfoList = DividendStoreInfo::where('is_delete', 0)->select()->toArray();

            if (empty($storeInfoList)) {
                $result['message'] = '没有找到门店分红配置表数据';
                Db::commit();
                return $result;
            }

            foreach ($storeInfoList as $storeInfo) {
                $storeId = $storeInfo['store_id'];

                // 2. 从门店分红明细表获取公司分红数据
                $companyDividendData = self::getCompanyDividendFromStoreDetail($storeId, $period);

                if (empty($companyDividendData)) {
                    // 如果没有公司分红数据，跳过该门店
                    continue;
                }

                // 3. 检查是否已存在该周期的公司分红数据
                $existingCompanyDetail = DividendCompanyDetail::where([
                    'store_id' => $storeId,
                    'period' => $period,
                    'is_delete' => 0
                ])->find();

                // 4. 准备公司分红明细数据
                $companyDetailData = [
                    'store_id' => $storeId,
                    'period' => $period,
                    'dividend_profit' => $companyDividendData['dividend_profit'],
                    'company_shareholding_ratio' => $companyDividendData['company_shareholding_ratio'],
                    'remark' => ''
                ];

                if ($existingCompanyDetail) {
                    // 更新现有记录
                    $companyDetailData['update_time'] = time();
                    DividendCompanyDetail::where('id', $existingCompanyDetail['id'])->update($companyDetailData);
                    $companyDetailId = $existingCompanyDetail['id'];

                    // 软删除原有的分红人记录
                    DividendCompanyDetailPerson::softDeleteByCompanyDetailId($companyDetailId);
                } else {
                    // 创建新记录
                    $companyDetailData['create_time'] = time();
                    $companyDetailData['update_time'] = time();
                    $companyDetailId = DividendCompanyDetail::insertGetId($companyDetailData);
                }

                // 5. 计算公司股东分红人数据
                $personData = self::calculateCompanyPersonDividend(
                    $storeInfo['id'], 
                    $companyDividendData['dividend_profit'], 
                    $companyDetailId
                );

                $calculatedData[] = [
                    'store_id' => $storeId,
                    'store_name' => self::getStoreName($storeId),
                    'period' => $period,
                    'dividend_profit' => $companyDividendData['dividend_profit'],
                    'company_shareholding_ratio' => $companyDividendData['company_shareholding_ratio'],
                    'person_count' => count($personData)
                ];
            }

            // 提交事务
            Db::commit();

            $result['data'] = $calculatedData;
            $result['message'] = '公司股东分红表计算完成，共处理 ' . count($calculatedData) . ' 个门店';

        } catch (\Exception $e) {
            Db::rollback();
            $result['success'] = false;
            $result['message'] = '计算失败：' . $e->getMessage();
        }

        return $result;
    }

    /**
     * 从门店分红明细表获取公司分红数据
     * @param int $storeId 门店ID
     * @param string $period 统计周期
     * @return array|null
     */
    private static function getCompanyDividendFromStoreDetail($storeId, $period)
    {
        // 从门店分红人子表中获取公司分红数据（shareholder_name为"公司"的数据）
        $companyPersonData = DividendStoreDetailPerson::alias('dsdp')
            ->join('oa_dividend_store_detail dsd', 'dsd.id = dsdp.dividend_store_detail_id', 'INNER')
            ->where([
                'dsd.store_id' => $storeId,
                'dsd.period' => $period,
                'dsdp.shareholder_name' => '公司',
                'dsd.is_delete' => 0,
                'dsdp.is_delete' => 0
            ])
            ->field('dsdp.actual_payable_amount as dividend_profit, dsdp.store_shareholding_ratio as company_shareholding_ratio')
            ->find();

        if (!$companyPersonData) {
            return null;
        }

        return [
            'dividend_profit' => floatval($companyPersonData['dividend_profit']),
            'company_shareholding_ratio' => floatval($companyPersonData['company_shareholding_ratio'])
        ];
    }

    /**
     * 计算公司股东分红人数据
     * @param int $storeInfoId 门店分红信息ID
     * @param float $dividendProfit 分红利润
     * @param int $companyDetailId 公司分红明细表ID
     * @return array
     */
    private static function calculateCompanyPersonDividend($storeInfoId, $dividendProfit, $companyDetailId)
    {
        $personData = [];

        // 获取公司股东信息（股东类型为1的股东）
        $companyShareholders = DividendShareholder::where([
            'dividend_store_info_id' => $storeInfoId,
            'shareholder_type' => DividendShareholder::TYPE_COMPANY,
            'is_delete' => 0
        ])->order('sort_order asc, id asc')->select()->toArray();

        if (empty($companyShareholders)) {
            return $personData;
        }

        foreach ($companyShareholders as $shareholder) {
            // 计算金额：分红利润 * 持股比例
            $amount = $dividendProfit * ($shareholder['company_shareholding_ratio'] / 100);

            $personRecord = [
                'company_detail_id' => $companyDetailId,
                'shareholder_id' => $shareholder['id'],
                'shareholder_name' => $shareholder['shareholder_name'],
                'shareholding_ratio' => $shareholder['company_shareholding_ratio'],
                'actual_shareholding' => $shareholder['store_shareholding_ratio'],
                'amount' => $amount,
                'adjustment_amount' => 0, // 默认为0
                'payable_amount' => $amount, // 默认等于金额
                'remark' => '',
                'create_time' => time(),
                'update_time' => time()
            ];

            DividendCompanyDetailPerson::insert($personRecord);
            $personData[] = $personRecord;
        }

        return $personData;
    }

    /**
     * 获取门店名称
     * @param int $storeId 门店ID
     * @return string
     */
    private static function getStoreName($storeId)
    {
        $store = Db::name('department')->where('id', $storeId)->find();
        return $store['title'] ?? '未知门店';
    }

    /**
     * 获取计算统计信息
     * @param string $period 统计周期
     * @return array
     */
    public static function getCalculateStatistics($period)
    {
        $statistics = DividendCompanyDetail::where([
            'period' => $period,
            'is_delete' => 0
        ])->field([
            'COUNT(*) as store_count',
            'SUM(dividend_profit) as total_dividend_profit'
        ])->find();

        return [
            'store_count' => intval($statistics['store_count'] ?? 0),
            'total_dividend_profit' => floatval($statistics['total_dividend_profit'] ?? 0)
        ];
    }
}
