<?php
namespace app\dividend\controller;

use app\base\BaseController;
use app\dividend\model\DividendPayment;
use app\dividend\service\DividendPaymentCalculateService;
use app\dividend\service\DividendCalculateService;
use app\dividend\service\DividendCompanyCalculateService;
use think\facade\View;
use think\facade\Filesystem;
use PhpOffice\PhpSpreadsheet\IOFactory;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use think\facade\Log;
use systematic\ConfigManager;

class Payment extends BaseController
{
    /**
     * 检查当前用户是否有全部数据访问权限
     * @return bool
     */
    private function hasFullDataAccess()
    {
        try {
            // 获取当前用户信息
            $currentUser = get_admin($this->uid);
            if (!$currentUser) {
                return false;
            }

            // 从ConfigManager获取分红系统数据访问权限配置
            $permissions = ConfigManager::get('dividend_data_access_permissions', [
                'positions' => [],
                'users' => []
            ]);

            $allowedPositions = $permissions['positions'] ?? [];
            $allowedUsers = $permissions['users'] ?? [];

            // 检查用户ID是否在允许列表中
            if (in_array($this->uid, $allowedUsers)) {
                return true;
            }

            // 检查职位ID是否在允许列表中
            if (!empty($currentUser['position_id']) && in_array($currentUser['position_id'], $allowedPositions)) {
                return true;
            }

            return false;
        } catch (\Exception $e) {
            Log::error('检查分红数据访问权限失败: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * 检查当前用户是否有按钮操作权限
     * @return bool
     */
    private function hasButtonAccess()
    {
        try {
            // 从ConfigManager获取分红系统数据访问权限配置
            $permissions = ConfigManager::get('dividend_data_access_permissions', [
                'positions' => [],
                'users' => [],
                'button_access_users' => []
            ]);

            $buttonAccessUsers = $permissions['button_access_users'] ?? [];

            // 检查用户ID是否在按钮访问允许列表中
            return in_array($this->uid, $buttonAccessUsers);
        } catch (\Exception $e) {
            Log::error('检查分红按钮访问权限失败: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * 股东分红清单列表页面
     */
    public function index()
    {
        if (request()->isAjax()) {
            return $this->getPaymentList();
        }
        // 记录查看股东分红清单列表页面日志
        add_log('view', 0, [], '[门店分红]-[股东分红清单表]：查看股东分红清单列表页面');
        Log::info('[门店分红]-[股东分红清单表]：用户查看股东分红清单列表页面，用户ID：' . $this->uid);
        return View::fetch();
    }

    /**
     * 获取股东分红清单表
     */
    private function getPaymentList()
    {
        // 兼容GET和POST请求
        $param = request()->param();
        $year = $param['year'] ?? date('Y');
        $shareholderName = $param['shareholder_name'] ?? '';

        $model = new DividendPayment();
        $where = [
            'year' => $year,
            'shareholder_name' => $shareholderName
        ];

        // 数据权限检查：如果用户没有全部数据访问权限，则只能查看自己姓名匹配的数据
        if (!$this->hasFullDataAccess()) {
            $currentUser = get_admin($this->uid);
            if ($currentUser && !empty($currentUser['name'])) {
                $where['shareholder_name'] = $currentUser['name'];
                Log::info('[门店分红]-[股东分红清单表]：应用数据权限限制，用户只能查看自己的分红数据，用户：' . $currentUser['name'] . '，用户ID：' . $this->uid);
            } else {
                // 如果无法获取用户姓名，则不返回任何数据
                $where['shareholder_name'] = '__NO_DATA__';
                Log::info('[门店分红]-[股东分红清单表]：无法获取当前用户姓名，限制查看所有分红数据，用户ID：' . $this->uid);
            }
        }

        $list = $model->getPaymentList($where);

        // 按分红人分组，每个分红人显示12个月的数据
        $groupedData = [];
        $months = [];
        for ($i = 1; $i <= 12; $i++) {
            $months[] = sprintf('%s-%02d', $year, $i);
        }

        // 先获取所有分红人
        $shareholders = [];
        foreach ($list as $item) {
            if (!in_array($item['shareholder_name'], $shareholders)) {
                $shareholders[] = $item['shareholder_name'];
            }
        }

        // 为每个分红人构建12个月的数据
        foreach ($shareholders as $shareholderName) {
            $shareholderData = [
                'shareholder_name' => $shareholderName,
                'shareholder_ids' => '',
            ];

            // 初始化12个月的数据
            foreach ($months as $month) {
                $shareholderData[$month] = [
                    'payable_amount' => '0.00',
                    'adjustment_amount' => '0.00',
                    'actual_payable_amount' => '0.00',
                    'paid_amount' => '0.00',
                    'unpaid_amount' => '0.00',
                    'remark' => ''
                ];
            }

            // 填充实际数据
            foreach ($list as $item) {
                if ($item['shareholder_name'] == $shareholderName) {
                    $shareholderData['shareholder_ids'] = $item['shareholder_ids'];
                    $shareholderData[$item['period']] = [
                        'payable_amount' => $item['payable_amount'],
                        'adjustment_amount' => $item['adjustment_amount'],
                        'actual_payable_amount' => $item['actual_payable_amount'],
                        'paid_amount' => $item['paid_amount'],
                        'unpaid_amount' => $item['unpaid_amount'],
                        'remark' => $item['remark']
                    ];
                }
            }

            $groupedData[] = $shareholderData;
        }

        // 计算合计行数据
        $totalRow = $this->calculateTotalRow($groupedData, $year);

        // 记录查询股东分红清单日志
        Log::info('[门店分红]-[股东分红清单表]：查询股东分红清单，结果数量：' . count($groupedData) . '，年份：' . $year . '，用户ID：' . $this->uid);

        // 返回响应数据
        $response = [
            'code' => 0,
            'msg' => '获取成功',
            'count' => count($groupedData),
            'data' => $groupedData,
            'totalRow' => $totalRow,
            'hasButtonAccess' => $this->hasButtonAccess() // 添加按钮权限信息
        ];

        return json($response);
    }

    /**
     * 计算合计行数据
     * @param array $data 表格数据
     * @param string $year 年份
     * @return array
     */
    private function calculateTotalRow($data, $year)
    {
        $totalRow = [
            'shareholder_name' => '合计'
        ];

        // 初始化合计字段
        $totalFields = [
            'total_payable' => 0,
            'total_adjustment' => 0,
            'total_actual_payable' => 0,
            'total_paid' => 0,
            'total_unpaid' => 0
        ];

        // 初始化12个月的合计字段
        for ($i = 1; $i <= 12; $i++) {
            $month = sprintf('%02d', $i);
            $period = $year . '-' . $month;
            $totalFields[$period . '_payable'] = 0;
            $totalFields[$period . '_adjustment'] = 0;
            $totalFields[$period . '_actual_payable'] = 0;
            $totalFields[$period . '_paid'] = 0;
            $totalFields[$period . '_unpaid'] = 0;
        }

        // 累加所有分红人的数据
        foreach ($data as $row) {
            for ($i = 1; $i <= 12; $i++) {
                $month = sprintf('%02d', $i);
                $period = $year . '-' . $month;

                if (isset($row[$period])) {
                    $monthData = $row[$period];

                    // 累加年度合计
                    $totalFields['total_payable'] += floatval($monthData['payable_amount'] ?? 0);
                    $totalFields['total_adjustment'] += floatval($monthData['adjustment_amount'] ?? 0);
                    $totalFields['total_actual_payable'] += floatval($monthData['actual_payable_amount'] ?? 0);
                    $totalFields['total_paid'] += floatval($monthData['paid_amount'] ?? 0);
                    $totalFields['total_unpaid'] += floatval($monthData['unpaid_amount'] ?? 0);

                    // 累加月度合计
                    $totalFields[$period . '_payable'] += floatval($monthData['payable_amount'] ?? 0);
                    $totalFields[$period . '_adjustment'] += floatval($monthData['adjustment_amount'] ?? 0);
                    $totalFields[$period . '_actual_payable'] += floatval($monthData['actual_payable_amount'] ?? 0);
                    $totalFields[$period . '_paid'] += floatval($monthData['paid_amount'] ?? 0);
                    $totalFields[$period . '_unpaid'] += floatval($monthData['unpaid_amount'] ?? 0);
                }
            }
        }

        // 格式化合计数据（保留2位小数）
        foreach ($totalFields as $key => $value) {
            $totalRow[$key] = number_format($value, 2);
        }

        return $totalRow;
    }

    /**
     * 编辑股东分红清单页面
     */
    public function edit()
    {
        if (request()->isAjax()) {
            return $this->updatePaymentData();
        }
        // 记录查看股东分红清单编辑页面日志
        add_log('view', 0, [], '[门店分红]-[股东分红清单表]：查看股东分红清单编辑页面');
        Log::info('[门店分红]-[股东分红清单表]：用户查看股东分红清单编辑页面，用户ID：' . $this->uid);
        return View::fetch();
    }

    /**
     * 获取股东年度数据
     */
    public function getShareholderYearData()
    {
        $param = request()->param();
        $shareholderName = $param['shareholder_name'] ?? '';
        $year = $param['year'] ?? date('Y');

        if (empty($shareholderName)) {
            Log::info('[门店分红]-[股东分红清单表]：获取股东年度数据参数错误，分红人姓名为空，用户ID：' . $this->uid);
            return to_assign(1, '分红人姓名不能为空');
        }

        $model = new DividendPayment();
        $data = $model->getShareholderYearData($shareholderName, $year);

        // 获取该分红人的shareholder_ids（从已有数据中获取，如果没有则设为空）
        $shareholderIds = '';
        if (!empty($data)) {
            $shareholderIds = $data[0]['shareholder_ids'] ?? '';
        }

        // 构建12个月的完整数据
        $months = [];
        for ($i = 1; $i <= 12; $i++) {
            $months[] = sprintf('%s-%02d', $year, $i);
        }

        $yearData = [];
        foreach ($months as $month) {
            $monthData = [
                'period' => $month,
                'payable_amount' => '0.00',
                'adjustment_amount' => '0.00',
                'actual_payable_amount' => '0.00',
                'paid_amount' => '0.00',
                'unpaid_amount' => '0.00',
                'remark' => '',
                'shareholder_ids' => $shareholderIds,  // 添加shareholder_ids字段
                'exists_in_db' => false  // 标识数据是否在数据库中真实存在
            ];

            // 查找对应月份的数据
            foreach ($data as $item) {
                if ($item['period'] == $month) {
                    $monthData = $item;
                    $monthData['exists_in_db'] = true;  // 数据库中存在的数据
                    break;
                }
            }

            $yearData[] = $monthData;
        }

        // 记录获取股东年度数据日志
        add_log('view', 0, ['shareholder_name' => $shareholderName, 'year' => $year], '[门店分红]-[股东分红清单表]：获取股东年度数据');
        Log::info('[门店分红]-[股东分红清单表]：获取股东年度数据成功，分红人：' . $shareholderName . '，年份：' . $year . '，用户ID：' . $this->uid);

        return to_assign(0, '', [
            'shareholder_name' => $shareholderName,
            'year' => $year,
            'shareholder_ids' => $shareholderIds,  // 在顶层也返回shareholder_ids
            'data' => $yearData
        ]);
    }

    /**
     * 更新股东分红清单表
     */
    private function updatePaymentData()
    {
        $param = request()->param();
        $shareholderName = $param['shareholder_name'] ?? '';
        $shareholderIds = $param['shareholder_ids'] ?? '';  // 获取shareholder_ids
        $monthlyData = $param['monthly_data'] ?? [];

        if (empty($shareholderName)) {
            Log::info('[门店分红]-[股东分红清单表]：更新股东分红清单参数错误，分红人姓名为空，用户ID：' . $this->uid);
            return to_assign(1, '分红人姓名不能为空');
        }

        if (empty($monthlyData)) {
            Log::info('[门店分红]-[股东分红清单表]：更新股东分红清单参数错误，月度数据为空，用户ID：' . $this->uid);
            return to_assign(1, '月度数据不能为空');
        }

        $model = new DividendPayment();

        // 验证和处理数据
        foreach ($monthlyData as &$data) {
            // 确保每条数据都包含shareholder_ids
            if (empty($data['shareholder_ids']) && !empty($shareholderIds)) {
                $data['shareholder_ids'] = $shareholderIds;
            }

            // 计算实际应付金额和未付金额
            $payableAmount = floatval($data['payable_amount'] ?? 0);
            $adjustmentAmount = floatval($data['adjustment_amount'] ?? 0);
            $paidAmount = floatval($data['paid_amount'] ?? 0);

            $data['actual_payable_amount'] = $payableAmount - $adjustmentAmount;
            $data['unpaid_amount'] = $data['actual_payable_amount'] - $paidAmount;
        }

        if ($model->batchUpdatePaymentData($shareholderName, $monthlyData)) {
            // 记录更新股东分红清单成功日志
            add_log('edit', 0, ['shareholder_name' => $shareholderName, 'data_count' => count($monthlyData)], '[门店分红]-[股东分红清单表]：更新股东分红清单数据');
            Log::info('[门店分红]-[股东分红清单表]：更新股东分红清单数据成功，分红人：' . $shareholderName . '，数据条数：' . count($monthlyData) . '，用户ID：' . $this->uid);
            return to_assign(0, '保存成功');
        } else {
            Log::info('[门店分红]-[股东分红清单表]：更新股东分红清单数据失败，分红人：' . $shareholderName . '，用户ID：' . $this->uid);
            return to_assign(1, '保存失败');
        }
    }

    /**
     * 计算分红人清单数据
     */
    public function calculate()
    {
        $param = request()->param();
        $period = $param['period'] ?? '';

        if (empty($period)) {
            Log::info('[门店分红]-[股东分红清单表]：计算分红人清单参数错误，统计周期为空，用户ID：' . $this->uid);
            return to_assign(1, '统计周期不能为空');
        }

        // 验证周期格式（YYYY-MM）
        if (!preg_match('/^\d{4}-\d{2}$/', $period)) {
            Log::info('[门店分红]-[股东分红清单表]：计算分红人清单周期格式错误，周期：' . $period . '，用户ID：' . $this->uid);
            return to_assign(1, '统计周期格式错误，请使用YYYY-MM格式');
        }

        try {
            // 记录计算分红人清单开始日志
            add_log('edit', 0, ['period' => $period], '[门店分红]-[股东分红清单表]：计算分红人清单数据');
            Log::info('[门店分红]-[股东分红清单表]：开始计算分红人清单数据，周期：' . $period . '，用户ID：' . $this->uid);

            $result = DividendPaymentCalculateService::calculatePaymentData($period, $this->getAdminId());

            if ($result['success']) {
                Log::info('[门店分红]-[股东分红清单表]：计算分红人清单数据成功，周期：' . $period . '，消息：' . $result['message'] . '，用户ID：' . $this->uid);
                return to_assign(0, $result['message'], $result['data']);
            } else {
                Log::info('[门店分红]-[股东分红清单表]：计算分红人清单数据失败，周期：' . $period . '，消息：' . $result['message'] . '，用户ID：' . $this->uid);
                return to_assign(1, $result['message']);
            }
        } catch (\Exception $e) {
            Log::info('[门店分红]-[股东分红清单表]：计算分红人清单数据异常，周期：' . $period . '，错误信息：' . $e->getMessage() . '，用户ID：' . $this->uid);
            return to_assign(1, '计算失败：' . $e->getMessage());
        }
    }

    /**
     * 重新计算指定分红人的数据
     */
    public function recalculate()
    {
        $param = request()->param();
        $shareholderName = $param['shareholder_name'] ?? '';
        $period = $param['period'] ?? '';

        if (empty($shareholderName)) {
            Log::info('[门店分红]-[股东分红清单表]：重新计算分红人数据参数错误，分红人姓名为空，用户ID：' . $this->uid);
            return to_assign(1, '分红人姓名不能为空');
        }

        if (empty($period)) {
            Log::info('[门店分红]-[股东分红清单表]：重新计算分红人数据参数错误，统计周期为空，用户ID：' . $this->uid);
            return to_assign(1, '统计周期不能为空');
        }

        // 验证周期格式（YYYY-MM）
        if (!preg_match('/^\d{4}-\d{2}$/', $period)) {
            Log::info('[门店分红]-[股东分红清单表]：重新计算分红人数据周期格式错误，周期：' . $period . '，用户ID：' . $this->uid);
            return to_assign(1, '统计周期格式错误，请使用YYYY-MM格式');
        }

        try {
            // 记录重新计算分红人数据开始日志
            add_log('edit', 0, ['shareholder_name' => $shareholderName, 'period' => $period], '[门店分红]-[股东分红清单表]：重新计算分红人数据');
            Log::info('[门店分红]-[股东分红清单表]：开始重新计算分红人数据，分红人：' . $shareholderName . '，周期：' . $period . '，用户ID：' . $this->uid);

            $result = DividendPaymentCalculateService::recalculateShareholderData($shareholderName, $period);

            if ($result['success']) {
                Log::info('[门店分红]-[股东分红清单表]：重新计算分红人数据成功，分红人：' . $shareholderName . '，周期：' . $period . '，消息：' . $result['message'] . '，用户ID：' . $this->uid);
                return to_assign(0, $result['message'], $result['data']);
            } else {
                Log::info('[门店分红]-[股东分红清单表]：重新计算分红人数据失败，分红人：' . $shareholderName . '，周期：' . $period . '，消息：' . $result['message'] . '，用户ID：' . $this->uid);
                return to_assign(1, $result['message']);
            }
        } catch (\Exception $e) {
            Log::info('[门店分红]-[股东分红清单表]：重新计算分红人数据异常，分红人：' . $shareholderName . '，周期：' . $period . '，错误信息：' . $e->getMessage() . '，用户ID：' . $this->uid);
            return to_assign(1, '重新计算失败：' . $e->getMessage());
        }
    }

    /**
     * 批量计算多个周期的数据
     */
    public function batchCalculate()
    {
        $param = request()->param();
        $periods = $param['periods'] ?? [];

        if (empty($periods) || !is_array($periods)) {
            Log::info('[门店分红]-[股东分红清单表]：批量计算参数错误，统计周期列表为空，用户ID：' . $this->uid);
            return to_assign(1, '统计周期列表不能为空');
        }

        // 验证周期格式
        foreach ($periods as $period) {
            if (!preg_match('/^\d{4}-\d{2}$/', $period)) {
                Log::info('[门店分红]-[股东分红清单表]：批量计算周期格式错误，周期：' . $period . '，用户ID：' . $this->uid);
                return to_assign(1, "统计周期 {$period} 格式错误，请使用YYYY-MM格式");
            }
        }

        try {
            // 记录批量计算开始日志
            add_log('edit', 0, ['periods' => $periods], '[门店分红]-[股东分红清单表]：批量计算分红人清单数据');
            Log::info('[门店分红]-[股东分红清单表]：开始批量计算分红人清单数据，周期：' . implode(',', $periods) . '，用户ID：' . $this->uid);

            $result = DividendPaymentCalculateService::batchCalculatePaymentData($periods, $this->getAdminId());

            if ($result['success']) {
                Log::info('[门店分红]-[股东分红清单表]：批量计算分红人清单数据成功，周期：' . implode(',', $periods) . '，消息：' . $result['message'] . '，用户ID：' . $this->uid);
                return to_assign(0, $result['message'], $result['data']);
            } else {
                Log::info('[门店分红]-[股东分红清单表]：批量计算分红人清单数据失败，周期：' . implode(',', $periods) . '，消息：' . $result['message'] . '，用户ID：' . $this->uid);
                return to_assign(1, $result['message'], $result);
            }
        } catch (\Exception $e) {
            Log::info('[门店分红]-[股东分红清单表]：批量计算分红人清单数据异常，周期：' . implode(',', $periods) . '，错误信息：' . $e->getMessage() . '，用户ID：' . $this->uid);
            return to_assign(1, '批量计算失败：' . $e->getMessage());
        }
    }

    /**
     * 获取计算统计信息
     */
    public function getStatistics()
    {
        $param = request()->param();
        $period = $param['period'] ?? '';

        if (empty($period)) {
            Log::info('[门店分红]-[股东分红清单表]：获取计算统计信息参数错误，统计周期为空，用户ID：' . $this->uid);
            return to_assign(1, '统计周期不能为空');
        }

        try {
            // 记录获取统计信息日志
            add_log('view', 0, ['period' => $period], '[门店分红]-[股东分红清单表]：获取计算统计信息');
            Log::info('[门店分红]-[股东分红清单表]：获取计算统计信息，周期：' . $period . '，用户ID：' . $this->uid);

            $statistics = DividendPaymentCalculateService::getCalculateStatistics($period);
            Log::info('[门店分红]-[股东分红清单表]：获取计算统计信息成功，周期：' . $period . '，用户ID：' . $this->uid);
            return to_assign(0, '获取成功', $statistics);
        } catch (\Exception $e) {
            Log::info('[门店分红]-[股东分红清单表]：获取计算统计信息失败，周期：' . $period . '，错误信息：' . $e->getMessage() . '，用户ID：' . $this->uid);
            return to_assign(1, '获取统计信息失败：' . $e->getMessage());
        }
    }

    /**
     * 完整计算流程（门店分红 → 公司股东分红 → 分红人清单）
     */
    public function fullCalculate()
    {
        $param = request()->param();
        $period = $param['period'] ?? '';

        if (empty($period)) {
            Log::info('[门店分红]-[股东分红清单表]：完整计算流程参数错误，统计周期为空，用户ID：' . $this->uid);
            return to_assign(1, '统计周期不能为空');
        }

        // 验证周期格式（YYYY-MM）
        if (!preg_match('/^\d{4}-\d{2}$/', $period)) {
            Log::info('[门店分红]-[股东分红清单表]：完整计算流程周期格式错误，周期：' . $period . '，用户ID：' . $this->uid);
            return to_assign(1, '统计周期格式错误，请使用YYYY-MM格式');
        }

        try {
            // 记录完整计算流程开始日志
            add_log('edit', 0, ['period' => $period], '[门店分红]-[股东分红清单表]：完整计算流程');
            Log::info('[门店分红]-[股东分红清单表]：开始完整计算流程，周期：' . $period . '，用户ID：' . $this->uid);

            $adminId = $this->getAdminId();
            $results = [];
            $startTime = time();

            // 第一步：门店分红计算
            $storeResult = DividendCalculateService::calculateDividend($period, $adminId);
            if (!$storeResult['success']) {
                return to_assign(1, "门店分红计算失败：{$storeResult['message']}");
            }
            $results['store_dividend'] = $storeResult;

            // 第二步：公司股东分红计算
            $companyResult = DividendCompanyCalculateService::calculateCompanyDividend($period, $adminId);
            if (!$companyResult['success']) {
                return to_assign(1, "公司股东分红计算失败：{$companyResult['message']}");
            }
            $results['company_dividend'] = $companyResult;

            // 第三步：分红人清单计算
            $paymentResult = DividendPaymentCalculateService::calculatePaymentData($period, $adminId);
            if (!$paymentResult['success']) {
                return to_assign(1, "分红人清单计算失败：{$paymentResult['message']}");
            }
            $results['payment_list'] = $paymentResult;

            // 获取最终统计信息
            $finalStats = [
                'store_stats' => DividendCalculateService::getCalculateStatistics($period),
                'company_stats' => DividendCompanyCalculateService::getCalculateStatistics($period),
                'payment_stats' => DividendPaymentCalculateService::getCalculateStatistics($period)
            ];

            $results['statistics'] = $finalStats;
            $results['total_time'] = time() - $startTime;

            Log::info('[门店分红]-[股东分红清单表]：完整计算流程执行成功，周期：' . $period . '，耗时：' . $results['total_time'] . '秒，用户ID：' . $this->uid);
            return to_assign(0, "完整计算流程执行成功，耗时 {$results['total_time']} 秒", $results);

        } catch (\Exception $e) {
            Log::info('[门店分红]-[股东分红清单表]：完整计算流程失败，周期：' . $period . '，错误信息：' . $e->getMessage() . '，用户ID：' . $this->uid);
            return to_assign(1, '完整计算失败：' . $e->getMessage());
        }
    }

    /**
     * 上传Excel文件
     */
    public function uploadExcel()
    {
        $file = request()->file('file');

        if (!$file) {
            Log::info('[门店分红]-[股东分红清单表]：上传Excel文件失败，未选择文件，用户ID：' . $this->uid);
            return to_assign(1, '请选择要上传的文件');
        }

        try {
            // 验证文件
            validate(['file' => 'filesize:51200|fileExt:xls,xlsx'])->check(['file' => $file]);

            // 生成文件路径
            $dataPath = date('Ym');
            $md5 = $file->hash('md5');
            $savename = Filesystem::disk('public')->putFile($dataPath, $file, function () use ($md5) {
                return $md5;
            });

            if (!$savename) {
                Log::info('[门店分红]-[股东分红清单表]：文件上传失败，保存文件失败，用户ID：' . $this->uid);
                return to_assign(1, '文件上传失败');
            }

            // 记录文件上传成功日志
            add_log('edit', 0, ['file_name' => $file->getOriginalName()], '[门店分红]-[股东分红清单表]：上传Excel文件');
            Log::info('[门店分红]-[股东分红清单表]：上传Excel文件成功，文件名：' . $file->getOriginalName() . '，保存路径：' . $savename . '，用户ID：' . $this->uid);

        } catch (\think\exception\ValidateException $e) {
            Log::info('[门店分红]-[股东分红清单表]：文件上传验证失败，错误信息：' . $e->getMessage() . '，用户ID：' . $this->uid);
            return to_assign(1, $e->getMessage());
        } catch (\Exception $e) {
            Log::info('[门店分红]-[股东分红清单表]：文件上传异常，错误信息：' . $e->getMessage() . '，用户ID：' . $this->uid);
            return to_assign(1, '上传失败：' . $e->getMessage());
        }


        return to_assign(0, '文件上传成功', [
            'file_path' => $savename,
            'original_name' => $file->getOriginalName()
        ]);
    }

    /**
     * 导入实付金额
     */
    public function importPaidAmount()
    {
        $param = request()->param();
        $period = $param['period'] ?? '';
        $filePath = $param['file_path'] ?? '';

        if (empty($period)) {
            Log::info('[门店分红]-[股东分红清单表]：导入实付金额参数错误，月份为空，用户ID：' . $this->uid);
            return to_assign(1, '请选择月份');
        }

        if (empty($filePath)) {
            Log::info('[门店分红]-[股东分红清单表]：导入实付金额参数错误，文件路径为空，用户ID：' . $this->uid);
            return to_assign(1, '请上传Excel文件');
        }

        // 验证月份格式
        if (!preg_match('/^\d{4}-\d{2}$/', $period)) {
            Log::info('[门店分红]-[股东分红清单表]：导入实付金额月份格式错误，月份：' . $period . '，用户ID：' . $this->uid);
            return to_assign(1, '月份格式错误，请使用YYYY-MM格式');
        }

        try {
            // 获取文件完整路径
            $fullPath = public_path() . 'storage/' . $filePath;

            if (!file_exists($fullPath)) {
                Log::info('[门店分红]-[股东分红清单表]：导入实付金额文件不存在，文件路径：' . $fullPath . '，用户ID：' . $this->uid);
                return to_assign(1, '文件不存在');
            }

            // 解析Excel文件
            $excelData = $this->parseExcelFile($fullPath);

            if (empty($excelData)) {
                Log::info('[门店分红]-[股东分红清单表]：导入实付金额Excel文件中没有有效数据，文件路径：' . $fullPath . '，用户ID：' . $this->uid);
                return to_assign(1, 'Excel文件中没有有效数据');
            }

            // 删除临时文件
            @unlink($fullPath);

        } catch (\Exception $e) {
            Log::info('[门店分红]-[股东分红清单表]：导入实付金额数据异常，月份：' . $period . '，错误信息：' . $e->getMessage() . '，用户ID：' . $this->uid);
            return to_assign(1, '导入失败：' . $e->getMessage());
        }

        // 处理导入数据
        $result = $this->processImportData($excelData, $period);

        if ($result['success']) {
            // 记录导入实付金额成功日志
            add_log('edit', 0, ['period' => $period, 'file_path' => $filePath, 'update_count' => $result['data']['updated_count'] ?? 0], '[门店分红]-[股东分红清单表]：导入实付金额数据');
            Log::info('[门店分红]-[股东分红清单表]：导入实付金额数据成功，月份：' . $period . '，更新条数：' . ($result['data']['updated_count'] ?? 0) . '，用户ID：' . $this->uid);
        } else {
            Log::info('[门店分红]-[股东分红清单表]：导入实付金额数据失败，月份：' . $period . '，错误信息：' . $result['message'] . '，用户ID：' . $this->uid);
        }

        return $result;
    }

    /**
     * 解析Excel文件
     * @param string $filePath 文件路径
     * @return array
     */
    private function parseExcelFile($filePath)
    {
        try {
            $fileExtension = strtolower(pathinfo($filePath, PATHINFO_EXTENSION));

            if ($fileExtension == 'xlsx') {
                $objReader = IOFactory::createReader('Xlsx');
            } else {
                $objReader = IOFactory::createReader('Xls');
            }

            $objReader->setReadDataOnly(TRUE);
            $objPHPExcel = $objReader->load($filePath);
            $sheet = $objPHPExcel->getSheet(0);   // excel中的第一张sheet
            $highestRow = $sheet->getHighestRow();       // 取得总行数

            $data = [];

            // 从第二行开始读取数据（第一行为表头）
            for ($row = 2; $row <= $highestRow; $row++) {
                $name = trim($sheet->getCell('A' . $row)->getCalculatedValue());
                $amount = $sheet->getCell('B' . $row)->getCalculatedValue();

                // 跳过空行
                if (empty($name) && (empty($amount) || $amount == 0)) {
                    continue;
                }

                if (empty($name)) {
                    throw new \Exception("第{$row}行姓名不能为空");
                }

                if (!is_numeric($amount) || $amount < 0) {
                    throw new \Exception("第{$row}行实付金额必须为非负数字");
                }

                $data[] = [
                    'name' => $name,
                    'paid_amount' => floatval($amount)
                ];
            }

            return $data;

        } catch (\Exception $e) {
            throw new \Exception('Excel文件解析失败：' . $e->getMessage());
        }
    }

    /**
     * 处理导入数据
     * @param array $excelData Excel数据
     * @param string $period 月份
     * @return array
     */
    private function processImportData($excelData, $period)
    {
        $model = new DividendPayment();
        $successCount = 0;
        $failedList = [];
        $notFoundList = [];

        $model->startTrans();

        try {
            foreach ($excelData as $item) {
                $shareholderName = $item['name'];
                $paidAmount = $item['paid_amount'];

                // 查找对应的分红人记录
                $existingRecord = $model->where('is_delete', 0)
                    ->where('shareholder_name', $shareholderName)
                    ->where('period', $period)
                    ->find();

                if (!$existingRecord) {
                    $notFoundList[] = $shareholderName;
                    continue;
                }

                // 计算新的未付金额
                $actualPayableAmount = floatval($existingRecord['actual_payable_amount']);
                $newUnpaidAmount = $actualPayableAmount - $paidAmount;

                // 准备新数据
                $newData = [
                    'shareholder_name' => $shareholderName,
                    'shareholder_ids' => $existingRecord['shareholder_ids'],
                    'period' => $period,
                    'payable_amount' => $existingRecord['payable_amount'],
                    'adjustment_amount' => $existingRecord['adjustment_amount'],
                    'actual_payable_amount' => $actualPayableAmount,
                    'paid_amount' => $paidAmount,
                    'unpaid_amount' => $newUnpaidAmount,
                    'remark' => $existingRecord['remark']
                ];

                // 使用软删除再创建的方式更新数据
                if ($model->savePaymentData($newData)) {
                    $successCount++;
                } else {
                    $failedList[] = $shareholderName;
                }
            }

            $model->commit();

            // 构建返回消息
            $message = "导入完成！成功更新 {$successCount} 条记录";

            if (!empty($notFoundList)) {
                $message .= "，未找到以下分红人：" . implode('、', $notFoundList);
            }

            if (!empty($failedList)) {
                $message .= "，以下分红人更新失败：" . implode('、', $failedList);
            }

        } catch (\Exception $e) {
            $model->rollback();
            return to_assign(1, '导入失败：' . $e->getMessage());
        }

        return to_assign(0, $message, [
            'success_count' => $successCount,
            'not_found_list' => $notFoundList,
            'failed_list' => $failedList
        ]);
    }

    /**
     * 下载Excel导入模板
     */
    public function downloadTemplate()
    {
        $param = request()->param();
        $period = $param['period'] ?? ''; // 可选参数，用于生成特定月份的模板
        try {
            // 创建新的Spreadsheet对象
            $spreadsheet = new Spreadsheet();
            $sheet = $spreadsheet->getActiveSheet();

            // 设置表头
            $sheet->setCellValue('A1', '姓名');
            $sheet->setCellValue('B1', '实付金额');

            // 设置表头样式
            $headerStyle = [
                'font' => [
                    'bold' => true,
                    'size' => 12,
                    'name' => '微软雅黑'
                ],
                'fill' => [
                    'fillType' => \PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID,
                    'startColor' => [
                        'argb' => 'FFE6E6E6',
                    ],
                ],
                'borders' => [
                    'allBorders' => [
                        'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                    ],
                ],
                'alignment' => [
                    'horizontal' => \PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER,
                    'vertical' => \PhpOffice\PhpSpreadsheet\Style\Alignment::VERTICAL_CENTER,
                ],
            ];

            $sheet->getStyle('A1:B1')->applyFromArray($headerStyle);

            // 使用固定的示例数据
            $exampleData = [
                ['张三', 8000.00],
                ['李四', 12000.00],
                ['王五', 5500.50]
            ];

            $row = 2;
            foreach ($exampleData as $data) {
                $sheet->setCellValue('A' . $row, $data[0]);
                $sheet->setCellValue('B' . $row, $data[1]);
                $row++;
            }

            // 设置数据样式
            $dataStyle = [
                'font' => [
                    'size' => 11,
                    'name' => '微软雅黑'
                ],
                'borders' => [
                    'allBorders' => [
                        'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                    ],
                ],
                'alignment' => [
                    'horizontal' => \PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER,
                    'vertical' => \PhpOffice\PhpSpreadsheet\Style\Alignment::VERTICAL_CENTER,
                ],
            ];

            $sheet->getStyle('A2:B' . ($row - 1))->applyFromArray($dataStyle);

            // 设置列宽
            $sheet->getColumnDimension('A')->setWidth(20);
            $sheet->getColumnDimension('B')->setWidth(15);

            // 添加说明信息
            $sheet->setCellValue('D1', '导入说明：');
            if (!empty($period)) {
                $sheet->setCellValue('D2', "目标月份：{$period}");
                $startRow = 3;
            } else {
                $startRow = 2;
            }

            $sheet->setCellValue('D' . $startRow, '1. 姓名必须与系统中的分红人姓名完全一致');
            $sheet->setCellValue('D' . ($startRow + 1), '2. 实付金额必须为数字格式，不能包含货币符号');
            $sheet->setCellValue('D' . ($startRow + 2), '3. 第一行为表头，请勿修改');
            $sheet->setCellValue('D' . ($startRow + 3), '4. 从第二行开始填写数据');
            $sheet->setCellValue('D' . ($startRow + 4), '5. 可以删除示例数据，填入实际数据');
            $sheet->setCellValue('D' . ($startRow + 5), '6. 支持.xls和.xlsx格式，文件大小不超过50MB');
            $sheet->setCellValue('D' . ($startRow + 6), '7. 导入时需要选择对应的月份');
            $sheet->setCellValue('D' . ($startRow + 7), '');
            $sheet->setCellValue('D' . ($startRow + 8), '注意：左侧为示例数据，请根据实际情况修改');
            $sheet->setCellValue('D' . ($startRow + 9), '姓名必须与系统中的分红人姓名完全一致');

            $endRow = $startRow + 9;

            // 设置说明样式
            $noteStyle = [
                'font' => [
                    'size' => 10,
                    'name' => '微软雅黑',
                    'color' => ['argb' => 'FF666666']
                ]
            ];

            $sheet->getStyle('D1:D' . $endRow)->applyFromArray($noteStyle);
            $sheet->getStyle('D1')->getFont()->setBold(true)->setSize(11);

            // 如果有月份信息，设置月份样式
            if (!empty($period)) {
                $sheet->getStyle('D2')->getFont()->setBold(true)->setColor(new \PhpOffice\PhpSpreadsheet\Style\Color('FF0066CC'));
            }

            // 设置注意事项样式
            $sheet->getStyle('D' . ($endRow - 1) . ':D' . $endRow)->getFont()->setColor(new \PhpOffice\PhpSpreadsheet\Style\Color('FF0066CC'));

            // 设置说明列宽
            $sheet->getColumnDimension('D')->setWidth(35);

            // 创建Writer对象
            $writer = new Xlsx($spreadsheet);

            // 设置文件名
            $filenameSuffix = !empty($period) ? "_{$period}" : '';
            $filename = '股东分红实付金额导入模板' . $filenameSuffix . '_' . date('YmdHis') . '.xlsx';

            // 设置响应头
            header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
            header('Content-Disposition: attachment;filename="' . $filename . '"');
            header('Cache-Control: max-age=0');

            // 输出文件
            $writer->save('php://output');
            exit;

        } catch (\Exception $e) {
            return to_assign(1, '模板下载失败：' . $e->getMessage());
        }
    }
}
