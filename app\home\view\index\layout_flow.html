<div class="layui-card">
	<div class="layui-card-header">
		<h3>最活跃员工
		<span style="color:#999; font-size:14px; font-weight:400; margin-left:5px">最近30天前十的活跃度</span>
		</h3></div>
	<div class="layui-card-body">
		<div id="mainFlow" style="width: 100%; height:1000px;"></div>
	</div>
</div>
<script>
//动态
var mainFlow;
function layoutFlow() {
	mainFlow = echarts.init(document.getElementById('mainFlow'));
	var option = {
        title: {
          // text: "流程",
        },
        series: [
          {
            type: "graph", //关系图
            layout: "none", // 图的布局 'none' 不采用任何布局，使用节点中提供的 x， y 作为节点的位置。
	         // 'circular' 采用环形布局;'force' 采用力引导布局.
            symbolSize: 100,
            roam: 'move', // 是否开启鼠标缩放和平移漫游。默认不开启。如果只想要开启缩放或者平移，可以设置成 'scale' 或者 'move'。设置成 true 为都开启
            // color: "#6A5ACD",
            label: {  // 关系对象上的标签
              show: true, // 是否显示标签
              fontSize: 16,  // 文本样式
              color: "#FFF",
              //backgroundColor:'white'
            },
            symbol: "roundRect", //形状
            edgeSymbol: ["circle", "arrow"],
            edgeSymbolSize: [4, 10], // 关系图节点标记的大小，可以设置成诸如 10 这样单一的数字，也可以用数组分开表示宽和高，例如 [4, 10] 表示标记宽为4，高为10。
            edgeLabel: { // 连接两个关系对象的线上的标签
              fontSize: 25,
            },
            // 图
            data: [
              {
                name: "提交",
                x: 100,
                y: 100,
				itemStyle: {
                 normal: {
                   color: "green"
                 }
               	},
				
              },
			  {
                name: "部门负责人",
                x: 0,
                y: 300,
              },
            //   {
            //     name: "B",
            //     x: 450,
            //     y: 100,
            //     tooltip: { //鼠标悬停在节点上时显示的内容
            //       show: true,
            //       position: "right", //标签位置
            //       textStyle: {
            //         width: 20,
            //       },
            //       formatter: function (params) {  //formatter这个参数是用来设置鼠标悬停时显示的内容的。
            //         return params.dataType == "node"
            //           ? "鼠标悬停的显示的内容"
            //           : "";
            //       },
            //     },
            //   },
              {
                name: "人事",
                x: 0,
                y: 500,
				itemStyle: {
                 normal: {
                   color: "red"
                 }
               	}
              },
              {
                name: "行政",
                x: 200,
                y: 300,
				itemStyle: {
                 normal: {
                   color: "red"
                 }
               	}
              },
              {
                name: "财务",
                x: 200,
                y: 500,
				itemStyle: {
                 normal: {
                   color: "red"
                 }
               	}
              },
			  {
                name: "结束",
                x: 100,
                y: 700,
				itemStyle: {
                 normal: {
                   color: "red"
                 }
               	}
              },
            ],
            // 线
            links: [
              {
                source: "提交",
                target: "部门负责人",
                lineStyle: {
                  color: "#473C8B",
                  width: 3,
                },
              },
              {
                source: "部门负责人",
                target: "人事",
                lineStyle: {
                  color: "#473C8B",
                  width: 3,
                },
                label: { //线上面显示内容
                  show: true,
                  position: "middle",
                  formatter: "有疑问",
                  fontSize: 15,
                },
              },
              {
                source: "人事",
                target: "结束",
                lineStyle: {
                  color: "#473C8B",
                  width: 3,
                },
                label: {
                  show: true,
                  position: "middle",
                  formatter: "完成",
                  fontSize: 15,
                },
              },
              {
                source: "提交",
                target: "行政",
                lineStyle: {
                  color: "#473C8B",
                  width: 3,
                },
              },
              {
                source: "行政",
                target: "财务",
                lineStyle: {
                  color: "#473C8B",
                  width: 3,
                },
              },
              {
                source: "财务",
                target: "结束",
                lineStyle: {
                  color: "#473C8B",
                  width: 3,
                },
              },
            ],
		},
        ],
      
		tooltip: { //鼠标悬停在节点上时显示的内容
			show: true,
			position: "right", //标签位置
			textStyle: {
				width: 20,
			},
			formatter: function (x) {  //formatter这个参数是用来设置鼠标悬停时显示的内容的。
				console.log(x)
				return x.data.name;
			},
		},
	
	};
	mainFlow.setOption(option);
}

</script>