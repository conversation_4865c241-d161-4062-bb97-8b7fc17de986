<?php
/**
 * @copyright Copyright (c) 2021 仲正堂工作室
 * @license https://opensource.org/licenses/GPL-3.0
 * @link https://www.gougucms.com
 */
declare (strict_types=1);

namespace app\api\controller;

use app\api\BaseController;
use app\contract\service\ContractrentService;
use app\oa\controller\Approve;
use app\oa\service\TypeService;
use app\store\model\StoreBill;
use app\store\service\StoreBillService;
use app\store\service\StoreBusinessService;
use app\store\service\StoreSalaryService;
use app\store\service\StoreService;
use PhpOffice\PhpSpreadsheet\Calculation\Logical\Boolean;
use SNMP;
use think\facade\Db;
use think\Session;

class Youzan
{

    protected $myClientId;
    protected $myClientSecret;
    protected $access_token;

    public function __construct()
    {
        $this->myClientId = get_config('youzan.myClientId');
        $this->myClientSecret = get_config('youzan.myClientSecret');
        $this->access_token = $this->gettoken();
    }

    public function index()
    {
        var_dump(1111111);
    }

//    public function message_syn()
//    {
//        ini_set("max_execution_time", "0");
//        $param = get_params();
//        $this->save_msg($param['type'], json_encode($param));
//        exit();
//        $myClientId = $this->myClientId; //应用的 client_id
//        $myClientSecret = $this->myClientSecret; //应用的 client_secret
//
//        $httpRequestBody = file_get_contents('php://input');
//        $httpSign = $_SERVER['HTTP_EVENT_SIGN'];
//        $httpType = $_SERVER['HTTP_EVENT_TYPE'];
//        $httpClientId = $_SERVER['HTTP_CLIENT_ID'];
//        // 判断消息是否合法，若合法则返回成功标识
//        $sign = md5(sprintf('%s%s%s', $myClientId, $httpRequestBody, $myClientSecret));
//        if ($sign != $httpSign) {
//            // sign校验失败, 自行检查
//            return json_encode(array("code" => 0, "msg" => "faild"));
//        }
//        // 业务逻辑处理
//        // 根据 Type 来识别消息事件类型, 具体的 type 值以文档为准, 此处仅是示例
//        $this->save_msg($httpType, $httpRequestBody);
//        if ($httpType == "mei_open_staff") { //员工新增、删除、修改
//            $httpData = json_decode($httpRequestBody, true);
//            /***
//             * todo
//             */
//            //$msg = json_decode(urldecode($httpData['msg']), true);
//            $this->mei_open_staff($httpData);
//        }
//        // 响应结果
//        return json_encode(array("code" => 0, "msg" => "success", 'data' => $httpRequestBody));
//    }

    public function message_syn()
    {
        ini_set("max_execution_time", "0");
        $param = get_params();
        //$this->save_msg('', json_encode($param));

        //$this->save_msg('url', json_encode($param));

        $type = isset($param['type']) ? $param['type'] : '';

        if (empty($param['type'])) {
            $tid = isset($param['tid']) ? $param['tid'] : '';
            if (!empty($tid)) { //1. 美业订单在商家端操作主动退款 2.美业订单在顾客端发起退货退款申请，并且商家同意退货退款申请后
                $this->save_msg("youzan_mei_OrderRefundStatusChange", json_encode($param));
                $this->youzan_mei_OrderRefundStatusChange($param);
            }
        }


        $msg = json_decode(urldecode($param['msg']), true);

//        if ($type == "mei_open_staff") { //员工新增、删除、修改
//            $this->mei_open_staff($param);
//        }

//        if ($type == "youzan_mei_OrderRefundStatusChange") { //1. 美业订单在商家端操作主动退款 2.美业订单在顾客端发起退货退款申请，并且商家同意退货退款申请后
//            //var_dump($param);
//            $this->save_msg($type, json_encode($param));
//            $this->youzan_mei_OrderRefundStatusChange($param);
//        }

        if ($type == "mei_open_trade") {
            $this->mei_open_trade($msg);
        }

        return json_encode(array("code" => 0, "msg" => "success"));
    }


    //处理员工消息
    public function mei_open_staff($data = [])
    {
        ini_set("max_execution_time", "0");
        $msg = json_decode(urldecode($data['msg']), true);

//        $httpData = '{"msg":"%7B%22oldDeptId%22%3A160991303%2C%22positionId%22%3A5%2C%22kdtId%22%3A43984049%2C%22open_id%22%3A%22kuzYQKC01077536713640820736%22%2C%22deptId%22%3A160991303%2C%22roleIdSet%22%3A%5B5%5D%2C%22yz_open_id%22%3A18661629658%2C%22eventType%22%3A3%2C%22yzUid%22%3A18661629658%7D","mode":1,"kdt_id":43984049,"test":false,"sign":"504b8ce9601c3c5478356e43a3db0a75","id":"147483647","type":"mei_open_staff","sendCount":1,"msg_id":"33e4500e-1f1b-4e08-9c65-d43ce392b66b","version":1,"client_id":"71570ff9a7e8ea31ae","status":"3"}';
//        $data = json_decode($httpData, true);
//        $msg = json_decode(urldecode($data['msg']), true);


        $status = $data['status']; //状态 1新增 2删除 3修改
        $deptId = $msg['deptId'];
        //调用接口获取有赞员工详情
        if ($msg['deptId'] == 1) {
            return;
        }
        $staff_query = $this->getstaffquery($msg['open_id'], $msg['deptId']);
        //新增
        if ($status == 1) {
            $data = array();
            $data['username'] = $staff_query->mobile; //
            $data['pwd'] = "52dab9db1f7d480b20bca9559bf64c01"; //
            $data['salt'] = "svm5wu27kgdpet031z6x"; //
            $data['name'] = $staff_query->real_name; //
            $data['mobile'] = $staff_query->mobile; //
            $data['thumb'] = !empty($staff_query->avatar) ? $staff_query->avatar : "/static/home/<USER>/icon.png"; //
            $data['theme'] = "white"; //
            $data['create_time'] = intval($staff_query->created_at / 1000); //
            $data['entry_time'] = intval($staff_query->created_at / 1000);
            $data['type'] = 1;
            $data['yz_uid'] = $staff_query->yz_open_id;

            $Department = Db::name("Department")->where(['kdt_id' => $staff_query->node_kdt_id])->find();
            if (!empty($Department)) {
                $data['did'] = $Department['id'];
            }

            $admin = Db::name("admin")->where(['mobile' => $staff_query->mobile])->find();
            if (!empty($admin)) {
                Db::name("Admin")->where(['id' => $admin['id']])->update([
                    'thumb' => $data['thumb'],
                    'yz_uid' => $data['yz_uid'],
                ]);
            } else {
                $re_id = Db::name("admin")->insertGetId($data);
                //查询是否有面试记录
                $Admin_view = Db::name("Admin_view")->where(['mobile' => $data['mobile']])->find();
                if (!empty($Admin_view)) {
                    $re_age = getAgeFromIdCard($Admin_view['id_card']);
                    $ex_data = [
                        'id' => $re_id,
                        'id_card_number' => $Admin_view['id_card'],
                        'birthday' => $re_age != false ? $re_age['birthday'] : null,
                        'age' => $re_age != false ? $re_age['age'] : $Admin_view['age'],
                        'education' => $Admin_view['edu_background'],
                        'graduation_school' => $Admin_view['edu_school'],
                        'major' => $Admin_view['edu_major'],
                        'certificates' => $Admin_view['certificate'],
                    ];
                    $re_id = Db::name("admin_expand")->insertGetId($ex_data);
                }
            }

            return;
        }

        //编辑 删除
        $admin = Db::name("admin")->where(['mobile' => $staff_query->mobile])->find();

        if (empty($admin)) {
            $admin = Db::name("admin")->where(['name' => $staff_query->real_name])->find();
        }

        if (empty($admin)) return;
        $department = Db::name("department")->where(['kdt_id' => $staff_query->node_kdt_id, 'remark' => '门店'])->find();

        if (empty($department)) return;
        $now_date = date("Y-m-d");

        //1删除
        if ($staff_query->status == 1) {

            //修改员工状态
            Db::name("admin")->where(['mobile' => $staff_query->mobile])
                ->update(['status' => 2, 'res_date' => $now_date]);
            //添加离职记录
            Db::name("store_transfer")->insertGetId([
                'create_time' => time(),
                'create_date' => $now_date,
                'transfer_date' => $now_date,
                'aid' => $admin['id'],
                'aname' => $admin['name'],
                'o_did' => !empty($department) ? $department['id'] : 0,
                'o_dname' => !empty($department) ? $department['title'] : '',
                'status' => 1,
            ]);

            $admin_view = Db::name("admin_view")
                ->where(['mobile' => $staff_query->mobile])->update(
                    ['status' => 6]
                );


        } else if ($staff_query->status == 0) { //修改

            Db::name("admin")->where(['id' => $admin['id']])->update([
                'thumb' => $staff_query->avatar,
                'yz_uid' => $staff_query->yz_open_id,
                'entry_time' => intval($staff_query->created_at / 1000),
            ]);

            $oldDeptId = isset($msg['oldDeptId']) ? $msg['oldDeptId'] : '';

            //调店修改 如果新老门店相同 跳过
            if ($oldDeptId == $deptId || empty($oldDeptId)) return;

            $o_department = Db::name("department")->where(['kdt_id' => $oldDeptId])->find();
            if (empty($o_department) && $oldDeptId != '43984049') return;
            $o_did = explode(",", $admin['did']);
            foreach ($o_did as $k => $v) {
                if ($v == $o_department['id']) {
                    $o_did[$k] = $department['id'];
                    break;
                }
            }

            if (!in_array($department['id'], $o_did)) {
                $o_did[] = $department['id'];
            }
            $n_did = implode(",", $o_did);

            //修改员工部门
            Db::name("admin")->where(['id' => $admin['id']])->update(['did' => $n_did]);
            //添加离职记录
            Db::name("store_transfer")->insertGetId([
                'create_time' => time(),
                'create_date' => $now_date,
                'transfer_date' => $now_date,
                'aid' => $admin['id'],
                'aname' => $admin['name'],
                'o_did' => !empty($o_department) ? $o_department['id'] : 0,
                'o_dname' => !empty($o_department) ? $o_department['title'] : '',
                'n_did' => !empty($department) ? $department['id'] : 0,
                'n_dname' => !empty($department) ? $department['title'] : '',
                'status' => 2,
            ]);
        }


    }

    public function save_msg($type, $msg)
    {
        Db::name("youzan_msg")->insertGetId(
            ['type' => $type, 'msg' => $msg, 'create_time' => time(), 'create_date' => date('Y-m-d H:i:s')]
        );
    }

    //1. 美业订单在商家端操作主动退款 2.美业订单在顾客端发起退货退款申请，并且商家同意退货退款申请后
    public function youzan_mei_OrderRefundStatusChange($msg)
    {
        $tid = $msg['tid']; //订单id
        $status = $msg['status']; //退款状态: 10-退款申请已受理,20-退款到账,30-退款关闭,40-退款完成
        if ($status == 40 && !empty($tid)) { //只处理退款完成订单
            $this->getorderinfo($tid);
        }

    }

    //
    public function mei_open_trade($msg)
    {
        $orderNo = $msg['orderNo']; //订单id
        $orderState = $msg['orderState']; //订单id
        if ($orderState != 40) return;
        $this->save_msg("mei_open_trade", json_encode($msg));
        $this->getorderinfo($orderNo);
    }

    //获取token
    public function gettoken($refresh = false)
    {
        $youzan_token = get_cache('youzan_token');

//        if (!empty($youzan_token)) {
//            return $youzan_token;
//        }

        $data = [
            'client_id' => $this->myClientId,
            'client_secret' => $this->myClientSecret,
            'authorize_type' => 'silent',
            'grant_id' => '43984049',
            'refresh' => false
        ];
        $response = $this->ajax('https://open.youzanyun.com/auth/token', $data);
        $data = json_decode($response)->data;
        $access_token = $data->access_token;
        $expires = $data->expires;

        set_cache('youzan_token', $access_token, 3000);

        return $access_token;
    }

    public function test()
    {
//        $httpData = '{"msg":"%7B%22kdtId%22%3A43984049%2C%22open_id%22%3A%22JFj0G0051211968439762096128%22%2C%22deptId%22%3A1%2C%22yz_open_id%22%3A20329421539%2C%22eventType%22%3A2%2C%22yzUid%22%3A20329421539%7D","mode":1,"kdt_id":43984049,"test":false,"sign":"08c4c6be4306eb9b9b9e2c694144f28c","id":"147483647","type":"mei_open_staff","sendCount":1,"msg_id":"4a22d546-03cb-4256-a597-daec801060ef","version":1,"client_id":"71570ff9a7e8ea31ae","status":"2"}';
//        $data = json_decode($httpData, true);
//        $msg = json_decode(urldecode($data['msg']), true);
//        var_dump($msg);
        //$staff_query = $this->getstaffquery($msg['open_id'], $msg['node_kdt_id']);
        //var_dump($staff_query);

//        $sdate = "2024-08-13";
//
//        $kdt_id = 160177107;
//        $time_type = 4; //已完成时间作为查询标准
//        $order_status = 40; //查询完成的订单
//        $page_no = 1;
//        $page_size = 20;
//
//        $param = [
//            "order_status" => $order_status,
//            "kdt_id" => $kdt_id,
//            "time_type" => $time_type,
//            "begin_time" => strtotime($sdate) * 1000,
//            "page_no" => $page_no,
//            "page_size" => $page_size,
//            "end_time" => strtotime('+1 day', strtotime($sdate)) * 1000
//        ];
//
//
//        $this->getorderAction($param);

//        $jsoncode = "%7B%22kdtId%22%3A43984049%2C%22deptId%22%3A1%2C%22beginDate%22%3A1724860800000%2C%22endDate%22%3A1724947199000%2C%22page%22%3A1%2C%22pageSize%22%3A20%2C%22periodType%22%3A%22%22%2C%22sortBy%22%3A%22totalSpecialCommAmt%22%2C%22sortType%22%3A0%7D";
//        var_dump(json_decode($jsoncode));

        Db::close();

    }

    //员工详情
    public function getstaffquery($yz_open_id, $node_kdt_id)
    {
        if ($node_kdt_id == 1) {
            $node_kdt_id = 43984049;
        }

        $token = $this->gettoken();
        $url = 'https://open.youzanyun.com/api/youzan.mei.staff.query/4.0.0?access_token=' . $token .
            '&yz_open_id=' . $yz_open_id .
            '&node_kdt_id=' . $node_kdt_id;
        $response = $this->ajax($url, [], 'GET');
        return json_decode($response)->data;
    }


    public function ajax($url, $data, $type = 'POST')
    {
        // 创建CURL句柄
        $ch = curl_init();
        // 设置请求的URL地址
        curl_setopt($ch, CURLOPT_URL, $url);
        // 设置请求头信息
        curl_setopt($ch, CURLOPT_HTTPHEADER, array(
            'Content-Type: application/json; charset=utf-8'
        ));
        // 设置请求方法
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, $type);
        // 设置传递的数据
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        // 设置返回数据不直接输出
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);    // 信任任何证
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
        // 执行请求并获取响应数据
        curl_setopt($ch, CURLOPT_HTTP_VERSION, CURL_HTTP_VERSION_1_0);
        $response = curl_exec($ch);
        // 关闭CURL句柄
        curl_close($ch);
        // 输出响应数据
        if (is_bool($response)) {
            var_dump(curl_error($ch));
            exit();
        }
        return $response;
    }


    public function getstore()
    {

//        https://open.youzanyun.com/api/youzan.mei.dept.org.get/4.0.0


//        $msg = json_decode($msg, true);
//
//
//        foreach ($msg['items'] as $k => $v) {
//            $dname = $v['name'];
//            $dname = str_replace('仲正堂推拿艾灸(', "", $dname);
//            $dname = str_replace(')', "", $dname);
//            $dname = str_replace('路', "", $dname);
//            $dname = str_replace('店', "", $dname);
//            $department = Db::name("department")->where(
//                [['title', 'like', "%{$dname}%"]]
//            )->update(['kdt_id' => $v['kdt_id']]);
//            var_dump($department);
//
//        }

    }


    public function test1()
    {
        set_time_limit(0);
        sleep(100);
    }

    //OA员工 信息 匹配神码的员工信息 通过手机号员工名称
    public function staff_mt()
    {
        $admin = Db::connect('mysql')->table('oa_admin')
            ->select()->toArray();

        $count = 0;
        foreach ($admin as $k => $v) {
            $staff_info = Db::connect('mt')->table('staff_info')
                ->where([['name', 'like', '%' . $v['name'] . '%']])
                ->field("id,name,mobile,yz_open_id")
                ->find();

            if (!empty($staff_info)) {
                $admin = Db::connect('mysql')->table('oa_admin')
                    ->where(['id' => $v['id']])->update(['yz_uid' => $staff_info['yz_open_id']]);
            }

        }
    }

    /***
     * @return void
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * 拉取订单 信息
     */
    public function youzan_get_order()
    {
        set_time_limit(0);

        //获取时间范围内 门店的订单列表
        $sdate = date('Y-m-d', strtotime('-1 day', strtotime(date('Y-m-d'))));
        $param = get_params();

        if (isset($param['sdate']) && !empty($param['sdate'])) {
            $sdate = $param['sdate'];
        }

//        $kdt_id = 96176002;
        $time_type = 4; //已完成时间作为查询标准
        $order_status = 40; //查询完成的订单
        $page_no = 1;
        $page_size = 20;

        $department = Db::name('department')->where(['remark' => '门店'])->select()->toArray();
        foreach ($department as $k => $v) {
            $kdt_id = $v['kdt_id'];
            //{"order_status":40,"kdt_id":93528127,"time_type":4,"begin_time":1723478400000,"end_time":1723564800000}
            $param = [
                "order_status" => $order_status,
                "kdt_id" => $kdt_id,
                "time_type" => $time_type,
                "begin_time" => strtotime($sdate) * 1000,
                "page_no" => $page_no,
                "page_size" => $page_size,
                "end_time" => strtotime('+1 day', strtotime($sdate)) * 1000
            ];

            //拉取订单
            $this->getorderAction($param);
            //拉取耗卡消费记录
            //$this->getConsume(['report_date' => $sdate, "node_kdt_id_list" => [$kdt_id]]);
        }

    }

    public function getorderAction($param)
    {
        set_time_limit(0);
        $token = $this->access_token;
        $url = 'https://open.youzanyun.com/api/youzan.mei.order.list/4.0.0?access_token=' . $token;
        $response = json_decode($this->ajax($url, $param, 'POST'));
        var_dump($token);
        $items = $response->items;

        if (!empty($items)) {
            foreach ($items as $k => $v) {
                try {
                    $this->getorderinfo($v->tid);
                } catch (\Exception $e) {
                    var_dump($v);
                }
            }
        } else {
            return;
        }

        $paginator = $response->paginator;
        $t_page_no = ceil(($paginator->total_count / $param['page_size']));
        if ($param['page_no'] >= $t_page_no) {
            return;
        } else {
            $param['page_no'] = $param['page_no'] + 1;
            $this->getorderAction($param);
        }

        $this->removal_order_pay();

    }


    public function youzan_get_orderUpdate()
    {
        set_time_limit(0);

        //获取时间范围内 门店的订单列表
        $sdate = date('Y-m-d', strtotime('-1 day', strtotime(date('Y-m-d'))));
        $param = get_params();

        if (isset($param['sdate']) && !empty($param['sdate'])) {
            $sdate = $param['sdate'];
        }

//        $kdt_id = 96176002;
        $time_type = 4; //已完成时间作为查询标准
        $order_status = 40; //查询完成的订单
        $page_no = 1;
        $page_size = 20;

        $department = Db::name('department')->where(['remark' => '门店'])->select()->toArray();
        foreach ($department as $k => $v) {
            $kdt_id = $v['kdt_id'];
            //{"order_status":40,"kdt_id":93528127,"time_type":4,"begin_time":1723478400000,"end_time":1723564800000}
            $param = [
                "order_status" => $order_status,
                "kdt_id" => $kdt_id,
                "time_type" => $time_type,
                "begin_time" => strtotime($sdate) * 1000,
                "page_no" => $page_no,
                "page_size" => $page_size,
                "end_time" => strtotime('+1 day', strtotime($sdate)) * 1000
            ];

            //拉取订单
            $this->getorderActionUpdate($param);
            //拉取耗卡消费记录
            //$this->getConsume(['report_date' => $sdate, "node_kdt_id_list" => [$kdt_id]]);
        }

    }

    public function getorderActionUpdate($param)
    {
        set_time_limit(0);
        $token = $this->access_token;
        $url = 'https://open.youzanyun.com/api/youzan.mei.order.list/4.0.0?access_token=' . $token;
        $response = json_decode($this->ajax($url, $param, 'POST'));

        $items = $response->items;

        if (!empty($items)) {
            foreach ($items as $k => $v) {
                try {
                    $order = Db::name('order')->where(['tid' => $v->tid])->find();

                    if (empty($order)) {
                        $this->getorderinfo($v->tid);
                    }
                } catch (\Exception $e) {
                    var_dump($v->tid);
                }
            }
        } else {
            return;
        }

        $paginator = $response->paginator;
        $t_page_no = ceil(($paginator->total_count / $param['page_size']));
        if ($param['page_no'] >= $t_page_no) {
            return;
        } else {
            $param['page_no'] = $param['page_no'] + 1;
            $this->getorderAction($param);
        }

    }


    public function youzan_get_consume()
    {
        set_time_limit(0);
        //获取时间范围内 门店的订单列表
//        $sdate = "2024-08-10";
        $sdate = date('Y-m-d', strtotime('-1 day', strtotime(date('Y-m-d'))));

//        $kdt_id = 96176002;

        $department = Db::name('department')->where(['remark' => '门店'])->select()->toArray();
        foreach ($department as $k => $v) {
            $kdt_id = $v['kdt_id'];
            //拉取耗卡消费记录
            $this->getConsume(['report_date' => $sdate, "node_kdt_id_list" => [$kdt_id]]);
        }

    }

    //获取门店耗卡
    public function getConsume($param)
    {
        set_time_limit(0);
        $token = $this->access_token;
        //订单详情
        $url = "https://open.youzanyun.com/api/youzan.mei.consume.report.get/4.0.0?access_token={$token}"; //订单详情
        $re = $this->ajax($url, $param, 'POST');
        $response = json_decode($re);
        $data = $response->data;
        if (empty($data)) {
            return;
        }

        $consume_prepaid_list = $data->consume_prepaid_list;
        $this->insert_consume($consume_prepaid_list, 1);

//        $consume_time_card_list = $data->consume_time_card_list;
//        $this->insert_consume($consume_time_card_list, 2);

    }

    public function insert_consume($items, $type)
    {
        foreach ($items as $k => $v) {
            $param_consume = array();
            $param_consume['capital_amount'] = isset($v->capital_amount) ? $v->capital_amount : 0;
            $param_consume['reverse_state'] = $v->reverse_state;
            $param_consume['member_no'] = $v->member_no;
            $param_consume['remark'] = $v->remark;
            $param_consume['yz_open_id'] = $v->yz_open_id;
            $param_consume['belong_node_name'] = $v->belong_node_name;
            $param_consume['tid'] = $v->tid;
            $param_consume['node_kdt_id'] = isset($v->node_kdt_id) ? $v->node_kdt_id : 0;
            $param_consume['coupon_pay'] = isset($v->coupon_pay) ? $v->coupon_pay : '';
            $param_consume['refund_amount'] = isset($v->refund_amount) ? $v->refund_amount : 0;
            $param_consume['belong_node_kdt_id'] = $v->belong_node_kdt_id;
            $param_consume['present_amount'] = isset($v->present_amount) ? $v->present_amount : 0;
            $param_consume['channel_type'] = isset($v->channel_type) ? $v->channel_type : 0;
            $param_consume['channel_name'] = isset($v->channel_name) ? $v->channel_name : '';
            $param_consume['finish_at'] = $v->finish_at;
            $param_consume['prepaid_amount'] = isset($v->prepaid_amount) ? $v->prepaid_amount : 0;
            $param_consume['order_state_name'] = $v->order_state_name;
            $param_consume['mobile'] = $v->mobile;
            $param_consume['member_name'] = $v->member_name;
            $param_consume['wipe_out_amount'] = isset($v->wipe_out_amount) ? $v->wipe_out_amount : 0;
            $param_consume['summary_amount'] = isset($v->summary_amount) ? $v->summary_amount : 0;
            $param_consume['order_state'] = $v->order_state;
            $param_consume['type'] = $type;
            $opening_time_map = get_object_vars($v->opening_time_map);
            foreach ($opening_time_map as $kk => $vv) {
                $param_consume['opening_time_map'] = $kk;
                $param_consume['opening_time_date'] = $vv;
            }

            $param_consume['jsoncode'] = json_encode($v);
            $consume_id = 0;
            $consume = Db::name("consume")->where(['tid' => $param_consume['tid']])->find();

            if (!empty($consume)) {
                $consume_id = $consume['id'];
                Db::name("consume")->where(['tid' => $param_consume['tid']])->update($param_consume);
            } else {
                $consume_id = Db::name("consume")->strict(false)->field(true)->insertGetId($param_consume);
            }


            $order_items = $v->order_items;
            Db::name("consume_item")->where(['consume_id' => $consume_id])->delete();
            foreach ($order_items as $order_items_key => $order_items_v) {
                $consume_item = array();
                $consume_item['consume_id'] = $consume_id;
                $consume_item['price'] = $order_items_v->price;
                $consume_item['goods_name'] = $order_items_v->goods_name;
                $consume_item['card_no'] = $order_items_v->card_no;
                $consume_item['goods_id'] = $order_items_v->goods_id;
                $consume_item['card_name'] = $order_items_v->card_name;
                $consume_item['card_id'] = $order_items_v->card_id;
                $consume_item['num'] = $order_items_v->num;
                $consume_item['promotion_price'] = isset($order_items_v->promotion_price) ? $order_items_v->promotion_price : 0;
                $consume_item['deduct_times'] = isset($order_items_v->deduct_times) ? $order_items_v->deduct_times : 0;
                $consume_item['jsoncode'] = json_encode($order_items_v);
                $consume_item['tid'] = $param_consume['tid'];
                $consume_item['index'] = $order_items_key + 1;
                $consume_item['real_pay'] = $consume_item['price'] * $consume_item['num'] - $consume_item['promotion_price'];
                $consume_item['finish_at'] = $param_consume['finish_at'];

                $technicians = $order_items_v->technicians;
                foreach ($technicians as $technician_k => $technician_v) {
                    $consume_item['yz_open_id'] = $technician_v->yz_open_id;
                    $consume_item['assigned'] = $technician_v->assigned;
                    $consume_item['tag'] = $technician_v->tag;
                    $consume_item['name'] = $technician_v->name;
                    Db::name("consume_item")->strict(false)->field(true)->insertGetId($consume_item);
                }
            }
        }
    }

    public function web_getorderinfo()
    {
        $param = get_params();
        if (!isset($param['tid']) || empty($param['tid'])) {
            return;
        }

        $this->getorderinfo($param['tid']);
    }

    public function web_getConsume()
    {
        $this->getConsume(['report_date' => '2024-08-10', "node_kdt_id_list" => [96176002]]);
    }

    //获取订单详情
    public function getorderinfo($tid)
    {
        $token = $this->access_token;
        //var_dump($tid . '</br>');
        //订单详情
        $param = ['tid' => $tid];
        $url = "https://open.youzanyun.com/api/youzan.mei.order.get/4.4.1?access_token={$token}&tid={$tid}"; //订单详情
        $re = $this->ajax($url, $param, 'POST');
        $response = json_decode($re);
        var_dump($response);

        $data = $response->data;

        if (empty($data)) {
            return;
        }
        if (empty($data->order_item_infos)) {
            return;
        }
        Db::startTrans();
        try {
            /***
             * 订单信息
             */
            $param_order = array();
            $param_order['tid'] = $tid;

            $param_order['order_out_biz_type'] = isset($data->order_out_biz_type) ? $data->order_out_biz_type : 0;
            $param_order['order_tag'] = isset($data->order_tag) ? $data->order_tag : 0;

            $param_order['create_time'] = $data->create_time;
            $param_order['create_date'] = $this->tidtodate($tid); //创建时间 这里创建时间有问题 接口数据不准确需要根据订单号来获取
            $param_order['finish_time'] = $data->finish_time; //完成时间
            $param_order['finish_date'] = date('Y-m-d H:i:s', ($data->finish_time / 1000)); //完成时间
            $param_order['update_time'] = $data->update_time; //更新时间
            $param_order['update_date'] = date('Y-m-d H:i:s', ($data->update_time / 1000)); //更新时间

            $param_order['bill_time'] = $data->bill_time;
            $param_order['real_pay'] = $data->real_pay;
            $param_order['total_pay'] = $data->total_pay;
            $param_order['order_type'] = $data->order_type;
            $param_order['order_state'] = $data->order_state;

            $order_sale_info = $data->order_sale_info;
            $param_order['sale_kdt_id'] = $order_sale_info->kdt_id;
            $param_order['sale_shop_name'] = $order_sale_info->shop_name;


            if (isset($data->order_buyer_info)) {
                $order_buyer_info = $data->order_buyer_info; //买家信息

                $param_order['buyer_gender'] = isset($order_buyer_info->gender) ? $order_buyer_info->gender : '';
                $param_order['buyer_new_customer'] = isset($order_buyer_info->new_customer) ? $order_buyer_info->new_customer : 0;
                $param_order['buyer_mobile'] = isset($order_buyer_info->mobile) ? $order_buyer_info->mobile : '';
                $param_order['buyer_yz_open_id'] = isset($order_buyer_info->yz_open_id) ? $order_buyer_info->yz_open_id : '';
                $param_order['buyer_remark'] = isset($order_buyer_info->remark) ? $order_buyer_info->remark : '';
                $param_order['buyer_customer_name'] = isset($order_buyer_info->customer_name) ? $order_buyer_info->customer_name : '';
                $param_order['buyer_belong_kdt_id'] = isset($order_buyer_info->belong_kdt_id) ? $order_buyer_info->belong_kdt_id : '';
            }

            $order_pay_info = $data->order_pay_info; //支付信息
            $param_order['pay_channel_name'] = $order_pay_info->pay_channel_name;
            $param_order['pay_real_pay'] = $order_pay_info->real_pay;
            $param_order['pay_pay_channel'] = $order_pay_info->pay_channel;
            $param_order['pay_pay_time'] = $order_pay_info->pay_time;
            $param_order['pay_pay_date'] = date('Y-m-d H:i:s', ($order_pay_info->pay_time / 1000)); //更新时间

            $param_order['jsoncode'] = json_encode($data);

            $order_reverse_infos = $data->order_reverse_infos; //退款信息
            $param_order['is_reverse'] = 0;
            if (count($order_reverse_infos) > 0) {
                $param_order['is_reverse'] = 1;
            }

            //订单商品信息
            $order_item_infos = $data->order_item_infos;
            $param_order['item_index'] = count($order_item_infos);

            $data_order = Db::name("order")->where(['tid' => $tid])->field("id")->find();

            $order_autoid = 0;
            if (empty($data_order)) {
                $order_autoid = Db::name("order")->insertGetId($param_order);

            } else {
                $order_autoid = $data_order['id'];
                Db::name("order")->where(['id' => $order_autoid])->update($param_order);
            }

            //订单商品信息
            $param_order_item = array();
            $param_order_item['order_id'] = $order_autoid;
            $param_order_item['tid'] = $tid;

            Db::name("order_item_staff")->where(['tid' => $tid])->delete();
            foreach ($order_item_infos as $order_item_key => $order_item) {
                $param_order_item['promotion_type'] = $order_item->promotion_type;
                $param_order_item['item_id'] = $order_item->item_id;
                $param_order_item['promotion_value'] = $order_item->promotion_value;
                $param_order_item['item_type'] = $order_item->item_type;
                $param_order_item['num'] = $order_item->num;
                $param_order_item['index'] = $order_item->index;
                $param_order_item['promotion_id'] = $order_item->promotion_id;
                $param_order_item['item_name'] = $order_item->item_name;
                $param_order_item['sku_id'] = $order_item->sku_id;
                $param_order_item['promotion_price'] = $order_item->promotion_price;
                $param_order_item['item_no'] = $order_item->item_no;
                $param_order_item['worth'] = $order_item->worth;
                $param_order_item['operate_price'] = $order_item->operate_price;
                $param_order_item['item_real_pay'] = $order_item->item_real_pay;
                $param_order_item['promotion_name'] = !empty($order_item->promotion_name) ? $order_item->promotion_name : '';
                $param_order_item['item_origin_price'] = $order_item->item_origin_price;
                $param_order_item['promotion_card_no'] = $order_item->promotion_card_no;
                $param_order_item['payment'] = isset($order_item->payment) ? $order_item->payment : 0;
                $param_order_item['jsoncode'] = json_encode($order_item);

                $data_order_item = Db::name("order_item")->where(['item_no' => $order_item->item_no])->field("id")->find();

                //获取当前项目的提成金额
//            $param_order_item['tc_price'] = 0;
//            $serving = Db::name("serving")->where(['item_id' => $order_item->item_id])->find();
//            if ($order_item->item_type == 1) { //服务项目 根据设定的金额提成
//                if (!empty($serving) && !empty($serving['tc_price'])) {
//                    $param_order_item['tc_price'] = $serving['tc_price'] * $order_item->num * 100;
//                }
//            } else { //其他根据 金额的5%提成
//                $param_order_item['tc_price'] = $order_item->item_real_pay * $order_item->num * 0.05;
//            }

                $param_order_item_autoid = 0;
                if (empty($data_order_item)) {
                    $param_order_item_autoid = Db::name("order_item")->insertGetId($param_order_item);
                } else {
                    $param_order_item_autoid = $data_order_item['id'];
                    Db::name("order_item")->where(['id' => $param_order_item_autoid])->update($param_order_item);
                }

                $param_order_item_staff = array();
                $param_order_item_staff['tid'] = $tid;
                $param_order_item_staff['order_id'] = $order_autoid;
                $param_order_item_staff['order_item_id'] = $param_order_item_autoid;
                $param_order_item_staff['finish_time'] = $data->finish_time;
                $param_order_item_staff['finish_date'] = date('Y-m-d H:i:s', ($data->finish_time / 1000));
                $param_order_item_staff['item_no'] = $order_item->item_no;
                $param_order_item_staff['index'] = $order_item->index;
                $param_order_item_staff['is_reverse'] = $param_order['is_reverse'];
                $tech_info_list = $order_item->tech_info_list;
                if (!empty($tech_info_list)) {
                    $count = count($tech_info_list);
                    foreach ($tech_info_list as $tk => $tv) {
                        $param_order_item_staff['yz_open_id'] = $tv->tech_yz_open_id;
                        $param_order_item_staff['name'] = $tv->name;
                        $param_order_item_staff['assigned'] = $tv->assigned;
                        $param_order_item_staff['type'] = 1;
//                    $param_order_item_staff['tc_price'] = intval($param_order_item['tc_price'] / $count);
                        Db::name("order_item_staff")->insertGetId($param_order_item_staff);
                    }
                }
                $sales_info_list = $order_item->sales_info_list;
                if (!empty($sales_info_list)) {
                    $count = count($sales_info_list);
                    foreach ($sales_info_list as $sk => $sv) {
                        $param_order_item_staff['assigned'] = 0;
                        $param_order_item_staff['yz_open_id'] = $sv->sales_yz_open_id;
                        $param_order_item_staff['name'] = $sv->name;
                        $param_order_item_staff['type'] = 2;
//                    $param_order_item_staff['tc_price'] = intval($param_order_item['tc_price'] / $count);
                        Db::name("order_item_staff")->insertGetId($param_order_item_staff);
                    }
                }
            }

            //支付信息
            $order_pay_list = $data->order_pay_list;
            Db::name('order_pay')->where(['tid' => $tid])->delete();
            foreach ($order_pay_list as $pay_key => $pay_item) {
                $param_order_pay = array();
                $param_order_pay['order_id'] = $order_autoid;
                $param_order_pay['tid'] = $tid;

                $param_order_pay['source'] = $pay_item->source;
                $param_order_pay['pay_channel_name'] = $pay_item->pay_channel_name;
                //$param_order_pay['outer_transaction_no'] = $pay_item->outer_transaction_no;
                $param_order_pay['present_pay'] = isset($pay_item->present_pay) ? $pay_item->present_pay : 0;
                $param_order_pay['real_pay'] = $pay_item->real_pay;
                $param_order_pay['capital_pay'] = isset($pay_item->capital_pay) ? $pay_item->capital_pay : 0;
                $param_order_pay['pay_channel'] = $pay_item->pay_channel;
                $param_order_pay['remark'] = $pay_item->remark;
                $param_order_pay['transaction_no'] = $pay_item->transaction_no;

                Db::name('order_pay')->insert($param_order_pay);
            }


            //退款信息
            $reverse_finish_time = 0;
            foreach ($order_reverse_infos as $reverse_key => $reverse_item) {
                $param_reverse = [
                    'order_id' => $order_autoid,
                    'tid' => $tid,
                    'refund_type' => $reverse_item->refund_type,
                    'operator_yz_open_id' => $reverse_item->operator_yz_open_id,
                    'reserve_no' => $reverse_item->reserve_no,
                    'reserve_amount' => $reverse_item->reserve_amount,
                    'reverse_time' => date('Y-m-d H:i:s', ($reverse_item->reverse_time / 1000)),
                    'refund_demand' => $reverse_item->refund_demand,
                    'reverse_finish_time' => date('Y-m-d H:i:s', ($reverse_item->reverse_finish_time / 1000)),
                    'reverse_state' => $reverse_item->reverse_state,
                    'kdt_id' => $param_order['sale_kdt_id'],
                    'order_finish_date' => $param_order['finish_date'],
                    'jsoncode' => json_encode($reverse_item)
                ];
                $reverse_finish_time = date('Y-m-d H:i:s', ($reverse_item->reverse_finish_time / 1000));

                $data_order_reverse = Db::name("order_reverse")->where(['reserve_no' => $reverse_item->reserve_no])->field("id")->find();
                $auto_order_reverse_id = 0;
                if (empty($data_order_reverse)) {
                    var_dump(1);
                    $auto_order_reverse_id = Db::name("order_reverse")->insertGetId($param_reverse);
                } else {
                    var_dump(2);
                    $auto_order_reverse_id = $data_order_reverse['id'];
                    Db::name("order_reverse")->where(['id' => $data_order_reverse['id']])->update($param_reverse);
                }

//            Db::name("order_refund_channels")->where(['reverse_id' => $auto_order_reverse_id])->delete();
//            $order_refund_channels = $reverse_item->refund_channels;
//            foreach ($order_refund_channels as $refund_key => $refund_item) {
//                $param_refund_channels = [
//                    'order_id' => $order_autoid,
//                    'tid' => $tid,
//                    'reverse_id' => $auto_order_reverse_id,
//                    'finish_time' => $refund_item->finish_time,
//                    'create_time' => $refund_item->create_time,
//                    'update_time' => $refund_item->update_time,
//                ];
//
//            }

            }

            //获取一下是否有退款信息
//        if (count($order_reverse_infos) > 0) {
//            $order_idata['is_reverse'] = 1;
//        }

            //优惠信息
            $order_promotion_infos = $data->order_promotion_infos; //优惠信息
            Db::name('order_promotion')->where(['tid' => $tid])->delete();
            foreach ($order_promotion_infos as $promotion_key => $promotion_value) {
                $param_order_promotion = array();
                $param_order_promotion['order_id'] = $order_autoid;
                $param_order_promotion['tid'] = $tid;

                $param_order_promotion['amount'] = isset($promotion_value->amount) ? $promotion_value->amount : 0;
                $param_order_promotion['identification'] = isset($promotion_value->identification) ? $promotion_value->identification : '';
                $param_order_promotion['scope'] = $promotion_value->scope;
                $param_order_promotion['name'] = $promotion_value->name;
                $param_order_promotion['type'] = $promotion_value->type;
                $param_order_promotion['value'] = $promotion_value->value;
                Db::name('order_promotion')->insert($param_order_promotion);
            }


            if ($reverse_finish_time != 0) {
                Db::name('order')->where(['id' => $order_autoid])->update([
                    'reverse_finish_time' => $reverse_finish_time
                ]);
            }

//            $this->removal_order_pay();
            Db::commit();
        } catch (\Exception $e) {
            $this->save_msg("rollback", json_encode($data));
            // 回滚事务
            Db::rollback();
        }

    }


    public function tidtodate($tid)
    {
        $tid = str_replace('M', '', $tid);
        $year = substr($tid, 0, 4);
        $month = substr($tid, 4, 2);
        $day = substr($tid, 6, 2);

        $hour = substr($tid, 8, 2);
        $minutes = substr($tid, 10, 2);
        $second = substr($tid, 12, 2);
        return "$year-$month-$day $hour:$minutes:$second";
    }

    //获取服务
    public function getservingAction()
    {
        $page_no = 1;
        $page_size = 200;
        $token = $this->access_token;
        $department_md = getmd('门店');
        $url = 'https://open.youzanyun.com/api/youzan.mei.serving.list/4.0.0?access_token=' . $token .
            '&kdt_id=' . $department_md[0]['kdt_id'] .
            '&page_no=' . $page_no .
            '&page_size=' . $page_size;

        $response = $this->ajax($url, [], 'GET');
        $data = json_decode($response)->data;
        $items = $data->items;

        if (empty($items)) {
            return;
        }

        foreach ($items as $k => $v) {
            //存储到数据库中
            $serving = Db::name('serving')->where(['item_id' => $v->item_id])->find();

            $data = [
                'service_time' => $v->service_time,
                'category_name' => $v->category_name,
                'item_tags' => $v->item_tags,
                'item_id' => $v->item_id,
                'title' => $v->title,
                'item_status' => $v->item_status,
                'is_wx_show' => $v->is_wx_show,
                'item_no' => $v->item_no,
                'create_time' => date('Y-m-d H:i:s', $v->create_time / 1000),
                'update_time' => date('Y-m-d H:i:s', $v->update_time / 1000),
                'alias' => $v->alias,
                'price' => doubleval($v->price / 100),
                'price_min' => doubleval($v->price_min / 100),
                'price_max' => doubleval($v->price_max / 100)
            ];
            if (!empty($serving)) {
                Db::name('serving')->where(['id' => $serving['id']])->update($data);
            } else {
                Db::name('serving')->insertGetId($data);
            }
        }

    }


    public function getproductAction()
    {
        $page_no = 1;
        $page_size = 200;
        $token = $this->access_token;
        $department_md = getmd('门店');
        $url = 'https://open.youzanyun.com/api/youzan.mei.product.list/4.0.0?access_token=' . $token .
            '&kdt_id=' . $department_md[0]['kdt_id'] .
            '&page_no=' . $page_no .
            '&page_size=' . $page_size;

        $response = $this->ajax($url, [], 'GET');
        $data = json_decode($response)->data;
        $items = $data->items;

        if (empty($items)) {
            return;
        }

        foreach ($items as $k => $v) {
            //存储到数据库中
            $serving = Db::name('serving')->where(['item_id' => $v->item_id])->find();

            $data = [
                'service_time' => 0,
                'category_name' => $v->category_name,
                'item_tags' => $v->item_tags,
                'item_id' => $v->item_id,
                'title' => $v->title,
                'item_status' => $v->item_status,
                'item_no' => $v->item_no,
                'create_time' => date('Y-m-d H:i:s', $v->create_time / 1000),
                'update_time' => date('Y-m-d H:i:s', $v->update_time / 1000),
                'price' => doubleval($v->price / 100),
                'price_min' => doubleval($v->price_min / 100),
                'price_max' => doubleval($v->price_max / 100)
            ];
            if (!empty($serving)) {
                Db::name('serving')->where(['id' => $serving['id']])->update($data);
            } else {
                Db::name('serving')->insertGetId($data);
            }
        }

    }

    public function getTcPrice()
    {
        set_time_limit(0);

        $order_item = Db::name("order_item")->where([['id', '>', '0']])->select()->toArray();

        foreach ($order_item as $k => $v) {
            $tc_price = 0;
            $serving = Db::name("serving")->where(['item_id' => $v['item_id']])->find();
            if ($v['item_type'] == 1) { //服务项目 根据设定的金额提成
                if (!empty($serving) && !empty($serving['tc_price'])) {
                    $tc_price = $serving['tc_price'] * $v['num'] * 100;
                }
            } else { //其他根据 金额的5%提成
                $tc_price = $v['item_real_pay'] * $v['num'] * 0.05;
            }

            $order_item_staff = Db::name("order_item_staff")->where(['order_item_id' => $v['id']])->select()->toArray();

            foreach ($order_item_staff as $kk => $vv) {
                $count = count($order_item_staff);
                $staff_tc_price = intval($tc_price / $count);
                Db::name("order_item_staff")->where(['id' => $vv['id']])->update(['tc_price' => $staff_tc_price]);
            }

            Db::name("order_item")->where(['id' => $v['id']])->update(['tc_price' => $tc_price]);
        }
    }


    public function getMemberCardList($yz_open_id = '')
    {
        //$yz_open_id = '72QQAoNB611464397029650432';
        $page_no = 1;
        $page_size = 20;
        $token = $this->access_token;
        $url = 'https://open.youzanyun.com/api/youzan.mei.member.card.list/4.0.0?access_token=' . $token .
            '&yz_open_id=' . $yz_open_id .
            '&status=1' .
            '&page_no=' . $page_no .
            '&page_size=' . $page_size;

        $response = $this->ajax($url, [], 'GET');

        if (!isset(json_decode($response)->data)){
            return;
        }


        $data = json_decode($response)->data;
        $items = $data->items;

        if (empty($items)) {
            return;
        }

        foreach ($items as $k => $v) {

            $insert_data = [
                'tid' => $v->source_id,
                'yz_open_id' => $yz_open_id,
                'total_value' => $v->total_value,
                'term_begin_at' => $v->term_begin_at,
                'term_days' => !empty($v->term_days) ? $v->term_days : 0,
                'remain_value' => $v->remain_value,
                'activate_status' => $v->activate_status,
                'source_type' => $v->source_type,
                'card_type' => $v->card_type,
                'card_alias' => $v->card_alias,
                'term_end_at' => $v->term_end_at,
                'card_no' => $v->card_no,
                'active_node_kdt_id' => $v->active_node_kdt_id,
                'card_name' => $v->card_name,
                'card_snapshot_id' => $v->card_snapshot_id,
                'source_id' => $v->source_id,
                'card_sub_type' => $v->card_sub_type,
                'active_at' => $v->active_at,
                'status' => $v->status,
                'jsoncode' => json_encode($v),
            ];

            $order_card = Db::name("order_card")->where(['card_no' => $v->card_no])->find();
            if (empty($order_card)) {
                Db::name("order_card")->insertGetId($insert_data);
            } else {
                Db::name("order_card")->where(['id' => $order_card['id']])->update($insert_data);
            }


            $order = Db::name("order")->where(['tid' => $v->source_id])->find();
            if (empty($order)) {
                $this->getorderinfo($v->source_id);
            }
        }


    }


    public function type_yj()
    {

        $start_date = '2024-12-30';
        $end_date = '2024-12-31';
        $did = 98;


        $store = Db::name("department")->where(['id' => $did])->find();

        $Store_Service = new StoreService();
        $re = $Store_Service->store_m_yj($start_date, $end_date, $did);

        var_dump($re);

    }

    public function tc()
    {

        $department['kdt_id'] = '121522204';
        $admin['yz_uid'] = 'gTmkDZRh676171969565036544';

        for ($i = 1; $i <= 20; $i++) {
            $day = $i;
            if ($i < 10) {
                $day = "0$i";
            }
            $insert_data['labor_amount'] = 0;
            $labor_amount = 0;
            if (!empty($department['kdt_id']) && !empty($admin) && !empty($admin['yz_uid'])) {
                $order_item_staff = Db::name("order_item_staff")
                    ->field('ois.*,oi.*,o.buyer_new_customer,o.pay_real_pay,o.item_index,o.pay_pay_channel,o.buyer_yz_open_id')
                    ->alias('ois')
                    ->join('order o', 'o.id = ois.order_id')
                    ->join('order_item oi', 'oi.id = ois.order_item_id')
                    ->where([
                        ['ois.yz_open_id', '=', $admin['yz_uid']],
                        ['o.sale_kdt_id', '=', $department['kdt_id']],
//                            ['o.finish_time', 'between', [strtotime("$sdate-01") * 1000, strtotime('+1 month', strtotime("$sdate-01")) * 1000]],
                        ['o.finish_date', 'between', ["2024-10-$day 00:00:00", "2024-10-$day 23:59:59"]],
                        ['o.order_state', '=', 40],
                        ['o.is_reverse', '=', 0],
                        ['oi.item_type', '=', 1],
                    ])->select()->toArray();

                foreach ($order_item_staff as $ois_key => $ois_value) {
                    $server_number = Db::name("order_item_staff")->where([
                        'order_id' => $ois_value['order_id'],
                        'index' => $ois_value['index']
                    ])->count();

                    $serving = Db::name("serving")->where(['item_id' => $ois_value['item_id']])->find();
                    var_dump($serving['tc_price']);
                    if (empty($serving) || empty($serving['tc_price'])) continue;

                    $tc = round($serving['tc_price'] * $ois_value['num'] / $server_number, 2);
                    $labor_amount += $tc;
                    var_dump($serving['tc_price']);
                    var_dump($ois_value['num']);
                    var_dump($ois_value['tid']);
                    var_dump($tc);
                    var_dump("</br>");
                }
                $insert_data['labor_amount'] = $labor_amount;
            }
            dump("$day tcze" . $insert_data['labor_amount']);
            var_dump("</br>");
        }


    }

    public function test2()
    {
        set_time_limit(0);
        $StoreBusinessService = new StoreBusinessService();

        $param = get_params();

        if (empty($param)) {
            $aid = 1050;
            $did = 62;
            $sdate = '2024-12-24';
        } else {
            $aid = $param['aid'];
            $did = $param['did'];
            $sdate = $param['sdate'];
        }

        $admin = Db::name("admin")->where(['id' => $aid])->find();
        $dep = Db::name("department")->where(['id' => $did])->find();

        for ($i = 1; $i <= 28; $i++) {
            if ($i < 10) {
                $sdate = "2025-02-0{$i}";
            } else {
                $sdate = "2025-02-{$i}";
            }
            $StoreBusinessService->everydayDataToOrder($admin, $dep, $sdate, 1);
        }


    }

    public function test2_1()
    {
        set_time_limit(0);
        $StoreBusinessService = new StoreBusinessService();

        $param = get_params();

        if (empty($param)) {
            $did = 43;
            $sdate = '2024-10';
        } else {
            $did = $param['did'];
            $sdate = $param['sdate'];
        }

        for ($i = 1; $i <= 28; $i++) {
            if ($i < 10) {
                $sdate = "2025-02-0{$i}";
            } else {
                $sdate = "2025-02-{$i}";
            }

            $store_business = Db::name("store_business")->where([
                'did' => $did,
                'sdate' => $sdate
            ])->select()->toArray();

            foreach ($store_business as $k => $v) {
                $admin = Db::name("admin")->where(['id' => $v['aid']])->find();
                $dep = Db::name("department")->where(['id' => $v['did']])->find();

                $StoreBusinessService->everydayDataToOrder($admin, $dep, $sdate, 1);
            }
        }

    }


    public function test3()
    {
        $StoreSalaryService = new StoreSalaryService();
        $param = get_params();

        if (empty($param)) {
            $did = 53;
            $sdate = '2024-12';
        } else {
            $did = $param['did'];
            $sdate = $param['sdate'];
        }

        $store = Db::name("store")->where([
            'sdate' => $sdate,
            'did' => $did
        ])->find();

        $StoreSalaryService->inittable($sdate, $did, 1, 973);
    }


    public function removal_order_pay()
    {
        $ot = Db::query("SELECT transaction_no,tid, COUNT(*)
FROM oa_order_pay
GROUP BY transaction_no,tid
HAVING COUNT(*) > 1");

        foreach ($ot as $k => $v) {
            $order_pay = Db::name("order_pay")->where(['transaction_no' => $v['transaction_no']])->order('id desc')->select()->toArray();

            foreach ($order_pay as $kk => $vv) {
                if ($kk == 0) continue;
                Db::name("order_pay")->where(['id' => $vv['id']])->delete();
            }
        }
    }

    public function removal_order()
    {
        $ot = Db::query("SELECT tid, COUNT(*)
FROM oa_order where tid like '%M202412%'
GROUP BY tid
HAVING COUNT(*) > 1");

        foreach ($ot as $k => $v) {
            $order_pay = Db::name("order")->where(['tid' => $v['tid']])->order('id desc')->select()->toArray();

            foreach ($order_pay as $kk => $vv) {
                if ($kk == 0) continue;
                Db::name("order")->where(['id' => $vv['id']])->delete();
                Db::name("order_item")->where(['order_id' => $vv['id']])->delete();
                Db::name("order_item_staff")->where(['order_id' => $vv['id']])->delete();
            }
        }
    }


    public function test4()
    {
        $tid = 'M2024110821574099340010';

        $order = Db::name('order')->where(['tid' => $tid])->find();

        dump(json_decode($order['jsoncode']));

    }


    public function service_numer()
    {


    }

    public function mytest()
    {
        //上个月
        $pre_month = '2024-09';
        $pre_last_month = getLastMonth($pre_month);

        $list = Db::query("SELECT 
    o.tid,
    o.finish_time,
    o.finish_date,
    o.sale_kdt_id,
    o.buyer_yz_open_id,
    ois.yz_open_id,
    DATE_FORMAT(o.finish_date, '%Y-%m-%d') as finish_day
FROM `oa_order` as o,  `oa_order_item_staff` as ois
where o.finish_date between '{$pre_month}-01 00:00:00' and '{$pre_last_month} 23:59:59'
and o.tid = ois.tid 
and o.is_reverse = 0
and ois.type = 1
GROUP BY o.tid,ois.yz_open_id");

        Db::name("order_second")->insertAll($list);


    }

    public function mytest2()
    {
//        $ContractrentService = new ContractrentService();
        getRentDatesBetween('2022-09-06', '2028-07-28');
    }


    public function u_sdate()
    {
        $interface = 'eth0'; // 指定接口名称
        $output = [];
        exec("ip link show dev $interface", $output);
        foreach ($output as $line) {
            if (preg_match('/link\/ether (\S+)/', $line, $matches)) {
                echo "MAC Address: " . $matches[1]; // 输出MAC地址
                break; // 找到后退出循环
            }
        }


    }


    public function customer_bymobile()
    {
        set_time_limit(0);

        $end_date = date("Y-m-d 00:00:00");
        $start_date = date("Y-m-d 00:00:00", strtotime("-1 year"));

        $sql = "
            SELECT o.buyer_yz_open_id,o.buyer_belong_kdt_id,o.buyer_mobile,o.buyer_customer_name
FROM `oa_order` o
where o.finish_date BETWEEN '$start_date' and '$end_date'
GROUP BY o.buyer_yz_open_id
        ";

        $res = Db::query($sql);

        foreach ($res as $k => $v){
            $this->customer_bymobile_url(
                $v['buyer_yz_open_id'] ,
                $v['buyer_belong_kdt_id'] ,
                $v['buyer_mobile'] ,
                $v['buyer_customer_name']
            );
        }

    }

    public function customer_bymobile_url($yz_open_id, $node_kdt_id , $buyer_mobile = "" , $buyer_customer_name = "")
    {
        set_time_limit(0);

        $params = [
            "yz_open_id" => $yz_open_id,
            "node_kdt_id" => $node_kdt_id
        ];

        $bi_member_consume = Db::connect('bidata')->table('bi_member_consume')
            ->where(['yz_open_id' => $yz_open_id])
            ->find();

        if (empty($bi_member_consume)) {
            $token = $this->access_token;
            $url = 'https://open.youzanyun.com/api/youzan.mei.member.consume.get/4.0.0?access_token=' . $token;
            $response = json_decode($this->ajax($url, $params, 'POST'));
            $data = isset($response->data) ? $response->data : [];

            if (!empty($data) && isset($data->first_consumer_time)) {

                try {
                    $ins = [
                        'yz_open_id' => $yz_open_id,
                        'node_kdt_id' => '',
                        'buyer_belong_kdt_id' => $node_kdt_id,
                        'first_consumer_time' => $data->first_consumer_time,
                        'average_amount' => $data->average_amount,
                        'total_trade_count' => $data->total_trade_count,
                        'total_trade_amount' => $data->total_trade_amount,
                        'last_trade_time' => $data->last_trade_time,
                        'create_time' => date("Y-m-d H:i:s"),
                        'buyer_mobile' => $buyer_mobile,
                        'buyer_customer_name' => $buyer_customer_name,
                        'first_consumer_date' => date("Y-m-d H:i:s", $data->first_consumer_time / 1000),
                        'last_trade_date' => date("Y-m-d H:i:s", $data->last_trade_time / 1000),
                    ];

                    Db::connect('bidata')->table('bi_member_consume')
                        ->insert($ins);
                }catch (\Exception $e){
                    dump($data);
                }

            }

        }


    }


    //获取时间范围内 门店的订单列表
    //24年1月-24年8月 的数据
    public function youzan_month_order()
    {
        set_time_limit(0);
        $sdate = "2024-01-01";
        $param = get_params();

        if (isset($param['sdate']) && !empty($param['sdate'])) {
            $sdate = $param['sdate'];
        }

//        $kdt_id = 96176002;
        $time_type = 4; //已完成时间作为查询标准
        $order_status = 40; //查询完成的订单
        $page_no = 1;
        $page_size = 20;

        $department = Db::name('department')->where(['remark' => '门店'])->select()->toArray();
        foreach ($department as $k => $v) {
            $kdt_id = $v['kdt_id'];
            //{"order_status":40,"kdt_id":93528127,"time_type":4,"begin_time":1723478400000,"end_time":1723564800000}
            $param = [
                "order_status" => $order_status,
                "kdt_id" => $kdt_id,
                "time_type" => $time_type,
                "begin_time" => strtotime($sdate) * 1000,
                "page_no" => $page_no,
                "page_size" => $page_size,
                "end_time" => strtotime('+1 day', strtotime($sdate)) * 1000
            ];

            //拉取订单
            $this->getorderAction($param);
            //拉取耗卡消费记录
            //$this->getConsume(['report_date' => $sdate, "node_kdt_id_list" => [$kdt_id]]);
        }

    }

    public function getorderinfo_month($tid)
    {
        $token = $this->access_token;
        //var_dump($tid . '</br>');
        //订单详情
        $param = ['tid' => $tid];
        $url = "https://open.youzanyun.com/api/youzan.mei.order.get/4.4.1?access_token={$token}&tid={$tid}"; //订单详情
        $re = $this->ajax($url, $param, 'POST');
        $response = json_decode($re);
        $data = $response->data;


        if (empty($data)) {
            return;
        }
        if (empty($data->order_item_infos)) {
            return;
        }
        Db::startTrans();
        try {
            /***
             * 订单信息
             */
            $param_order = array();
            $param_order['tid'] = $tid;

            $param_order['order_out_biz_type'] = isset($data->order_out_biz_type) ? $data->order_out_biz_type : 0;
            $param_order['order_tag'] = isset($data->order_tag) ? $data->order_tag : 0;

            $param_order['create_time'] = $data->create_time;
            $param_order['create_date'] = $this->tidtodate($tid); //创建时间 这里创建时间有问题 接口数据不准确需要根据订单号来获取
            $param_order['finish_time'] = $data->finish_time; //完成时间
            $param_order['finish_date'] = date('Y-m-d H:i:s', ($data->finish_time / 1000)); //完成时间
            $param_order['update_time'] = $data->update_time; //更新时间
            $param_order['update_date'] = date('Y-m-d H:i:s', ($data->update_time / 1000)); //更新时间

            $param_order['bill_time'] = $data->bill_time;
            $param_order['real_pay'] = $data->real_pay;
            $param_order['total_pay'] = $data->total_pay;
            $param_order['order_type'] = $data->order_type;
            $param_order['order_state'] = $data->order_state;

            $order_sale_info = $data->order_sale_info;
            $param_order['sale_kdt_id'] = $order_sale_info->kdt_id;
            $param_order['sale_shop_name'] = $order_sale_info->shop_name;


            if (isset($data->order_buyer_info)) {
                $order_buyer_info = $data->order_buyer_info; //买家信息

                $param_order['buyer_gender'] = isset($order_buyer_info->gender) ? $order_buyer_info->gender : '';
                $param_order['buyer_new_customer'] = isset($order_buyer_info->new_customer) ? $order_buyer_info->new_customer : 0;
                $param_order['buyer_mobile'] = isset($order_buyer_info->mobile) ? $order_buyer_info->mobile : '';
                $param_order['buyer_yz_open_id'] = isset($order_buyer_info->yz_open_id) ? $order_buyer_info->yz_open_id : '';
                $param_order['buyer_remark'] = isset($order_buyer_info->remark) ? $order_buyer_info->remark : '';
                $param_order['buyer_customer_name'] = isset($order_buyer_info->customer_name) ? $order_buyer_info->customer_name : '';
                $param_order['buyer_belong_kdt_id'] = isset($order_buyer_info->belong_kdt_id) ? $order_buyer_info->belong_kdt_id : '';
            }

            $order_pay_info = $data->order_pay_info; //支付信息
            $param_order['pay_channel_name'] = $order_pay_info->pay_channel_name;
            $param_order['pay_real_pay'] = $order_pay_info->real_pay;
            $param_order['pay_pay_channel'] = $order_pay_info->pay_channel;
            $param_order['pay_pay_time'] = $order_pay_info->pay_time;
            $param_order['pay_pay_date'] = date('Y-m-d H:i:s', ($order_pay_info->pay_time / 1000)); //更新时间

            $param_order['jsoncode'] = json_encode($data);

            $order_reverse_infos = $data->order_reverse_infos; //退款信息
            $param_order['is_reverse'] = 0;
            if (count($order_reverse_infos) > 0) {
                $param_order['is_reverse'] = 1;
            }

            //订单商品信息
            $order_item_infos = $data->order_item_infos;
            $param_order['item_index'] = count($order_item_infos);

            $data_order = Db::name("order")->where(['tid' => $tid])->field("id")->find();

            $order_autoid = 0;
            if (empty($data_order)) {
                $order_autoid = Db::name("order")->insertGetId($param_order);

            } else {
                $order_autoid = $data_order['id'];
                Db::name("order")->where(['id' => $order_autoid])->update($param_order);
            }

            //订单商品信息
            $param_order_item = array();
            $param_order_item['order_id'] = $order_autoid;
            $param_order_item['tid'] = $tid;

            Db::name("order_item_staff")->where(['tid' => $tid])->delete();
            foreach ($order_item_infos as $order_item_key => $order_item) {
                $param_order_item['promotion_type'] = $order_item->promotion_type;
                $param_order_item['item_id'] = $order_item->item_id;
                $param_order_item['promotion_value'] = $order_item->promotion_value;
                $param_order_item['item_type'] = $order_item->item_type;
                $param_order_item['num'] = $order_item->num;
                $param_order_item['index'] = $order_item->index;
                $param_order_item['promotion_id'] = $order_item->promotion_id;
                $param_order_item['item_name'] = $order_item->item_name;
                $param_order_item['sku_id'] = $order_item->sku_id;
                $param_order_item['promotion_price'] = $order_item->promotion_price;
                $param_order_item['item_no'] = $order_item->item_no;
                $param_order_item['worth'] = $order_item->worth;
                $param_order_item['operate_price'] = $order_item->operate_price;
                $param_order_item['item_real_pay'] = $order_item->item_real_pay;
                $param_order_item['promotion_name'] = !empty($order_item->promotion_name) ? $order_item->promotion_name : '';
                $param_order_item['item_origin_price'] = $order_item->item_origin_price;
                $param_order_item['promotion_card_no'] = $order_item->promotion_card_no;
                $param_order_item['payment'] = isset($order_item->payment) ? $order_item->payment : 0;
                $param_order_item['jsoncode'] = json_encode($order_item);

                $data_order_item = Db::name("order_item")->where(['item_no' => $order_item->item_no])->find();

                //获取当前项目的提成金额
//            $param_order_item['tc_price'] = 0;
//            $serving = Db::name("serving")->where(['item_id' => $order_item->item_id])->find();
//            if ($order_item->item_type == 1) { //服务项目 根据设定的金额提成
//                if (!empty($serving) && !empty($serving['tc_price'])) {
//                    $param_order_item['tc_price'] = $serving['tc_price'] * $order_item->num * 100;
//                }
//            } else { //其他根据 金额的5%提成
//                $param_order_item['tc_price'] = $order_item->item_real_pay * $order_item->num * 0.05;
//            }

                $param_order_item_autoid = 0;
                if (empty($data_order_item)) {
                    $param_order_item_autoid = Db::name("order_item")->insertGetId($param_order_item);
                } else {
                    $param_order_item_autoid = $data_order_item['id'];
                    Db::name("order_item")->where(['id' => $param_order_item_autoid])->update($param_order_item);
                }

                $param_order_item_staff = array();
                $param_order_item_staff['tid'] = $tid;
                $param_order_item_staff['order_id'] = $order_autoid;
                $param_order_item_staff['order_item_id'] = $param_order_item_autoid;
                $param_order_item_staff['finish_time'] = $data->finish_time;
                $param_order_item_staff['finish_date'] = date('Y-m-d H:i:s', ($data->finish_time / 1000));
                $param_order_item_staff['item_no'] = $order_item->item_no;
                $param_order_item_staff['index'] = $order_item->index;
                $param_order_item_staff['is_reverse'] = $param_order['is_reverse'];
                $tech_info_list = $order_item->tech_info_list;
                if (!empty($tech_info_list)) {
                    $count = count($tech_info_list);
                    foreach ($tech_info_list as $tk => $tv) {
                        $param_order_item_staff['yz_open_id'] = $tv->tech_yz_open_id;
                        $param_order_item_staff['name'] = $tv->name;
                        $param_order_item_staff['assigned'] = $tv->assigned;
                        $param_order_item_staff['type'] = 1;
//                    $param_order_item_staff['tc_price'] = intval($param_order_item['tc_price'] / $count);
                        Db::name("order_item_staff")->insertGetId($param_order_item_staff);
                    }
                }
                $sales_info_list = $order_item->sales_info_list;
                if (!empty($sales_info_list)) {
                    $count = count($sales_info_list);
                    foreach ($sales_info_list as $sk => $sv) {
                        $param_order_item_staff['assigned'] = 0;
                        $param_order_item_staff['yz_open_id'] = $sv->sales_yz_open_id;
                        $param_order_item_staff['name'] = $sv->name;
                        $param_order_item_staff['type'] = 2;
//                    $param_order_item_staff['tc_price'] = intval($param_order_item['tc_price'] / $count);
                        Db::name("order_item_staff")->insertGetId($param_order_item_staff);
                    }
                }
            }

            //支付信息
            $order_pay_list = $data->order_pay_list;
            Db::name('order_pay')->where(['tid' => $tid])->delete();
            foreach ($order_pay_list as $pay_key => $pay_item) {
                $param_order_pay = array();
                $param_order_pay['order_id'] = $order_autoid;
                $param_order_pay['tid'] = $tid;

                $param_order_pay['source'] = $pay_item->source;
                $param_order_pay['pay_channel_name'] = $pay_item->pay_channel_name;
                //$param_order_pay['outer_transaction_no'] = $pay_item->outer_transaction_no;
                $param_order_pay['present_pay'] = isset($pay_item->present_pay) ? $pay_item->present_pay : 0;
                $param_order_pay['real_pay'] = $pay_item->real_pay;
                $param_order_pay['capital_pay'] = isset($pay_item->capital_pay) ? $pay_item->capital_pay : 0;
                $param_order_pay['pay_channel'] = $pay_item->pay_channel;
                $param_order_pay['remark'] = $pay_item->remark;
                $param_order_pay['transaction_no'] = $pay_item->transaction_no;

                Db::name('order_pay')->insert($param_order_pay);
            }


            //退款信息
            $reverse_finish_time = 0;
            foreach ($order_reverse_infos as $reverse_key => $reverse_item) {

                $param_reverse = [
                    'order_id' => $order_autoid,
                    'tid' => $tid,
                    'refund_type' => $reverse_item->refund_type,
                    'operator_yz_open_id' => $reverse_item->operator_yz_open_id,
                    'reserve_no' => $reverse_item->reserve_no,
                    'reserve_amount' => $reverse_item->reserve_amount,
                    'reverse_time' => date('Y-m-d H:i:s', ($reverse_item->reverse_time / 1000)),
                    'refund_demand' => $reverse_item->refund_demand,
                    'reverse_finish_time' => date('Y-m-d H:i:s', ($reverse_item->reverse_finish_time / 1000)),
                    'reverse_state' => $reverse_item->reverse_state,
                    'kdt_id' => $param_order['sale_kdt_id'],
                    'order_finish_date' => $param_order['finish_date'],
                    'jsoncode' => json_encode($reverse_item)
                ];
                $reverse_finish_time = date('Y-m-d H:i:s', ($reverse_item->reverse_finish_time / 1000));

                $data_order_reverse = Db::name("order_reverse")->where(['reserve_no' => $reverse_item->reserve_no])->find();

                $auto_order_reverse_id = 0;
                if (empty($data_order_reverse)) {
                    var_dump(1);
                    $auto_order_reverse_id = Db::name("order_reverse")->insertGetId($param_reverse);
                } else {
                    var_dump(2);
                    $auto_order_reverse_id = $data_order_reverse['id'];
                    Db::name("order_reverse")->where(['id' => $data_order_reverse['id']])->update($param_reverse);
                }

//            Db::name("order_refund_channels")->where(['reverse_id' => $auto_order_reverse_id])->delete();
//            $order_refund_channels = $reverse_item->refund_channels;
//            foreach ($order_refund_channels as $refund_key => $refund_item) {
//                $param_refund_channels = [
//                    'order_id' => $order_autoid,
//                    'tid' => $tid,
//                    'reverse_id' => $auto_order_reverse_id,
//                    'finish_time' => $refund_item->finish_time,
//                    'create_time' => $refund_item->create_time,
//                    'update_time' => $refund_item->update_time,
//                ];
//
//            }

            }

            //获取一下是否有退款信息
//        if (count($order_reverse_infos) > 0) {
//            $order_idata['is_reverse'] = 1;
//        }

            //优惠信息
            $order_promotion_infos = $data->order_promotion_infos; //优惠信息
            Db::name('order_promotion')->where(['tid' => $tid])->delete();
            foreach ($order_promotion_infos as $promotion_key => $promotion_value) {
                $param_order_promotion = array();
                $param_order_promotion['order_id'] = $order_autoid;
                $param_order_promotion['tid'] = $tid;

                $param_order_promotion['amount'] = isset($promotion_value->amount) ? $promotion_value->amount : 0;
                $param_order_promotion['identification'] = isset($promotion_value->identification) ? $promotion_value->identification : '';
                $param_order_promotion['scope'] = $promotion_value->scope;
                $param_order_promotion['name'] = $promotion_value->name;
                $param_order_promotion['type'] = $promotion_value->type;
                $param_order_promotion['value'] = $promotion_value->value;
                Db::name('order_promotion')->insert($param_order_promotion);
            }


            if ($reverse_finish_time != 0) {
                Db::name('order')->where(['id' => $order_autoid])->update([
                    'reverse_finish_time' => $reverse_finish_time
                ]);
            }

            $this->removal_order_pay();
            Db::commit();
        } catch (\Exception $e) {
            $this->save_msg("rollback", json_encode($data));
            // 回滚事务
            Db::rollback();
        }

    }

}

//SELECT ois.*,oi.item_name,oi.item_id,oi.num,oi.item_type FROM `oa_order_item_staff` `ois`
//INNER JOIN `oa_order` `o` ON `o`.`id`=`ois`.`order_id`
//INNER JOIN `oa_order_item` `oi` ON `oi`.`id`=`ois`.`order_item_id`
//WHERE  `ois`.`yz_open_id` = '4VmMFB6p697407163861110784'  AND `o`.`sale_kdt_id` = '147982431'
//AND `o`.`finish_time` BETWEEN '1722441600000' AND '1725120000000'
//AND `o`.`order_state` = '40'
//AND `oi`.`item_type` = '1'
//AND `o`.`is_reverse` = '0'
//order by ois.finish_date asc
