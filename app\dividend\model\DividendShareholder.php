<?php
/**
 * @copyright Copyright (c) 2021 仲正堂工作室
 * @license https://opensource.org/licenses/GPL-3.0
 * @link https://www.gougucms.com
 */

namespace app\dividend\model;

use think\Model;

class DividendShareholder extends Model
{
    protected $name = 'dividend_shareholder';

    // 设置字段信息
    protected $schema = [
        'id'                          => 'int',
        'dividend_store_info_id'      => 'int',
        'admin_id'                    => 'int',
        'shareholder_name'            => 'string',
        'shareholder_type'            => 'int',
        'company_shareholding_ratio'  => 'decimal',
        'store_shareholding_ratio'    => 'decimal',
        'sort_order'                  => 'int',
        'remark'                      => 'string',
        'is_delete'                   => 'int',
        'delete_time'                 => 'int',
        'create_time'                 => 'int',
        'update_time'                 => 'int',
    ];

    // 自动时间戳
    protected $autoWriteTimestamp = true;
    protected $createTime = 'create_time';
    protected $updateTime = 'update_time';

    // 股东类型常量
    const TYPE_COMPANY = 1;  // 公司股东
    const TYPE_PERSONAL = 2; // 个人股东

    /**
     * 关联分红信息
     */
    public function dividendStoreInfo()
    {
        return $this->belongsTo('app\dividend\model\DividendStoreInfo', 'dividend_store_info_id', 'id');
    }

    /**
     * 关联员工信息
     */
    public function admin()
    {
        return $this->belongsTo('app\user\model\Admin', 'admin_id', 'id');
    }

    /**
     * 获取股东类型文本
     * @param int $type 股东类型
     * @return string
     */
    public static function getTypeText($type)
    {
        $types = [
            self::TYPE_COMPANY => '公司股东',
            self::TYPE_PERSONAL => '个人股东'
        ];

        return $types[$type] ?? '未知类型';
    }

    /**
     * 获取股东列表
     * @param int $dividendStoreInfoId 分红信息ID
     * @param int $shareholderType 股东类型
     * @return array
     */
    public static function getList($dividendStoreInfoId, $shareholderType = null)
    {
        $where = [
            ['dividend_store_info_id', '=', $dividendStoreInfoId],
            ['is_delete', '=', 0]
        ];

        if (!is_null($shareholderType)) {
            $where[] = ['shareholder_type', '=', $shareholderType];
        }

        return self::where($where)
            ->order('sort_order asc, id asc')
            ->select()
            ->toArray();
    }

    /**
     * 计算持股比例总和
     * @param int $dividendStoreInfoId 分红信息ID
     * @param int $shareholderType 股东类型
     * @return float
     */
    public static function getTotalRatio($dividendStoreInfoId, $shareholderType = null)
    {
        $where = [
            ['dividend_store_info_id', '=', $dividendStoreInfoId],
            ['is_delete', '=', 0]
        ];

        if (!is_null($shareholderType)) {
            $where[] = ['shareholder_type', '=', $shareholderType];
        }

        $result = self::where($where)->sum('store_shareholding_ratio');
        // 确保返回数值类型，避免后续使用时出现类型错误
        return is_numeric($result) ? floatval($result) : 0.0;
    }

    /**
     * 批量保存股东信息
     * @param int $dividendStoreInfoId 分红信息ID
     * @param array $shareholders 股东数据
     * @param int $shareholderType 股东类型
     * @return bool
     */
    public static function batchSave($dividendStoreInfoId, $shareholders, $shareholderType)
    {
        if (empty($shareholders)) {
            return true;
        }

        $data = [];
        foreach ($shareholders as $index => $shareholder) {
            if (!empty($shareholder['shareholder_name'])) {
                $shareholderData = [
                    'dividend_store_info_id' => $dividendStoreInfoId,
                    'shareholder_name' => $shareholder['shareholder_name'],
                    'shareholder_type' => $shareholderType,
                    'store_shareholding_ratio' => $shareholder['store_shareholding_ratio'] ?? 0,
                    'sort_order' => $index + 1,
                    'remark' => $shareholder['remark'] ?? '',
                    'create_time' => time(),
                    'update_time' => time()
                ];

                // 如果是公司股东，添加公司内部持股比例
                if ($shareholderType == self::TYPE_COMPANY) {
                    $shareholderData['company_shareholding_ratio'] = $shareholder['company_shareholding_ratio'] ?? 0;
                }

                $data[] = $shareholderData;
            }
        }

        if (!empty($data)) {
            return self::insertAll($data);
        }

        return true;
    }

    /**
     * 软删除股东信息
     * @param int $dividendStoreInfoId 分红信息ID
     * @param int $shareholderType 股东类型（可选）
     * @return bool
     */
    public static function softDelete($dividendStoreInfoId, $shareholderType = null)
    {
        $where = [
            ['dividend_store_info_id', '=', $dividendStoreInfoId],
            ['is_delete', '=', 0]
        ];

        if (!is_null($shareholderType)) {
            $where[] = ['shareholder_type', '=', $shareholderType];
        }

        return self::where($where)->update([
            'is_delete' => 1,
            'delete_time' => time()
        ]);
    }

    /**
     * 验证持股比例
     * @param array $companyShareholders 公司股东数据
     * @param array $personalShareholders 个人股东数据
     * @return array [是否通过验证, 错误信息, 公司股东总比例, 个人股东总比例]
     */
    public static function validateRatio($companyShareholders = [], $personalShareholders = [])
    {
        $companyTotal = 0;
        $personalTotal = 0;

        // 计算公司股东持股比例总和
        if (!empty($companyShareholders)) {
            foreach ($companyShareholders as $shareholder) {
                if (!empty($shareholder['store_shareholding_ratio'])) {
                    $companyTotal += floatval($shareholder['store_shareholding_ratio']);
                }
            }
        }

        // 计算个人股东持股比例总和
        if (!empty($personalShareholders)) {
            foreach ($personalShareholders as $shareholder) {
                if (!empty($shareholder['store_shareholding_ratio'])) {
                    $personalTotal += floatval($shareholder['store_shareholding_ratio']);
                }
            }
        }

        $totalRatio = $companyTotal + $personalTotal;

        if ($totalRatio > 100) {
            return [false, '总持股比例不能超过100%，当前为' . number_format($totalRatio, 3) . '%', $companyTotal, $personalTotal];
        }

        if ($totalRatio < 100 && $totalRatio > 0) {
            return [false, '总持股比例未达到100%，当前为' . number_format($totalRatio, 3) . '%，请检查持股比例配置', $companyTotal, $personalTotal];
        }

        return [true, '', $companyTotal, $personalTotal];
    }

    /**
     * 获取股东统计信息
     * @param int $dividendStoreInfoId 分红信息ID
     * @return array
     */
    public static function getStatistics($dividendStoreInfoId)
    {
        $companyTotal = self::getTotalRatio($dividendStoreInfoId, self::TYPE_COMPANY);
        $personalTotal = self::getTotalRatio($dividendStoreInfoId, self::TYPE_PERSONAL);

        // 确保数值类型，避免number_format报错
        $companyTotal = is_numeric($companyTotal) ? floatval($companyTotal) : 0;
        $personalTotal = is_numeric($personalTotal) ? floatval($personalTotal) : 0;

        return [
            'company_total' => number_format($companyTotal, 2),
            'personal_total' => number_format($personalTotal, 2),
            'grand_total' => number_format($companyTotal + $personalTotal, 2),
            'company_count' => self::where('dividend_store_info_id', $dividendStoreInfoId)
                ->where('shareholder_type', self::TYPE_COMPANY)
                ->where('is_delete', 0)
                ->count(),
            'personal_count' => self::where('dividend_store_info_id', $dividendStoreInfoId)
                ->where('shareholder_type', self::TYPE_PERSONAL)
                ->where('is_delete', 0)
                ->count()
        ];
    }
}
