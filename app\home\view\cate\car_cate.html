{extend name="../../base/view/common/base" /}
<!-- 主体 -->
{block name="body"}
<div class="p-3">
    <table class="layui-hide" id="test" lay-filter="test"></table>
</div>
<script type="text/html" id="toolbarDemo">
  <div class="layui-btn-container">
  	<button class="layui-btn layui-btn-sm addNew" type="button">+ 添加车辆类型</button>
  </div>
</script>
{/block}
<!-- /主体 -->

<!-- 脚本 -->
{block name="script"}
	<script>
	const moduleInit = ['tool'];
	function gouguInit() {
		var table = layui.table, tool = layui.tool, form = layui.form;
		layui.pageTable = table.render({
			elem: '#test'
			,toolbar: '#toolbarDemo'
			,defaultToolbar: false
			,title:'车辆类型列表'
			,url: "/home/<USER>/car_cate"
			,page: false
			,cellMinWidth: 80
			,cols: [[
					{field:'id',width:80, title: 'ID号', align:'center'}
					,{field:'title',title: '车辆名称'}
					,{field:'name',title: '车牌号码'}
					,{field:'status', title: '状态',width:80,align:'center',templet: function(d){
						var html1='<span class="green">正常</span>';
						var html2='<span class="yellow">禁用</span>';
						if(d.status==1){
							return html1;
						}
						else{
							return html2;
						}
					}}
					,{width:100,title: '操作', align:'center',templet: function(d){
						var html='';
						var btn='<a class="layui-btn layui-btn-normal layui-btn-xs" lay-event="edit">编辑</a>';
						var btn1='<a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="disable">禁用</a>';
						var btn2='<a class="layui-btn layui-btn-xs" lay-event="open">启用</a>';
						if(d.status==1){
							html = '<div class="layui-btn-group">'+btn+btn1+'</div>';
						}
						else{
							html = '<div class="layui-btn-group">'+btn+btn2+'</div>';
						}
						return html;
					}}
				]]
			});
			
			table.on('tool(test)',function (obj) {
				if(obj.event === 'edit'){					
					addExpense(obj.data.id,obj.data.title,obj.data.name);
				}
				if(obj.event === 'disable'){
					layer.confirm('确定要禁用该车辆吗?', {icon: 3, title:'提示'}, function(index){
						let callback = function (e) {
							layer.msg(e.msg);
							if (e.code == 0) {
								layui.pageTable.reload();
							}
						}
						tool.post("/home/<USER>/car_cate_check", { id: obj.data.id,status: 0}, callback);
						layer.close(index);
					});
				}
				if(obj.event === 'open'){
					layer.confirm('确定要启用该车辆吗?', {icon: 3, title:'提示'}, function(index){
						let callback = function (e) {
							layer.msg(e.msg);
							if (e.code == 0) {
								layui.pageTable.reload();
							}
						}
						tool.post("/home/<USER>/car_cate_check", { id: obj.data.id,status: 1}, callback);
						layer.close(index);
					});
				}
			});
			
			$('body').on('click','.addNew',function(){
				addExpense(0,'','');	
			});
			
			function addExpense(id,title,name){
				var biaoti = '新增车辆';
				if(id>0){
					biaoti = '编辑车辆';
				}			
				
				layer.open({
					type: 1
					,title: biaoti
					,area: '368px;'
					,id: 'LAY_module' //设定一个id，防止重复弹出
					,btn: ['确定', '取消']
					,btnAlign: 'c'
					,content: '<div style="padding-top:15px;">\
								<div class="layui-form-item">\
								  <label class="layui-form-label">车辆名称</label>\
								  <div class="layui-input-inline">\
									<input type="hidden" name="id" value="'+id+'">\
									<input type="text" name="title" autocomplete="off" value="'+title+'" placeholder="请输入车辆名称" class="layui-input">\
								  </div>\
								</div>\
								<div class="layui-form-item">\
								  <label class="layui-form-label">车牌号码</label>\
								  <div class="layui-input-inline">\
									<input type="text" name="name" autocomplete="off" value="'+name+'" placeholder="请输入车牌号码" class="layui-input">\
								  </div>\
								</div>\
							  </div>'
					,yes: function(index){
						let id = $('#LAY_module').find('[name="id"]').val();
						let title = $('#LAY_module').find('[name="title"]').val();
						let name = $('#LAY_module').find('[name="name"]').val();
						let callback = function (e) {
							layer.msg(e.msg);
							if (e.code == 0) {
								layui.pageTable.reload();	
								layer.close(index);			
							}
						}
						tool.post("/home/<USER>/car_cate_add", {
							id: id,
							title: title,
							name: name
						}, callback);						
					}
					,btn2: function(){
						layer.closeAll();
					}
				});
			}
		}
	</script>
{/block}
<!-- /脚本 -->