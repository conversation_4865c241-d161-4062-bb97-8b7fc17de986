{extend name="../../base/view/common/base" /}

{block name="style"}
<style>
    /* 查看页面样式 - 参考编辑页面的卡片布局 */
    .layui-card {
        border: 1px solid #e6e6e6;
        border-radius: 4px;
        margin-bottom: 15px;
    }

    .layui-card-header {
        background-color: #f8f8f8;
        padding: 10px 15px;
        border-bottom: 1px solid #e6e6e6;
        font-weight: bold;
    }

    .layui-card-body {
        padding: 15px;
    }

    .layui-td-gray {
        background-color: #f5f5f5;
        font-weight: bold;
        text-align: left;
        vertical-align: middle;
        width: 140px;
        padding: 8px 12px;
    }

    /* 表格样式优化 */
    .layui-table {
        margin-bottom: 0;
    }

    .layui-table td {
        padding: 8px 12px;
        vertical-align: middle;
    }

    .layui-table th {
        background-color: #f8f8f8;
        font-weight: bold;
    }

    /* 页面内容区域 */
    .view-content {
        padding: 20px;
        height: 100%;
        box-sizing: border-box;
        overflow-y: auto;
    }

    /* 金额显示样式 - 参考门店分红明细表 */
    .amount-display {
        text-align: right;
        font-weight: bold;
        color: #4CAF50;
    }

    /* 次数显示样式 - 参考门店分红明细表 */
    .count-display {
        text-align: right;
        font-weight: bold;
        color: #2196F3;
    }

    /* 负数金额样式 */
    .negative-amount {
        color: #dc3545 !important;
    }

    /* 统计信息卡片 */
    .stats-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 20px;
        border-radius: 8px;
        margin-bottom: 20px;
        text-align: center;
    }

    .stats-card h3 {
        margin: 0;
        font-size: 24px;
        font-weight: bold;
    }

    .stats-card p {
        margin: 5px 0 0 0;
        opacity: 0.9;
    }

    /* 只读字段样式 */
    .readonly-value {
        background-color: #fff;
        color: #333;
        font-weight: normal;
    }
</style>
{/block}

{block name="body"}
<div class="view-content">
    <!-- 页面标题 -->
    <div style="margin-bottom: 20px;">
        <h3 style="margin: 0 0 15px 0; font-size: 18px; font-weight: bold; color: #333;">跨店结算汇总详情</h3>
    </div>

    <!-- 基础信息 -->
    <div class="layui-card">
        <div class="layui-card-header">基础信息</div>
        <div class="layui-card-body">
            <table class="layui-table">
                <tr>
                    <td class="layui-td-gray">汇总月份</td>
                    <td style="width: 200px;">
                        <span class="readonly-value" style="font-weight: bold;">{$detail.period}</span>
                    </td>
                    <td class="layui-td-gray">总对账金额</td>
                    <td style="width: 200px;">
                        <span class="readonly-value amount-display">{$detail.total_reconciliation_amount|number_format=2}</span>
                    </td>
                </tr>
                <tr>
                    <td class="layui-td-gray">门店</td>
                    <td>
                        <span class="readonly-value" style="font-weight: bold;">{$detail.store_name|default='未知门店'}</span>
                    </td>
                    <td class="layui-td-gray">门店类型</td>
                    <td>
                        <span class="readonly-value">{$detail.store_type}</span>
                    </td>
                </tr>
                <tr>
                    <td class="layui-td-gray">他店</td>
                    <td>
                        <span class="readonly-value" style="font-weight: bold;">{$detail.other_store_name|default='未知门店'}</span>
                    </td>
                    <td class="layui-td-gray">他店类型</td>
                    <td>
                        <span class="readonly-value">{$detail.other_store_type}</span>
                    </td>
                </tr>
            </table>
        </div>
    </div>

    <!-- 储值本金部分 -->
    <div class="layui-card">
        <div class="layui-card-header">储值本金部分</div>
        <div class="layui-card-body">
            <table class="layui-table">
                <tr>
                    <td class="layui-td-gray">本店耗他店本金</td>
                    <td style="width: 200px;">
                        <span class="readonly-value amount-display">{$detail.p_local_consume_foreign|number_format=2}</span>
                    </td>
                    <td class="layui-td-gray">本店回退他店本金</td>
                    <td style="width: 200px;">
                        <span class="readonly-value amount-display">{$detail.p_local_refund_foreign|number_format=2}</span>
                    </td>
                </tr>
                <tr>
                    <td class="layui-td-gray">他店耗本店本金</td>
                    <td>
                        <span class="readonly-value amount-display">{$detail.p_foreign_consume_local|number_format=2}</span>
                    </td>
                    <td class="layui-td-gray">他店回退本店本金</td>
                    <td>
                        <span class="readonly-value amount-display">{$detail.p_foreign_refund_local|number_format=2}</span>
                    </td>
                </tr>
                <tr>
                    <td class="layui-td-gray">本金合计</td>
                    <td>
                        <span class="readonly-value amount-display">{$detail.p_total_amount|number_format=2}</span>
                    </td>
                    <td class="layui-td-gray">本金对账金额</td>
                    <td>
                        <span class="readonly-value amount-display {if $detail.p_reconciliation_amount < 0}negative-amount{/if}">{$detail.p_reconciliation_amount|number_format=2}</span>
                    </td>
                </tr>
            </table>
        </div>
    </div>

    <!-- 储值赠金部分 -->
    <div class="layui-card">
        <div class="layui-card-header">储值赠金部分</div>
        <div class="layui-card-body">
            <table class="layui-table">
                <tr>
                    <td class="layui-td-gray">本店耗他店赠金</td>
                    <td style="width: 200px;">
                        <span class="readonly-value amount-display">{$detail.b_local_consume_foreign|number_format=2}</span>
                    </td>
                    <td class="layui-td-gray">本店回退他店赠金</td>
                    <td style="width: 200px;">
                        <span class="readonly-value amount-display">{$detail.b_local_refund_foreign|number_format=2}</span>
                    </td>
                </tr>
                <tr>
                    <td class="layui-td-gray">他店耗本店赠金</td>
                    <td>
                        <span class="readonly-value amount-display">{$detail.b_foreign_consume_local|number_format=2}</span>
                    </td>
                    <td class="layui-td-gray">他店回退本店赠金</td>
                    <td>
                        <span class="readonly-value amount-display">{$detail.b_foreign_refund_local|number_format=2}</span>
                    </td>
                </tr>
                <tr>
                    <td class="layui-td-gray">赠金合计</td>
                    <td>
                        <span class="readonly-value amount-display">{$detail.b_total_amount|number_format=2}</span>
                    </td>
                    <td class="layui-td-gray">赠金对账金额</td>
                    <td>
                        <span class="readonly-value amount-display {if $detail.b_reconciliation_amount < 0}negative-amount{/if}">{$detail.b_reconciliation_amount|number_format=2}</span>
                    </td>
                </tr>
            </table>
        </div>
    </div>

    <!-- 次卡部分 -->
    <div class="layui-card">
        <div class="layui-card-header">次卡部分</div>
        <div class="layui-card-body">
            <table class="layui-table">
                <tr>
                    <td class="layui-td-gray">本店耗他店卡(次数)</td>
                    <td style="width: 200px;">
                        <span class="readonly-value count-display">{$detail.cc_local_consume_foreign_count}</span>
                    </td>
                    <td class="layui-td-gray">本店回退他店卡(次数)</td>
                    <td style="width: 200px;">
                        <span class="readonly-value count-display">{$detail.cc_local_refund_foreign_count}</span>
                    </td>
                </tr>
                <tr>
                    <td class="layui-td-gray">他店耗本店卡(次数)</td>
                    <td>
                        <span class="readonly-value count-display">{$detail.cc_foreign_consume_local_count}</span>
                    </td>
                    <td class="layui-td-gray">他店回退本店卡(次数)</td>
                    <td>
                        <span class="readonly-value count-display">{$detail.cc_foreign_refund_local_count}</span>
                    </td>
                </tr>
                <tr>
                    <td class="layui-td-gray">本店升他店卡(金额)</td>
                    <td>
                        <span class="readonly-value amount-display">{$detail.cc_local_upgrade_foreign_amount|number_format=2}</span>
                    </td>
                    <td class="layui-td-gray">他店升本店卡(金额)</td>
                    <td>
                        <span class="readonly-value amount-display">{$detail.cc_foreign_upgrade_local_amount|number_format=2}</span>
                    </td>
                </tr>
                <tr>
                    <td class="layui-td-gray">次卡跨店金额合计</td>
                    <td>
                        <span class="readonly-value amount-display">{$detail.cc_total_amount|number_format=2}</span>
                    </td>
                    <td class="layui-td-gray">次卡对账金额</td>
                    <td>
                        <span class="readonly-value amount-display {if $detail.cc_reconciliation_amount < 0}negative-amount{/if}">{$detail.cc_reconciliation_amount|number_format=2}</span>
                    </td>
                </tr>
            </table>
        </div>
    </div>

    <!-- 网店核销部分 -->
    <div class="layui-card">
        <div class="layui-card-header">网店核销部分</div>
        <div class="layui-card-body">
            <table class="layui-table">
                <tr>
                    <td class="layui-td-gray">本店核销他店码(次数)</td>
                    <td style="width: 200px;">
                        <span class="readonly-value count-display">{$detail.ol_local_redeem_foreign_count}</span>
                    </td>
                    <td class="layui-td-gray">本店回退他店码(次数)</td>
                    <td style="width: 200px;">
                        <span class="readonly-value count-display">{$detail.ol_local_refund_foreign_count}</span>
                    </td>
                </tr>
                <tr>
                    <td class="layui-td-gray">他店核销本店码(次数)</td>
                    <td>
                        <span class="readonly-value count-display">{$detail.ol_foreign_redeem_local_count}</span>
                    </td>
                    <td class="layui-td-gray">他店回退本店码(次数)</td>
                    <td>
                        <span class="readonly-value count-display">{$detail.ol_foreign_refund_local_count}</span>
                    </td>
                </tr>
                <tr>
                    <td class="layui-td-gray">网店核销金额合计</td>
                    <td>
                        <span class="readonly-value amount-display">{$detail.ol_total_amount|number_format=2}</span>
                    </td>
                    <td class="layui-td-gray">网店核销对账金额</td>
                    <td>
                        <span class="readonly-value amount-display {if $detail.ol_reconciliation_amount < 0}negative-amount{/if}">{$detail.ol_reconciliation_amount|number_format=2}</span>
                    </td>
                </tr>
            </table>
        </div>
    </div>


</div>
{/block}

{block name="script"}
<script>
    const moduleInit = [];
    function gouguInit() {
        // 查看页面无需特殊初始化
    }
</script>
{/block}
