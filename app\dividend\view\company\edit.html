{extend name="../../base/view/common/base" /}

{block name="style"}
<style>
    /* 编辑页面样式 */
    .layui-card {
        border: 1px solid #e6e6e6;
        border-radius: 4px;
        margin-bottom: 15px;
    }

    .layui-card-header {
        background-color: #f8f8f8;
        padding: 10px 15px;
        border-bottom: 1px solid #e6e6e6;
        font-weight: bold;
    }

    .layui-card-body {
        padding: 15px;
    }

    .layui-td-gray {
        background-color: #f5f5f5;
        font-weight: bold;
        text-align: left;
        vertical-align: middle;
        width: 120px;
        padding-left: 12px;
    }

    /* 分红人表格样式 */
    #personTable .layui-input {
        height: 32px;
        line-height: 32px;
    }

    /* 表格样式优化 */
    .layui-table {
        margin-bottom: 0;
    }

    .layui-table td {
        padding: 8px;
        vertical-align: middle;
    }

    .layui-table th {
        background-color: #f8f8f8;
        font-weight: bold;
    }

    /* 必填项红色星号 */
    font {
        color: #FF6347;
        margin-left: 3px;
    }

    /* 页面内容区域 */
    .edit-content {
        padding: 20px;
        height: 100%;
        box-sizing: border-box;
        overflow-y: auto;
    }
</style>
{/block}

{block name="body"}
<div class="edit-content">
    <!-- 页面标题 -->
    <div style="margin-bottom: 20px;">
        <h3 style="margin: 0 0 15px 0; font-size: 18px; font-weight: bold; color: #333;">编辑公司股东分红表</h3>

        <!-- 重要提示 -->
        <div class="layui-alert layui-alert-warning" style="border: 2px solid #ff0000; border-left-width: 8px; border-radius: 6px; box-shadow: 0 2px 8px rgba(255,0,0,0.08); padding: 18px 20px; margin-bottom: 18px;">
            <h4 style="color: #d32f2f; font-weight: bold; margin-bottom: 10px;">重要提示*</h4>
            <p style="font-size: 14px; color: #ff0000; font-weight: bold; margin-bottom: 10px;">
                1. 此操作将影响公司股东分红表、分红清单数据，请务必谨慎操作！
            </p>
            <p style="font-size: 14px; color: #d32f2f; font-weight: bold; margin-bottom: 0;">
                2. 调整金额为正数表示扣减，负数表示增加，应付金额 = 金额 - 调整金额
            </p>
        </div>
    </div>

    <form class="layui-form" lay-filter="editForm" id="editForm">
        <input type="hidden" name="store_id" id="edit_store_id">
        <input type="hidden" name="period" id="edit_period">

        <!-- 门店基础数据 -->
        <div class="layui-card">
            <div class="layui-card-header">门店基础数据</div>
            <div class="layui-card-body">
                <table class="layui-table">
                    <tr>
                        <td class="layui-td-gray">门店名称</td>
                        <td style="width: 200px;">
                            <span id="edit_store_name" style="font-weight: bold;"></span>
                        </td>
                        <td class="layui-td-gray">统计周期</td>
                        <td style="width: 200px;">
                            <span id="edit_period_display" style="font-weight: bold;"></span>
                        </td>
                    </tr>
                    <tr>
                        <td class="layui-td-gray">分红利润</td>
                        <td>
                            <span id="edit_dividend_profit" style="font-weight: bold;"></span>
                        </td>
                        <td class="layui-td-gray">公司持股比例(%)</td>
                        <td>
                            <span id="edit_company_shareholding_ratio" style="font-weight: bold;"></span>
                        </td>
                    </tr>
                    <tr>
                        <td class="layui-td-gray">备注</td>
                        <td colspan="3">
                            <textarea name="remark" id="edit_remark" class="layui-textarea" placeholder="请输入备注信息"></textarea>
                        </td>
                    </tr>
                </table>
            </div>
        </div>

        <!-- 公司股东分红详情 -->
        <div class="layui-card">
            <div class="layui-card-header">公司股东分红详情</div>
            <div class="layui-card-body">
                <table class="layui-table" id="personTable">
                    <thead>
                        <tr>
                            <th style="width: 12%;">分红人<font>*</font></th>
                            <th style="width: 12%;">持股比例(%)<font>*</font></th>
                            <th style="width: 12%;">实际持股(%)<font>*</font></th>
                            <th style="width: 15%;">金额</th>
                            <th style="width: 15%;">调整金额</th>
                            <th style="width: 15%;">应付金额
                            <span style="color: #999; margin-left: 10px; font-size: 12px;">
                                (金额 - 调整金额)
                            </span>
                            </th>
                            <th style="width: 19%;">备注</th>
                        </tr>
                    </thead>
                    <tbody id="personTableBody">
                        <!-- 动态生成分红人行 -->
                    </tbody>
                </table>
            </div>
        </div>

        <!-- 提交按钮 -->
        <div style="margin-top: 15px;">
            <button class="layui-btn layui-btn-normal" lay-submit lay-filter="editSubmit">保存修改</button>
            <button type="button" class="layui-btn layui-btn-primary" id="cancelEditBtn">取消</button>
        </div>
    </form>
</div>
{/block}

{block name="script"}
<script>
    // 限制输入框小数位数的函数
    function limitDecimalPlaces(input, maxDecimalPlaces) {
        var value = input.value;
        if (value.indexOf('.') !== -1) {
            var parts = value.split('.');
            if (parts[1] && parts[1].length > maxDecimalPlaces) {
                input.value = parts[0] + '.' + parts[1].substring(0, maxDecimalPlaces);
            }
        }
    }

    // 当调整金额输入框变化时自动计算应付金额（全局函数）
    function calculatePayableForInput(input) {
        var $row = $(input).closest('tr');
        var amount = parseFloat($row.find('.amount-hidden').val()) || 0;
        var adjustmentAmount = parseFloat($row.find('.adjustment-amount').val()) || 0;
        var payableAmount = amount - adjustmentAmount;

        // 更新应付金额输入框的值
        $row.find('.payable-amount').val(payableAmount.toFixed(2));

        // 更新颜色
        updatePayableAmountColor($row.find('.payable-amount')[0]);
    }

    // 更新应付金额的颜色（全局函数）
    function updatePayableAmountColor(input) {
        var value = parseFloat($(input).val()) || 0;
        var color = value > 0 ? '#4CAF50' : (value < 0 ? '#F44336' : '#000000');
        $(input).css('color', color);
    }

    const moduleInit = ['tool'];

    function gouguInit() {
        var form = layui.form, tool = layui.tool;

        // 获取URL参数
        function getUrlParam(name) {
            var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)");
            var r = window.location.search.substr(1).match(reg);
            if (r != null) return unescape(r[2]);
            return null;
        }

        var storeId = getUrlParam('store_id');
        var period = getUrlParam('period');

        if (!storeId || !period) {
            layer.msg('参数错误', {icon: 2});
            return;
        }

        // 加载门店数据
        loadStoreData(storeId, period);

        // 获取门店详细数据
        function loadStoreData(storeId, period) {
            layer.load(1);
            $.ajax({
                url: '/dividend/company/getStoreDetail',
                type: 'post',
                dataType: 'json',
                data: {
                    store_id: storeId,
                    period: period
                },
                success: function(res) {
                    layer.closeAll('loading');
                    if (res.code == 0) {
                        fillFormData(res.data);
                    } else {
                        layer.msg(res.msg, {icon: 2});
                    }
                },
                error: function() {
                    layer.closeAll('loading');
                    layer.msg('获取门店数据失败', {icon: 2});
                }
            });
        }

        // 填充表单数据
        function fillFormData(data) {
            // 填充基础信息
            $('#edit_store_id').val(data.store_id);
            $('#edit_period').val(data.period);
            $('#edit_store_name').text(data.store_name);
            $('#edit_period_display').text(data.period);
            $('#edit_dividend_profit').text(data.dividend_profit + ' 元');
            $('#edit_company_shareholding_ratio').text(data.company_shareholding_ratio);
            $('#edit_remark').val(data.remark || '');

            // 填充分红人数据
            fillPersonData(data.persons || []);

            // 初始化计算所有应付金额
            calculateAllPayableAmount();
        }

        // 填充分红人数据
        function fillPersonData(persons) {
            var tbody = $('#personTableBody');
            tbody.empty();

            if (persons && persons.length > 0) {
                $.each(persons, function(index, person) {
                    addPersonRow(person, index);
                });
            } else {
                tbody.append('<tr class="no-data"><td colspan="7" style="text-align: center; color: #999;">暂无分红人信息</td></tr>');
            }
        }

        // 添加分红人行
        function addPersonRow(data, index) {
            data = data || {};
            var html = '<tr>' +
                '<td>' +
                    '<span class="layui-input" style="border: none; background: #f5f5f5; color: #666; cursor: not-allowed;">' +
                    (data.shareholder_name || '') + '</span>' +
                    '<input type="hidden" name="persons[' + index + '][shareholder_id]" ' +
                    'value="' + (data.shareholder_id || '0') + '">' +
                    '<input type="hidden" name="persons[' + index + '][shareholder_name]" ' +
                    'value="' + (data.shareholder_name || '') + '">' +
                '</td>' +
                '<td>' +
                    '<span class="layui-input" style="border: none; background: #f5f5f5; color: #666; cursor: not-allowed;">' +
                    (data.shareholding_ratio || '') + '</span>' +
                    '<input type="hidden" name="persons[' + index + '][shareholding_ratio]" ' +
                    'value="' + (data.shareholding_ratio || '') + '">' +
                '</td>' +
                '<td>' +
                    '<span class="layui-input" style="border: none; background: #f5f5f5; color: #666; cursor: not-allowed;">' +
                    (data.actual_shareholding || '') + '</span>' +
                    '<input type="hidden" name="persons[' + index + '][actual_shareholding]" ' +
                    'value="' + (data.actual_shareholding || '') + '">' +
                '</td>' +
                '<td>' +
                    '<span class="layui-input amount-display" style="border: none; background: #f5f5f5; color: #666; cursor: not-allowed;">' +
                    (data.amount || '0.00') + '</span>' +
                    '<input type="hidden" name="persons[' + index + '][amount]" ' +
                    'class="amount-hidden" value="' + (data.amount || '0.00') + '">' +
                '</td>' +
                '<td>' +
                    '<input type="number" name="persons[' + index + '][adjustment_amount]" ' +
                    'class="layui-input adjustment-amount" placeholder="0.00" ' +
                    'value="' + (data.adjustment_amount || '0.00') + '" step="0.01" ' +
                    'oninput="limitDecimalPlaces(this, 2); calculatePayableForInput(this)">' +
                '</td>' +
                '<td>' +
                    '<input type="number" name="persons[' + index + '][payable_amount]" ' +
                    'class="layui-input payable-amount" placeholder="0.00" ' +
                    'value="' + (data.payable_amount || '0.00') + '" step="0.01" ' +
                    'style="background: #f0f8ff; font-weight: bold;" ' +
                    'oninput="limitDecimalPlaces(this, 2); updatePayableAmountColor(this)">' +
                '</td>' +
                '<td>' +
                    '<input type="text" name="persons[' + index + '][remark]" ' +
                    'class="layui-input" placeholder="备注" ' +
                    'value="' + (data.remark || '') + '">' +
                '</td>' +
                '</tr>';

            $('#personTableBody .no-data').remove();
            $('#personTableBody').append(html);

            // 计算应付金额并设置颜色
            calculatePayableForRow($('#personTableBody tr').last());

            // 为已有的应付金额设置颜色
            $('#personTableBody tr').last().find('.payable-amount').each(function() {
                updatePayableAmountColor(this);
            });
        }

        // 计算单行应付金额（自动计算模式）
        function calculatePayableForRow($row) {
            var amount = parseFloat($row.find('.amount-hidden').val()) || 0;
            var adjustmentAmount = parseFloat($row.find('.adjustment-amount').val()) || 0;
            var payableAmount = amount - adjustmentAmount;

            // 更新应付金额输入框的值
            $row.find('.payable-amount').val(payableAmount.toFixed(2));

            // 更新颜色
            updatePayableAmountColor($row.find('.payable-amount')[0]);
        }

        // 计算所有行的应付金额
        function calculateAllPayableAmount() {
            $('#personTableBody tr:not(.no-data)').each(function() {
                calculatePayableForRow($(this));
                // 为每行的应付金额设置颜色
                $(this).find('.payable-amount').each(function() {
                    updatePayableAmountColor(this);
                });
            });
        }

        // 监听表单提交
        form.on('submit(editSubmit)', function(data) {
            // 验证是否有分红人数据
            var personCount = $('#personTableBody tr:not(.no-data)').length;
            if (personCount === 0) {
                layer.msg('请至少有一个分红人信息', {icon: 2});
                return false;
            }

            // 验证分红人姓名不能为空
            var hasEmptyName = false;
            $('#personTableBody tr:not(.no-data)').each(function() {
                var $row = $(this);
                var shareholderName = $row.find('input[type="hidden"][name*="[shareholder_name]"]').val().trim();
                if (!shareholderName) {
                    hasEmptyName = true;
                    return false;
                }
            });

            if (hasEmptyName) {
                layer.msg('分红人姓名不能为空', {icon: 2});
                return false;
            }

            // 提交数据
            layer.load(1);
            $.ajax({
                url: '/dividend/company/updateStoreDetail',
                type: 'post',
                dataType: 'json',
                data: data.field,
                success: function(res) {
                    layer.closeAll('loading');
                    if (res.code == 0) {
                        layer.msg('保存成功', {icon: 1});
                        // 关闭抽屉并刷新父页面
                        tool.sideClose(1000);
                    } else {
                        layer.msg(res.msg, {icon: 2});
                    }
                },
                error: function() {
                    layer.closeAll('loading');
                    layer.msg('保存失败', {icon: 2});
                }
            });

            return false;
        });

        // 取消编辑按钮事件
        $(document).on('click', '#cancelEditBtn', function() {
            tool.sideClose();
        });
    }
</script>
{/block}
