<?php

namespace app\mobile\controller;

use app\callback\HttpUtils;
use app\callback\Prpcrypt;
use app\callback\SHA1;
use app\callback\WXBizMsgCrypt;
use DOMDocument;
use PhpOffice\PhpSpreadsheet\Cell\Coordinate;
use PhpOffice\PhpSpreadsheet\IOFactory;
use think\facade\Db;
use think\facade\Request;

class Test
{

    protected $token;
    protected $encodingAesKey;
    protected $receiveId;

    public function __construct()
    {
        //通讯录
        $this->token = 'QkWJLSte40St4mI2DEeSUcr1m2KC8';
        $this->encodingAesKey = '0i1etqWUuyxxrG2nwKkSqm7yocaYnktFv5DKdox8iHE';
        $this->receiveId = 'wwf87ba13d7d2bff11';

//        $this->receiveId = 'wwe89f37f6e6cd3c86';
    }


    public function index11()
    {
        $require = $_REQUEST;
        $request = Request::instance();
        $param = $request->param();

        $sVerifyMsgSig = $param['msg_signature'];
        $sVerifyTimeStamp = $param['timestamp'];
        $sVerifyNonce = $param['nonce'];
        $sVerifyEchoStr = $param['echostr'];

        foreach ($param as $key => $value) {
            if ($key == 'timestamp') {
                Db::name('data')->insert(['key' => $key, 'value' => date("Y-m-d H:i:s", $value)]);
            } else {
                Db::name('data')->insert(['key' => $key, 'value' => $value]);
            }
        }

        // 需要返回的明文
        $sEchoStr = "";

        $wxcpt = new WXBizMsgCrypt($this->token, $this->encodingAesKey, $this->receiveId);
        $errCode = $wxcpt->VerifyURL($sVerifyMsgSig, $sVerifyTimeStamp, $sVerifyNonce, $sVerifyEchoStr, $sEchoStr);
        if ($errCode == 0) {
            var_dump($sEchoStr);
            //
            // 验证URL成功，将sEchoStr返回
            // HttpUtils.SetResponce($sEchoStr);
        } else {
            print("ERR: " . $errCode . "\n\n");
        }

    }

    function index()
    {
        //TODO:配置接收消息的应用相关信息
        $sReqMsgSig = $_GET["msg_signature"];
        $sReqTimeStamp = $_GET["timestamp"];
        $sReqNonce = $_GET["nonce"];
        $sReqData = file_get_contents("php://input");

        $sMsg = "";  // 解析之后的明文
        $wxcpt = new WXBizMsgCrypt($this->token, $this->encodingAesKey, $this->receiveId);
        $errCode = $wxcpt->DecryptMsg($sReqMsgSig, $sReqTimeStamp, $sReqNonce, $sReqData, $sMsg);

        if ($errCode == 0) {

            $param = Request::instance()->param();

            foreach ($param as $key => $value) {
                if ($key == 'timestamp') {
                    Db::name('data')->insert(['key' => $key, 'value' => date("Y-m-d H:i:s", $value)]);
                } else {
                    Db::name('data')->insert(['key' => $key, 'value' => $value]);
                }
            }
            Db::name('data')->insert(['key' => 'test', 'value' => $sMsg]);
            // 解密成功，sMsg即为xml格式的明文
            $xml = new DOMDocument('1.0', 'utf-8');
            $xml->loadXML($sMsg);

            $ChangeType = $xml->getElementsByTagName('ChangeType')->item(0)->nodeValue;
            $DepId = $xml->getElementsByTagName('Id')->item(0)->nodeValue;
            $ParentId = $xml->getElementsByTagName('ParentId')->item(0)->nodeValue;
            //TODO ... 业务逻辑
            Db::name('data')->insert(['key' => 'test', 'value' => '1']);
            Db::name('data')->insertAll(
                [
                    ['key' => 'ChangeType', 'value' => $ChangeType],
                    ['key' => 'DepId', 'value' => $DepId],
                    ['key' => 'ParentId', 'value' => $ParentId],
                ]
            );
            Db::name('data')->insert(['key' => 'test', 'value' => '2']);

        } else {
            print("ERR: " . $errCode . "\n\n");
        }
    }


    function tage()
    {
        $sMsg = '<xml><ToUserName><![CDATA[wwf87ba13d7d2bff11]]></ToUserName><FromUserName><![CDATA[sys]]></FromUserName><CreateTime>1709617794</CreateTime><MsgType><![CDATA[event]]></MsgType><Event><![CDATA[change_contact]]></Event><ChangeType><![CDATA[create_party]]></ChangeType><Id>9</Id><ParentId>1</ParentId></xml>';
        $xml = new DOMDocument('1.0', 'utf-8');
        $xml->loadXML($sMsg);
        $ChangeType = $xml->getElementsByTagName('ChangeType')->item(0)->nodeValue;
        $ChangeType = $xml->getElementsByTagName('Id')->item(0)->nodeValue;
        var_dump($ChangeType);

    }


    function iindex()
    {
        $admin = DB::name("admin")->select();

        foreach ($admin as $k => $v) {
            $position = DB::name("position")->where(['id' => $v['position_id']])->find();
            if (empty($position)) {
                DB::name("admin")->where(['id' => $v['id']])->update(['position_id' => 85]);
            }
        }


        //$list = Db::name("position_group")->where('pid', '>=', 6)->update(['group_id' => 3]);


    }


    /***
     * @return void
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * 门店统计 门店名称表
     */
    public function import()
    {
        $excelReader = IOFactory::load('D:\zzt\s.xlsx');
        $sheets = $excelReader->getAllSheets();
        $rowCount = $sheets[0]->getHighestRow();
        $columnCount = $sheets[0]->getHighestColumn();
        $dataArr = $sheets[0]->rangeToArray('A1:' . $columnCount . $rowCount);

        foreach ($dataArr as $k => $v) {
            $result = str_replace('仲正堂', "", $v[0]);
            $result = str_replace('路', "", $result);
            $result = str_replace('店', "", $result);

            $detail = [];

            $dep = DB::name("department")->where([['title', 'like', '%' . $result . '%']])->find();
            $detail['did'] = 0;
            $detail['dname'] = $v[0];
            if (!empty($dep)) {
                $detail['did'] = $dep['id'];
                $detail['dname'] = $dep['title'];
            } else {
                var_dump($result);
            }

            $detail['aid'] = 0;
            $detail['aname'] = empty($v[1]) ? '' : $v[1];
            if (!empty($v[1])) {
                $admin = DB::name("admin")->where(['name' => $v[1]])->find();
                if (!empty($admin)) {
                    $detail['aid'] = $admin['id'];
                    $detail['aname'] = $admin['name'];
                }
            }

            $detail['create_time'] = time();
            $detail['amount'] = $v[2];
            $detail['nstatus'] = $v[3] == '是' ? 1 : 0;
            $detail['rstatus'] = $v[4] == '是' ? 1 : 0;


            $store = Db::name("store")->where(['did' => $detail['did']])->find();

            if (!empty($store)) {
                $re = Db::name("store")->where(['id' => $store['id']])->update($detail);
            } else {
                $re = Db::name("store")->insertGetId($detail);
            }


        }


    }

    /***
     * @return void
     * 更新通讯录
     */
    public function addressBook()
    {
        set_time_limit(0);
        $excelReader = IOFactory::load('D:\zzt\仲正堂通讯录.xlsx');
        $sheets = $excelReader->getAllSheets();
        $rowCount = $sheets[0]->getHighestRow();
        $columnCount = $sheets[0]->getHighestColumn();
        $dataArr = $sheets[0]->rangeToArray('A1:' . $columnCount . $rowCount);

        $update_time = time();

        foreach ($dataArr as $k => $v) {

            if ($k < 10) continue;
            $data = array();

            $mobile = !empty($v[6]) ? $v[6] : $v[7];
            $email = !empty($v[8]) ? $v[8] : $v[10];

            $admin = Db::name("admin")->where(['mobile' => $mobile])->find();

            $data['username'] = !empty($mobile) ? $mobile : strtolower($v[1]); //
            $data['pwd'] = !empty($admin) ? $admin['pwd'] : "52dab9db1f7d480b20bca9559bf64c01"; //
            $data['salt'] = !empty($admin) ? $admin['salt'] : "svm5wu27kgdpet031z6x"; //
            $data['wx_account'] = $v[1]; //
            $data['name'] = $v[0]; //
            $data['mobile'] = $mobile; //
            $data['sex'] = ($v[5] == '男' ? 1 : 0); //
            $data['nickname'] = $v[2]; //
            $data['email'] = $email; //
            $data['thumb'] = "/static/home/<USER>/icon.png"; //
            $data['theme'] = "white"; //
            $data['is_qw'] = "1"; //
            $data['update_time'] = $update_time; //
            $data['create_time'] = time(); //


            $explode = explode(";", $v[4]);
            $did = array();

            if (empty($admin) || empty($mobile)) {
                $admin = Db::name("admin")->where(['name' => $v[0]])->find();
            }

            foreach ($explode as $exk => $exv) {
                $e_explode = explode("/", $exv);
                $dname = end($e_explode);

                $dname = str_replace('路', "", $dname);
                $dname = str_replace('店', "", $dname);

                $department = Db::name("department")->where([['title', 'like', '%' . $dname . '%']])->find();
                if (!empty($department)) {
                    $did[] = $department['id'];
                }

                //将前台预约微信添加到对应部门下
                if ($dname == "前台预约微信") {
                    $all_dep = Db::name("department")->where(['remark' => '门店'])->select();
                    foreach ($all_dep as $dk => $dv) {
                        $ddname = str_replace('路', "", $dv['title']);
                        $ddname = str_replace('店', "", $ddname);

                        if ($ddname == "莘朱") {
                            $ddname = "莘庄";
                        } else if ($ddname == "金桥国际") {
                            $ddname = "金桥";
                        }

                        if (stripos($data['name'], $ddname) !== false) {
                            $did[] = $dv['id'];
                            break;
                        }
                    }
                }
            }

            $data['did'] = implode(",", $did);

            if (strpos($v[3], "前台") !== false) {
                $data['position_id'] = "85";
            } else {
                $position = Db::name("position")->where(['title' => $v[3]])->find();
                if (!empty($position)) {
                    $data['position_id'] = $position['id'];
                }
            }

            if ($v[13] == '禁用') {
                $data['status'] = 2;
                $data['res_date'] = date("Y-m-d");
            } else {
                $data['status'] = 1;
            }

            if (empty($admin)) {
                $data['entry_time'] = time(); //
                var_dump($data['name']);
                var_dump('</br>');
                Db::name("admin")->insertGetId($data);
            } else {
                if ($admin['status'] != 1) {
                    continue;
                }
                unset($data['did']);
                Db::name("admin")->where(['id' => $admin['id']])->update($data);
            }

        }

        //是否有更改
//        $admin = Db::name("admin")->where([['update_time', '<>', $update_time], ['is_qw', '=', 1], ['status', '=', 1]])
//            ->field("id")->select()->toArray();
//        foreach ($admin as $k => $v) {
//            Db::name("admin")->where(['id' => $v['id']])->update(['status' => 2, 'res_date' => date("Y-m-d")]);
//        }

    }


    public function exceldaoru()
    {
        $excelReader = IOFactory::load('D:\zzt\12月对账外.xlsx');
        $sheets = $excelReader->getAllSheets();
        $rowCount = $sheets[0]->getHighestRow();
        $columnCount = $sheets[0]->getHighestColumn();
        $dataArr = $sheets[0]->rangeToArray('A3:' . $columnCount . $rowCount);

        foreach ($dataArr as $k => $v) {
            $admin = Db::name("admin")->where([['name', 'like', "%{$v[1]}%"]])->find();

            $sdate = '2024-12';

            if (empty($admin)) {
                var_dump($v[1]);
                continue;
            }

            $store_salary = Db::name("store_salary")->where([
                'date_time' => strtotime($sdate),
                'aid' => $admin['id']
            ])->find();

            if (empty($store_salary)) {
                var_dump($v[1]);
                continue;
            }

            $dname = str_replace('路', "", $v[0]);
            $dname = str_replace('店', "", $dname);
            $dname = str_replace('东', "", $dname);
            $store = Db::name("department")->where([['title', 'like', "%$dname%"]])->find();

            if (empty($store)) {
                var_dump($v[0]);
                continue;
            }

            $param = [
                'aid' => $admin['id'],
                'aname' => $admin['name'],

                'did' => $store['id'],
                'dname' => $store['title'],

                'payable_salary' => $v[6], //应发工资
                'wh_pension' => $v[7], //代扣养老
                'wh_medical' => $v[8], //代扣医疗
                'wh_unemployed' => $v[9], //代扣失业

                'wh_accumulation_fund' => empty($v[11]) ? 0 :$v[11], //代扣公积金
                'income_tax' => empty($v[12]) ? 0 :$v[12], //代扣个人所得税

                'real_salary' => $v[13], //实发工资
                'sdate' => strtotime($sdate),
                'sdate_date' => "$sdate-01 00:00:00",
            ];

            Db::name("store_salary_external")->insertGetId($param);
        }


    }


    public function exceldaoru2()
    {
        $excelReader = IOFactory::load('D:\zzt\社保人员.xlsx');
        $sheets = $excelReader->getAllSheets();
        $rowCount = $sheets[0]->getHighestRow();
        $columnCount = $sheets[0]->getHighestColumn();
        $dataArr = $sheets[0]->rangeToArray('A3:' . $columnCount . $rowCount);

        foreach ($dataArr as $k => $v) {

            if (empty($v[1])) {
                var_dump($v[1]);
                continue;
            }

            $admin = Db::name("admin")->where([['name', 'like', "%{$v[1]}%"]])->find();

            if (empty($admin)) {
                var_dump($v[1]);
                continue;
            }

            $dname = str_replace('路', "", $v[0]);
            $dname = str_replace('店', "", $dname);
            $store = Db::name("department")->where([['title', 'like', "%$dname%"]])->find();

            if (empty($store)) {
                var_dump($v[0]);
                continue;
            }

            $param = [
                'pay_type' => 1,
                'pay_type_did' => $store['id'],
            ];

            $re = Db::name("admin")->where(['id' => $admin['id']])->update($param);

            if ($re){
                var_dump($v[1]);
            }
        }


    }

    public function exceldaoru3()
    {
        $excelReader = IOFactory::load('D:\zzt\24年门店分红清单.xlsx');
        $sheets = $excelReader->getAllSheets();
        $rowCount = $sheets[0]->getHighestRow();
        $columnCount = $sheets[0]->getHighestColumn();
        $dataArr = $sheets[0]->rangeToArray('A1:' . $columnCount . $rowCount);

        $aid = array();
        foreach ($dataArr as $k => $v) {

            $result = str_replace('仲正堂', "", $v[1]);
            $result = str_replace('路', "", $result);
            $result = str_replace('店', "", $result);

            $dep = DB::name("department")->where([['title', 'like', '%' . $result . '%']])->find();

            $admin = Db::name("admin")->where([['name', 'like', "%{$v[2]}%"]])->find();

            Db::name("store")->where(['did' => $dep['id']])->update(['holder_id' => $admin['id']]);


        }
    }

    public function approve()
    {
        $flow_detail = Db::name("flow")->where(['name' => '新店开业'])->find();
        $flow = unserialize($flow_detail['flow_list']);


        $data = array(["flow_type" => "3", "flow_uids" => "1236", "flow_title" => "设计交底", "id" => "1236"]);

        $insertIndex = 10; // 要插入的位置索引
        array_splice($flow, $insertIndex, 0, $data);

        var_dump($flow);

    }


    public function store_()
    {
        $excelReader = IOFactory::load('D:\zzt\2月业绩指标.xlsx');
        $sheets = $excelReader->getAllSheets();
        $rowCount = $sheets[0]->getHighestRow();
        $columnCount = $sheets[0]->getHighestColumn();
        $dataArr = $sheets[0]->rangeToArray('A1:' . $columnCount . $rowCount);

        $aid = array();
        foreach ($dataArr as $k => $v) {

            $result = str_replace('仲正堂', "", $v[0]);
            $result = str_replace('路', "", $result);
            $result = str_replace('店', "", $result);

            $target = $v[1];
            $grade = $v[2];

            $dep = DB::name("department")->where([['title', 'like', '%' . $result . '%']])->find();

            $admin = Db::name("admin")->where([['name', 'like', "%{$v[2]}%"]])->find();

            Db::name("store")->where([
                'did' => $dep['id'],
                'sdate' => '2025-02'
            ])->update(['target_amount' => $target , 'grade' => $grade]);


        }
    }


    public function contract_link()
    {
        $excelReader = IOFactory::load('D:\zzt\合同链接.xlsx');
        $sheets = $excelReader->getAllSheets();
        $rowCount = $sheets[0]->getHighestRow();
        $columnCount = $sheets[0]->getHighestColumn();
        $dataArr = $sheets[0]->rangeToArray('A2:' . $columnCount . $rowCount);

        $aid = array();
        $count = 0;
        foreach ($dataArr as $k => $v) {

            $admin = Db::name("admin")->where([['name', 'like', "%{$v[1]}%"]])->find();


            if (!empty($admin)){
                Db::name("admin_expand")->where(['id' => $admin['id']])->update([
                    'contract_link' => $v[2]
                ]);

                $count++;
            }else{
                dump($v[1]);
            }
        }

        var_dump(count($dataArr));
        var_dump($count);

    }

    public function admin_mxy()
    {
        $excelReader = IOFactory::load('D:\zzt\员工统计-数据统计.xlsx');
        $sheets = $excelReader->getAllSheets();
        $rowCount = $sheets[0]->getHighestRow();
        $columnCount = $sheets[0]->getHighestColumn();
        $dataArr = $sheets[0]->rangeToArray('A2:' . $columnCount . $rowCount);

        $aid = array();
        $count = 0;
        foreach ($dataArr as $k => $v) {

            $admin = Db::name("admin")->where([['name', '=', "$v[0]"]])->find();

            if (!empty($admin)){
                Db::name("admin_mxy")->where(['id' => $admin['id']])->insert([
                    'aid' => $admin['id'],
                    'myx_id' => $v[1],
                    'create_time' => date("Y-m-d H:i:s"),
                    'update_time' => date("Y-m-d H:i:s"),
                ]);

                $count++;
            }else{
                dump($v[1]);
            }
        }

        var_dump(count($dataArr));
        var_dump($count);

    }
}





