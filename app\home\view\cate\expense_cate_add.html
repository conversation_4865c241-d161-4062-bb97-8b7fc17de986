{extend name="../../base/view/common/base" /}
<!-- 主体 -->
{block name="body"}
<form class="layui-form p-4">
	<h3 class="pb-3">费用类型编辑</h3>
	<table class="layui-table layui-table-form">
		<tr>
			<td class="layui-td-gray-2">费用类型名称<font>*</font></td>
			<td>
				<input type="text" name="name" lay-verify="required"
					   placeholder="请填写费用类型名称"
					   lay-reqText="请填写费用类型名称" autocomplete="off" class="layui-input" {if condition="!empty($details)"} value="{$details.name}" {/if}>
			</td>
			<td class="layui-td-gray-2">费用类型归属<font>*</font></td>
			<td>
				<select name="type">
					<option value="1" {if condition="(!empty($details) && ($details.type == '1'))"} selected {/if} >总部</option>
					<option value="2" {if condition="(!empty($details) && ($details.type == '2'))"} selected {/if} >培训部</option>
					<option value="3" {if condition="(!empty($details) && ($details.type == '3'))"} selected {/if} >门店</option>
				</select>
			</td>
		</tr>
		<tr>
			<td class="layui-td-gray-2">对账类型名称<font>*</font></td>
			<td colspan="3">
				<select name="type_id" lay-search="">
					{volist name=":get_type('store_bill','14',1)" id="vo"}
					<option value="{$vo.id}" {if condition="(!empty($details) && ($details.type_id == $vo.id))"} selected {/if}>{$vo.name}</option>
					{/volist}
				</select>
			</td>
		</tr>
	</table>

	<div class="py-3">
		<button class="layui-btn layui-btn-normal" lay-submit="" lay-filter="webform">立即提交</button>
		<button type="reset" class="layui-btn layui-btn-primary">重置</button>
	</div>
</form>
<style>
	td{
		text-align: center;
	}
</style>
{/block}
<!-- /主体 -->

<!-- 脚本 -->
{block name="script"}
<script type="text/html" id="TPL-laydate-demo">
	<input class="layui-input laydate-demo" placeholder="选择日期" value="{{= d.sdate || '' }}">
</script>
<script>
	const moduleInit = ['tool'];
	function gouguInit() {
		var form = layui.form, tool = layui.tool, laydate = layui.laydate, upload = layui.upload, table = layui.table;

		let now = new Date();
		let year = now.getFullYear(); // 获取当前年份
		let month = now.getMonth() + 1; // 获取当前月份，注意getMonth()返回的是0-11，所以需要加1
		if (month < 10){
			month = `0${month}`
		}
		let day = now.getDate() - 1;
		if (day < 10){
			day = `0${day}`
		}
		let sdate = `${year}-${month}-${day}`

		//日期选择
		laydate.render({
			elem: '#sdate',
			value: `${sdate}`,
			max:-1,
			showBottom: false
		});

		//监听提交
		form.on('submit(webform)', function (data) {
			var loadIndex = layer.load(0);
			$.ajax({
				url: "",
				data: data.field,
				type: "post",
				success: function (e) {
					if (e.code == 0){
						layer.msg(e.msg);
						tool.sideClose(1000);
						layer.close(loadIndex)
					}
				},
				error : function (e){
					layer.close(loadIndex)
				}
			})



			return false;
		});


		var url = document.location.toString();
		var arrUrl = url.split("?");
		var para = arrUrl[1];
		console.log(para);

		table.render({
			elem: '#ID-table-demo-editable',
			url: `/store/storebusiness/add?${para}`, // 此处为静态模拟数据，实际使用时需换成真实接口
			page: false,
			//,editTrigger: 'dblclick' // 触发编辑的事件类型（默认 click ）。 v2.7.0 新增，之前版本固定为单击触发
			css: [
				// 对开启了编辑的单元格追加样式
				'.layui-table-view td[data-edit]{color: #16B777;}'
			].join(''),
			cols: [[
					{field: 'sdate', title: '日期', width: 100, fixed: true, rowspan: 4,align:'center'},
					{field: 'aname', title: '老师姓名', width: 100, fixed: true, rowspan: 4,align:'center'},
					{field: 'project_268', title: '268', width: 100, rowspan: 4,align:'center',edit: true},
					{field: 'guasha', title: '刮痧拔罐', width: 100, rowspan: 4,align:'center',edit: true},
					{field: 'dianzhong', title: '点钟数', width: 100, rowspan: 4,align:'center',edit: true},
					{field: 'haoping', title: '好评数', width: 100, rowspan: 4,align:'center',edit: true},
					{field: 'sanke', title: '散客数', width: 100, rowspan: 4,align:'center',edit: true},
					{field: 'sanke2', title: '散客二次预约数', width: 100, rowspan: 4,align:'center',edit: true,hide:true},
					{field: 'sanke_yj', title: '散客业绩', width: 100, rowspan: 4,align:'center',edit: true},
					{field: 'sanke_kdj', title: '散客客单价', width: 100, rowspan: 4,align:'center'},
					{field: 'deal_num', title: '成交人数', width: 100, rowspan: 4,align:'center'},
					{field: 'sanke_deal_rate', title: '散客成交率%', width: 100, rowspan: 4,align:'center'},
					{field: 'service_num', title: '服务总人数', width: 100, rowspan: 4,align:'center'},
					{title: '会员卡', width: 100, colspan: 10,align:'center'},
					{field: 'total_yj', title: '总业绩', width: 100, rowspan: 4,align:'center'},
					{field: 'total_kdj', title: '总客单价', width: 100, rowspan: 4,align:'center'},
					{field: 'haoka_yj', title: '耗卡业绩', width: 100, rowspan: 4,align:'center'},
				],
				[
					{title: '次卡', width: 100,colspan:2,align:'center'},
					{title: '新卡', width: 100,colspan:4,align:'center'},
					{title: '续充值', width: 100,colspan:4,align:'center'},
				],
				[
					{title: '1800次卡', width: 100,align:'center'},
					{title: '2680次卡', width: 100,align:'center'},
					{title: '2000元', width: 100,colspan:2,align:'center'},
					{title: '5000元', width: 100,colspan:2,align:'center'},
					{title: '2000元', width: 100,colspan:2,align:'center'},
					{title: '5000元', width: 100,colspan:2,align:'center'},
				],
				[
					{field: 'cika_amount_1',title: '金额', width: 100,align:'center',edit: true},
					{field: 'cika_amount_2',title: '金额', width: 100,align:'center',edit: true},
					{field: 'xka_num_1',title: '张数', width: 100,align:'center',edit: true},
					{field: 'xka_amount_1',title: '金额', width: 100,align:'center',edit: true},
					{field: 'xka_num_2',title: '张数', width: 100,align:'center',edit: true},
					{field: 'xka_amount_2',title: '金额', width: 100,align:'center',edit: true},
					{field: 'xcz_num_1',title: '张数', width: 100,align:'center',edit: true},
					{field: 'xcz_amount_1',title: '金额', width: 100,align:'center',edit: true},
					{field: 'xcz_num_2',title: '张数', width: 100,align:'center',edit: true},
					{field: 'xcz_amount_2',title: '金额', width: 100,align:'center',edit: true},
				]
			],
			height: 500
		});
		table.on('edit(ID-table-demo-editable)', function(obj,index){
			var field = obj.field; // 得到修改的字段
			var value = obj.value // 得到修改后的值
			var oldValue = obj.oldValue // 得到修改前的值 -- v2.8.0 新增
			var data = obj.data // 得到所在行所有键值
			var col = obj.getCol(); // 得到当前列的表头配置属性 -- v2.8.0 新增

			// 值的校验
			if(value.replace(/\s/g, '') === ''){
				layer.tips('值不能为空', this, {tips: 1});
				return obj.reedit(); // 重新编辑 -- v2.8.0 新增
			}
			// 编辑后续操作，如提交更新请求，以完成真实的数据更新
			// …

			// 显示 - 仅用于演示
			layer.msg('[ID: '+ data.id +'] ' + field + ' 字段更改值为：'+ value);

			if (field == 'sanke_yj' || field == 'sanke'){
				let v = data.sanke_yj / data.sanke ;
				v = parseFloat(v.toFixed(2))
				if (v != Infinity){
					updateRow( obj.index , {'sanke_kdj': v ? v : 0} );
				}
			}
			console.log(data.total_yj)
			console.log(data.sanke_yj)
			if (field == 'sanke_yj'){
				let haoka_yj = parseFloat(data.total_yj) - parseFloat(data.sanke_yj);
				updateRow( obj.index , {'haoka_yj': haoka_yj ? haoka_yj : 0} );
			}

			if (field == 'xka_num_1' || field == 'xka_num_2'){
				let deal_num = parseInt(data.xka_num_1)  + parseInt(data.xka_num_2)  ;
				updateRow( obj.index , {'deal_num': deal_num ? deal_num : 0} );
				let sanke_deal_rate =  deal_num / data.sanke * 100
				sanke_deal_rate =  parseFloat(sanke_deal_rate.toFixed(2))
				if (v != Infinity){
					updateRow( obj.index , {'sanke_deal_rate': sanke_deal_rate ? sanke_deal_rate : 0} );
				}
			}

		});

		let updateRow = (ind,da) => {
			console.log(da)
			table.updateRow('ID-table-demo-editable', {
				index: ind,
				data: da,
				// 是否更新关联的列视图
				related: true
			});
		}

	}


</script>
{/block}
<!-- /脚本 -->