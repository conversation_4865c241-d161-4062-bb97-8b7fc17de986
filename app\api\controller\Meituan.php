<?php
/**
 * @copyright Copyright (c) 2021 仲正堂工作室
 * @license https://opensource.org/licenses/GPL-3.0
 * @link https://www.gougucms.com
 */
declare (strict_types=1);

namespace app\api\controller;

use app\api\BaseController;
use app\oa\controller\Approve;
use app\store\service\StoreBusinessService;
use app\store\service\StoreSalaryService;
use app\store\service\StoreService;
use PhpOffice\PhpSpreadsheet\Calculation\Logical\Boolean;
use think\facade\Db;
use think\Session;

class Meituan
{

    function test()
    {
        $data = array(
//            "biz" => "{'platform':'\'ALL\'','startDate':'\'2024-11-01\'','endDate': '\'2024-11-02\''}",
//            "biz" => "{'date':'2024-12-01','bizType':'1','offset': '0','type':'0','limit':10}",
//            "biz" => "{'dateType':30 }",
            "biz" => "{'opPoiId':'AS27G3KM6947IGL6S789GUCLI7K','displayType':2 }",
            "appAuthToken" => 'V2-d33760e8fa5b275cc91b2d63765a567adc2bf1e43b9bcbb8eadef88b20be7a8fa0e21b7abe568e7063b5d3b5cb04b26d2f69128e06cda6140adcc956723a9014ccb808869a830082f1664dcac0579e420e6e10c977d4036da445a8eb6f85efb7',
            "charset" => "utf-8",
            "businessId" => '58',
            "developerId" => '113422',
            "timestamp" => strtotime(date("Y-m-d H:i:s")),
            "version" => "2"
        );
        $sign = $this->get_sign('go030yspehph5erm', $data);

        $data['sign'] = $sign;

        $url = 'https://api-open-cater.meituan.com/ddzh/merchantdata/poitraffic';
        $url = 'https://api-open-cater.meituan.com/ddzh/tuangou/receipt/querylistbydate';
        $url = 'https://api-open-cater.meituan.com/ddzh/tuangou/receipt/getconsumed';
        $url = 'https://api-open-cater.meituan.com/ddzh/tuangou/receipt/prepare';
        $url = 'https://api-open-cater.meituan.com/ddzh/merchantdata/poitraffic';


        //美团运营数据
        $url = "https://api-open-cater.meituan.com/ddzh/merchantdata/consumption";

//        //美团团购信息
//        $url = "https://api-open-cater.meituan.com/ddzh/tuangou/deal/queryshopdeal";
//
//        //验券记录
//        $url = "https://api-open-cater.meituan.com/ddzh/tuangou/receipt/querylistbydate";


        //
        $url = "https://api-open-cater.meituan.com/ddzh/technician/techInfo/query";

        $response = $this->ajax($url, $data, 'POST');

        dump(json_decode($response));

    }

    function get_sign($sign_key, $data)
    {
        if ($data == null) {
            return null;
        }
        ksort($data);
        $result_str = "";
        foreach ($data as $key => $val) {
            if ($key != "sign" && $val != null && $val != "") {
                $result_str = $result_str . $key . $val;
            }
        }
        $result_str = $sign_key . $result_str;


        $ret = bin2hex(sha1($result_str, true));

        return $ret;
    }


    public function ajax($url, $data, $method = 'POST',$https=true)
    {
        //1.初始化url
        $ch = curl_init($url);
        //2.设置相关的参数
        //字符串不直接输出,进行一个变量的存储
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        //判断是否为https请求
        if($https === true){
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
            curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
        }
        //判断是否为post请求
        if($method == 'POST'){
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($data));
        }
        curl_setopt($ch, CURLOPT_HTTPHEADER, array('Content-type:application/x-www-form-urlencoded'));
        //3.发送请求
        $str = curl_exec($ch);
        //4.关闭连接
        curl_close($ch);
        //6.返回请求到的结果
        return $str;

    }


    //美团授权
    public function meituan_shouquan()
    {
        $url = "https://open-erp.meituan.com/general/auth";

        $data = array(
            "appAuthToken" => '',
            "developerId" => '113422',
            "businessId" => '58',
            "timestamp" => strtotime(date("Y-m-d H:i:s")),
            "charset" => "utf-8",
            "state" => "test",
//            "scope" => "tuangou",
            "version" => "2"
        );

        $sign = $this->get_sign('go030yspehph5erm', $data);

        $d = "";
        foreach ($data as $k => $v){
            $d = $d . "$k=$v&amp;";
        }

        var_dump($url . '?' . $d . "sign={$sign}");

    }


    //美团团购
    public function meituan_tuangou()
    {

        $data = array(
            "biz" => "{'source':'1','offset': '1','limit':10}",
            "appAuthToken" => 'V2-013668858c4265eb5b4a063c1d804c5358aac457496aeac75f86f273e716960f6d303b4450e09153b85970a652d140e161f15597066aee95d7ea0e6497f1497336dedd0c8cec8171b169f57b194d261603823d5908ee4c79cb053bbfa0201115',
            "charset" => "utf-8",
            "businessId" => '58',
            "developerId" => '113422',
            "timestamp" => strtotime(date("Y-m-d H:i:s")),
            "version" => "2",
        );
        $sign = $this->get_sign('go030yspehph5erm', $data);

        $data['sign'] = $sign;

        //美团团购信息
        $url = "https://api-open-cater.meituan.com/ddzh/tuangou/deal/queryshopdeal";

        $response = $this->ajax($url, $data, 'POST');

        dump(json_decode($response));

    }


    function meituan_yanquan()
    {
        $data = array(
//            "biz" => "{'platform':'\'ALL\'','startDate':'\'2024-11-01\'','endDate': '\'2024-11-02\''}",
//            "biz" => "{'date':'2024-12-01','bizType':'1','offset': '0','type':'0','limit':10}",
//            "biz" => "{'dateType':30 }",
            "biz" => "{'date':'2025-05-07','bizType':'0','offset': '0','type':'0','limit':300}",
            "appAuthToken" => 'V2-3aee32fcc26444427be5b7a3d3efcc31b0bf48910feeb308df72ef5501c01aeb4aaf3f2ce21d333d9e0eab49794ed22e528c523190d542137dd7f74a39811418e023f9228b876c8c3ebf9f41ef1059f59b9565d181af1dede6cb6ec72d99750b',
            "charset" => "utf-8",
            "businessId" => '58',
            "developerId" => '113422',
            "timestamp" => strtotime(date("Y-m-d H:i:s")),
            "version" => "2"
        );
        $sign = $this->get_sign('go030yspehph5erm', $data);

        $data['sign'] = $sign;

        $url = 'https://api-open-cater.meituan.com/ddzh/tuangou/receipt/querylistbydate';

        $response = $this->ajax($url, $data, 'POST');

        dump(json_decode($response));

    }


    function meituan_yanquan2()
    {
        $data = array(
//            "biz" => "{'platform':'\'ALL\'','startDate':'\'2024-11-01\'','endDate': '\'2024-11-02\''}",
//            "biz" => "{'date':'2024-12-01','bizType':'1','offset': '0','type':'0','limit':10}",
//            "biz" => "{'dateType':30 }",
            "biz" => "{'receiptCode':'3828931724'}",
            "biz" => "{'consumeItem':'5534743549','productCode':1}",
            "appAuthToken" => 'V2-3aee32fcc26444427be5b7a3d3efcc31b0bf48910feeb308df72ef5501c01aeb4aaf3f2ce21d333d9e0eab49794ed22e528c523190d542137dd7f74a39811418e023f9228b876c8c3ebf9f41ef1059f59b9565d181af1dede6cb6ec72d99750b',
            "charset" => "utf-8",
            "businessId" => '58',
            "developerId" => '113422',
            "timestamp" => strtotime(date("Y-m-d H:i:s")),
            "version" => "2"
        );
        $sign = $this->get_sign('go030yspehph5erm', $data);

        $data['sign'] = $sign;

        $url = 'https://api-open-cater.meituan.com/ddzhkh/finance/order/detail';

        $response = $this->ajax($url, $data, 'POST');

        dump(json_decode($response));

    }

    function meituan_yunying()
    {
        $data = array(
//            "biz" => "{'platform':'\'ALL\'','startDate':'\'2024-11-01\'','endDate': '\'2024-11-02\''}",
//            "biz" => "{'date':'2024-12-01','bizType':'1','offset': '0','type':'0','limit':10}",
//            "biz" => "{'dateType':30 }",
            "biz" => "{'dateType':1}",
            "appAuthToken" => 'V2-3aee32fcc26444427be5b7a3d3efcc31b0bf48910feeb308df72ef5501c01aeb4aaf3f2ce21d333d9e0eab49794ed22e528c523190d542137dd7f74a39811418e023f9228b876c8c3ebf9f41ef1059f59b9565d181af1dede6cb6ec72d99750b',
            "charset" => "utf-8",
            "businessId" => '58',
            "developerId" => '113422',
            "timestamp" => strtotime(date("Y-m-d H:i:s")),
            "version" => "2"
        );
        $sign = $this->get_sign('go030yspehph5erm', $data);

        $data['sign'] = $sign;

        $url = 'https://api-open-cater.meituan.com/ddzh/merchantdata/consumption';

        $response = $this->ajax($url, $data, 'POST');

        dump(json_decode($response));

    }


    function meituan_finance()
    {

        $sdate = "2025-05-07";
        $start_time = strtotime("$sdate 00:00:00");
        $end_time = strtotime("$sdate 23:59:59");

        $data = array(
//            "biz" => "{'platform':'\'ALL\'','startDate':'\'2024-11-01\'','endDate': '\'2024-11-02\''}",
//            "biz" => "{'date':'2024-12-01','bizType':'1','offset': '0','type':'0','limit':10}",
//            "biz" => "{'dateType':30 }",
            "biz" => "{'queryGeneralShop':false,'productCode':1,'opPoiIds':'AL3VV4QK63P32M4FGQ5JGLNHKV0','createBeginTime':$start_time,'createEndTime':$end_time}",
            "appAuthToken" => 'V2-3aee32fcc26444427be5b7a3d3efcc31b0bf48910feeb308df72ef5501c01aeb4aaf3f2ce21d333d9e0eab49794ed22e528c523190d542137dd7f74a39811418e023f9228b876c8c3ebf9f41ef1059f59b9565d181af1dede6cb6ec72d99750b',
            "charset" => "utf-8",
            "businessId" => '58',
            "developerId" => '113422',
            "timestamp" => strtotime(date("Y-m-d H:i:s")),
            "version" => "2"
        );
        $sign = $this->get_sign('go030yspehph5erm', $data);

        $data['sign'] = $sign;

        $url = 'https://api-open-cater.meituan.com/ddzhkh/finance/query/payplan';

        $response = $this->ajax($url, $data, 'POST');

        dump(json_decode($response));

    }




}


