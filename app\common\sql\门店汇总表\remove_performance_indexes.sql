-- 删除为优化查询性能而添加的索引
-- 此脚本用于回滚索引更改，如果出现问题需要恢复原状

-- 删除oa_order表的索引
ALTER TABLE `oa_order` DROP INDEX `idx_sale_kdt_id_finish_date`;

-- 删除oa_store_business表的索引
ALTER TABLE `oa_store_business` DROP INDEX `idx_did_sdate`;

-- 删除oa_store_business_m表的索引
ALTER TABLE `oa_store_business_m` DROP INDEX `idx_did_sdate_range`;

-- 删除oa_order_pay表的索引
ALTER TABLE `oa_order_pay` DROP INDEX `idx_tid_pay_channel`;

-- 删除oa_order_item表的索引
ALTER TABLE `oa_order_item` DROP INDEX `idx_tid_item_type`;
ALTER TABLE `oa_order_item` DROP INDEX `idx_tid_promotion_type`;

-- 删除oa_store表的索引
ALTER TABLE `oa_store` DROP INDEX `idx_sdate_did`;

-- 删除oa_department表的索引
ALTER TABLE `oa_department` DROP INDEX `idx_id_title`; 