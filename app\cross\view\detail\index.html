{extend name="../../base/view/common/base" /}

{block name="style"}
<style>
    .layui-form-item {
        margin-bottom: 0;
        display: inline-block;
        vertical-align: top;
    }

    .layui-form-item .layui-inline {
        margin-bottom: 0;
        margin-right: 5px;
    }

    .layui-input-block {
        min-height: 24px;
    }

    .gg-form-bar .layui-input-inline {
        margin-bottom: 0;
        margin-right: 5px;
        vertical-align: top;
    }

    /* 多选下拉样式 */
    .layui-form-select dl {
        min-width: 100%;
    }

    .layui-form-select dl dd.layui-this {
        background-color: #5FB878;
        color: #fff;
    }

    .label-content {
        max-height: 34px !important;
        width: 125px !important;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }

    .xm-select {
        height: 34px !important;
        line-height: 34px !important;
    }

    /* 按钮组样式 */
    .filter-button-group .layui-btn {
        display: inline-block !important;
        margin-right: 5px !important;
        margin-bottom: 0 !important;
    }

    /* 合计行数字样式 */
    .layui-table-total td {
        font-weight: bold;
    }
</style>
{/block}

{block name="body"}
<div class="p-3" style="height:100%; box-sizing: border-box;">
    <div class="body-table" style="overflow:hidden;">
        <!-- 搜索表单 -->
        <form class="layui-form gg-form-bar border-t border-x" lay-filter="webform" style="padding-bottom:10px;">
            <div class="layui-form-item" style="margin-bottom: 10px">
                <input type="text" name="period" id="period" placeholder="请选择月份" autocomplete="off" class="layui-input"
                    style="width:120px">
            </div>
            <div class="layui-form-item" style="margin-bottom: 10px">
                <div class="layui-input-inline" style="width:140px">
                    <select name="settlement_type" lay-search="">
                        <option value="">结算类型</option>
                        <option value="储值卡跨店消费">储值卡跨店消费</option>
                        <option value="储值卡跨店消费退款">储值卡跨店消费退款</option>
                    </select>
                </div>
            </div>
            <div id="consume-store-select-container" class="layui-input-inline" style="width:200px; height: 38px;">
            </div>
            <div id="settlement-store-select-container" class="layui-input-inline" style="width:200px; height: 38px;">
            </div>
            <div class="layui-form-item">
                <input type="text" name="customer_mobile" placeholder="客户手机号" autocomplete="off" class="layui-input"
                    style="width:120px">
            </div>
            <div class="layui-form-item">
                <input type="text" name="customer_name" placeholder="客户姓名" autocomplete="off" class="layui-input"
                    style="width:100px">
            </div>
            <div class="layui-form-item">
                <input type="text" name="order_number" placeholder="订单号" autocomplete="off" class="layui-input"
                    style="width:120px">
            </div>
            <div class="layui-form-item" style="display:none;">
                <input type="text" name="settlement_time_start" id="settlement_time_start" placeholder="开始时间"
                    autocomplete="off" class="layui-input" style="width:120px">
            </div>
            <div class="layui-form-item" style="display:none;">
                <input type="text" name="settlement_time_end" id="settlement_time_end" placeholder="结束时间"
                    autocomplete="off" class="layui-input" style="width:120px">
            </div>
            <!-- 按钮组 -->
            <div class="layui-input-inline filter-button-group" style="width: auto; white-space: nowrap;">
                <button class="layui-btn layui-btn-normal" lay-submit="" lay-filter="webform"><i
                        class="layui-icon layui-icon-search mr-1"></i>搜索
                </button>
                <button type="button" class="layui-btn layui-btn-primary" id="resetBtn"><i
                        class="layui-icon layui-icon-refresh mr-1"></i>重置
                </button>
            </div>
        </form>
        <!-- 增加外边距，方便页面布局更美观 -->
        <div style="margin: 10px 0;">
            <span style="font-weight:600">储值跨店结算明细表</span>
            <div id="periodDescription" style="font-weight:600; color: #F44336; font-size:12px;">
                月份说明：举例如2025-07指的是2025年6月26日至2025年7月25日期间的数据</div>
        </div>
        <!-- 数据表格 -->
        <table class="layui-hide" id="dataTable" lay-filter="dataTable"></table>
    </div>
</div>

<!-- 表格行工具栏模板 -->
<script type="text/html" id="barDemo">
    <div class="layui-btn-group">
        <a class="layui-btn layui-btn-xs layui-btn-normal" lay-event="view" title="查看详情">查看</a>
        <a class="layui-btn layui-btn-xs" lay-event="edit" title="编辑">编辑</a>
    </div>
</script>

{/block}

{block name="script"}
<script>
    const moduleInit = ['tool', 'tablePlus'];
    function gouguInit() {
        var table = layui.tablePlus, form = layui.form, tool = layui.tool, laydate = layui.laydate;

        // 初始化日期选择器
        laydate.render({
            elem: '#settlement_time_start',
            type: 'date'
        });

        laydate.render({
            elem: '#settlement_time_end',
            type: 'date'
        });

        // 根据月份生成日期区间说明的函数
        function generatePeriodDescription(period) {
            if (!period || period.length !== 7) {
                return '月份说明：举例如2025-07指的是2025年6月26日至2025年7月25日期间的数据';
            }

            var year = parseInt(period.substring(0, 4));
            var month = parseInt(period.substring(5, 7));

            // 计算开始日期（上月26日）
            var startMonth = month - 1;
            var startYear = year;
            if (startMonth === 0) {
                startMonth = 12;
                startYear = year - 1;
            }

            // 计算结束日期（当月25日）
            var endMonth = month;
            var endYear = year;

            // 月份名称数组
            var monthNames = ['', '1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'];

            return '月份说明：' + period + '指的是' + startYear + '年' + monthNames[startMonth] + '26日至' + endYear + '年' + monthNames[endMonth] + '25日期间的数据';
        }

        // 更新月份说明
        function updatePeriodDescription(period) {
            $('#periodDescription').text(generatePeriodDescription(period));
        }

        // 初始化月份选择器
        laydate.render({
            elem: '#period',
            type: 'month',
            value: '{:date("Y-m")}',
            format: 'yyyy-MM',
            done: function (value, date, endDate) {
                // 更新月份说明
                updatePeriodDescription(value);
            }
        });

        // 初始化时更新月份说明
        updatePeriodDescription('{:date("Y-m")}');

        // 消费门店多选组件
        var consumeStoreMultiSelect = xmSelect.render({
            el: '#consume-store-select-container',
            name: 'consume_store_ids',
            language: 'zn',
            filterable: true,
            tips: '请选择消费门店',
            data: [],
            model: { label: { type: 'xm-select-count', max: 0 } },
            prop: {
                name: 'title',
                value: 'id'
            },
            placeholder: '全部消费门店',
            on: function (data) {
                // 门店选择变化时不立即刷新
            }
        });

        // 结算门店多选组件
        var settlementStoreMultiSelect = xmSelect.render({
            el: '#settlement-store-select-container',
            name: 'settlement_store_ids',
            language: 'zn',
            filterable: true,
            tips: '请选择结算门店',
            data: [],
            model: { label: { type: 'xm-select-count', max: 0 } },
            prop: {
                name: 'title',
                value: 'id'
            },
            placeholder: '全部结算门店',
            on: function (data) {
                // 门店选择变化时不立即刷新
            }
        });

        // 加载门店列表
        function loadStoreList() {
            $.ajax({
                url: '/api/cross/getStoreList',
                type: 'post',
                dataType: 'json',
                success: function (res) {
                    if (res.code == 0) {
                        var storesForXmSelect = [];
                        if (res.data && res.data.length > 0) {
                            storesForXmSelect = res.data.map(function (item) {
                                return { id: item.id, title: item.title };
                            });
                        }
                        consumeStoreMultiSelect.update({
                            data: storesForXmSelect,
                            autoRow: true
                        });
                        settlementStoreMultiSelect.update({
                            data: storesForXmSelect,
                            autoRow: true
                        });
                    } else {
                        layer.msg(res.msg || '获取门店列表失败', { icon: 2 });
                    }
                },
                error: function () {
                    layer.msg('获取门店列表失败', { icon: 2 });
                }
            });
        }

        // 初始化加载门店列表
        loadStoreList();

        // 渲染表格
        try {
            layui.pageTable = table.render({
                elem: '#dataTable',
                toolbar: true,
                title: '储值跨店结算明细列表',
                page: true,
                limit: 20,
                limits: [20, 50, 100, 500, 1000, 10000],
                height: 'full-150',
                url: "/cross/detail/index",
                loading: true,
                even: true,
                totalRow: true,
                where: {
                    period: '{:date("Y-m")}' // 默认加载当月数据
                },
                parseData: function (res) {
                    return {
                        "code": res.code,
                        "msg": res.msg,
                        "count": res.count,
                        "data": res.data,
                        "totalRow": res.totalRow
                    };
                },
                cols: [[
                    { field: 'settlement_type', title: '结算类型', width: 120, totalRowText: '合计：' },
                    { field: 'consume_store_name', title: '消费门店', width: 120 },
                    { field: 'product_name', title: '商品名称', width: 150 },
                    { field: 'product_quantity', title: '数量', width: 80, totalRow: true },
                    { field: 'card_name', title: '充值卡名称', width: 120 },
                    { field: 'settlement_amount_formatted', title: '结算金额', width: 100, totalRow: true },
                    { field: 'settlement_store_name', title: '结算门店', width: 120 },
                    { field: 'settlement_principal_amount_formatted', title: '本金金额', width: 100, totalRow: true },
                    { field: 'settlement_bonus_amount_formatted', title: '赠金金额', width: 100, totalRow: true },
                    { field: 'settlement_time_formatted', title: '结算时间', width: 150 },
                    { field: 'order_number', title: '订单号', width: 150 },
                    { field: 'customer_name', title: '客户姓名', width: 100 },
                    { field: 'customer_mobile', title: '客户手机号', width: 120 },
                    { field: 'customer_belong_store_name', title: '客户归属门店', width: 120 },
                    { title: '操作', width: 120, align: 'center', toolbar: '#barDemo', fixed: 'right' }
                ]],
                done: function (res, curr, count) {
                    // 表格渲染完成后的回调
                    if (res.code !== 0) {
                        console.error('表格数据加载失败:', res.msg);
                    }
                }
            });
        } catch (e) {
            console.error('表格渲染失败:', e);
            layer.msg('表格初始化失败，请刷新页面重试', { icon: 2 });
        }

        // 监听搜索按钮
        form.on('submit(webform)', function (data) {
            var selectedConsumeStoreIds = consumeStoreMultiSelect.getValue().map(function (item) { return item.id; });
            var selectedSettlementStoreIds = settlementStoreMultiSelect.getValue().map(function (item) { return item.id; });

            var searchData = data.field;
            searchData.consume_store_ids = selectedConsumeStoreIds;
            searchData.settlement_store_ids = selectedSettlementStoreIds;

            layui.pageTable.reload({
                where: searchData
            });
            return false;
        });

        // 监听重置按钮
        $('#resetBtn').on('click', function () {
            // 重置表单
            form.val('webform', {
                period: '{:date("Y-m")}',
                settlement_type: '',
                customer_mobile: '',
                customer_name: '',
                order_number: '',
                settlement_time_start: '',
                settlement_time_end: ''
            });

            // 重置门店选择
            consumeStoreMultiSelect.setValue([]);
            settlementStoreMultiSelect.setValue([]);
            form.render();

            // 重置月份说明
            updatePeriodDescription('{:date("Y-m")}');

            // 重置后自动执行搜索
            layui.pageTable.reload({
                where: {
                    period: '{:date("Y-m")}',
                    settlement_type: '',
                    consume_store_ids: [],
                    settlement_store_ids: [],
                    customer_mobile: '',
                    customer_name: '',
                    order_number: '',
                    settlement_time_start: '',
                    settlement_time_end: ''
                }
            });
        });

        // 监听工具条
        table.on('tool(dataTable)', function (obj) {
            var data = obj.data;

            switch (obj.event) {
                case 'view':
                    tool.side('/cross/detail/view?id=' + data.id, '查看储值跨店结算明细详情');
                    break;
                case 'edit':
                    tool.side('/cross/detail/edit?id=' + data.id, '编辑储值跨店结算明细');
                    break;
            }
        });

        // 监听抽屉关闭事件，刷新表格
        $(document).on('sideClose', function () {
            if (layui.pageTable && layui.pageTable.reload) {
                layui.pageTable.reload();
            }
        });
    }
</script>
{/block}