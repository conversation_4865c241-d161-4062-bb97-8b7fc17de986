<?php
/**
 * @copyright Copyright (c) 2021 仲正堂工作室
 * @license https://opensource.org/licenses/GPL-3.0
 * @link https://www.gougucms.com
 */

namespace app\cross\validate;

use think\Validate;

class StoreNameMappingCheck extends Validate
{
    protected $rule = [
        'department_id'         => 'require|integer|gt:0',
        'oa_name'              => 'require|max:100',
        'reconciliation_table'  => 'max:200',
        'terminal_match'        => 'max:200',
        'boss_account'          => 'max:200',
        'douyin_verification'   => 'max:200',
        'cross_store_settlement' => 'max:200',
        'reconciliation_table2' => 'max:200',
        'store_dividend'        => 'max:200',
        'data_framework'        => 'max:200',
        'meituan_id'           => 'max:100',
        'meituan_name'         => 'max:200',
        'youzan_name'          => 'max:200',
        'purchase_system'       => 'max:200',
        'dianping_name'        => 'max:200',
        'douyin_ad'            => 'max:200',
        'visitor_collection'    => 'max:200',
        'status'               => 'require|in:0,1',
    ];

    protected $message = [
        'department_id.require'  => '请选择门店',
        'department_id.integer'  => '门店ID必须为整数',
        'department_id.gt'       => '请选择有效的门店',
        'oa_name.require'        => 'OA系统门店名称不能为空',
        'oa_name.max'           => 'OA系统门店名称不能超过100个字符',
        'reconciliation_table.max'  => '对账表名称不能超过200个字符',
        'terminal_match.max'        => '惠美云终端号匹配不能超过200个字符',
        'boss_account.max'          => '老板管账名称不能超过200个字符',
        'douyin_verification.max'   => '抖音核销门店名称不能超过200个字符',
        'cross_store_settlement.max' => '跨店结算表门店名称不能超过200个字符',
        'reconciliation_table2.max' => '对账表2名称不能超过200个字符',
        'store_dividend.max'        => '门店分红表名称不能超过200个字符',
        'data_framework.max'        => '数据框架名称不能超过200个字符',
        'meituan_id.max'           => '美团门店ID不能超过100个字符',
        'meituan_name.max'         => '美团门店名称不能超过200个字符',
        'youzan_name.max'          => '有赞门店名称不能超过200个字符',
        'purchase_system.max'       => '采购系统名称不能超过200个字符',
        'dianping_name.max'        => '大众点评名称不能超过200个字符',
        'douyin_ad.max'            => '抖音广告名称不能超过200个字符',
        'visitor_collection.max'    => '访客收藏名称不能超过200个字符',
        'status.require'           => '请选择状态',
        'status.in'               => '状态值无效',
    ];

    protected $scene = [
        'add' => ['department_id', 'oa_name', 'status'],
        'edit' => ['department_id', 'oa_name', 'status'],
    ];
}