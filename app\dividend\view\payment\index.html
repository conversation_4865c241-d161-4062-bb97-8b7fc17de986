{extend name="../../base/view/common/base" /}
{block name="style"}
<style>
    .layui-form-item {
        margin-bottom: 0;
        display: inline-block;
        vertical-align: top;
    }

    .layui-form-item .layui-inline {
        margin-bottom: 0;
        margin-right: 5px;
    }

    .layui-input-block {
        min-height: 24px;
    }

    .gg-form-bar .layui-input-inline{
        margin-bottom: 0;
        margin-right: 5px;
        vertical-align: top;
    }

    /* 按钮组样式 */
    .filter-button-group .layui-btn {
        display: inline-block !important;
        vertical-align: top;
        margin-left: 5px;
    }
    .filter-button-group .layui-btn:first-child {
        margin-left: 0;
    }

    /* 合计行样式 */
    .layui-table-total {
        background-color: #f8f8f8;
        font-weight: bold;
    }

    .layui-table-total td {
        border-top: 2px solid #e6e6e6;
    }

    /* 表格样式优化 */
    .layui-table th {
        text-align: center;
        font-weight: bold;
        background-color: #f8f8f8;
        border: 1px solid #e6e6e6;
    }

    .layui-table td {
        text-align: center;
        border: 1px solid #e6e6e6;
    }

    /* 表头分组样式 */
    .layui-table thead tr th {
        background-color: #f0f0f0;
        font-weight: bold;
        border: 1px solid #d0d0d0;
    }

    /* 分红人列样式 */
    .layui-table tbody tr td:first-child {
        text-align: center;
        font-weight: bold;
        background-color: #f9f9f9;
    }

    /* 合计列背景色 */
    .layui-table tbody tr td:nth-child(2),
    .layui-table tbody tr td:nth-child(3),
    .layui-table tbody tr td:nth-child(4),
    .layui-table tbody tr td:nth-child(5),
    .layui-table tbody tr td:nth-child(6) {
        background-color: #f5f5f5;
    }

    /* 月份数据列样式 */
    .layui-table tbody tr td {
        font-size: 12px;
        padding: 4px;
    }

    /* 0值显示样式 */
    .zero-value {
        color: #999;
    }

    /* 表格紧凑布局 */
    .layui-table {
        font-size: 12px;
    }

    .layui-table th,
    .layui-table td {
        padding: 6px 4px;
        min-width: 60px;
    }

    /* LayUI表格样式优化 */
    .layui-table th {
        text-align: center;
        font-weight: bold;
        background-color: #f8f8f8;
        border: 1px solid #e6e6e6;
    }

    .layui-table td {
        text-align: center;
        border: 1px solid #e6e6e6;
    }

    /* 数值颜色样式 */
    .amount-positive {
        color: #4CAF50;
        font-weight: bold;
    }

    .amount-negative {
        color: #F44336;
        font-weight: bold;
    }

    .amount-zero {
        color: #999;
    }

    .amount-adjustment-positive {
        color: #F44336;
        font-weight: bold;
    }

    .amount-unpaid-positive {
        color: #F44336;
        font-weight: bold;
    }
</style>
{/block}

{block name="body"}
<div class="p-3" style="height:100%; box-sizing: border-box;">
    <div class="body-table" style="overflow:hidden;">
        <!-- 搜索表单 -->
        <form class="layui-form gg-form-bar border-t border-x" lay-filter="webform" style="padding-bottom:10px;">
            <div class="layui-form-item" style="margin-bottom: 10px">
                <input type="text" name="year" id="year"
                       placeholder="请选择年份"
                       autocomplete="off" class="layui-input">
            </div>
            <div class="layui-form-item" style="margin-bottom: 10px">
                <input type="text" name="shareholder_name" id="shareholder_name"
                       placeholder="请输入分红人姓名"
                       autocomplete="off" class="layui-input">
            </div>
            <!-- 按钮组 -->
            <div class="layui-input-inline filter-button-group" style="width: auto; white-space: nowrap;">
                <button class="layui-btn layui-btn-normal" lay-submit="" lay-filter="webform"><i
                        class="layui-icon layui-icon-search mr-1"></i>搜索
                </button>
                <button type="button" class="layui-btn layui-btn-primary" id="resetBtn"><i
                        class="layui-icon layui-icon-refresh mr-1"></i>重置
                </button>
            </div>
        </form>
        <!-- 增加外边距，方便页面布局更美观 -->
        <div style="margin: 10px 0;">
            <span style="font-weight:600">股东分红清单表</span>
            <div style="font-weight:600; color: #F44336; font-size:12px;">[调整金额]是减数，正数表示扣减，负数表示增加</div>
        </div>
        <!-- 数据表格 -->
        <table class="layui-hide" id="dataTable" lay-filter="dataTable"></table>
    </div>
</div>

<!-- 表格工具栏模板 -->
<script type="text/html" id="toolbarDemo">
    <div class="layui-btn-group" id="toolbarButtons" style="display: none;">
        <button class="layui-btn layui-btn-sm layui-btn-normal" lay-event="importPaidAmount">
            <i class="layui-icon layui-icon-upload"></i>导入实付金额
        </button>
    </div>
    <div id="toolbarNoPermissionText" style="display: none; color: #999; line-height: 28px; font-size:12px;"></div>
</script>

<!-- 导入实付金额弹窗 -->
<div id="importPaidAmountModal" style="display: none; padding: 20px;">
    <form class="layui-form" lay-filter="importForm">
        <div class="layui-form-item">
            <label class="layui-form-label" style="color: red;">*月份</label>
            <div class="layui-input-block">
                <input type="text" name="import_period" id="import_period"
                       placeholder="请选择月份"
                       autocomplete="off" class="layui-input" lay-verify="required">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label" style="color: red;">*Excel文件</label>
            <div class="layui-input-block">
                <button type="button" class="layui-btn" id="uploadExcelBtn">
                    <i class="layui-icon layui-icon-upload"></i>选择文件
                </button>
                <div id="uploadResult" style="margin-top: 10px; color: #666;"></div>
            </div>
        </div>
        <div class="layui-form-item">
            <div class="layui-input-block">
                <div style="background-color: #f8f8f8; padding: 10px; border-radius: 4px; margin-bottom: 10px;">
                    <div style="font-weight: bold; color: #333; margin-bottom: 5px;">Excel文件格式要求：</div>
                    <div style="color: #666; font-size: 12px;">
                        • 第一列：姓名（对应分红人姓名）<br>
                        • 第二列：实付金额（数字格式）<br>
                        • 第一行为表头，从第二行开始为数据<br>
                        • 支持.xls和.xlsx格式
                    </div>
                    <div style="margin-top: 8px;">
                        <button type="button" class="layui-btn layui-btn-xs layui-btn-normal" id="downloadTemplateBtn">
                            <i class="layui-icon layui-icon-download-circle"></i>下载Excel模板
                        </button>
                    </div>
                </div>
            </div>
        </div>
        <div class="layui-form-item">
            <div class="layui-input-block">
                <button class="layui-btn" lay-submit lay-filter="submitImport">确认导入</button>
                <button type="button" class="layui-btn layui-btn-primary" id="cancelImport">取消</button>
            </div>
        </div>
    </form>
</div>
{/block}

{block name="script"}
<style>
/* 合计行样式 */
.layui-table-total .layui-table-cell {
    font-weight: bold;
}
</style>
<script>
    const moduleInit = ['tool', 'tablePlus', 'upload'];

    function gouguInit() {
        var table = layui.tablePlus, tool = layui.tool, laydate = layui.laydate, form = layui.form, upload = layui.upload, layer = layui.layer;

        // 初始化年份选择器
        laydate.render({
            elem: '#year',
            type: 'year',
            value: '{:date("Y")}',
            done: function(value, date, endDate) {
                // 不立即刷新
            }
        });

        // 格式化金额显示
        function formatAmount(amount, type) {
            var value = parseFloat(amount || 0);
            var displayValue = value === 0 ? '0' : value.toFixed(2);
            var className = '';

            if (value === 0) {
                className = 'amount-zero';
            } else if (type === 'adjustment' || type === 'unpaid') {
                className = value > 0 ? 'amount-adjustment-positive' : '';
            } else {
                className = value > 0 ? 'amount-positive' : 'amount-negative';
            }

            return '<span class="' + className + '">' + displayValue + '</span>';
        }

        // 动态生成表格列配置（参考门店汇总表的多级表头结构）
        function generateTableCols() {
            var currentYear = $('#year').val() || '{:date("Y")}';

            // 第一级表头
            var firstRow = [
                {field: 'shareholder_name', title: '分红人', width: 120, fixed: 'left', rowspan: 2, align: 'center', totalRowText: '合计'},
                {title: '合计', colspan: 5, align: 'center', fixed: 'left'}
            ];

            // 添加12个月的第一级表头
            for (var i = 1; i <= 12; i++) {
                var month = String(i).padStart(2, '0');
                firstRow.push({
                    title: month + '月',
                    colspan: 5,
                    align: 'center'
                });
            }

            // 添加操作列
            firstRow.push({
                field: 'operate',
                title: '操作',
                width: 80,
                fixed: 'right',
                rowspan: 2,
                align: 'center',
                templet: function(d) {
                    if (layui.pageTable.config.hasButtonAccess) {
                        return '<button class="layui-btn layui-btn-xs edit-payment-btn" data-shareholder-name="' + d.shareholder_name + '">编辑</button>';
                    }
                    return '<span style="color: #999;"></span>';
                }
            });

            // 第二级表头
            var secondRow = [];

            // 合计列的第二级表头
            secondRow.push(
                {
                    field: 'total_payable',
                    title: '应付金额',
                    align: 'center',
                    width: 90,
                    fixed: 'left',
                    templet: function(d) {
                        // 如果是合计行，直接返回后端计算的值
                        if (d.shareholder_name === '合计') {
                            return '<span class="amount-positive">' + (d.total_payable || '0.00') + '</span>';
                        }
                        // 普通行计算合计
                        var total = 0;
                        for (var i = 1; i <= 12; i++) {
                            var month = String(i).padStart(2, '0');
                            var fullPeriod = currentYear + '-' + month;
                            if (d[fullPeriod]) {
                                total += parseFloat(d[fullPeriod].payable_amount || 0);
                            }
                        }
                        return formatAmount(total, 'payable');
                    },
                    totalRow: true
                },
                {
                    field: 'total_adjustment',
                    title: '调整金额',
                    align: 'center',
                    width: 90,
                    fixed: 'left',
                    templet: function(d) {
                        // 如果是合计行，直接返回后端计算的值
                        if (d.shareholder_name === '合计') {
                            return '<span class="amount-adjustment-positive">' + (d.total_adjustment || '0.00') + '</span>';
                        }
                        // 普通行计算合计
                        var total = 0;
                        for (var i = 1; i <= 12; i++) {
                            var month = String(i).padStart(2, '0');
                            var fullPeriod = currentYear + '-' + month;
                            if (d[fullPeriod]) {
                                total += parseFloat(d[fullPeriod].adjustment_amount || 0);
                            }
                        }
                        return formatAmount(total, 'adjustment');
                    },
                    totalRow: true
                },
                {
                    field: 'total_actual_payable',
                    title: '实际应付',
                    align: 'center',
                    width: 90,
                    fixed: 'left',
                    templet: function(d) {
                        // 如果是合计行，直接返回后端计算的值
                        if (d.shareholder_name === '合计') {
                            return '<span class="amount-positive">' + (d.total_actual_payable || '0.00') + '</span>';
                        }
                        // 普通行计算合计
                        var total = 0;
                        for (var i = 1; i <= 12; i++) {
                            var month = String(i).padStart(2, '0');
                            var fullPeriod = currentYear + '-' + month;
                            if (d[fullPeriod]) {
                                total += parseFloat(d[fullPeriod].actual_payable_amount || 0);
                            }
                        }
                        return formatAmount(total, 'actual_payable');
                    },
                    totalRow: true
                },
                {
                    field: 'total_paid',
                    title: '实付金额',
                    align: 'center',
                    width: 90,
                    fixed: 'left',
                    templet: function(d) {
                        // 如果是合计行，直接返回后端计算的值
                        if (d.shareholder_name === '合计') {
                            return '<span class="amount-positive">' + (d.total_paid || '0.00') + '</span>';
                        }
                        // 普通行计算合计
                        var total = 0;
                        for (var i = 1; i <= 12; i++) {
                            var month = String(i).padStart(2, '0');
                            var fullPeriod = currentYear + '-' + month;
                            if (d[fullPeriod]) {
                                total += parseFloat(d[fullPeriod].paid_amount || 0);
                            }
                        }
                        return formatAmount(total, 'paid');
                    },
                    totalRow: true
                },
                {
                    field: 'total_unpaid',
                    title: '未付',
                    align: 'center',
                    width: 90,
                    fixed: 'left',
                    templet: function(d) {
                        // 如果是合计行，直接返回后端计算的值
                        if (d.shareholder_name === '合计') {
                            var value = parseFloat(d.total_unpaid || 0);
                            var className = value > 0 ? 'amount-unpaid-positive' : (value < 0 ? 'amount-negative' : 'amount-zero');
                            return '<span class="' + className + '">' + (d.total_unpaid || '0.00') + '</span>';
                        }
                        // 普通行计算合计
                        var total = 0;
                        for (var i = 1; i <= 12; i++) {
                            var month = String(i).padStart(2, '0');
                            var fullPeriod = currentYear + '-' + month;
                            if (d[fullPeriod]) {
                                total += parseFloat(d[fullPeriod].unpaid_amount || 0);
                            }
                        }
                        return formatAmount(total, 'unpaid');
                    },
                    totalRow: true
                }
            );

            // 12个月的第二级表头
            for (var i = 1; i <= 12; i++) {
                var month = String(i).padStart(2, '0');
                var period = currentYear + '-' + month;

                secondRow.push(
                    {
                        field: period + '_payable',
                        title: '应付金额',
                        align: 'center',
                        width: 80,
                        templet: function(d) {
                            // 如果是合计行，直接返回后端计算的值
                            if (d.shareholder_name === '合计') {
                                var fieldName = this.field;
                                var value = d[fieldName] || '0.00';
                                return '<span class="amount-positive">' + value + '</span>';
                            }
                            // 普通行处理
                            var monthNum = this.field.split('-')[1].split('_')[0];
                            var fullPeriod = currentYear + '-' + monthNum;
                            var amount = d[fullPeriod] ? d[fullPeriod].payable_amount : '0';
                            return formatAmount(amount, 'payable');
                        },
                        totalRow: true
                    },
                    {
                        field: period + '_adjustment',
                        title: '调整金额',
                        align: 'center',
                        width: 80,
                        templet: function(d) {
                            // 如果是合计行，直接返回后端计算的值
                            if (d.shareholder_name === '合计') {
                                var fieldName = this.field;
                                var value = d[fieldName] || '0.00';
                                return '<span class="amount-adjustment-positive">' + value + '</span>';
                            }
                            // 普通行处理
                            var monthNum = this.field.split('-')[1].split('_')[0];
                            var fullPeriod = currentYear + '-' + monthNum;
                            var amount = d[fullPeriod] ? d[fullPeriod].adjustment_amount : '0';
                            return formatAmount(amount, 'adjustment');
                        },
                        totalRow: true
                    },
                    {
                        field: period + '_actual_payable',
                        title: '实际应付',
                        align: 'center',
                        width: 80,
                        templet: function(d) {
                            // 如果是合计行，直接返回后端计算的值
                            if (d.shareholder_name === '合计') {
                                var fieldName = this.field;
                                var value = d[fieldName] || '0.00';
                                return '<span class="amount-positive">' + value + '</span>';
                            }
                            // 普通行处理
                            var monthNum = this.field.split('-')[1].split('_')[0];
                            var fullPeriod = currentYear + '-' + monthNum;
                            var amount = d[fullPeriod] ? d[fullPeriod].actual_payable_amount : '0';
                            return formatAmount(amount, 'actual_payable');
                        },
                        totalRow: true
                    },
                    {
                        field: period + '_paid',
                        title: '实付金额',
                        align: 'center',
                        width: 80,
                        templet: function(d) {
                            // 如果是合计行，直接返回后端计算的值
                            if (d.shareholder_name === '合计') {
                                var fieldName = this.field;
                                var value = d[fieldName] || '0.00';
                                return '<span class="amount-positive">' + value + '</span>';
                            }
                            // 普通行处理
                            var monthNum = this.field.split('-')[1].split('_')[0];
                            var fullPeriod = currentYear + '-' + monthNum;
                            var amount = d[fullPeriod] ? d[fullPeriod].paid_amount : '0';
                            return formatAmount(amount, 'paid');
                        },
                        totalRow: true
                    },
                    {
                        field: period + '_unpaid',
                        title: '未付',
                        align: 'center',
                        width: 80,
                        templet: function(d) {
                            // 如果是合计行，直接返回后端计算的值
                            if (d.shareholder_name === '合计') {
                                var fieldName = this.field;
                                var value = parseFloat(d[fieldName] || 0);
                                var className = value > 0 ? 'amount-unpaid-positive' : (value < 0 ? 'amount-negative' : 'amount-zero');
                                return '<span class="' + className + '">' + (d[fieldName] || '0.00') + '</span>';
                            }
                            // 普通行处理
                            var monthNum = this.field.split('-')[1].split('_')[0];
                            var fullPeriod = currentYear + '-' + monthNum;
                            var amount = d[fullPeriod] ? d[fullPeriod].unpaid_amount : '0';
                            return formatAmount(amount, 'unpaid');
                        },
                        totalRow: true
                    }
                );
            }

            return [firstRow, secondRow];
        }

        // 渲染表格
        function renderTable() {
            try {
                layui.pageTable = table.render({
                    elem: '#dataTable',
                    title: '股东分红清单列表',
                    page: false,
                    height: 'full-150',
                    url: "/dividend/payment/index",
                    loading: true,
                    even: true,
                    totalRow: true,
                    toolbar: '#toolbarDemo',
                    cellMinWidth: 80,
                    where: {
                        year: '{:date("Y")}',
                        shareholder_name: ''
                    },
                    parseData: function(res) {
                        this.hasButtonAccess = res.hasButtonAccess;
                        return {
                            "code": res.code,
                            "msg": res.msg,
                            "count": res.data ? res.data.length : 0,
                            "data": res.data || [],
                            "totalRow": res.totalRow || {}
                        };
                    },
                    cols: generateTableCols(),
                    done: function(res, curr, count) {
                        // 表格渲染完成后的处理
                        var hasPermission = this.hasButtonAccess;
                        if (hasPermission) {
                            $('.layui-table-tool').find('#toolbarButtons').show();
                            $('.layui-table-tool').find('#toolbarNoPermissionText').hide();
                        } else {
                            $('.layui-table-tool').find('#toolbarButtons').hide();
                            $('.layui-table-tool').find('#toolbarNoPermissionText').show();
                        }
                    }
                });
            } catch (e) {
                console.error('表格初始化失败:', e);
            }
        }

        // 初始化渲染表格
        renderTable();

        // 监听搜索按钮
        form.on('submit(webform)', function(data) {
            // 重新生成表格列配置（因为年份可能改变）
            var newCols = generateTableCols();
            layui.pageTable.reload({
                where: {
                    year: $('#year').val(),
                    shareholder_name: $('#shareholder_name').val()
                },
                cols: newCols
            });
            return false;
        });

        // 监听重置按钮
        $('#resetBtn').on('click', function(){
            $('#year').val('{:date("Y")}');
            $('#shareholder_name').val('');
            form.render();

            // 重新生成表格列配置
            var newCols = generateTableCols();
            layui.pageTable.reload({
                where: {
                    year: '{:date("Y")}',
                    shareholder_name: ''
                },
                cols: newCols
            });
        });

        // 监听表格工具栏事件
        table.on('toolbar(dataTable)', function(obj) {
            if (obj.event === 'importPaidAmount') {
                uploadedFile = null;
                $('#uploadResult').html('');
                layer.open({
                    type: 1,
                    title: '导入实付金额',
                    content: $('#importPaidAmountModal'),
                    area: ['500px', '400px'],
                    btn: false,
                    success: function(layero, index) {
                        form.render();
                    }
                });
            }
        });

        // 监听编辑按钮点击
        $(document).on('click', '.edit-payment-btn', function() {
            var shareholderName = $(this).data('shareholder-name');
            var year = $('#year').val() || '{:date("Y")}';

            // 使用抽屉打开编辑页面
            tool.side('/dividend/payment/edit?shareholder_name=' + encodeURIComponent(shareholderName) + '&year=' + year, '编辑股东分红清单表');
        });

        // 监听抽屉关闭事件，刷新表格
        $(document).on('sideClose', function() {
            if (layui.pageTable && layui.pageTable.reload) {
                layui.pageTable.reload();
            }
        });

        // 导入实付金额功能
        var uploadedFile = null;

        // 初始化月份选择器
        laydate.render({
            elem: '#import_period',
            type: 'month',
            value: '{:date("Y-m")}',
            format: 'yyyy-MM'
        });

        // 文件上传
        upload.render({
            elem: '#uploadExcelBtn',
            url: '/dividend/payment/uploadExcel',
            accept: 'file',
            exts: 'xls|xlsx',
            size: 51200, // 50MB
            before: function(obj) {
                layer.load();
            },
            done: function(res) {
                layer.closeAll('loading');
                if (res.code === 0) {
                    uploadedFile = res.data.file_path;
                    $('#uploadResult').html('<span style="color: green;">文件上传成功：' + res.data.original_name + '</span>');
                } else {
                    $('#uploadResult').html('<span style="color: red;">上传失败：' + res.msg + '</span>');
                }
            },
            error: function() {
                layer.closeAll('loading');
                $('#uploadResult').html('<span style="color: red;">上传失败，请重试</span>');
            }
        });

        // 监听导入表单提交
        form.on('submit(submitImport)', function(data) {
            if (!uploadedFile) {
                layer.msg('请先上传Excel文件', {icon: 2});
                return false;
            }

            var submitData = {
                period: data.field.import_period,
                file_path: uploadedFile
            };

            layer.load();
            $.post('/dividend/payment/importPaidAmount', submitData, function(res) {
                layer.closeAll('loading');
                if (res.code === 0) {
                    layer.msg(res.msg, {icon: 1});
                    layer.closeAll();
                    // 刷新表格
                    if (layui.pageTable && layui.pageTable.reload) {
                        layui.pageTable.reload();
                    }
                } else {
                    layer.msg(res.msg, {icon: 2});
                }
            }).fail(function() {
                layer.closeAll('loading');
                layer.msg('请求失败，请重试', {icon: 2});
            });

            return false;
        });

        // 监听取消按钮
        $('#cancelImport').on('click', function() {
            layer.closeAll();
        });

        // 监听下载模板按钮
        $('#downloadTemplateBtn').on('click', function() {
            var period = $('#import_period').val();
            var url = '/dividend/payment/downloadTemplate';
            if (period) {
                url += '?period=' + encodeURIComponent(period);
            }
            window.open(url, '_blank');
        });
    }
</script>
{/block}
