{extend name="../../base/view/common/base" /}
{block name="style"}
<style>
	.layui-tree-entry{font-size:15px; line-height:24px}
	.layui-tree-set{padding:2px 0}
	.layui-tree-iconClick .layui-icon{color:#1E9FFF}
	.layui-icon layui-icon-file{font-size:16px;}
	.layui-tree-icon {height: 15px;width: 15px; text-align: center;border: 1px solid #1E9FFF; color:#1E9FFF}
	.layui-tree-line .layui-tree-set .layui-tree-set:after{top:18px;}
	.tree-left{width:200px; float:left; height:calc(100% - 30px); overflow: scroll; border:1px solid #eeeeee; background-color:#FAFAFA; padding:12px 12px 12px 5px;}
	.tree-left h3{font-size:16px; height:30px; padding-left:10px; font-weight:800}

	.gougu-upload-files{background-color: #ffffff; border:1px solid #e4e7ed;color: #c0c4cc;cursor: not-allowed; padding:0 12px; width:180px; box-sizing: border-box; display: inline-block; font-size: inherit; height: 38px; line-height: 35px; margin-right:8px; border-radius:2px;}
	.gougu-upload-tips{color:#969696}
	.layui-form-item{margin-bottom:8px;}
	.layui-input-block{min-height:24px;}
</style>
{/block}
<!-- 主体 -->
{block name="body"}
<div class="p-3" style="height:100%; box-sizing: border-box;">
	<div class="body-table" style="overflow:hidden;">
		<form class="layui-form gg-form-bar border-t border-x" lay-filter="barsearchform">
			<div class="layui-input-inline" style="width:136px">
				<select name="status">
					<option value="">选择员工状态</option>
					<option value="1">正常状态</option>
					<option value="2">离职状态</option>
					<option value="0">禁止登录</option>
				</select>
			</div>
			<div class="layui-input-inline" style="width:136px">
				<select name="type">
					<option value="">选择员工类型</option>
					<option value="1">正式员工</option>
					<option value="2">试用员工</option>
					<option value="3">实习员工</option>
				</select>
			</div>
			<div class="layui-input-inline" style="width:240px">
				<input type="text" name="keywords" placeholder="输入关键字，如：ID/姓名/手机号码" class="layui-input" autocomplete="off" />
			</div>
			<div class="layui-input-inline" style="width:150px;">
				<button class="layui-btn layui-btn-normal" lay-submit="" lay-filter="webform"><i class="layui-icon layui-icon-search mr-1"></i>搜索</button>
				<button type="reset" class="layui-btn layui-btn-reset" lay-filter="reset">清空</button>
			</div>
		</form>
		<table class="layui-hide" id="test" lay-filter="test"></table>
	</div>
</div>

<script type="text/html" id="thumb">
	<img src="{{d.thumb}}" width="30" height="30" />
</script>
<script type="text/html" id="toolbara">
	<div class="layui-btn-group">
		<button class="layui-btn layui-btn-sm" lay-event="add"><i class="layui-icon">&#xe61f;</i>添加</button>
<!--		<button class="layui-btn layui-btn-danger layui-btn-sm" lay-event="disable"><i class="layui-icon">&#x1006;</i>禁止登录</button>-->
<!--		<button class="layui-btn layui-btn-warm layui-btn-sm" lay-event="recovery"><i class="layui-icon">&#xe605;</i>恢复正常</button>-->
<!--		<button class="layui-btn layui-btn-normal layui-btn-sm" lay-event="import"><i class="layui-icon">&#xe66f;</i>批量导入</button>-->
	</div>
</script>

{/block}
<!-- /主体 -->

<!-- 脚本 -->
{block name="script"}
<script>
	const moduleInit = ['tool','tablePlus'];
	function gouguInit() {
		var table = layui.tablePlus, tool = layui.tool,tree = layui.tree,form = layui.form,upload = layui.upload;

		let uploadFiles;
		function clearFile() {
			for (let x in uploadFiles) {
				delete uploadFiles[x];
			}
			$('#gougu-upload-choosed').html('');
		}

		layui.pageTable = table.render({
			elem: '#test',
			title: '员工列表',
			toolbar: '#toolbara',
			defaultToolbar: [],
			is_excel:true,
			url: "/marketing/rent/index", //数据接口
			page: true, //开启分页
			limit: 20,
			height: 'full-85',
			cols: [
				[
					{
						field: 'id',
						title: 'ID号',
						align: 'center',
					}, {
					field: 'dname',
					title: '门店名称',
				}, {
					field: 'con_amount',
					title: '合同金额',
				},{
					field: 'date',
					title: '起始日期',
					align: 'center',
					templet: function (d) {
						var html = d.con_start_date + '至' + d.con_end_date;

						return html;
					}
				},{
					field: 'right',
					fixed:'right',
					title: '操作',
					width: 160,
					align: 'center',
					templet: function (d) {
						var html = '';
						var btn1 = '<span class="layui-btn layui-btn-xs layui-btn-normal" lay-event="view">详情</span>';
						var btn2 = '<span class="layui-btn layui-btn-xs" lay-event="edit">编辑</span>';
						if(d.reg_pwd == ''){
							html = '<div class="layui-btn-group">'+btn1+btn2+'</div>';
						}
						else{
							html = '<div class="layui-btn-group">'+btn1+btn2+'</div>';
						}
						return html;
					}
				}
				]
			]
		});

		//表头工具栏事件
		table.on('toolbar(test)', function(obj){
			var checkStatus = table.checkStatus(obj.config.id); //获取选中行状态
			var data = checkStatus.data;
			if (obj.event === 'add') {
				tool.side("/marketing/rent/add");
				return;
			}
		});

		//监听行工具事件
		table.on('tool(test)', function (obj) {
			var data = obj.data;
			if (obj.event === 'view') {
				tool.side('/marketing/rent/view?id='+data.id);
				return;
			}
		});

		//监听搜索提交
		form.on('submit(webform)', function (data) {
			layui.pageTable.reload({
				where: {
					keywords: data.field.keywords,
					status: data.field.status,
					type: data.field.type
				},
				page: {
					curr: 1
				}
			});
			return false;
		});
	}

	//拷贝密码
	function copyToClip(content) {
		var save = function(e){
			e.clipboardData.setData('text/plain', content);
			e.preventDefault();
		}
		document.addEventListener('copy', save);
		document.execCommand('copy');
		document.removeEventListener('copy',save);
		if (content != '') {
			layer.msg('复制成功');
		}
	}
</script>
{/block}
<!-- /脚本 -->