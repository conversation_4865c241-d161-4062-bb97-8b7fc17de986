<?php

namespace app\store\service;

use app\api\controller\Youzan;
use app\home\service\YouzanService;
use app\store\model\StoreRank;
use app\store\model\StoreRecord;
use think\facade\Db;

class StoreBusinessService
{

    protected $StoreRecordModel;

    public function __construct()
    {
        $this->StoreRecordModel = new StoreRecord();
    }


    public function addData($did, $sdate, $uid, $name)
    {
        $dep = Db::name('department')->where(['id' => $did])->find();
        $admin_list = Db::name('admin')->where([['did', 'find in set', $did]])->select()->toArray();

        //查询当前日期前是否有转店的老师
        $store_transfers = Db::name('store_transfer')->where([
            ['status', '=', 2], ['o_did', '=', $did],
            ['transfer_date', '>', $sdate]
        ])->select()->toArray();

        if (!empty($store_transfers)) {
            foreach ($store_transfers as $kk => $vv) {
                $admin = Db::name('admin')->where(['id' => $vv['aid']])->find();
                if (!empty($admin)) {
                    array_push($admin_list, $admin);
                }
            }
        }

        //补交 添加前一天的数据时 添加操作记录
        $this->carchUpRecord($sdate, $did, $uid, $name);
        foreach ($admin_list as $k => $v) {

            //查看当前员工是否选择月份内离职
            $s_sdate = strtotime($sdate);
            $e_sdate = strtotime(getLastMonth($sdate));

            $store_business = Db::name('store_business')->where([
                'sdate' => $sdate, 'did' => $did, 'aid' => $v['id']
            ])->find();

            if (!empty($v['res_date']) && $v['status'] == 2) {
                $res_time = strtotime($v['res_date']);
                if ($res_time < $s_sdate || $e_sdate < $res_time) {

                    if (!empty($store_business)) {
                        Db::name('store_business')->where(['id' => $store_business['id']])->update(['status' => -1]);
                    }
                    if ($res_time < $s_sdate) {
                        continue;
                    }
                }
            }

            //调离记录
            $store_transfer = Db::name('store_transfer')->where([
                'status' => 2, 'n_did' => $did, 'aid' => $v['id']
            ])->find();

            if (!empty($store_transfer)) {
                $sdate_time = strtotime($sdate);
                $transfer_time = strtotime($store_transfer['transfer_date']);
                if ($sdate_time < $transfer_time) {
                    continue;
                }
            }

            if (empty($store_business)) {

                $bol = $this->everydayDataToOrder($v, $dep, $sdate);
                //$bol = false;

                if (!$bol) {
                    Db::name('store_business')->insertGetId([
                        'sdate' => $sdate,
                        'aid' => $v['id'],
                        'aname' => $v['name'],
                        'yz_name' => $v['yz_name'],
                        'service_num' => 0,
                        'did' => $did,
                        'dname' => $dep['title'],
                        'create_time' => time()
                    ]);
                }

            }
        }
    }


    //补交记录
    public function carchUpRecord($sdate, $did, $uid, $uname)
    {
        $count = Db::name('store_business')->where([
            'sdate' => $sdate, 'did' => $did
        ])->count();

        $dep = Db::name('department')->where(['id' => $did])->find();

        if ($count == 0) {
            $now_date = date("Y-m-d");
            if ($now_date != $sdate) {
                $now_time = strtotime(date("Y-m-d 12:00:00"));
                $sdate_time = strtotime('+1 day', strtotime("$sdate 12:00:00"));

                if ($now_time != $sdate_time) {
                    $re = $this->StoreRecordModel->add([
                        'create_time' => time(),
                        'aid' => $uid,
                        'aname' => $uname,
                        'did' => $did,
                        'dname' => $dep['title'],
                        'status' => 1,
                        'sdate' => strtotime($sdate)
                    ]);
                }
            }
        }

    }


    //通过订单获取各项目 完成情况
    public function everydayDataToOrder($admin, $department, $sdate, $flag = 0)
    {
        set_time_limit(0);

        $yz_uid = array();
        $yz_uid[] = $admin['yz_uid'];

        if (!empty($admin['yz_uid2'])) {
            $yz_uid = array_merge($yz_uid, explode(",", $admin['yz_uid2']));
        }

        if (empty($admin['yz_uid'])) {
            return false;
        }

        $sale_kdt_id = $department['kdt_id'];
//        $yz_uid = "Sb3663l21346531125740212224";
        $sanke_tids = array();

        $order_item_staff = Db::name("order_item_staff")
            ->field('ois.*,oi.*,
                o.buyer_new_customer,
                o.pay_real_pay,
                o.item_index,
                o.pay_pay_channel,
                o.buyer_yz_open_id,
                o.buyer_yz_open_id as buyer_yz_open_id,
                o.reverse_finish_time,
                o.finish_time')
            ->alias('ois')
            ->join('order o', 'o.id = ois.order_id')
            ->join('order_item oi', 'oi.id = ois.order_item_id')
            ->where([
                ['ois.yz_open_id', 'in', $yz_uid],
                ['o.sale_kdt_id', '=', $sale_kdt_id],
                ['o.finish_time', 'between', [strtotime($sdate) * 1000, strtotime('+1 day', strtotime($sdate)) * 1000]],
                ['o.order_state', '=', 40],
//                ['o.is_reverse', '=', 1],
            ])->select()->toArray();


        $order_reverse = Db::name("order_reverse")
            ->field(' or.order_id,or.tid,o . finish_date,o.pay_real_pay as pay_real_pay, o.pay_pay_channel as pay_pay_channel')
            ->alias(' or ')
            ->join('order o', 'o . id = or.order_id')
            ->join('order_item_staff ois', 'ois . order_id = or.order_id')
            ->where([
                [' or.reverse_finish_time', 'between', [date("Y-m-d", strtotime($sdate)), date("Y-m-d", strtotime(' + 1 day', strtotime($sdate)))]],
                [' or.kdt_id', '=', $sale_kdt_id],
                ['ois.yz_open_id', 'in', $yz_uid],
            ])->select()->toArray();

        $store_business = Db::name("store_business")->where([
            'aid' => $admin['id'],
            'did' => $department['id'],
            'sdate' => $sdate
        ])->find();

        if (empty($order_item_staff) && empty($order_reverse)) {
            if ($flag == 1 && !empty($store_business)) {
                Db::name("store_business")->where(['id' => $store_business['id']])->update([
                    'project_268' => 0,
                    'guasha' => 0,
                    'cika_num' => 0,
                    'cika_amount_1' => 0,
                    'cika_amount_2' => 0,
                    'xka_num_1' => 0,
                    'xka_amount_1' => 0,
                    'xka_num_2' => 0,
                    'xka_amount_2' => 0,
                    'xcz_num_1' => 0,
                    'xcz_amount_1' => 0,
                    'xcz_num_2' => 0,
                    'xcz_amount_2' => 0,
                    'sanke_yj' => 0,
                    'total_yj' => 0,
                    'haoka_yj' => 0,
                    'second_service' => 0,
                ]);
            }
            return false;
        }

        $store = Db::name("store")
            ->where([
                'did' => $department['id'],
                'sdate' => date("Y-m", strtotime($sdate))
            ])->find();

        $param_business = [
            'aid' => $admin['id'],
            'aname' => $admin['name'],
            'did' => $department['id'],
            'dname' => $department['title'],
            'sdate' => $sdate,
            'yz_name' => $admin['yz_name'],
            'create_time' => time(),
            //'sort' => $admin['sort'],

            'project_268' => 0,
            'guasha' => 0,
            'dianzhong' => 0,
            'sanke' => 0,

            'cika_num' => 0,
            'cika_amount_1' => 0,
            'cika_amount_2' => 0,

            'xka_num_1' => 0,
            'xka_amount_1' => 0,
            'xka_num_2' => 0,
            'xka_amount_2' => 0,

            'xcz_num_1' => 0,
            'xcz_amount_1' => 0,
            'xcz_num_2' => 0,
            'xcz_amount_2' => 0,

            'sanke_yj' => 0,
            'total_yj' => 0,
            'haoka_yj' => 0,

            'second_service' => 0
        ];
        $tid_arr = array();
        $seconds_tids = array();
        $order_id_arr = array();
        $card_id_arr = array();


        $youzan_controller = new Youzan();

        if (!empty($order_item_staff)) {
            foreach ($order_item_staff as $k => $v) {

                $reverse_finish_time = $v['reverse_finish_time'];
                if (!empty($reverse_finish_time)) {
                    $time_reverse = strtotime($reverse_finish_time);
                    $s_today = strtotime("$sdate 00:00:00");
                    $e_today = strtotime("$sdate 23:59:59");
                    if ($s_today <= $time_reverse && $time_reverse <= $e_today) {
                        continue;
                    }
                }

                //$youzan_controller->getMemberCardList($v['buyer_yz_open_id']);

                //获取当前售卡的 销售人数
                $server_number = Db::name("order_item_staff")->where([
                    'order_id' => $v['order_id'],
                    'index' => $v['index']
                ])->count();

                $order_serve_number = Db::name("order_item_staff")->where([
                    'order_id' => $v['order_id'],
                ])->group("yz_open_id")->select()->toArray();

                //使用次卡
                //2025-07 只统计三伏灸次卡
                if (strtotime($sdate) >= strtotime("2025-07")) {
                    if ($v['promotion_type'] == 1) {
                        $tj_item_id = ['4170648764', '4170647919' , '3026705820'];
                        if ( in_array($v['item_id'] , $tj_item_id ) && $v['item_id'] != '894865736') {
                            $param_business['project_268'] += round($v['num'] / $server_number, 2);
                        }else if ($store['nstatus'] == 1) {
                            if (strpos($v['item_name'], '体态拉伸推拿套餐') !== false || strpos($v['item_name'], '疼痛推拿艾灸') !== false || strpos($v['item_name'], '推拿艾灸/') !== false) {
                                $param_business['project_268'] += round($v['num'] / $server_number, 2);
                            }else if ($v['item_id'] == '859022232' || $v['item_id'] == '3535376332' || $v['item_id'] == '2756903340') {
                                $param_business['project_268'] += round($v['num'] / $server_number, 2);
                            }
                        }
                    }else if ($store['nstatus'] == 1) {
                        if (strpos($v['item_name'], '体态拉伸推拿套餐') !== false || strpos($v['item_name'], '疼痛推拿艾灸') !== false || strpos($v['item_name'], '推拿艾灸/') !== false) {
                            $param_business['project_268'] += round($v['num'] / $server_number, 2);
                        }else if ($v['item_id'] == '859022232' || $v['item_id'] == '3535376332' || $v['item_id'] == '2756903340') {
                            $param_business['project_268'] += round($v['num'] / $server_number, 2);
                        }
                    }
                }else{
                    if ($v['promotion_type'] == 1) {
                        if (($v['item_id'] == '859027104' ||            //疼痛推拿艾灸/90mins
                                $v['item_id'] == '4104989942' ||        //2680次卡艾灸+经络/脾胃/推拿90mins
                                $v['item_id'] == '3026705820' ||        // 1800次卡关元灸/60mins
                                $v['item_id'] == '983370953'            // 经络/脾胃调理灸
                            ) && $v['item_id'] != '894865736'
                        ) {
                            $param_business['project_268'] += round($v['num'] / $server_number, 2);
                        } else {
                            if ($store['nstatus'] == 1) {
                                //锦安东路 和万象汇 增加关元灸 268
                                if ($v['item_id'] == '859022232' || $v['item_id'] == '3535376332' || $v['item_id'] == '2756903340') {
                                    $param_business['project_268'] += round($v['num'] / $server_number, 2);
                                }
                            }
                        }
                    } else if (strpos($v['item_name'], '体态拉伸推拿套餐') !== false || strpos($v['item_name'], '疼痛推拿艾灸') !== false || strpos($v['item_name'], '推拿艾灸/') !== false) {
                        $param_business['project_268'] += round($v['num'] / $server_number, 2);
                    } else if ($store['nstatus'] == 1) {
                        // 增加关元灸 268
                        if ($v['item_id'] == '859022232' || $v['item_id'] == '3535376332' || $v['item_id'] == '2756903340') {
                            $param_business['project_268'] += round($v['num'] / $server_number, 2);
                        }
                    }
                }


                if (strpos($v['item_name'], "刮痧") !== false || strpos($v['item_name'], "火罐") !== false) {
                    $param_business['guasha'] += round($v['num'] / $server_number, 2);
                }

                //售卡订单 2次卡 4会员卡
                if ($v['item_type'] == 2 || $v['item_type'] == 4 || $v['item_type'] == 9) {
                    if ($v['is_reverse'] == 1) continue;
                    if (in_array($v['item_no'], $card_id_arr)) {
                        continue;
                    }


                    $card_id_arr[] = $v['item_no'];

                    $card_real_pay = round($v['pay_real_pay'] / count($order_serve_number) / 100, 2);
                    $card_sales_number = round(1 / count($order_serve_number), 2);

                    //查询这个顾客当前之前是否有开卡
                    $before_order = Db::name("order_item")
                        ->field('o.*')
                        ->alias('oi')
                        ->join('order o', 'o.id = oi.order_id')
                        ->where([
                            ['o.buyer_yz_open_id', '=', $v['buyer_yz_open_id']],
                            ['o.finish_time', '<', (strtotime($sdate) * 1000)],
                            ['o.order_state', '=', 40],
                            ['o.is_reverse', '=', 0],
                            ['oi.item_type', 'in', '2,4,9'],
                        ])->find();

                    //当天是否开卡 充值
                    $today_order = Db::name("order_item")
                        ->field('o.*')
                        ->alias('oi')
                        ->join('order o', 'o.id = oi.order_id')
                        ->where([
                            ['o.buyer_yz_open_id', '=', $v['buyer_yz_open_id']],
                            ['o.finish_time', '>=', (strtotime($sdate . " 00:00:00") * 1000)],
                            ['o.finish_time', '<=', (strtotime($sdate . " 23:59:59") * 1000)],
                            ['o.order_state', '=', 40],
                            ['o.is_reverse', '=', 0],
                            ['oi.item_type', 'in', '4,9'],
                        ])->find();


                    if ($v['item_type'] == 2) { //次卡

                        $ci_order_item = Db::name("order_item")
                            ->where([
                                ['tid', '=', $v['tid']],
                                ['index', '=', $v['index']],
                            ])->select()->toArray();

                        foreach ($ci_order_item as $ci_k => $ci_v) {

                            $ci_order_item_staff_num = Db::name("order_item_staff")
                                ->where([
                                    ['item_no', '=', $ci_v['item_no']],
                                ])->group("yz_open_id")->select()->toArray();

                            $card_real_pay = round($ci_v['item_real_pay'] / count($ci_order_item_staff_num) / 100, 2);

                            if (($ci_v['item_real_pay'] / $ci_v['num']) <= 180000) {
                                $param_business['cika_amount_1'] += $card_real_pay;
                            } else{
                                $param_business['cika_amount_2'] += $card_real_pay;
                            }
                        }

                        if (empty($before_order) && empty($today_order)) {
                            $param_business['cika_num'] += $card_sales_number;
                        }

                    } else {
                        $before_order = Db::name("order_item")
                            ->field('o.*')
                            ->alias('oi')
                            ->join('order o', 'o.id = oi.order_id')
                            ->where([
                                ['o.buyer_yz_open_id', '=', $v['buyer_yz_open_id']],
                                ['o.finish_time', '<', (strtotime($sdate) * 1000)],
                                ['o.order_state', '=', 40],
                                ['o.is_reverse', '=', 0],
                                ['oi.item_type', 'in', '4,9'],
                            ])->find();

                        if ($v['item_type'] == 4 && $v['promotion_type'] != 32) {

                            if (empty($before_order)) {
                                if (strpos($v['item_name'], "白银会员") !== false) {
                                    $param_business['xka_num_1'] += $card_sales_number;
                                    $param_business['xka_amount_1'] += $card_real_pay;
                                } else if (strpos($v['item_name'], "黄金会员") !== false) {
                                    $param_business['xka_num_2'] += $card_sales_number;
                                    $param_business['xka_amount_2'] += $card_real_pay;
                                }
                                $param_business['sanke'] += round(1 / $server_number, 1);
                                $sanke_tids[] = $v['tid'];

                            } else {

                                if (strpos($v['item_name'], "白银会员") !== false) {
                                    $param_business['xcz_num_1'] += $card_sales_number;
                                    $param_business['xcz_amount_1'] += $card_real_pay;
                                } else if (strpos($v['item_name'], "黄金会员") !== false) {
                                    $param_business['xcz_num_2'] += $card_sales_number;
                                    $param_business['xcz_amount_2'] += $card_real_pay;
                                } else if ($v['item_real_pay'] <= 210000) {
                                    $param_business['xcz_num_1'] += $card_sales_number;
                                    $param_business['xcz_amount_1'] += $card_real_pay;
                                } else if ($v['item_real_pay'] > 210000) {
                                    $param_business['xcz_num_2'] += $card_sales_number;
                                    $param_business['xcz_amount_2'] += $card_real_pay;
                                }
                            }
                        } else if ($v['item_type'] == 9 || $v['promotion_type'] == 32) {

                            $today_order = Db::name("order_item")
                                ->field('o.*')
                                ->alias('oi')
                                ->join('order o', 'o.id = oi.order_id')
                                ->where([
                                    ['o.buyer_yz_open_id', '=', $v['buyer_yz_open_id']],
                                    ['o.finish_time', '>=', (strtotime($sdate . " 00:00:00") * 1000)],
                                    ['o.finish_time', '<', $v['finish_time']],
                                    ['o.order_state', '=', 40],
                                    ['o.is_reverse', '=', 0],
                                    ['oi.item_id', '=', $v['item_id']],
                                    ['o.tid', '<>', $v['tid']],
                                    ['oi.item_type', 'in', '4'],
                                ])->find();

                            if (strpos($v['item_name'], "白银会员") !== false) {

                                if (empty($today_order)) {
                                    $param_business['xcz_num_1'] += $card_sales_number;
                                }
                                $param_business['xcz_amount_1'] += $card_real_pay;
                            } else if (strpos($v['item_name'], "黄金会员") !== false) {

                                if (empty($today_order)) {
                                    $param_business['xcz_num_2'] += $card_sales_number;
                                }
                                $param_business['xcz_amount_2'] += $card_real_pay;
                            } else if ($v['item_real_pay'] <= 210000) {
                                if (empty($today_order)) {
                                    $param_business['xcz_num_1'] += $card_sales_number;
                                }
                                $param_business['xcz_amount_1'] += $card_real_pay;
                            } else if ($v['item_real_pay'] > 210000) {
                                if (empty($today_order)) {
                                    $param_business['xcz_num_2'] += $card_sales_number;
                                }
                                $param_business['xcz_amount_2'] += $card_real_pay;
                            }
                        }
                    }


                } else {
                    if (!in_array($v['tid'], $tid_arr)) {
                        $tid_arr[] = $v['tid'];
                    }
                }

                //品相订单 计算总业绩
                if ($v['item_type'] == 1) {

//                    $order_card = Db::name("order_card")->where(['card_no' => $v['promotion_card_no'] ])->find();
//                    if (!empty($order_card) && $order_card['card_type'] == 4){
//                        continue;
//                    }

//                    var_dump($v['tid'] . "</br>");
                    if ($v['promotion_type'] == 1) { //使用次卡
                        //var_dump(round($v['worth'] / $server_number));
                        $param_business['total_yj'] += round($v['worth'] / $server_number);
                        //$param_business['haoka_yj'] += round($v['worth'] / $server_number);
                    } else {
                        $param_business = $this->yj_yunsuan($v, $order_serve_number, $order_id_arr, $param_business, $server_number);
                        $order_id_arr[] = $v['order_id'];
                    }
                    //var_dump("</br>");
                    //二次服务数
//                    if (!in_array($v['tid'], $seconds_tids)) {
//                        //二次服务数
//                        $count_buyer = Db::name("order_item_staff")
//                            ->field('o.tid')
//                            ->alias('ois')
//                            ->join('order o', 'o.id = ois.order_id')
//                            ->join('order_item oi', 'oi.id = ois.order_item_id')
//                            ->where([
//                                ['ois.yz_open_id', 'in', $yz_uid],
//                                ['o.sale_kdt_id', '=', $sale_kdt_id],
//                                ['o.buyer_yz_open_id', '=', $v['buyer_yz_open_id']],
//                                ['o.finish_time', '>=', strtotime(getFirstMonth($sdate) . " 00:00:00") * 1000],
//                                ['o.finish_time', '<=', strtotime($sdate . " 00:00:00") * 1000],
//                                ['o.order_state', '=', 40],
//                                ['o.is_reverse', '=', 0],
//                                ['oi.item_type', '=', '1'],
//                            ])->group("o.tid")->select()->toArray();
//
//                        if (count($count_buyer) == 1) {
//                            $param_business['second_service'] += 1;
//                        }
//                        $seconds_tids[] = $v['tid'];
//                    }

                    if ($v['assigned'] == 1) {
                        $param_business['dianzhong'] += 1;
//                        $dz_tids[] = $v['tid'];
                    }

                }
            }
        }
//        var_dump($param_business);

//        var_dump("==========================");
//        var_dump($param_business['xcz_num_1']);
//        var_dump($param_business['xcz_num_2']);
        $param_business['service_num'] = count(array_unique($tid_arr));
//        if (!empty($param_business['xka_amount_1']) || !empty($param_business['xka_amount_2'])) {
//            $param_business['cika_num'] = 0;
//        }


        //散客业绩
        $sanke_order_id = array();
        foreach ($tid_arr as $tid_arr_key => $tid_arr_value) {
            //会员卡 消费
            $sanke_order_pay = Db::name("order_pay")->where([
                ['tid', '=', $tid_arr_value],
                ['pay_channel', '=', '120']
            ])->find();

            if (!empty($sanke_order_pay)) continue;

            //是否存在次卡 消费内容
            $order_order_item = Db::name("order_item")->where([
                ['tid', '=', $tid_arr_value],
                ['promotion_type', 'in', '1,2']
            ])->find();

            if (!empty($order_order_item)) continue;

            //获取技师的项目 编号
            $order_item_staff_item_no = Db::name("order_item_staff")
                ->where([
                    ['yz_open_id', 'in', $yz_uid],
                    ['tid', '=', $tid_arr_value],
                ])->select()->toArray();

            //订单服务人数
            $sanke_order_servernum = Db::name("order_item_staff")->where([
                'tid' => $tid_arr_value,
            ])->group("yz_open_id")->select()->toArray();

            //item_no
            foreach ($order_item_staff_item_no as $item_no_key => $item_no_value) {

                if (count($sanke_order_servernum) == 1) {
                    if (!in_array($item_no_value['tid'], $sanke_order_id)) {

                        $order = Db::name("order")->where(['tid' => $item_no_value['tid']])->find();

                        if (empty($order) || empty($order['pay_real_pay'])) {
                            continue;
                        }

                        $param_business['sanke_yj'] += $order['pay_real_pay'];

                        $sanke_order_id[] = $item_no_value['tid'];

                    }
                } else {
                    //项目服务人数
                    $server_number = Db::name("order_item_staff")->where([
                        'item_no' => $item_no_value['item_no']
                    ])->field("yz_open_id")->group("yz_open_id")->select()->toArray();

                    //项目金额
                    $sanke_order_item = Db::name("order_item")->where([
                        ['item_no', '=', $item_no_value['item_no']]
                    ])->field("payment")->find();

                    if (empty($sanke_order_item) || empty($sanke_order_item['payment'])) {
                        continue;
                    }
                    $param_business['sanke_yj'] += $sanke_order_item['payment'] / count($server_number);
                }
            }

        }

        //散客数
        $sanke_yj = Db::name("order_item_staff")
            ->field('oi.item_real_pay as item_real_pay,
                o.id as order_id ,
                oi.index as `index`,
                o.tid as tid,
                o.pay_pay_channel as pay_pay_channel,
                oi.item_real_pay as item_real_pay,
                o.buyer_yz_open_id as buyer_yz_open_id',
            )
            ->alias('ois')
            ->join('order o', 'o.id = ois.order_id')
            ->join('order_item oi', 'oi.id = ois.order_item_id')
            ->join('order_pay op', 'op.tid = ois.tid')
            ->where([
                ['ois.yz_open_id', 'in', $yz_uid],
                ['o.sale_kdt_id', '=', $sale_kdt_id],
                ['o.finish_time', 'between', [strtotime($sdate) * 1000, strtotime('+1 day', strtotime($sdate)) * 1000]],
                ['o.order_state', '=', 40],
                ['o.is_reverse', '=', 0],
                ['o.order_type', '=', 0],
                ['o.pay_pay_channel', '<>', 13],
                ['o.pay_pay_channel', '<>', 120],
//                ['oi.promotion_value', 'not in', '80,1'],
                ['oi.promotion_type', '=', '0'],
                ['op.pay_channel', '<>', '13'],
                ['op.pay_channel', '<>', '120'],
            ])->select()->toArray();
        //getLastSql("order_item_staff");


        $tids_yj = array();

        foreach ($sanke_yj as $k => $v) {

            //获取当前售卡的 销售人数
            $order_promotion = Db::name("order_promotion")->where([
                ['order_id', '=', $v['order_id']], ['scope', '<>', '0']
            ])->field("sum(amount) as amount")->find();

            $promotion_amount = 0;

            $order_item_member = Db::name("order_item")->where([
                ['tid', '=', $v['tid']], ['promotion_type', '=', '1']
            ])->find();

            if (!in_array($v['tid'], $sanke_tids)) {
                //获取支付是否存在 会员卡 和 次卡的消费
                $order_pay = Db::name("order_pay")->field("sum(real_pay) as real_pay")->where([
                    ['tid', '=', $v['tid']], ['pay_channel', 'in', '13,120']
                ])->find();

                //M2024092916030361053085
//                if (!empty($order_pay) && $order_pay['real_pay'] !== null) {
//                    $v['item_real_pay'] = $v['item_real_pay'] - $order_pay['real_pay'];
//                }

                if (empty($order_item_member)) {
                    $order_item = Db::name("order_item")->where([
                        ['tid', '=', $v['tid']], ['promotion_type', '=', '0']
                    ])->find();

                    if (!empty($order_item) && $order_pay['real_pay'] == null) {
                        $sanke_server_number = Db::name("order_item_staff")->where([
                            'order_id' => $v['order_id'],
                            'index' => $v['index']
                        ])->group('yz_open_id')->count();
                        $param_business['sanke'] += round(1 / $sanke_server_number, 1);
                    }

                    if (!empty($order_promotion)) {
                        $promotion_amount = $order_promotion['amount'];
                    }
                }

                $sanke_tids[] = $v['tid'];
            }
        }


        $reverse_tid = array();
        //获取退款订单
        $order_id_arr = array();
        $reverse_haoka_id_arr = array();

        foreach ($order_reverse as $k => $v) {
            if (strtotime(date("Y-m-d", strtotime($v['finish_date']))) == strtotime($sdate) || in_array($v['order_id'], $reverse_tid)) {
                continue;
            }

            $order_item = Db::name("order_item")
                ->alias('oi')
                ->join('order_item_staff ois', 'oi.item_no = ois.item_no')
                ->where([
                    ['oi.order_id', '=', $v['order_id']],
                    ['ois.yz_open_id', 'in', $yz_uid]
                ])->select()->toArray();

            foreach ($order_item as $order_item_key => $order_item_v) {
                if ($order_item_v['item_type'] != 1) continue;

                $server_number = Db::name("order_item_staff")->where([
                    'order_id' => $order_item_v['order_id'],
                    'index' => $order_item_v['index']
                ])->count();

                $order_serve_number = Db::name("order_item_staff")->where([
                    'order_id' => $order_item_v['order_id'],
                ])->group("yz_open_id")->select()->toArray();

                if ($order_item_v['promotion_type'] == 1) { //使用次卡
                    $param_business['total_yj'] -= round($order_item_v['worth'] / $server_number);
                } else {
                    $order_item_v['pay_real_pay'] = $v['pay_real_pay'];
                    $order_item_v['pay_pay_channel'] = $v['pay_pay_channel'];
                    $param_business = $this->yj_yunsuan($order_item_v, $order_serve_number, $order_id_arr, $param_business, $server_number, 'minus');
                    $order_id_arr[] = $order_item_v['order_id'];
                }

            }
            $reverse_tid[] = $v['order_id'];
        }

        $param_business['total_yj'] = $param_business['total_yj'] / 100;
        $param_business['haoka_yj'] = ($param_business['total_yj'] - $param_business['sanke_yj']) / 100;
        $param_business['sanke_yj'] = $param_business['sanke_yj'] / 100;


        $param_business['sanke_order'] = $param_business['sanke'];

        //二次服务数
        $param_business['second_service'] = $this->get_second_service($admin['id'], $department['id'], $sdate);

        if ($flag == 1) {

            Db::name("store_business")->where(['id' => $store_business['id']])->update([
                'project_268' => $param_business['project_268'],
                'guasha' => $param_business['guasha'],

                'cika_num' => $param_business['cika_num'],
                'cika_amount_1' => $param_business['cika_amount_1'],
                'cika_amount_2' => $param_business['cika_amount_2'],

                'xka_num_1' => $param_business['xka_num_1'],
                'xka_amount_1' => $param_business['xka_amount_1'],
                'xka_num_2' => $param_business['xka_num_2'],
                'xka_amount_2' => $param_business['xka_amount_2'],
                'sanke_order' => $param_business['sanke_order'],

                'xcz_num_1' => $param_business['xcz_num_1'],
                'xcz_amount_1' => $param_business['xcz_amount_1'],
                'xcz_num_2' => $param_business['xcz_num_2'],
                'xcz_amount_2' => $param_business['xcz_amount_2'],

                'sanke_yj' => $param_business['sanke_yj'],
                'total_yj' => $param_business['total_yj'],
                'haoka_yj' => $param_business['haoka_yj'],
                'second_service' => $param_business['second_service'],
            ]);

        } else {
            if (empty($store_business)) {
                $re = Db::name("store_business")->insertGetId($param_business);
            }
        }

        return true;

    }

    //获取二次服务数
    public function get_second_service($aid, $did, $sdate)
    {
        $store_second = Db::name("store_second")
            ->where([
                'aid' => $aid,
                'did' => $did,
                'sdate' => date("Y-m", strtotime($sdate))
            ])
            ->field(" (num_2 + num_3 + num_4 + num_5 + num_6 + num_7 + num_8 + num_9) as total ")
            ->find();

        return empty($store_second['total']) ? 0 : $store_second['total'];

    }


    public function yj_yunsuan($v, $order_serve_number, $order_id_arr, $param_business, $server_number, $type = 'add')
    {
        $r_pay = $v['payment'];

        if (count($order_serve_number) == 1) {
            if (!in_array($v['order_id'], $order_id_arr)) {
                if ($type == 'add') {
                    //var_dump( $v['tid'] . " add------》" . $v['pay_real_pay'] . "</br>");
                    $param_business['total_yj'] += $v['pay_real_pay'];
                } else {
                    //if ($v['pay_pay_channel'] == '1014' || $v['pay_pay_channel'] == '1001' || $v['pay_pay_channel'] == '1015') return $param_business;
                    //var_dump( $v['tid'] . " else---------------------》" . $v['pay_real_pay'] . "</br>");
                    $param_business['total_yj'] -= $v['pay_real_pay'];
                }
            }
        } else {
            //项目业绩总和 是否等于订单金额
            $order_item_payment = Db::name("order_item")->where(['tid' => $v['tid']])->field("sum(payment) as payment")->find();
            $order_real_pay = Db::name("order")->where(['tid' => $v['tid']])->field("pay_real_pay")->find();

            if ($order_item_payment['payment'] != $order_real_pay['pay_real_pay']) {
                $r_pay = round(($order_real_pay['pay_real_pay'] / $order_item_payment['payment']) * $v['payment'], 2);
            }
            if ($type == 'add') {
                //var_dump( $v['tid'] . "add ------》" . round($r_pay / $server_number) . "</br>");
                $param_business['total_yj'] += round($r_pay / $server_number);
            } else {
                //var_dump( $v['tid'] . " else----------------》" . round($r_pay / $server_number) . "</br>");
                $param_business['total_yj'] -= round($r_pay / $server_number);
            }
        }

        return $param_business;

    }


}
