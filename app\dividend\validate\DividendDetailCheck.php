<?php
/**
 * @copyright Copyright (c) 2021 仲正堂工作室
 * @license https://opensource.org/licenses/GPL-3.0
 * @link https://www.gougucms.com
 */

namespace app\dividend\validate;

use think\Validate;

/**
 * 门店分红明细验证类
 * 注意：此验证类主要用于定时任务生成数据时的验证，不用于手动表单提交
 */
class DividendDetailCheck extends Validate
{
    protected $rule = [
        'store_id' => 'require|number|gt:0',
        'period' => 'require|regex:/^\d{4}-\d{2}$/',
        'income' => 'require|float|egt:0',
        'expense' => 'require|float|egt:0',
        'risk_reserve_current' => 'require|float|egt:0',
        'other_adjustment' => 'float',
        'dividend_profit' => 'require|float',
        'risk_reserve_total' => 'require|float|egt:0',
        'company_shareholding_ratio' => 'require|float|between:0,100',
        'remark' => 'max:500',
    ];

    protected $message = [
        'store_id.require' => '门店ID不能为空',
        'store_id.number' => '门店ID必须为数字',
        'store_id.gt' => '门店ID必须大于0',
        'period.require' => '统计周期不能为空',
        'period.regex' => '统计周期格式错误，应为YYYY-MM格式',
        'income.require' => '收入金额不能为空',
        'income.float' => '收入金额必须为数字',
        'income.egt' => '收入金额不能为负数',
        'expense.require' => '支出金额不能为空',
        'expense.float' => '支出金额必须为数字',
        'expense.egt' => '支出金额不能为负数',
        'risk_reserve_current.require' => '风险金计提金额不能为空',
        'risk_reserve_current.float' => '风险金计提金额必须为数字',
        'risk_reserve_current.egt' => '风险金计提金额不能为负数',
        'other_adjustment.float' => '其他调整金额必须为数字',
        'dividend_profit.require' => '分红利润不能为空',
        'dividend_profit.float' => '分红利润必须为数字',
        'risk_reserve_total.require' => '风险金总金额不能为空',
        'risk_reserve_total.float' => '风险金总金额必须为数字',
        'risk_reserve_total.egt' => '风险金总金额不能为负数',
        'company_shareholding_ratio.require' => '公司持股门店比例不能为空',
        'company_shareholding_ratio.float' => '公司持股门店比例必须为数字',
        'company_shareholding_ratio.between' => '公司持股门店比例必须在0-100之间',
        'remark.max' => '备注信息最多500个字符',
    ];
}
