<?php
/**
 * @copyright Copyright (c) 2021 仲正堂工作室
 * @license https://opensource.org/licenses/GPL-3.0
 * @link https://www.gougucms.com
 */
declare (strict_types=1);

namespace app\api\controller;

use app\api\BaseController;
use think\facade\Db;

class Approve extends BaseController
{
    public function addApproveCont()
    {
        $param = get_params();

        if ( empty($param['file_ids']) && empty($param['content']) ){
            return to_assign(1, '请填写');
        }

        $data = [
            'content' => $param['content'],
            'create_time' => date("Y-m-d H:i:s"),
            'aid' => $this->uid,
            'aname' => Db::name("admin")->where(['id' => $this->uid])->value('name'),
            'files_id' => $param['file_ids'],
            'approve_id' => $param['approve_id']
        ];

        $re = Db::name("approve_cont")->insertGetId($data);
        return to_assign(0, '添加成功');
    }


    public function getApproveCont()
    {
        $param = get_params();

        if (request()->isAjax()) {
            $approve_cont = Db::name("approve_cont")->where([
                'approve_id' => $param['approve_id'],
                'status' => 1,
            ])->order("create_time desc")->select()->toArray();
            foreach ($approve_cont as $k => $v){
                $approve_cont[$k]['item'] = array();
                if (!empty($v['files_id'])){
                    $fileArray = Db::name('File')->where('id', 'in', $v['files_id'])->select();

                    //$approve_cont[$k]['fileArray'] = $fileArray;

                    foreach ($fileArray as $kk => $vv ){
                        $item = file_card2($vv['id'],'view');
                        $approve_cont[$k]['item'][] = $item;
                    }
                }
            }
            return to_assign(0, '添加成功' , $approve_cont);
        }
    }


    public function delApproveCont()
    {
        $param = get_params();

        if (request()->isAjax()) {
            $approve_cont = Db::name("approve_cont")->where([
                'id' => $param['id']
            ])->find();



            if (!empty($approve_cont) && $approve_cont['aid'] != $this->uid){
                return to_assign(1, '请选择自己的删除！' , $approve_cont);
            }

            $re = Db::name("approve_cont")->where([
                'id' => $param['id'],
            ])->update(['status' => -1 , 'update_time' => date("Y-m-d H:i:s")]);

            return to_assign(0, '删除成功' , $approve_cont);
        }
    }


    //费用归属部门选择 门店 培训部 总部
    public function get_store()
    {
        $param = get_params();

        $name = $param['name'];

        if ($name == "培训部"){
            $data = Db::name("department")->where(['id' => 27])->select()->toArray();
            return json($data);
        }else if ($name == "总部" || $name == "公司采购（采购部）"){
            return json(getmd("后勤"));
        }else if ($name == "门店"){

            $md = getmd("门店");

            $md[] = Db::name("department")->where(['id' => 16])->find();
            return json($md);
        }
    }

    public function get_pay_type()
    {
        $param = get_params();

        $name = isset($param['name']) ? $param['name'] : '总部';

        if ($name == "培训部"){
            return json(get_type_pay($controller = '' , $contract = '' , $name = '' , 2));
        }else if ($name == "总部" || $name == "公司采购（采购部）"){
            return json(get_type_pay($controller = '' , $contract = '' , $name = '' , 1));
        }else if ($name == "门店"){
            return json(get_type_pay($controller = '' , $contract = '' , $name = '' , 3));
        }
    }

}


