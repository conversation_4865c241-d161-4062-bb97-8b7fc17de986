<?php
/**
 * @copyright Copyright (c) 2021 仲正堂工作室
 * @license https://opensource.org/licenses/GPL-3.0
 * @link https://www.gougucms.com
 */

namespace app\cross\validate;

use think\Validate;

class CrossStoreSettleCardDetailCheck extends Validate
{
    protected $rule = [
        'period' => 'require|regex:/^\d{4}-\d{2}$/',
        'settlement_type' => 'require|max:50',
        'consume_store_id' => 'require|integer|gt:0',
        'product_name' => 'require|max:255',
        'product_quantity' => 'require|integer|egt:0',
        'card_name' => 'require|max:255',
        'settlement_amount' => 'require|float|egt:0',
        'settlement_time' => 'dateFormat:Y-m-d H:i:s',
        'card_open_store_id' => 'integer|egt:0',
        'order_number' => 'require|max:100',
        'related_order_number' => 'max:100',
        'customer_name' => 'require|max:100',
        'customer_mobile' => 'require|max:20',
        'customer_belong_store_id' => 'integer|egt:0',
    ];

    protected $message = [
        'period.require' => '快照月份不能为空',
        'period.regex' => '快照月份格式错误，应为YYYY-MM格式',
        'settlement_type.require' => '结算类型不能为空',
        'settlement_type.max' => '结算类型长度不能超过50个字符',
        'consume_store_id.require' => '消费门店不能为空',
        'consume_store_id.integer' => '消费门店ID必须为整数',
        'consume_store_id.gt' => '消费门店ID必须大于0',
        'product_name.require' => '商品名称不能为空',
        'product_name.max' => '商品名称长度不能超过255个字符',
        'product_quantity.require' => '商品数量不能为空',
        'product_quantity.integer' => '商品数量必须为整数',
        'product_quantity.egt' => '商品数量不能小于0',
        'card_name.require' => '次卡名称不能为空',
        'card_name.max' => '次卡名称长度不能超过255个字符',
        'settlement_amount.require' => '结算金额不能为空',
        'settlement_amount.float' => '结算金额必须为数字',
        'settlement_amount.egt' => '结算金额不能小于0',
        'settlement_time.dateFormat' => '结算时间格式错误，应为YYYY-MM-DD HH:MM:SS格式',
        'card_open_store_id.integer' => '开卡门店ID必须为整数',
        'card_open_store_id.egt' => '开卡门店ID不能小于0',
        'order_number.require' => '发生订单号不能为空',
        'order_number.max' => '发生订单号长度不能超过100个字符',
        'related_order_number.max' => '关联订单号长度不能超过100个字符',
        'customer_name.require' => '客户姓名不能为空',
        'customer_name.max' => '客户姓名长度不能超过100个字符',
        'customer_mobile.require' => '客户手机号不能为空',
        'customer_mobile.max' => '客户手机号长度不能超过20个字符',
        'customer_belong_store_id.integer' => '客户归属门店ID必须为整数',
        'customer_belong_store_id.egt' => '客户归属门店ID不能小于0',
    ];

    protected $scene = [
        'edit' => [
            'period', 'settlement_type', 'consume_store_id', 'product_name', 'product_quantity',
            'card_name', 'settlement_amount', 'settlement_time', 'card_open_store_id',
            'order_number', 'related_order_number', 'customer_name', 'customer_mobile', 'customer_belong_store_id'
        ],
    ];
}
