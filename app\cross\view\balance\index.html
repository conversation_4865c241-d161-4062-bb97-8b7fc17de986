{extend name="../../base/view/common/base" /}

{block name="style"}
<style>
    .layui-form-item {
        margin-bottom: 0;
        display: inline-block;
        vertical-align: top;
    }

    .layui-form-item .layui-inline {
        margin-bottom: 0;
        margin-right: 5px;
    }

    .layui-input-block {
        min-height: 24px;
    }

    .gg-form-bar .layui-input-inline{
        margin-bottom: 0;
        margin-right: 5px;
        vertical-align: top;
    }

    /* 多选下拉样式 */
    .layui-form-select dl {
        min-width: 100%;
    }
    .layui-form-select dl dd.layui-this {
        background-color: #5FB878;
        color: #fff;
    }

    .label-content {
        max-height: 34px !important;
        width: 125px !important;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }

    .xm-select {
        height: 34px !important;
        line-height: 34px !important;
    }

    /* 按钮组样式 */
    .filter-button-group .layui-btn {
        display: inline-block !important;
        vertical-align: top;
        margin-left: 5px;
    }
    .filter-button-group .layui-btn:first-child {
        margin-left: 0;
    }

    /* 合计行样式 */
    .layui-table-total {
        background-color: #f8f8f8;
        font-weight: bold;
    }

    .layui-table-total td {
        border-top: 2px solid #e6e6e6;
    }

    /* 表头样式 - 注释掉可能影响列宽的样式 */
    /*
    .layui-table thead tr th {
        background-color: #f0f0f0;
        font-weight: bold;
        border: 1px solid #d0d0d0;
        text-align: center;
    }
    */

    /* 表格样式优化 - 注释掉可能影响列宽的样式 */
    /*
    .layui-table th {
        text-align: center;
        font-weight: bold;
        background-color: #f8f8f8;
        border: 1px solid #e6e6e6;
    }

    .layui-table td {
        text-align: center;
        border: 1px solid #e6e6e6;
    }
    */

    /* 合计行数字样式 */
    .layui-table-total td {
        font-weight: bold;
    }

    /* 门店类型标签样式 */
    .store-type-old {
        color: #FF5722;
        font-weight: bold;
    }

    .store-type-new {
        color: #2196F3;
        font-weight: bold;
    }
</style>
{/block}

{block name="body"}
<div class="p-3" style="height:100%; box-sizing: border-box;">
    <div class="body-table" style="overflow:hidden;">
        <!-- 搜索表单 -->
        <form class="layui-form gg-form-bar border-t border-x" lay-filter="webform" style="padding-bottom:10px;">
            <div class="layui-form-item" style="margin-bottom: 10px">
                <input type="text" name="period" id="period"
                       placeholder="请选择月份"
                       autocomplete="off" class="layui-input">
            </div>
            <div id="store-select-container" class="layui-input-inline" style="width:200px; height: 38px;"></div>
            <div class="layui-form-item">
                <div class="layui-input-inline" style="width:120px">
                    <select name="logical_store_type" lay-search="">
                        <option value="">逻辑门店类型</option>
                        <option value="新店">新店</option>
                        <option value="老店">老店</option>
                    </select>
                </div>
            </div>
            <div class="layui-form-item">
                <div class="layui-input-inline" style="width:120px">
                    <select name="settlement_store_type" lay-search="">
                        <option value="">结算门店类型</option>
                        <option value="新店">新店</option>
                        <option value="老店">老店</option>
                    </select>
                </div>
            </div>
            <!-- 按钮组 -->
            <div class="layui-input-inline filter-button-group" style="width: auto; white-space: nowrap;">
                <button class="layui-btn layui-btn-normal" lay-submit="" lay-filter="webform"><i
                        class="layui-icon layui-icon-search mr-1"></i>搜索
                </button>
                <button type="button" class="layui-btn layui-btn-primary" id="resetBtn"><i
                        class="layui-icon layui-icon-refresh mr-1"></i>重置
                </button>
            </div>
        </form>
        <!-- 增加外边距，方便页面布局更美观 -->
        <div style="margin: 10px 0;">
            <span style="font-weight:600">门店卡余额表</span>
            <div id="periodDescription" style="font-weight:600; color: #F44336; font-size:12px;">月份说明：举例如2025-07指的是2025年6月26日至2025年7月25日期间的数据</div>
        </div>
        <!-- 数据表格 -->
        <table class="layui-hide" id="dataTable" lay-filter="dataTable"></table>
    </div>
</div>

<!-- 表格工具栏模板 -->
<script type="text/html" id="toolbarDemo">
    <div class="layui-btn-container">
        <button class="layui-btn layui-btn-normal layui-btn-sm" lay-event="import">
            <i class="layui-icon layui-icon-upload"></i> 导入门店卡余额
        </button>
    </div>
</script>

<!-- Excel导入弹窗 -->
<div id="importModal" style="display: none; padding: 20px;">
    <form class="layui-form" lay-filter="importForm">
        <div class="layui-form-item">
            <label class="layui-form-label" style="color: red;">*导入月份</label>
            <div class="layui-input-block">
                <input type="text" name="import_period" id="import_period"
                       placeholder="请选择导入月份"
                       autocomplete="off" class="layui-input" lay-verify="required">
                <div id="importPeriodDescription" style="color: #F44336; font-size: 12px; margin-top: 5px;">
                    月份说明：举例如2025-07指的是2025年6月26日至2025年7月25日期间的数据
                </div>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label" style="color: red;">*Excel文件</label>
            <div class="layui-input-block">
                <button type="button" class="layui-btn" id="uploadExcelBtn">
                    <i class="layui-icon layui-icon-upload"></i>选择文件
                </button>
                <div id="uploadResult" style="margin-top: 10px; color: #666;"></div>
            </div>
        </div>
        <div class="layui-form-item">
            <div class="layui-input-block">
                <div style="background-color: #f8f8f8; padding: 10px; border-radius: 4px; margin-bottom: 10px;">
                    <div style="font-weight: bold; color: #333; margin-bottom: 5px;">Excel文件格式要求：</div>
                    <div style="color: #666; font-size: 12px;">
                        • 第一列：门店名称（支持OA门店名称或有赞门店名称）<br>
                        • 第二列：会员卡总余额（数字格式）<br>
                        • 第一行为表头，从第二行开始为数据<br>
                        • 支持.xls和.xlsx格式
                    </div>
                    <div style="margin-top: 8px;">
                        <div style="font-weight: bold; color: #333; margin-bottom: 5px;">业务规则：</div>
                        <div style="color: #666; font-size: 12px;">
                            • 逻辑门店类型：余额≥60万为老店，否则为新店<br>
                            • 结算门店类型：延迟一个月生效（"一旦变老，永不降级"）<br>
                            • 同一门店在同一月份只能有一条记录
                        </div>
                    </div>
                    <div style="margin-top: 8px;">
                        <button type="button" class="layui-btn layui-btn-xs layui-btn-normal" id="downloadTemplateBtn">
                            <i class="layui-icon layui-icon-download-circle"></i>下载Excel模板
                        </button>
                    </div>
                </div>
            </div>
        </div>
        <div class="layui-form-item">
            <div class="layui-input-block">
                <button class="layui-btn" lay-submit lay-filter="submitImport">确认导入</button>
                <button type="button" class="layui-btn layui-btn-primary" id="cancelImport">取消</button>
            </div>
        </div>
    </form>
</div>

<!-- 操作列模板 -->
<script type="text/html" id="operationTpl">
    <div class="layui-btn-group">
        <a class="layui-btn layui-btn-xs layui-btn-normal" lay-event="view" title="查看详情">查看</a>
        <a class="layui-btn layui-btn-xs" lay-event="edit" title="编辑">编辑</a>
    </div>
</script>

<!-- 门店类型显示模板 -->
<script type="text/html" id="storeTypeTpl">
    {{# if(d.logical_store_type === '老店') { }}
        <span class="store-type-old">{{d.logical_store_type}}</span>
    {{# } else { }}
        <span class="store-type-new">{{d.logical_store_type}}</span>
    {{# } }}
</script>

<script type="text/html" id="settlementTypeTpl">
    {{# if(d.settlement_store_type === '老店') { }}
        <span class="store-type-old">{{d.settlement_store_type}}</span>
    {{# } else { }}
        <span class="store-type-new">{{d.settlement_store_type}}</span>
    {{# } }}
</script>
{/block}

<!-- 脚本 -->
{block name="script"}
<script>
    const moduleInit = ['tool', 'tablePlus', 'upload'];
    function gouguInit() {
        var table = layui.tablePlus, form = layui.form, tool = layui.tool, laydate = layui.laydate;

        // 根据月份生成日期区间说明的函数
        function generatePeriodDescription(period) {
            if (!period || period.length !== 7) {
                return '月份说明：举例如2025-07指的是2025年6月26日至2025年7月25日期间的数据';
            }

            var year = parseInt(period.substring(0, 4));
            var month = parseInt(period.substring(5, 7));

            // 计算开始日期（上月26日）
            var startMonth = month - 1;
            var startYear = year;
            if (startMonth === 0) {
                startMonth = 12;
                startYear = year - 1;
            }

            // 计算结束日期（当月25日）
            var endMonth = month;
            var endYear = year;

            // 月份名称数组
            var monthNames = ['', '1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'];

            return '月份说明：' + period + '指的是' + startYear + '年' + monthNames[startMonth] + '26日至' + endYear + '年' + monthNames[endMonth] + '25日期间的数据';
        }

        // 更新主页面的月份说明
        function updateMainPeriodDescription(period) {
            $('#periodDescription').text(generatePeriodDescription(period));
        }

        // 更新导入弹窗的月份说明
        function updateImportPeriodDescription(period) {
            $('#importPeriodDescription').text(generatePeriodDescription(period));
        }

        // 初始化月份选择器
        laydate.render({
            elem: '#period',
            type: 'month',
            value: '{:date("Y-m")}',
            format: 'yyyy-MM',
            done: function(value, date, endDate) {
                // 更新主页面的月份说明
                updateMainPeriodDescription(value);
            }
        });

        // 初始化时更新主页面的月份说明
        updateMainPeriodDescription('{:date("Y-m")}');

        // 门店多选组件
        var storeMultiSelect = xmSelect.render({
            el: '#store-select-container',
            name: 'store_ids',
            language: 'zn',
            filterable: true,
            tips: '请选择门店',
            data: [],
            model: { label: { type: 'xm-select-count', max: 0 } },
            prop: {
                name: 'title',
                value: 'id'
            },
            placeholder: '全部门店',
            on: function(data){
                // 门店选择变化时不立即刷新
            }
        });

        // 加载门店列表
        function loadStoreList() {
            $.ajax({
                url: '/api/cross/getStoreList',
                type: 'post',
                dataType: 'json',
                success: function(res) {
                    if (res.code == 0) {
                        var storesForXmSelect = [];
                        if (res.data && res.data.length > 0) {
                            storesForXmSelect = res.data.map(function(item) {
                                return { id: item.id, title: item.title };
                            });
                        }
                        storeMultiSelect.update({
                            data: storesForXmSelect,
                            autoRow: true
                        });
                    } else {
                        layer.msg(res.msg || '获取门店列表失败', {icon: 2});
                    }
                },
                error: function(){
                    layer.msg('获取门店列表失败', {icon:2});
                }
            });
        }

        // 初始化加载门店列表
        loadStoreList();

        // 表格列配置
        var tableCols = [
            // 去掉月份列
            {field: 'store_name', title: '门店', align: 'center', totalRowText: '合计：'},
            {field: 'card_balance_formatted', title: '卡余额', align: 'center', totalRow: true, sort: true},
            {field: 'logical_store_type', title: '逻辑类型', align: 'center', totalRow: true, templet: function(d) {
                var html = '';
                if(d.logical_store_type === '老店') {
                    html = '<span class="store-type-old" title="逻辑门店类型：余额≥60万为老店，否则为新店">' + d.logical_store_type + '</span>';
                } else {
                    html = '<span class="store-type-new" title="逻辑门店类型：余额≥60万为老店，否则为新店">' + d.logical_store_type + '</span>';
                }
                return html;
            }},
            {field: 'previous_month_logical_type', title: '上月逻辑类型', align: 'center', templet: function(d) {
                var html = '';
                if(d.previous_month_logical_type === '老店') {
                    html = '<span class="store-type-old" title="上月逻辑类型：上个月的逻辑门店类型">' + d.previous_month_logical_type + '</span>';
                } else {
                    html = '<span class="store-type-new" title="上月逻辑类型：上个月的逻辑门店类型">' + d.previous_month_logical_type + '</span>';
                }
                return html;
            }},
            {field: 'settlement_store_type', title: '结算类型', align: 'center', totalRow: true, templet: function(d) {
                var html = '';
                if(d.settlement_store_type === '老店') {
                    html = '<span class="store-type-old" title="结算门店类型：延迟一个月生效（一旦变老，永不降级）">' + d.settlement_store_type + '</span>';
                } else {
                    html = '<span class="store-type-new" title="结算门店类型：延迟一个月生效（一旦变老，永不降级）">' + d.settlement_store_type + '</span>';
                }
                return html;
            }},

            {title: '操作', width: 120, align: 'center', toolbar: '#operationTpl'}
        ];

        // 渲染表格
        try {
            layui.pageTable = table.render({
                elem: '#dataTable',
                toolbar: '#toolbarDemo',
                title: '门店卡余额列表',
                page: false, // 关闭分页
                height: 'full-150',
                url: "/cross/balance/index",
                loading: true,
                even: true,
                totalRow: true,
                text: {
                    none: '暂无相关数据'
                },
                where: {
                    period: '{:date("Y-m")}', // 默认加载当月数据
                    store_ids: [],
                    logical_store_type: '',
                    settlement_store_type: ''
                },
                parseData: function(res) {
                    return {
                        "code": res.code,
                        "msg": res.msg,
                        "count": res.count,
                        "data": res.data,
                        "totalRow": res.totalRow
                    };
                },
                // 移除后端排序，使用前端本地排序
                initSort: {
                    field: 'card_balance_formatted',
                    type: 'desc'
                },
                cols: [tableCols],
                done: function(res, curr, count) {
                    // 表格渲染完成后的回调
                    if (res.code !== 0) {
                        console.error('表格数据加载失败:', res.msg);
                    }
                }
            });
            
            // 监听排序事件 - 改为前端本地排序
            table.on('sort(dataTable)', function(obj) {
                // 前端本地排序，不向后端发送请求
                var data = table.cache.dataTable || [];
                var field = obj.field;
                var type = obj.type;
                
                // 对数据进行排序
                data.sort(function(a, b) {
                    var aVal = a[field];
                    var bVal = b[field];
                    
                    // 处理数值型字段
                    if (field === 'card_balance_formatted') {
                        // 移除千分位分隔符，然后转换为数值
                        aVal = parseFloat(String(aVal).replace(/,/g, '')) || 0;
                        bVal = parseFloat(String(bVal).replace(/,/g, '')) || 0;
                    }
                    
                    // 处理字符串型字段
                    if (typeof aVal === 'string') {
                        aVal = aVal.toLowerCase();
                    }
                    if (typeof bVal === 'string') {
                        bVal = bVal.toLowerCase();
                    }
                    
                    if (type === 'asc') {
                        return aVal > bVal ? 1 : (aVal < bVal ? -1 : 0);
                    } else {
                        return aVal < bVal ? 1 : (aVal > bVal ? -1 : 0);
                    }
                });
                
                // 重新渲染表格，使用排序后的数据
                layui.pageTable.reload({
                    data: data,
                    initSort: obj
                });
            });
        } catch (e) {
            console.error('表格渲染失败:', e);
            layer.msg('表格初始化失败，请刷新页面重试', {icon: 2});
        }

        // 监听搜索按钮
        form.on('submit(webform)', function(data) {
            var selectedStoreIds = storeMultiSelect.getValue().map(function(item) { return item.id; });
            
            // 搜索时不传递排序参数，让前端自己处理排序
            layui.pageTable.reload({
                where: {
                    period: $('#period').val(),
                    store_ids: selectedStoreIds,
                    logical_store_type: data.field.logical_store_type,
                    settlement_store_type: data.field.settlement_store_type
                }
            });
            return false;
        });

        // 监听重置按钮
        $('#resetBtn').on('click', function() {
            // 重置表单
            form.val('webform', {
                period: '{:date("Y-m")}',
                logical_store_type: '',
                settlement_store_type: ''
            });
            
            // 重置门店选择
            storeMultiSelect.setValue([]);
            form.render('select');
            
            // 重新加载表格，不传递排序参数
            layui.pageTable.reload({
                where: {
                    period: '{:date("Y-m")}',
                    store_ids: [],
                    logical_store_type: '',
                    settlement_store_type: ''
                }
            });
        });

        // 监听头工具栏事件
        table.on('toolbar(dataTable)', function(obj) {
            switch(obj.event) {
                case 'import':
                    openImportModal();
                    break;
            }
        });

        // 监听工具条
        table.on('tool(dataTable)', function(obj) {
            var data = obj.data;

            switch(obj.event) {
                case 'view':
                    tool.side('/cross/balance/view?id=' + data.id, '查看门店卡余额详情');
                    break;
                case 'edit':
                    tool.side('/cross/balance/edit?id=' + data.id, '编辑门店卡余额');
                    break;
            }
        });

        // 监听抽屉关闭事件，刷新表格
        $(document).on('sideClose', function() {
            if (layui.pageTable && layui.pageTable.reload) {
                layui.pageTable.reload();
            }
        });

        // Excel导入功能
        var uploadedFile = null;

        function openImportModal() {
            uploadedFile = null;
            $('#uploadResult').html('');
            layer.open({
                type: 1,
                title: 'Excel导入门店卡余额',
                content: $('#importModal'),
                area: ['600px', '500px'],
                btn: false,
                success: function(layero, index) {
                    form.render();
                    
                    // 初始化月份选择器
                    laydate.render({
                        elem: '#import_period',
                        type: 'month',
                        value: '{:date("Y-m")}',
                        format: 'yyyy-MM',
                        done: function(value, date, endDate) {
                            // 更新导入弹窗的月份说明
                            updateImportPeriodDescription(value);
                        }
                    });

                    // 初始化时更新导入弹窗的月份说明
                    updateImportPeriodDescription('{:date("Y-m")}');
                }
            });
        }

        // 文件上传
        var upload = layui.upload;
        upload.render({
            elem: '#uploadExcelBtn',
            url: '/cross/balance/uploadExcel',
            accept: 'file',
            exts: 'xls|xlsx',
            size: 51200, // 50MB
            before: function(obj) {
                layer.load();
            },
            done: function(res) {
                layer.closeAll('loading');
                if (res.code === 0) {
                    uploadedFile = res.data.file_path;
                    $('#uploadResult').html('<span style="color: green;">文件上传成功：' + res.data.original_name + '</span>');
                } else {
                    $('#uploadResult').html('<span style="color: red;">上传失败：' + res.msg + '</span>');
                }
            },
            error: function() {
                layer.closeAll('loading');
                $('#uploadResult').html('<span style="color: red;">上传失败，请重试</span>');
            }
        });

        // 监听导入表单提交
        form.on('submit(submitImport)', function(data) {
            if (!uploadedFile) {
                layer.msg('请先上传Excel文件', {icon: 2});
                return false;
            }

            var submitData = {
                period: data.field.import_period,
                file_path: uploadedFile
            };

            layer.load();
            $.post('/cross/balance/importExcel', submitData, function(res) {
                layer.closeAll('loading');
                if (res.code === 0) {
                    layer.msg(res.msg, {icon: 1});
                    layer.closeAll();
                    // 刷新表格
                    if (layui.pageTable && layui.pageTable.reload) {
                        layui.pageTable.reload();
                    }
                } else {
                    layer.msg(res.msg, {icon: 2});
                }
            }).fail(function() {
                layer.closeAll('loading');
                layer.msg('请求失败，请重试', {icon: 2});
            });

            return false;
        });

        // 监听取消按钮
        $('#cancelImport').on('click', function() {
            layer.closeAll();
        });

        // 监听下载模板按钮
        $('#downloadTemplateBtn').on('click', function() {
            var period = $('#import_period').val();
            var url = '/cross/balance/downloadTemplate';
            if (period) {
                url += '?period=' + encodeURIComponent(period);
            }
            window.open(url, '_blank');
        });
    }
</script>
{/block}