<?php
/**
 * @copyright Copyright (c) 2021 仲正堂工作室
 * @license https://opensource.org/licenses/GPL-3.0
 * @link https://www.gougucms.com
 */

namespace app\dividend\service;

use think\facade\Db;
use app\dividend\model\DividendRiskReserveLog;

class DividendService
{
    /**
     * 获取门店列表（排除已有分红信息的门店）
     * @param int $excludeId 排除的分红信息ID（编辑时使用）
     * @return array
     */
    public function getAvailableStores($excludeId = 0)
    {
        // 获取已有分红信息的门店ID
        $excludeStoreIds = [];
        if (empty($excludeId)) {
            $excludeStoreIds = Db::name('DividendStoreInfo')
                ->where('is_delete', 0)
                ->column('store_id');
        } else {
            $excludeStoreIds = Db::name('DividendStoreInfo')
                ->where('is_delete', 0)
                ->where('id', '<>', $excludeId)
                ->column('store_id');
        }

        // 获取可选择的门店列表
        $storeList = Db::name('Department')
            ->where('status', '>=', 0)
            ->where('remark', '门店')
            ->where('id', 'not in', $excludeStoreIds)
            ->field('id, title')
            ->order('sort asc, id asc')
            ->select()
            ->toArray();

        return $storeList;
    }

    /**
     * 验证持股比例
     * @param array $companyShareholders 公司股东数据
     * @param array $personalShareholders 个人股东数据
     * @return array [是否通过验证, 错误信息, 公司股东总比例, 个人股东总比例]
     */
    public function validateShareholdingRatio($companyShareholders = [], $personalShareholders = [])
    {
        $companyTotal = 0;
        $personalTotal = 0;

        // 计算公司股东持股比例总和
        if (!empty($companyShareholders)) {
            foreach ($companyShareholders as $shareholder) {
                if (!empty($shareholder['store_shareholding_ratio'])) {
                    $companyTotal += floatval($shareholder['store_shareholding_ratio']);
                }
            }
        }

        // 计算个人股东持股比例总和
        if (!empty($personalShareholders)) {
            foreach ($personalShareholders as $shareholder) {
                if (!empty($shareholder['store_shareholding_ratio'])) {
                    $personalTotal += floatval($shareholder['store_shareholding_ratio']);
                }
            }
        }

        $totalRatio = $companyTotal + $personalTotal;

        if ($totalRatio > 100) {
            return [false, '总持股比例不能超过100%，当前为' . number_format($totalRatio, 3) . '%', $companyTotal, $personalTotal];
        }

        if ($totalRatio < 100 && $totalRatio > 0) {
            return [false, '总持股比例未达到100%，当前为' . number_format($totalRatio, 3) . '%，请检查持股比例配置', $companyTotal, $personalTotal];
        }

        return [true, '', $companyTotal, $personalTotal];
    }

    /**
     * 保存股东信息
     * @param int $dividendId 分红信息ID
     * @param array $shareholders 股东数据
     * @param int $shareholderType 股东类型 1:公司股东 2:个人股东
     * @return bool
     */
    public function saveShareholders($dividendId, $shareholders, $shareholderType)
    {
        if (empty($shareholders)) {
            return true;
        }

        foreach ($shareholders as $index => $shareholder) {
            if (!empty($shareholder['shareholder_name'])) {
                $shareholderData = [
                    'dividend_store_info_id' => $dividendId,
                    'shareholder_name' => $shareholder['shareholder_name'],
                    'shareholder_type' => $shareholderType,
                    'store_shareholding_ratio' => $shareholder['store_shareholding_ratio'] ?? 0,
                    'sort_order' => $index + 1,
                    'remark' => $shareholder['remark'] ?? '',
                    'create_time' => time(),
                    'update_time' => time()
                ];

                // 如果是公司股东，添加公司内部持股比例
                if ($shareholderType == 1) {
                    $shareholderData['company_shareholding_ratio'] = $shareholder['company_shareholding_ratio'] ?? 0;
                }

                Db::name('DividendShareholder')->insert($shareholderData);
            }
        }

        return true;
    }

    /**
     * 获取门店分红详情
     * @param int $id 分红信息ID
     * @return array
     */
    public function getDividendDetail($id)
    {
        // 获取主表信息
        $detail = Db::name('DividendStoreInfo')
            ->alias('dsi')
            ->join('Department d', 'd.id = dsi.store_id', 'LEFT')
            ->field('dsi.*, d.title as store_name')
            ->where('dsi.id', $id)
            ->find();

        if (empty($detail)) {
            return [];
        }

        // 获取公司股东信息
        $companyShareholders = Db::name('DividendShareholder')
            ->where('dividend_store_info_id', $id)
            ->where('shareholder_type', 1)
            ->where('is_delete', 0)
            ->order('sort_order asc')
            ->select()
            ->toArray();

        // 获取个人股东信息
        $personalShareholders = Db::name('DividendShareholder')
            ->where('dividend_store_info_id', $id)
            ->where('shareholder_type', 2)
            ->where('is_delete', 0)
            ->order('sort_order asc')
            ->select()
            ->toArray();

        return [
            'detail' => $detail,
            'company_shareholders' => $companyShareholders,
            'personal_shareholders' => $personalShareholders
        ];
    }

    /**
     * 删除股东信息（软删除）
     * @param int $dividendId 分红信息ID
     * @return bool
     */
    public function deleteShareholders($dividendId)
    {
        return Db::name('DividendShareholder')
            ->where('dividend_store_info_id', $dividendId)
            ->update([
                'is_delete' => 1,
                'delete_time' => time()
            ]);
    }

    /**
     * 检查门店是否已存在分红信息
     * @param int $storeId 门店ID
     * @param int $excludeId 排除的分红信息ID
     * @return bool
     */
    public function checkStoreExists($storeId, $excludeId = 0)
    {
        $where = [
            ['store_id', '=', $storeId],
            ['is_delete', '=', 0]
        ];

        if ($excludeId > 0) {
            $where[] = ['id', '<>', $excludeId];
        }

        $exists = Db::name('DividendStoreInfo')->where($where)->find();
        return !empty($exists);
    }

    /**
     * 记录风险金变化日志
     * @param int $storeId 门店ID
     * @param int $dividendStoreInfoId 分红配置ID
     * @param int $changeType 变化类型：1=计提,2=调整,3=初始化
     * @param float $beforeAmount 变化前金额
     * @param float $changeAmount 变化金额（正数为增加，负数为减少）
     * @param float $afterAmount 变化后金额
     * @param int|null $dividendDetailId 关联分红明细ID（仅计提时有值）
     * @param string $remark 备注说明
     * @param int $adminId 操作人ID
     * @param string $period 计算周期（如：2025-05）
     * @return bool
     */
    public function addRiskReserveLog($storeId, $dividendStoreInfoId, $changeType, $beforeAmount, $changeAmount, $afterAmount, $dividendDetailId = null, $remark = '', $adminId = 0, $period = '')
    {
        $logData = [
            'store_id' => $storeId,
            'dividend_store_info_id' => $dividendStoreInfoId,
            'change_type' => $changeType,
            'before_amount' => $beforeAmount,
            'change_amount' => $changeAmount,
            'after_amount' => $afterAmount,
            'dividend_detail_id' => $dividendDetailId,
            'remark' => $remark,
            'admin_id' => $adminId
        ];

        // 只有计提类型（change_type=1）才设置period字段
        if ($changeType == 1 && !empty($period)) {
            $logData['period'] = $period;
        }

        return DividendRiskReserveLog::create($logData);
    }

    /**
     * 记录风险金初始化变化
     * @param int $storeId 门店ID
     * @param int $dividendStoreInfoId 分红配置ID
     * @param float $initialAmount 初始金额
     * @param int $adminId 操作人ID
     * @return bool
     */
    public function addRiskReserveInitLog($storeId, $dividendStoreInfoId, $initialAmount, $adminId = 0)
    {
        if ($initialAmount > 0) {
            $remark = '门店分红配置表初始化，设置初始风险金：' . number_format($initialAmount, 2) . '元';
        } else {
            $remark = '门店分红配置表初始化，初始风险金为：0.00元';
        }

        return $this->addRiskReserveLog(
            $storeId,
            $dividendStoreInfoId,
            3, // 3=初始化
            0.00,
            $initialAmount,
            $initialAmount,
            null,
            $remark,
            $adminId,
            '' // 初始化时不需要周期
        );
    }

    /**
     * 记录风险金调整变化
     * @param int $storeId 门店ID
     * @param int $dividendStoreInfoId 分红配置ID
     * @param float $beforeAmount 调整前金额
     * @param float $afterAmount 调整后金额
     * @param string $adjustReason 调整原因
     * @param int $adminId 操作人ID
     * @return bool
     */
    public function addRiskReserveAdjustLog($storeId, $dividendStoreInfoId, $beforeAmount, $afterAmount, $adjustReason = '', $adminId = 0)
    {
        $changeAmount = $afterAmount - $beforeAmount;
        $remark = $adjustReason;

        return $this->addRiskReserveLog(
            $storeId,
            $dividendStoreInfoId,
            2, // 2=调整
            $beforeAmount,
            $changeAmount,
            $afterAmount,
            null,
            $remark,
            $adminId,
            '' // 调整类型不需要周期
        );
    }

    /**
     * 记录风险金计提变化
     * @param int $storeId 门店ID
     * @param int $dividendStoreInfoId 分红配置ID
     * @param float $beforeAmount 计提前金额
     * @param float $provisionAmount 计提金额
     * @param float $afterAmount 计提后金额
     * @param int $dividendDetailId 关联分红明细ID
     * @param int $adminId 操作人ID
     * @param string $period 计算周期（如：2025-04）
     * @return bool
     */
    public function addRiskReserveProvisionLog($storeId, $dividendStoreInfoId, $beforeAmount, $provisionAmount, $afterAmount, $dividendDetailId, $adminId = 0, $period = '')
    {
        $remark = '分红计算时自动计提风险金：' . number_format($provisionAmount, 2) . '元';
        if (!empty($period)) {
            $remark .= '（计算周期：' . $period . '）';
        }

        return $this->addRiskReserveLog(
            $storeId,
            $dividendStoreInfoId,
            1, // 1=计提
            $beforeAmount,
            $provisionAmount,
            $afterAmount,
            $dividendDetailId,
            $remark,
            $adminId,
            $period // 将周期传递给基础方法
        );
    }
}
