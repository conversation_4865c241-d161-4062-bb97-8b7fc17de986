{extend name="../../base/view/common/base" /}
<!-- 主体 -->
{block name="body"}
<div class="p-3">
	<form class="layui-form gg-form-bar border-x border-t">
		<div class="layui-input-inline" style="width:150px;">
			<select name="cate_id">
				<option value="">请选择合同类别</option>
				{volist name=":contract_cate()" id="v"}
				<option value="{$v.id}">{$v.title}</option>
				{/volist}
			</select>
		</div>
		<div class="layui-input-inline" style="width:150px;">
			<select name="type">
				<option value="">请选择合同性质</option>
				<option value="1">普通合同</option>
				<option value="2">框架合同</option>
				<option value="3">补充协议</option>
				<option value="4">其他合同</option>
			</select>
		</div>
		<div class="layui-input-inline" style="width:240px;">
			<input type="text" name="keywords" placeholder="输入关键字" class="layui-input" autocomplete="off" />
		</div>
		<div class="layui-input-inline" style="width:150px;">
			<button class="layui-btn layui-btn-normal" lay-submit="" lay-filter="webform"><i class="layui-icon layui-icon-search mr-1"></i>搜索</button>
			<button type="reset" class="layui-btn layui-btn-reset" lay-filter="reset">清空</button>
		</div>
	</form>
	<table class="layui-hide" id="test" lay-filter="test"></table>
</div>
{/block}
<!-- /主体 -->

<!-- 脚本 -->
{block name="script"}
<script>
	const moduleInit = ['tool'];
	function gouguInit() {
		var table = layui.table, tool = layui.tool ,form = layui.form;
		layui.pageTable = table.render({
			elem: '#test',
			title: '合同归档列表',
			url: "/contract/index/archive", //数据接口
			cellMinWidth: 80,
			page: true, //开启分页
			limit: 20,
			cols: [
				[ //表头
					{
						field: 'id',
						title: '编号',
						align: 'center',
						width: 80
					},{ field: 'check_status', title: '状态', align: 'center', width: 80, templet: function (d) {
						var html = '<span class="layui-btn layui-btn-xs layui-bg-' + d.check_status + '">' + d.status_name + '</span>';
						return html;
						}
					},{
						field: 'code',
						title: '合同编号',
						width: 160
					},{
						field: 'name',
						title: '合同名称',
						minWidth:240,
						templet: '<div><a data-href="/contract/index/view/id/{{d.id}}.html" class="side-a">{{d.name}}</a></div>'
					}, {
						field: 'cate_title',
						title: '合同类别',
						align: 'center',
						width: 100
					}, {
						field: 'type_name',
						title: '合同性质',
						align: 'center',
						width: 80,
						templet: function (d) {
							var html = '<span class="layui-color-' + d.type + '">' + d.type_name + '</span>';
							return html;
						}
					},{
						field: 'cost',
						title: '合同金额/元',
						align: 'right',
						width: 100
					}, {
						field: 'sign_name',
						title: '签定人',
						align: 'center',
						width: 80
					},{
						field: 'keeper_name',
						title: '保管人',
						align: 'center',
						width: 80
					}, {
						field: 'sign_time',
						title: '签订时间',
						align: 'center',
						width: 100
					}, {
						field: 'right',
						fixed:'right',
						title: '操作',
						width: 100,
						align: 'center',
						templet: function (d) {
							return '<span class="layui-btn layui-btn-normal layui-btn-xs" lay-event="view">详情</span>';
						}
					}
				]
			]
		});
		
		//表头工具栏事件
		table.on('toolbar(test)', function(obj){
			if (obj.event === 'add') {
				selectType();
				return;
			}
		});
		//监听行工具事件
		table.on('tool(test)', function(obj) {
			var data = obj.data;
			if(obj.event === 'view'){
				tool.side('/contract/index/view?id='+data.id);
				return;
			}
		});
		

		//监听搜索提交
		form.on('submit(webform)', function(data) {
			layui.pageTable.reload({
				where: {
					keywords: data.field.keywords,
					cate_id: data.field.cate_id,
					type: data.field.type
				},
				page: {
					curr: 1
				}
			});
			return false;
		});
	}
</script>
{/block}
<!-- /脚本 -->
