<div class="pb-3">
	<button class="layui-btn layui-btn-sm add-chance">+ 新建销售机会</button>
</div>
<div>
	<table class="layui-hide" id="chance" lay-filter="chance"></table>
</div>
<script>
function chance(){
	if($('#customerTab').find('li').eq(2).data('load') =='true'){
		return false;
	}
	$('#customerTab').find('li').eq(2).data('load','true');
	let tool = layui.tool, table = layui.table;
	//项目任务
	parent.layui.chanceTable = table.render({
		elem: '#chance',
		title: '销售机会列表',
		cellMinWidth:80,
		url: "/customer/api/get_chance",
		where:{'customer_id':customer_id},
		page: true, //开启分页
		limit: 20,
		cols:  [[
			{field: 'id', title: '编号', width: 80, align: 'center'}
			,{field:'belong_name',title: '归属人',align:'center',width: 80}
			,{field:'title',title: '机会主题'}
			,{field:'discovery_time',title: '发现时间',align:'center',width: 100}
			,{ field: 'expected_time', title: '预计签单时间', width: 110, align: 'center'}
			,{ field: 'expected_amount', title: '预计签单金额', width: 110, align: 'center'}
			,{field:'stage_name',title: '当前阶段',width: 80, align: 'center'}
			,{fixed:'right',width:132,title: '操作', align:'center',templet: function(d){
					var html = '<div class="layui-btn-group">';
					var btn='<a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>';
					var btn1='<a class="layui-btn layui-btn-normal layui-btn-xs" lay-event="view">详情</a>';
					var btn2='<a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">删除</a>';
					return html+btn+btn1+btn2+'</div>';
			}}			
		]]
	});
	$('.add-chance').on('click',function(){
		parent.layui.tool.side('/customer/api/add_chance?cid='+customer_id);
	});
	
	table.on('tool(chance)', function(obj){
		var data = obj.data; //获得当前行数据
		var layEvent = obj.event;		 
		if(layEvent === 'edit'){ //编辑
			let url = '/customer/api/add_chance/id/'+data.id;
			parent.layui.tool.side(url);
		}
		if(layEvent === 'view'){ //查看
			let url = '/customer/api/view_chance/id/'+data.id;
			parent.layui.tool.side(url);
		}
		if(layEvent === 'del'){ //删除
			layer.confirm('确定要删除该销售机会吗?', {icon: 3, title:'提示'}, function(index){
				let callback = function (e) {
					layer.msg(e.msg);
					if (e.code == 0) {
						parent.layui.chanceTable.reload();
					}
				}
				tool.delete("/customer/api/delete_chance",{"id":data.id},callback);
				layer.close(index);
			});
		}
		return false;
	})
}
</script>