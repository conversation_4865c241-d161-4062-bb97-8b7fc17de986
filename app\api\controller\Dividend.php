<?php
/**
 * @copyright Copyright (c) 2021 仲正堂工作室
 * @license https://opensource.org/licenses/GPL-3.0
 * @link https://www.gougucms.com
 */

declare (strict_types=1);

namespace app\api\controller;

use app\base\BaseController;
use think\facade\Db;
use think\facade\Log;

class Dividend extends BaseController
{
    /**
     * 获取人员列表（用于股东选择）
     * @return \think\response\Json
     */
    public function getAdminList()
    {
        try {
            // 获取请求参数
            $param = get_params();
            $keywords = $param['keywords'] ?? '';

            // 构建查询条件
            $where = [
                ['a.status', '>', 0] // 状态大于0表示正常和离职员工，排除删除(-1)和禁止登录(0)
            ];

            // 如果有关键词搜索，添加姓名模糊查询
            if (!empty($keywords)) {
                $where[] = ['a.name', 'like', '%' . $keywords . '%'];
            }

            // 查询人员列表，只返回必要字段
            $list = Db::name('Admin')
                ->alias('a')
                ->where($where)
                ->field('a.id, a.name')
                ->order('a.id asc')
                ->select()
                ->toArray();

            return json([
                'code' => 0,
                'msg' => '获取成功',
                'data' => $list
            ]);

        } catch (\Exception $e) {
            // 记录错误日志
            Log::error('获取人员列表失败：' . $e->getMessage());
            return json([
                'code' => 1,
                'msg' => '获取人员列表失败：' . $e->getMessage()
            ]);
        }
    }

    /**
     * 根据门店ID获取门店相关人员列表
     * @return \think\response\Json
     */
    public function getStoreAdminList()
    {
        try {
            $param = get_params();
            $store_id = $param['store_id'] ?? 0;
            $keywords = $param['keywords'] ?? '';

            if (empty($store_id)) {
                return json(['code' => 1, 'msg' => '门店ID不能为空']);
            }

            // 构建查询条件
            $where = [
                ['status', '>', 0] // 状态大于0表示正常和离职员工，排除删除(-1)和禁止登录(0)
            ];

            // 添加部门条件（查找该门店的人员）
            $where[] = ['did', 'like', '%' . $store_id . '%'];

            // 如果有关键词搜索，添加姓名模糊查询
            if (!empty($keywords)) {
                $where[] = ['name', 'like', '%' . $keywords . '%'];
            }

            // 查询人员列表，只返回必要字段
            $list = Db::name('Admin')
                ->where($where)
                ->field('id, name')
                ->order('id asc')
                ->select()
                ->toArray();

            return json([
                'code' => 0,
                'msg' => '获取成功',
                'data' => $list
            ]);

        } catch (\Exception $e) {
            // 记录错误日志
            Log::error('获取门店人员列表失败：' . $e->getMessage());
            return json([
                'code' => 1,
                'msg' => '获取门店人员列表失败：' . $e->getMessage()
            ]);
        }
    }

    /**
     * 获取门店列表（用于搜索下拉框）
     * @return \think\response\Json
     */
    public function getStoreList()
    {
        try {
            // 获取所有门店列表
            $storeList = Db::name('Department')
                ->where('status', '>=', 0)
                ->where('remark', '门店')
                ->field('id, title')
                ->order('sort asc, id asc')
                ->select()
                ->toArray();

            return json([
                'code' => 0,
                'msg' => '获取成功',
                'data' => $storeList
            ]);
        } catch (\Exception $e) {
            Log::error('获取门店列表失败: ' . $e->getMessage());
            return json([
                'code' => 1,
                'msg' => '获取门店列表失败'
            ]);
        }
    }

    /**
     * 根据门店ID获取股东信息（用于分红明细）
     * @return \think\response\Json
     */
    public function getShareholdersByStore()
    {
        try {
            $param = get_params();
            $store_id = $param['store_id'] ?? 0;

            if (empty($store_id)) {
                return json(['code' => 1, 'msg' => '门店ID不能为空']);
            }

            // 先查找该门店的分红信息
            $storeInfo = Db::name('DividendStoreInfo')
                ->where('store_id', $store_id)
                ->where('is_delete', 0)
                ->find();

            if (empty($storeInfo)) {
                return json(['code' => 1, 'msg' => '该门店暂无分红信息配置']);
            }

            // 获取该门店的股东信息
            $shareholders = Db::name('DividendShareholder')
                ->alias('ds')
                ->leftJoin('Admin a', 'ds.admin_id = a.id')
                ->field('ds.id, ds.shareholder_name, ds.shareholder_type, ds.store_shareholding_ratio, a.name as admin_name')
                ->where('ds.dividend_store_info_id', $storeInfo['id'])
                ->where('ds.is_delete', 0)
                ->order('ds.shareholder_type asc, ds.sort_order asc')
                ->select()
                ->toArray();

            // 处理返回数据
            foreach ($shareholders as &$item) {
                $item['type_text'] = $item['shareholder_type'] == 1 ? '公司股东' : '个人股东';
                $item['display_name'] = !empty($item['admin_name']) ? $item['admin_name'] : $item['shareholder_name'];
            }

            return json([
                'code' => 0,
                'msg' => '获取成功',
                'data' => [
                    'store_info' => $storeInfo,
                    'shareholders' => $shareholders
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('获取门店股东信息失败: ' . $e->getMessage());
            return json([
                'code' => 1,
                'msg' => '获取门店股东信息失败'
            ]);
        }
    }

    /**
     * 计算门店风险金总金额
     * @return \think\response\Json
     */
    public function calculateTotalRiskReserve()
    {
        try {
            $param = get_params();
            $store_id = $param['store_id'] ?? 0;
            $current_risk_reserve = floatval($param['current_risk_reserve'] ?? 0);

            if (empty($store_id)) {
                return json(['code' => 1, 'msg' => '门店ID不能为空']);
            }

            // 获取门店分红信息中的已计提风险金
            $storeInfo = Db::name('DividendStoreInfo')
                ->where('store_id', $store_id)
                ->where('is_delete', 0)
                ->find();

            $existingRiskReserve = $storeInfo['risk_reserve'] ?? 0;

            // 获取该门店历史分红明细中的风险金计提总额
            $historicalRiskReserve = Db::name('DividendStoreDetail')
                ->where('store_id', $store_id)
                ->where('is_delete', 0)
                ->sum('risk_reserve_current');

            $totalRiskReserve = $existingRiskReserve + $historicalRiskReserve + $current_risk_reserve;

            return json([
                'code' => 0,
                'msg' => '计算成功',
                'data' => [
                    'existing_risk_reserve' => number_format($existingRiskReserve, 2),
                    'historical_risk_reserve' => number_format($historicalRiskReserve, 2),
                    'current_risk_reserve' => number_format($current_risk_reserve, 2),
                    'total_risk_reserve' => number_format($totalRiskReserve, 2)
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('计算风险金总金额失败: ' . $e->getMessage());
            return json([
                'code' => 1,
                'msg' => '计算风险金总金额失败'
            ]);
        }
    }

    /**
     * 获取公司股东分红统计信息
     * @return \think\response\Json
     */
    public function getCompanyDividendStatistics()
    {
        try {
            $param = get_params();
            $period = $param['period'] ?? '';
            $store_ids = $param['store_ids'] ?? [];

            if (empty($period)) {
                return json(['code' => 1, 'msg' => '统计周期不能为空']);
            }

            // 构建查询条件
            $where = [
                ['period', '=', $period],
                ['is_delete', '=', 0]
            ];

            if (!empty($store_ids) && is_array($store_ids)) {
                $where[] = ['store_id', 'in', $store_ids];
            }

            // 获取公司分红统计数据
            $statistics = Db::name('DividendCompanyDetail')
                ->where($where)
                ->field([
                    'COUNT(*) as store_count',
                    'SUM(dividend_profit) as total_dividend_profit'
                ])
                ->find();

            // 获取分红人统计数据
            $personStatistics = Db::name('DividendCompanyDetailPerson')
                ->alias('dcdp')
                ->join('oa_dividend_company_detail dcd', 'dcd.id = dcdp.company_detail_id', 'LEFT')
                ->where($where)
                ->where('dcdp.is_delete', 0)
                ->field([
                    'SUM(dcdp.amount) as total_amount',
                    'SUM(dcdp.adjustment_amount) as total_adjustment_amount',
                    'SUM(dcdp.payable_amount) as total_payable_amount'
                ])
                ->find();

            $result = [
                'store_count' => intval($statistics['store_count'] ?? 0),
                'total_dividend_profit' => floatval($statistics['total_dividend_profit'] ?? 0),
                'total_amount' => floatval($personStatistics['total_amount'] ?? 0),
                'total_adjustment_amount' => floatval($personStatistics['total_adjustment_amount'] ?? 0),
                'total_payable_amount' => floatval($personStatistics['total_payable_amount'] ?? 0)
            ];

            return json([
                'code' => 0,
                'msg' => '获取成功',
                'data' => $result
            ]);
        } catch (\Exception $e) {
            Log::error('获取公司股东分红统计信息失败: ' . $e->getMessage());
            return json([
                'code' => 1,
                'msg' => '获取统计信息失败'
            ]);
        }
    }

    /**
     * 执行公司股东分红计算
     * @return \think\response\Json
     */
    public function calculateCompanyDividend()
    {
        try {
            $param = get_params();
            $period = $param['period'] ?? '';

            if (empty($period)) {
                return json(['code' => 1, 'msg' => '统计周期不能为空']);
            }

            // 引入计算服务
            $calculateService = new \app\dividend\service\DividendCompanyCalculateService();
            $result = $calculateService::calculateCompanyDividend($period, $this->uid);

            if ($result['success']) {
                return json([
                    'code' => 0,
                    'msg' => $result['message'],
                    'data' => $result['data']
                ]);
            } else {
                return json([
                    'code' => 1,
                    'msg' => $result['message']
                ]);
            }
        } catch (\Exception $e) {
            Log::error('执行公司股东分红计算失败: ' . $e->getMessage());
            return json([
                'code' => 1,
                'msg' => '计算失败：' . $e->getMessage()
            ]);
        }
    }

    /**
     * 获取股东分红清单列表（用于分红清单页面）
     * @return \think\response\Json
     */
    public function getPaymentList()
    {
        try {
            $param = get_params();
            $year = $param['year'] ?? date('Y');
            $shareholderName = $param['shareholder_name'] ?? '';

            // 使用分红清单模型
            $model = new \app\dividend\model\DividendPayment();
            $where = [
                'year' => $year,
                'shareholder_name' => $shareholderName
            ];

            $list = $model->getPaymentList($where);

            return json([
                'code' => 0,
                'msg' => '获取成功',
                'data' => $list
            ]);
        } catch (\Exception $e) {
            Log::error('获取股东分红清单失败: ' . $e->getMessage());
            return json([
                'code' => 1,
                'msg' => '获取股东分红清单失败'
            ]);
        }
    }

    /**
     * 获取分红人年度数据（用于编辑页面）
     * @return \think\response\Json
     */
    public function getShareholderYearData()
    {
        try {
            $param = get_params();
            $shareholderName = $param['shareholder_name'] ?? '';
            $year = $param['year'] ?? date('Y');

            if (empty($shareholderName)) {
                return json(['code' => 1, 'msg' => '分红人姓名不能为空']);
            }

            // 使用分红清单模型
            $model = new \app\dividend\model\DividendPayment();
            $data = $model->getShareholderYearData($shareholderName, $year);

            // 获取该分红人的shareholder_ids（从已有数据中获取）
            $shareholderIds = '';
            if (!empty($data)) {
                $shareholderIds = $data[0]['shareholder_ids'] ?? '';
            }

            // 构建12个月的完整数据
            $months = [];
            for ($i = 1; $i <= 12; $i++) {
                $months[] = sprintf('%s-%02d', $year, $i);
            }

            $yearData = [];
            foreach ($months as $month) {
                $monthData = [
                    'period' => $month,
                    'payable_amount' => '0.00',
                    'adjustment_amount' => '0.00',
                    'actual_payable_amount' => '0.00',
                    'paid_amount' => '0.00',
                    'unpaid_amount' => '0.00',
                    'remark' => '',
                    'shareholder_ids' => $shareholderIds,  // 添加shareholder_ids字段
                    'exists_in_db' => false  // 标识数据是否在数据库中真实存在
                ];

                // 查找对应月份的数据
                foreach ($data as $item) {
                    if ($item['period'] == $month) {
                        $monthData = $item;
                        $monthData['exists_in_db'] = true;  // 数据库中存在的数据
                        break;
                    }
                }

                $yearData[] = $monthData;
            }

            return json([
                'code' => 0,
                'msg' => '获取成功',
                'data' => [
                    'shareholder_name' => $shareholderName,
                    'year' => $year,
                    'shareholder_ids' => $shareholderIds,  // 在顶层也返回shareholder_ids
                    'data' => $yearData
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('获取分红人年度数据失败: ' . $e->getMessage());
            return json([
                'code' => 1,
                'msg' => '获取分红人年度数据失败'
            ]);
        }
    }

    /**
     * 更新股东分红清单表
     * @return \think\response\Json
     */
    public function updatePaymentData()
    {
        try {
            $param = get_params();
            $shareholderName = $param['shareholder_name'] ?? '';
            $shareholderIds = $param['shareholder_ids'] ?? '';  // 获取shareholder_ids
            $monthlyData = $param['monthly_data'] ?? [];

            if (empty($shareholderName)) {
                return json(['code' => 1, 'msg' => '分红人姓名不能为空']);
            }

            if (empty($monthlyData)) {
                return json(['code' => 1, 'msg' => '月度数据不能为空']);
            }

            // 验证和处理数据
            foreach ($monthlyData as &$data) {
                // 确保每条数据都包含shareholder_ids
                if (empty($data['shareholder_ids']) && !empty($shareholderIds)) {
                    $data['shareholder_ids'] = $shareholderIds;
                }

                // 计算实际应付金额和未付金额
                $payableAmount = floatval($data['payable_amount'] ?? 0);
                $adjustmentAmount = floatval($data['adjustment_amount'] ?? 0);
                $paidAmount = floatval($data['paid_amount'] ?? 0);

                $data['actual_payable_amount'] = $payableAmount - $adjustmentAmount;
                $data['unpaid_amount'] = $data['actual_payable_amount'] - $paidAmount;
            }

            // 使用分红清单模型
            $model = new \app\dividend\model\DividendPayment();

            if ($model->batchUpdatePaymentData($shareholderName, $monthlyData)) {
                return json([
                    'code' => 0,
                    'msg' => '保存成功'
                ]);
            } else {
                return json([
                    'code' => 1,
                    'msg' => '保存失败'
                ]);
            }
        } catch (\Exception $e) {
            Log::error('更新股东分红清单表失败: ' . $e->getMessage());
            return json([
                'code' => 1,
                'msg' => '更新数据失败：' . $e->getMessage()
            ]);
        }
    }
}