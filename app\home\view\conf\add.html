{extend name="../../base/view/common/base" /}
<!-- 主体 -->
{block name="body"}
<form class="layui-form p-4">
	<h3 class="pb-3">配置项</h3>
	<table class="layui-table layui-table-form">
		<tr>
			<td class="layui-td-gray">配置名称<font>*</font>
			</td>
			<td>
				<input type="hidden" name="id" value="{$id}" />
				<input type="text" name="title" lay-verify="required" autocomplete="off" placeholder="请输入配置名称" lay-reqText="请输入配置名称" class="layui-input" value="{$config.title|default=''}">
			</td>
			<td class="layui-td-gray">状态<font>*</font>
			</td>
			<td>{if condition="$id eq 0"}
				<input type="radio" name="status" value="1" title="正常" checked>
				<input type="radio" name="status" value="0" title="禁用">
				{else/}
				<input type="radio" name="status" value="1" title="正常" {eq name="$config.status" value="1"} checked{/eq}>
				<input type="radio" name="status" value="0" title="禁用" {eq name="$config.status" value="0"} checked{/eq}>
				{/if}
			</td>
		</tr>
		<tr>
			<td class="layui-td-gray">
				配置标识<font>*</font>
			</td>
			<td colspan="3">
				<input type="text" name="name" lay-verify="required" placeholder="请输入配置标识" lay-reqText="请输入配置标识"
					autocomplete="off" class="layui-input" value="{$config.name|default=''}">
			</td>
		</tr>
		<tr>
			<td colspan="4">
				<span class="red">注意：新增配置项以后，需要对应新增模板文件，模板文件名称需与标识名称一致，建议复制现有的配置模板文件，然后根据需求修改对应的表单即可。</span>
			</td>
		</tr>
	</table>
	<div class="py-3">
		<button class="layui-btn layui-btn-normal" lay-submit="" lay-filter="webform">立即提交</button>
		<button type="reset" class="layui-btn layui-btn-primary">重置</button>
	</div>
</form>
{/block}
<!-- /主体 -->

<!-- 脚本 -->
{block name="script"}
<script>
	const moduleInit = ['tool'];
	function gouguInit() {
		var form = layui.form, tool = layui.tool;
		//监听提交
		form.on('submit(webform)', function (data) {
			let callback = function (e) {
				layer.msg(e.msg);
				if (e.code == 0) {
					tool.sideClose(1000);
				}
			}
			tool.post("/home/<USER>/add", data.field, callback);
			return false;
		});
	}
</script>
{/block}
<!-- /脚本 -->