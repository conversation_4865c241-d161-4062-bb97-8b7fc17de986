<?php
/**
 * @copyright Copyright (c) 2021 仲正堂工作室
 * @license https://opensource.org/licenses/GPL-3.0
 * @link https://www.gougucms.com
 */

namespace app\cross\validate;

use think\Validate;

class CrossStoreSettleCardBalanceCheck extends Validate
{
    protected $rule = [
        'period' => 'require|regex:/^\d{4}-\d{2}$/',
        'store_id' => 'require|integer|gt:0',
        'card_balance' => 'require|float|egt:0',
        'logical_store_type' => 'require|in:新店,老店',
        'previous_month_logical_type' => 'require|in:新店,老店',
        'settlement_store_type' => 'require|in:新店,老店',
    ];

    protected $message = [
        'period.require' => '月份不能为空',
        'period.regex' => '月份格式错误，应为YYYY-MM格式',
        'store_id.require' => '门店不能为空',
        'store_id.integer' => '门店ID必须为整数',
        'store_id.gt' => '门店ID必须大于0',
        'card_balance.require' => '卡余额不能为空',
        'card_balance.float' => '卡余额必须为数字',
        'card_balance.egt' => '卡余额不能小于0',
        'logical_store_type.require' => '逻辑门店类型不能为空',
        'logical_store_type.in' => '逻辑门店类型只能是新店或老店',
        'previous_month_logical_type.require' => '上月逻辑类型不能为空',
        'previous_month_logical_type.in' => '上月逻辑类型只能是新店或老店',
        'settlement_store_type.require' => '结算门店类型不能为空',
        'settlement_store_type.in' => '结算门店类型只能是新店或老店',
    ];

    protected $scene = [
        'edit' => ['period', 'store_id', 'card_balance', 'logical_store_type', 'previous_month_logical_type', 'settlement_store_type'],
    ];
}