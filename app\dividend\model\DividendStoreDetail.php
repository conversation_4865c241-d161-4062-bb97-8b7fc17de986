<?php
/**
 * @copyright Copyright (c) 2021 仲正堂工作室
 * @license https://opensource.org/licenses/GPL-3.0
 * @link https://www.gougucms.com
 */

declare (strict_types=1);

namespace app\dividend\model;

use think\Model;

class DividendStoreDetail extends Model
{
    protected $name = 'dividend_store_detail';

    // 设置字段信息
    protected $schema = [
        'id'                          => 'int',
        'store_id'                    => 'int',
        'period'                      => 'string',
        'income'                      => 'decimal',
        'expense'                     => 'decimal',
        'risk_reserve_current'        => 'decimal',
        'other_adjustment'            => 'decimal',
        'dividend_profit'             => 'decimal',
        'risk_reserve_total'          => 'decimal',
        'company_shareholding_ratio'  => 'decimal',
        'remark'                      => 'string',
        'is_delete'                   => 'int',
        'delete_time'                 => 'int',
        'create_time'                 => 'int',
        'update_time'                 => 'int',
    ];

    // 自动时间戳
    protected $autoWriteTimestamp = true;
    protected $createTime = 'create_time';
    protected $updateTime = 'update_time';

    /**
     * 关联门店信息
     */
    public function store()
    {
        return $this->belongsTo('app\user\model\Department', 'store_id', 'id');
    }

    /**
     * 关联分红人信息
     */
    public function persons()
    {
        return $this->hasMany('app\dividend\model\DividendStoreDetailPerson', 'dividend_store_detail_id', 'id');
    }

    /**
     * 获取门店分红明细列表
     * @param array $where 查询条件
     * @param int $page 页码
     * @param int $limit 每页数量
     * @return array
     */
    public static function getList($where = [], $page = 1, $limit = 20)
    {
        $query = self::alias('dsd')
            ->join('oa_department d', 'd.id = dsd.store_id', 'LEFT')
            ->field('dsd.*, d.title as store_name')
            ->where('dsd.is_delete', 0);

        if (!empty($where)) {
            $query->where($where);
        }

        $list = $query->order('dsd.id desc')
            ->paginate($limit, false, ['page' => $page])
            ->toArray();

        // 处理数据格式
        foreach ($list['data'] as &$item) {
            // 格式化金额显示
            $item['income_formatted'] = number_format($item['income'], 2);
            $item['expense_formatted'] = number_format($item['expense'], 2);
            $item['risk_reserve_current_formatted'] = number_format($item['risk_reserve_current'], 2);
            $item['other_adjustment_formatted'] = number_format($item['other_adjustment'], 2);
            $item['dividend_profit_formatted'] = number_format($item['dividend_profit'], 2);
            $item['risk_reserve_total_formatted'] = number_format($item['risk_reserve_total'], 2);

            // 格式化比例显示
            $item['company_shareholding_ratio_formatted'] = number_format($item['company_shareholding_ratio'], 3) . '%';
        }

        return $list;
    }

    /**
     * 获取门店分红明细详情
     * @param int $id 分红明细ID
     * @return array
     */
    public static function getDetail($id)
    {
        $detail = self::alias('dsd')
            ->join('oa_department d', 'd.id = dsd.store_id', 'LEFT')
            ->field('dsd.*, d.title as store_name')
            ->where('dsd.id', $id)
            ->where('dsd.is_delete', 0)
            ->find();

        if (empty($detail)) {
            return [];
        }

        // 获取分红人信息
        $detail['persons'] = DividendStoreDetailPerson::where('dividend_store_detail_id', $id)
            ->where('is_delete', 0)
            ->order('shareholder_type asc, id asc')
            ->select()
            ->toArray();

        return $detail;
    }



    /**
     * 计算合计行数据
     * @param array $where 查询条件
     * @return array
     */
    public static function getTotalRow($where = [])
    {
        $query = self::alias('dsd')
            ->where('dsd.is_delete', 0);

        if (!empty($where)) {
            $query->where($where);
        }

        $totalData = $query->field([
            'SUM(dsd.income) as total_income',
            'SUM(dsd.expense) as total_expense',
            'SUM(dsd.risk_reserve_current) as total_risk_reserve_current',
            'SUM(dsd.other_adjustment) as total_other_adjustment',
            'SUM(dsd.dividend_profit) as total_dividend_profit',
            'SUM(dsd.risk_reserve_total) as total_risk_reserve_total'
        ])->find();

        if (empty($totalData)) {
            return [];
        }

        return [
            'store_name' => '合计',
            'period' => '-',
            'income' => number_format(floatval($totalData['total_income'] ?? 0), 2),
            'expense' => number_format(floatval($totalData['total_expense'] ?? 0), 2),
            'risk_reserve_current' => number_format(floatval($totalData['total_risk_reserve_current'] ?? 0), 2),
            'other_adjustment' => number_format(floatval($totalData['total_other_adjustment'] ?? 0), 2),
            'dividend_profit' => number_format(floatval($totalData['total_dividend_profit'] ?? 0), 2),
            'risk_reserve_total' => number_format(floatval($totalData['total_risk_reserve_total'] ?? 0), 2),
            'company_shareholding_ratio' => '-',
            'remark' => '-'
        ];
    }
}
