<?php
/**
 * @copyright Copyright (c) 2021 仲正堂工作室
 * @license https://opensource.org/licenses/GPL-3.0
 * @link https://www.gougucms.com
 */

declare (strict_types=1);

namespace app\dividend\controller;

use app\base\BaseController;
use app\dividend\validate\DividendCheck;
use app\dividend\service\DividendService;
use think\exception\ValidateException;
use think\facade\Db;
use think\facade\View;
use think\facade\Log;
use think\App;
use systematic\ConfigManager;

class Index extends BaseController
{
    protected $dividendService;

    public function __construct(App $app)
    {
        parent::__construct($app);
        $this->dividendService = new DividendService();
    }

    /**
     * 检查当前用户是否有数据查看权限
     * @return bool
     */
    private function hasDataAccess()
    {
        try {
            // 获取当前用户信息
            $currentUser = get_admin($this->uid);
            if (!$currentUser) {
                return false;
            }

            // 从ConfigManager获取分红系统数据访问权限配置
            $permissions = ConfigManager::get('dividend_data_access_permissions', [
                'positions' => [],
                'users' => [],
                'button_access_users' => []
            ]);

            $allowedPositions = $permissions['positions'] ?? [];
            $allowedUsers = $permissions['users'] ?? [];
            $buttonAccessUsers = $permissions['button_access_users'] ?? [];

            // 检查用户ID是否在允许列表中
            if (in_array($this->uid, $allowedUsers)) {
                return true;
            }

            // 检查用户ID是否在按钮访问允许列表中
            if (in_array($this->uid, $buttonAccessUsers)) {
                return true;
            }

            // 检查职位ID是否在允许列表中
            if (!empty($currentUser['position_id']) && in_array($currentUser['position_id'], $allowedPositions)) {
                return true;
            }

            return false;
        } catch (\Exception $e) {
            Log::error('检查分红数据访问权限失败: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * 检查当前用户是否有按钮操作权限
     * @return bool
     */
    private function hasButtonAccess()
    {
        try {
            // 从ConfigManager获取分红系统数据访问权限配置
            $permissions = ConfigManager::get('dividend_data_access_permissions', [
                'positions' => [],
                'users' => [],
                'button_access_users' => []
            ]);

            $buttonAccessUsers = $permissions['button_access_users'] ?? [];

            // 检查用户ID是否在按钮访问允许列表中
            return in_array($this->uid, $buttonAccessUsers);
        } catch (\Exception $e) {
            Log::error('检查分红按钮访问权限失败: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * 门店分红信息列表
     */
    public function index()
    {
        // 权限检查：如果用户没有数据查看权限，则返回友好的错误页面
        if (!$this->hasDataAccess()) {
            Log::info('[门店分红]-[门店分红配置表]：用户无权限访问门店分红配置表，用户ID：' . $this->uid);
            if (request()->isAjax()) {
                return [
                    'code' => 1,
                    'msg' => '无权限访问',
                    'count' => 0,
                    'data' => []
                ];
            } else {
                // 返回友好的错误页面
                View::assign('error_msg', '抱歉，您没有权限访问门店分红配置表');
                View::assign('error_title', '访问受限');
                return view('error/permission');
            }
        }

        if (request()->isAjax()) {
            $param = get_params();

            // 构建查询条件
            $where = [];
            $shareholderWhere = [];

            if (!empty($param['store_ids']) && is_array($param['store_ids'])) {
                // 通过门店ID数组搜索
                $where[] = ['dsi.store_id', 'in', $param['store_ids']];
            }

            if (!empty($param['shareholder_name'])) {
                // 通过股东姓名搜索，需要关联股东表
                $shareholderWhere[] = ['ds.shareholder_name', 'like', '%' . $param['shareholder_name'] . '%'];
                $shareholderWhere[] = ['ds.is_delete', '=', 0];
            }

            $where[] = ['dsi.is_delete', '=', 0];

            // 分页查询
            $rows = empty($param['limit']) ? 10000 : $param['limit'];

            $query = Db::name('DividendStoreInfo')
                ->alias('dsi')
                ->join('Department d', 'd.id = dsi.store_id', 'LEFT');

            // 如果有股东姓名搜索条件，需要关联股东表
            if (!empty($shareholderWhere)) {
                $query->join('DividendShareholder ds', 'ds.dividend_store_info_id = dsi.id', 'INNER')
                      ->where($shareholderWhere)
                      ->group('dsi.id'); // 避免重复记录
            }

            $list = $query->field('dsi.*, d.title as store_name')
                ->where($where)
                ->order('dsi.id desc')
                ->paginate($rows, false, ['query' => $param])
                ->each(function ($item) {
                    // 确保基础字段存在
                    $item['id'] = $item['id'] ?? 0;
                    $item['store_name'] = $item['store_name'] ?? '未知门店';

                    // 计算公司股东持股比例总和
                    $companyTotal = Db::name('DividendShareholder')
                        ->where('dividend_store_info_id', $item['id'])
                        ->where('shareholder_type', 1)
                        ->where('is_delete', 0)
                        ->sum('store_shareholding_ratio');
                    // 确保是数值类型，避免number_format报错
                    $companyTotal = is_numeric($companyTotal) ? floatval($companyTotal) : 0;
                    $item['company_shareholding_total'] = number_format($companyTotal, 3);

                    // 计算个人股东持股比例总和
                    $personalTotal = Db::name('DividendShareholder')
                        ->where('dividend_store_info_id', $item['id'])
                        ->where('shareholder_type', 2)
                        ->where('is_delete', 0)
                        ->sum('store_shareholding_ratio');
                    // 确保是数值类型，避免number_format报错
                    $personalTotal = is_numeric($personalTotal) ? floatval($personalTotal) : 0;
                    $item['personal_shareholding_total'] = number_format($personalTotal, 3);

                    // 格式化已计提风险金
                    $riskReserve = is_numeric($item['risk_reserve']) ? floatval($item['risk_reserve']) : 0;
                    $item['risk_reserve_formatted'] = number_format($riskReserve, 2);

                    // 格式化创建时间
                    $createTime = $item['create_time'] ?? time();
                    $item['create_time'] = is_numeric($createTime) ? date('Y-m-d H:i:s', $createTime) : $createTime;

                    return $item;
                });

            // 计算合计数据
            $totalRowData = [];
            if (count($list) > 0) {
                // 计算已计提风险金总和
                $totalQuery = Db::name('DividendStoreInfo')
                    ->alias('dsi');

                // 如果有股东姓名搜索条件，需要关联股东表
                if (!empty($shareholderWhere)) {
                    $totalQuery->join('DividendShareholder ds', 'ds.dividend_store_info_id = dsi.id', 'INNER')
                              ->where($shareholderWhere)
                              ->group('dsi.id'); // 避免重复记录
                }

                $totalRiskReserve = $totalQuery->where($where)->sum('dsi.risk_reserve');

                $totalRowData = [
                    'risk_reserve' => number_format($totalRiskReserve, 2)
                ];
            }

            // 转换分页数据为数组格式
            $listArray = $list->toArray();

            return [
                'code' => 0,
                'msg' => '',
                'count' => $listArray['total'],
                'data' => $listArray['data'],
                'totalRow' => $totalRowData
            ];
        } else {
            // 记录查看分红列表日志
            add_log('view', 0, [], '[门店分红]-[门店分红配置表]：查看门店分红列表');
            Log::info('[门店分红]-[门店分红配置表]：用户查看门店分红列表页面，用户ID：' . $this->uid);

            // 传递按钮权限状态到模板（转换为数值）
            View::assign('has_button_access', $this->hasButtonAccess() ? 1 : 0);

            return view();
        }
    }

    /**
     * 查看门店分红信息（只读模式）
     */
    public function view()
    {
        // 权限检查：只有有数据查看权限的用户才能访问
        if (!$this->hasDataAccess()) {
            Log::info('[门店分红]-[门店分红配置表]：用户无权限查看门店分红信息详情，用户ID：' . $this->uid);
            View::assign('error_msg', '抱歉，您没有权限查看门店分红信息');
            View::assign('error_title', '访问受限');
            return view('error/permission');
        }

        $param = get_params();
        $id = isset($param['id']) ? $param['id'] : 0;

        if (empty($id)) {
            return to_assign(1, '参数错误');
        }

        // 获取主表信息
        $detail = Db::name('DividendStoreInfo')
            ->alias('dsi')
            ->join('Department d', 'd.id = dsi.store_id', 'LEFT')
            ->field('dsi.*, d.title as store_name')
            ->where('dsi.id', $id)
            ->find();

        if (empty($detail)) {
            return to_assign(1, '数据不存在');
        }

        // 记录查看门店分红详情日志
        add_log('view', $id, ['store_name' => $detail['store_name']], '[门店分红]-[门店分红配置表]：查看门店分红详情');
        Log::info('[门店分红]-[门店分红配置表]：用户查看门店分红详情，门店：' . $detail['store_name'] . '，ID：' . $id . '，用户ID：' . $this->uid);

        // 获取公司股东信息（关联员工信息）
        $companyShareholders = Db::name('DividendShareholder')
            ->alias('ds')
            ->leftJoin('Admin a', 'ds.admin_id = a.id')
            ->field('ds.*, a.name as admin_name')
            ->where('ds.dividend_store_info_id', $id)
            ->where('ds.shareholder_type', 1)
            ->where('ds.is_delete', 0)
            ->order('ds.sort_order asc')
            ->select()
            ->toArray();

        // 获取个人股东信息（关联员工信息）
        $personalShareholders = Db::name('DividendShareholder')
            ->alias('ds')
            ->leftJoin('Admin a', 'ds.admin_id = a.id')
            ->field('ds.*, a.name as admin_name')
            ->where('ds.dividend_store_info_id', $id)
            ->where('ds.shareholder_type', 2)
            ->where('ds.is_delete', 0)
            ->order('ds.sort_order asc')
            ->select()
            ->toArray();

        View::assign('detail', $detail);
        View::assign('company_shareholders', $companyShareholders);
        View::assign('personal_shareholders', $personalShareholders);
        View::assign('company_shareholders_count', count($companyShareholders));
        View::assign('personal_shareholders_count', count($personalShareholders));

        return view();
    }

    /**
     * 新增/编辑门店分红信息
     */
    public function add()
    {
        // 权限检查：只有有按钮权限的用户才能访问
        if (!$this->hasButtonAccess()) {
            Log::info('[门店分红]-[门店分红配置表]：用户无权限访问门店分红配置功能，用户ID：' . $this->uid);
            if (request()->isPost()) {
                return to_assign(1, '无权限操作');
            } else {
                View::assign('error_msg', '抱歉，您没有权限访问门店分红配置功能');
                View::assign('error_title', '访问受限');
                return view('error/permission');
            }
        }

        $param = get_params();

        if (request()->isPost()) {
            try {
                $scene = !empty($param['id']) && $param['id'] > 0 ? 'edit' : 'add';
                validate(DividendCheck::class)->scene($scene)->check($param);
            } catch (ValidateException $e) {
                return to_assign(1, $e->getError());
            }

            // 验证门店是否已存在分红信息（新增时）
            if (empty($param['id'])) {
                $exists = Db::name('DividendStoreInfo')
                    ->where('store_id', $param['store_id'])
                    ->where('is_delete', 0)
                    ->find();
                if ($exists) {
                    return to_assign(1, '该门店已存在分红信息');
                }
            }

            // 验证总持股比例不能超过100%
            $companyTotal = 0;
            $personalTotal = 0;

            if (!empty($param['company_shareholders'])) {
                foreach ($param['company_shareholders'] as $shareholder) {
                    if (!empty($shareholder['store_shareholding_ratio'])) {
                        $companyTotal += floatval($shareholder['store_shareholding_ratio']);
                    }
                }
            }

            if (!empty($param['personal_shareholders'])) {
                foreach ($param['personal_shareholders'] as $shareholder) {
                    if (!empty($shareholder['store_shareholding_ratio'])) {
                        $personalTotal += floatval($shareholder['store_shareholding_ratio']);
                    }
                }
            }

            $totalRatio = $companyTotal + $personalTotal;
            if ($totalRatio > 100) {
                return to_assign(1, '总持股比例不能超过100%，当前为' . number_format($totalRatio, 3) . '%');
            }

            // 检查是否允许不足100%的提交（前端确认后会传递此参数）
            $allowIncomplete = isset($param['allow_incomplete_ratio']) && $param['allow_incomplete_ratio'] == 1;
            if ($totalRatio < 100 && $totalRatio > 0 && !$allowIncomplete) {
                return to_assign(1, '总持股比例未达到100%，当前为' . number_format($totalRatio, 3) . '%，请检查持股比例配置');
            }

            // 开启事务
            Db::startTrans();

            try {
                $operationType = !empty($param['id']) && $param['id'] > 0 ? 'edit' : 'add';
                $storeName = '';

                // 获取门店名称用于日志记录
                if (!empty($param['store_id'])) {
                    $storeName = Db::name('Department')
                        ->where('id', $param['store_id'])
                        ->value('title') ?: '未知门店';
                }

                if (!empty($param['id']) && $param['id'] > 0) {
                    // 更新主表
                    $mainData = [
                        'store_id' => $param['store_id'],
                        'risk_reserve' => $param['risk_reserve'],
                        'company_shareholding_ratio' => $param['company_shareholding_ratio'] ?? 0,
                        'remark' => $param['remark'] ?? '',
                        'update_time' => time()
                    ];

                    Db::name('DividendStoreInfo')->where('id', $param['id'])->update($mainData);

                    // 删除原有股东信息
                    Db::name('DividendShareholder')
                        ->where('dividend_store_info_id', $param['id'])
                        ->update(['is_delete' => 1, 'delete_time' => time()]);

                    $dividendId = $param['id'];
                } else {
                    // 新增主表
                    $mainData = [
                        'store_id' => $param['store_id'],
                        'risk_reserve' => $param['risk_reserve'],
                        'company_shareholding_ratio' => $param['company_shareholding_ratio'] ?? 0,
                        'remark' => $param['remark'] ?? '',
                        'create_time' => time(),
                        'update_time' => time()
                    ];

                    $dividendId = Db::name('DividendStoreInfo')->insertGetId($mainData);
                }

                // 保存公司股东信息
                if (!empty($param['company_shareholders'])) {
                    foreach ($param['company_shareholders'] as $index => $shareholder) {
                        if (!empty($shareholder['shareholder_name'])) {
                            $shareholderData = [
                                'dividend_store_info_id' => $dividendId,
                                'admin_id' => $shareholder['admin_id'] ?? 0,
                                'shareholder_name' => $shareholder['shareholder_name'],
                                'shareholder_type' => 1, // 公司股东
                                'company_shareholding_ratio' => $shareholder['company_shareholding_ratio'] ?? 0,
                                'store_shareholding_ratio' => $shareholder['store_shareholding_ratio'] ?? 0,
                                'sort_order' => $index + 1,
                                'remark' => $shareholder['remark'] ?? '',
                                'create_time' => time(),
                                'update_time' => time()
                            ];

                            Db::name('DividendShareholder')->insert($shareholderData);
                        }
                    }
                }

                // 保存个人股东信息
                if (!empty($param['personal_shareholders'])) {
                    foreach ($param['personal_shareholders'] as $index => $shareholder) {
                        if (!empty($shareholder['shareholder_name'])) {
                            $shareholderData = [
                                'dividend_store_info_id' => $dividendId,
                                'admin_id' => $shareholder['admin_id'] ?? 0,
                                'shareholder_name' => $shareholder['shareholder_name'],
                                'shareholder_type' => 2, // 个人股东
                                'store_shareholding_ratio' => $shareholder['store_shareholding_ratio'] ?? 0,
                                'sort_order' => $index + 1,
                                'remark' => $shareholder['remark'] ?? '',
                                'create_time' => time(),
                                'update_time' => time()
                            ];

                            Db::name('DividendShareholder')->insert($shareholderData);
                        }
                    }
                }

                // 如果是新增操作，记录风险金初始化变化（作为完整台账，即使为0也要记录）
                if ($operationType === 'add') {
                    $riskReserveAmount = isset($param['risk_reserve']) ? floatval($param['risk_reserve']) : 0.00;

                    // 使用服务类记录风险金初始化变化
                    $this->dividendService->addRiskReserveInitLog(
                        $param['store_id'],
                        $dividendId,
                        $riskReserveAmount,
                        $this->uid
                    );
                }

                Db::commit();

                // 记录操作日志
                add_log($operationType, $dividendId, json_encode($param, JSON_UNESCAPED_UNICODE), '[门店分红]-[门店分红配置表]：' . ($operationType === 'add' ? '新增' : '编辑') . '门店分红信息');
                Log::info('[门店分红]-[门店分红配置表]：' . ($operationType === 'add' ? '新增' : '编辑') . '门店分红信息，门店：' . $storeName . '，ID：' . $dividendId . '，用户ID：' . $this->uid);

            } catch (\Exception $e) {
                Db::rollback();
                Log::info('[门店分红]-[门店分红配置表]：门店分红信息操作失败，错误信息：' . $e->getMessage() . '，用户ID：' . $this->uid);
                return to_assign(1, '操作失败：' . $e->getMessage());
            }

            return to_assign(0, '操作成功');
        } else {
            // 显示表单
            $id = isset($param['id']) ? $param['id'] : 0;
            $detail = [];
            $companyShareholders = [];
            $personalShareholders = [];

            if ($id > 0) {
                // 获取主表信息
                $detail = Db::name('DividendStoreInfo')
                    ->alias('dsi')
                    ->join('Department d', 'd.id = dsi.store_id', 'LEFT')
                    ->field('dsi.*, d.title as store_name')
                    ->where('dsi.id', $id)
                    ->find();

                if (empty($detail)) {
                    return to_assign(1, '数据不存在');
                }

                // 记录查看门店分红详情日志
                add_log('view', $id, ['store_name' => $detail['store_name']], '[门店分红]-[门店分红配置表]：查看门店分红编辑表单');
                Log::info('[门店分红]-[门店分红配置表]：用户查看门店分红编辑表单，门店：' . $detail['store_name'] . '，ID：' . $id . '，用户ID：' . $this->uid);

                // 获取公司股东信息（关联员工信息）
                $companyShareholders = Db::name('DividendShareholder')
                    ->alias('ds')
                    ->leftJoin('Admin a', 'ds.admin_id = a.id')
                    ->field('ds.*, a.name as admin_name')
                    ->where('ds.dividend_store_info_id', $id)
                    ->where('ds.shareholder_type', 1)
                    ->where('ds.is_delete', 0)
                    ->order('ds.sort_order asc')
                    ->select()
                    ->toArray();

                // 获取个人股东信息（关联员工信息）
                $personalShareholders = Db::name('DividendShareholder')
                    ->alias('ds')
                    ->leftJoin('Admin a', 'ds.admin_id = a.id')
                    ->field('ds.*, a.name as admin_name')
                    ->where('ds.dividend_store_info_id', $id)
                    ->where('ds.shareholder_type', 2)
                    ->where('ds.is_delete', 0)
                    ->order('ds.sort_order asc')
                    ->select()
                    ->toArray();
            } else {
                // 记录新增分红表单查看日志
                add_log('view', 0, [], '[门店分红]-[门店分红配置表]：查看新增门店分红表单');
                Log::info('[门店分红]-[门店分红配置表]：用户查看新增门店分红表单，用户ID：' . $this->uid);
            }

            // 获取可选择的门店列表（排除已有分红信息的门店，编辑时除外）
            $excludeStoreIds = [];
            if (empty($id)) {
                $excludeStoreIds = Db::name('DividendStoreInfo')
                    ->where('is_delete', 0)
                    ->column('store_id');
            } else {
                $excludeStoreIds = Db::name('DividendStoreInfo')
                    ->where('is_delete', 0)
                    ->where('id', '<>', $id)
                    ->column('store_id');
            }

            $query = Db::name('Department')
                ->where('status', '>=', 0)
                ->where('remark', '门店');

            if (!empty($excludeStoreIds)) {
                $query->where('id', 'not in', $excludeStoreIds);
            }

            $storeList = $query->field('id, title')
                ->order('sort asc, id asc')
                ->select()
                ->toArray();

            View::assign('detail', $detail);
            View::assign('company_shareholders', $companyShareholders);
            View::assign('personal_shareholders', $personalShareholders);
            View::assign('company_shareholders_count', count($companyShareholders));
            View::assign('personal_shareholders_count', count($personalShareholders));
            View::assign('store_list', $storeList);

            return view();
        }
    }

    /**
     * 删除门店分红信息
     */
    public function delete()
    {
        // 权限检查：只有有按钮权限的用户才能删除
        if (!$this->hasButtonAccess()) {
            Log::info('[门店分红]-[门店分红配置表]：用户无权限删除门店分红信息，用户ID：' . $this->uid);
            return to_assign(1, '无权限操作');
        }

        $param = get_params();
        $id = isset($param['id']) ? $param['id'] : 0;

        if (empty($id)) {
            return to_assign(1, '参数错误');
        }

        $detail = Db::name('DividendStoreInfo')->where('id', $id)->find();
        if (empty($detail)) {
            return to_assign(1, '数据不存在');
        }

        // 获取门店名称用于日志记录
        $storeName = Db::name('Department')
            ->where('id', $detail['store_id'])
            ->value('title') ?: '未知门店';

        // 开启事务
        Db::startTrans();

        try {
            // 软删除主表记录
            Db::name('DividendStoreInfo')
                ->where('id', $id)
                ->update([
                    'is_delete' => 1,
                    'delete_time' => time()
                ]);

            // 软删除关联的股东记录
            Db::name('DividendShareholder')
                ->where('dividend_store_info_id', $id)
                ->update([
                    'is_delete' => 1,
                    'delete_time' => time()
                ]);

            Db::commit();

            // 尝试记录日志，但不影响主流程
            add_log('delete', $id, $detail, '[门店分红]-[门店分红配置表]：删除门店分红信息');
            Log::info('[门店分红]-[门店分红配置表]：删除门店分红信息，门店：' . $storeName . '，ID：' . $id . '，用户ID：' . $this->uid);

        } catch (\Exception $e) {
            Db::rollback();
            Log::info('[门店分红]-[门店分红配置表]：删除门店分红信息失败，错误信息：' . $e->getMessage() . '，用户ID：' . $this->uid);
            return to_assign(1, '删除失败：' . $e->getMessage());
        }
        return to_assign(0, '删除成功');
    }

    /**
     * 查看单个门店的风险金变化记录（用于侧边抽屉）
     */
    public function storeRiskReserveLog()
    {
        // 权限检查：只有有按钮权限的用户才能查看风险金记录
        if (!$this->hasButtonAccess()) {
            Log::info('[门店分红]-[门店分红配置表]：用户无权限查看门店风险金记录，用户ID：' . $this->uid);
            if (request()->isAjax()) {
                return [
                    'code' => 1,
                    'msg' => '无权限查看',
                    'count' => 0,
                    'data' => []
                ];
            } else {
                View::assign('error_msg', '抱歉，您没有权限查看门店风险金记录');
                View::assign('error_title', '访问受限');
                return view('error/permission');
            }
        }

        $param = get_params();

        if (request()->isAjax()) {
            $storeId = isset($param['store_id']) ? intval($param['store_id']) : 0;

            if (empty($storeId)) {
                return [
                    'code' => 1,
                    'msg' => '门店ID不能为空',
                    'count' => 0,
                    'data' => []
                ];
            }

            // 构建查询条件 - 只按门店ID查询，不需要其他筛选
            $where = [
                ['rrlog.store_id', '=', $storeId]
            ];

            // 查询所有数据，不分页
            $list = Db::name('DividendRiskReserveLog')
                ->alias('rrlog')
                ->leftJoin('Department d', 'rrlog.store_id = d.id')
                ->leftJoin('Admin a', 'rrlog.admin_id = a.id')
                ->field('rrlog.*, d.title as store_name, a.name as admin_name')
                ->where($where)
                ->order('rrlog.id desc')
                ->select()
                ->each(function ($item) {
                    // 格式化变化类型
                    $changeTypes = [1 => '计提', 2 => '调整', 3 => '初始化'];
                    $item['change_type_text'] = $changeTypes[$item['change_type']] ?? '未知';

                    // 格式化金额 - 先转换为浮点数再格式化
                    $item['before_amount_formatted'] = number_format(floatval($item['before_amount']), 2);
                    $item['change_amount_formatted'] = ($item['change_amount'] >= 0 ? '+' : '') . number_format(floatval($item['change_amount']), 2);
                    $item['after_amount_formatted'] = number_format(floatval($item['after_amount']), 2);

                    // 格式化时间
                    $item['create_time_formatted'] = date('Y-m-d H:i:s', $item['create_time']);

                    return $item;
                });

            return [
                'code' => 0,
                'msg' => '',
                'count' => count($list),
                'data' => $list->toArray()
            ];
        } else {
            // 获取门店信息
            $storeId = isset($param['store_id']) ? intval($param['store_id']) : 0;
            $storeName = isset($param['store_name']) ? $param['store_name'] : '';

            if (empty($storeId)) {
                return to_assign(1, '门店ID不能为空');
            }

            // 如果没有传递门店名称，从数据库获取
            if (empty($storeName)) {
                $storeName = Db::name('Department')
                    ->where('id', $storeId)
                    ->value('title') ?: '未知门店';
            }

            View::assign('store_id', $storeId);
            View::assign('store_name', $storeName);

            // 记录查看单个门店风险金变化记录日志
            add_log('view', $storeId, ['store_name' => $storeName], '[门店分红]-[门店分红配置表]：查看单个门店风险金变化记录');
            Log::info('[门店分红]-[门店分红配置表]：用户查看单个门店风险金变化记录，门店：' . $storeName . '，门店ID：' . $storeId . '，用户ID：' . $this->uid);

            return view();
        }
    }

    /**
     * 调整门店风险金
     */
    public function adjustRiskReserve()
    {
        // 权限检查：只有有按钮权限的用户才能调整风险金
        if (!$this->hasButtonAccess()) {
            Log::info('[门店分红]-[门店分红配置表]：用户无权限调整门店风险金，用户ID：' . $this->uid);
            return to_assign(1, '无权限操作');
        }

        $param = get_params();

        if (request()->isPost()) {
            try {
                // 验证参数
                try {
                    validate(DividendCheck::class)->scene('adjust')->check($param);
                } catch (ValidateException $e) {
                    return to_assign(1, $e->getError());
                }

                $storeId = intval($param['store_id']);
                $adjustType = $param['adjust_type']; // increase 或 decrease
                $adjustAmount = floatval($param['adjust_amount']);
                $remark = trim($param['remark'] ?? '');

                // 开启事务
                Db::startTrans();

                // 获取门店分红信息
                $storeInfo = Db::name('DividendStoreInfo')
                    ->where('store_id', $storeId)
                    ->where('is_delete', 0)
                    ->find();

                if (!$storeInfo) {
                    Db::rollback();
                    try {
                        return to_assign(1, '未找到门店分红信息');
                    } catch (\Exception $e) {
                    }
                }

                $beforeAmount = floatval($storeInfo['risk_reserve']);

                // 计算调整后金额
                if ($adjustType === 'increase') {
                    $changeAmount = $adjustAmount;
                    $afterAmount = $beforeAmount + $adjustAmount;
                } else {
                    $changeAmount = -$adjustAmount;
                    $afterAmount = $beforeAmount - $adjustAmount;
                }

                // 更新门店分红信息表的风险金
                Db::name('DividendStoreInfo')
                    ->where('id', $storeInfo['id'])
                    ->update([
                        'risk_reserve' => $afterAmount,
                        'update_time' => time()
                    ]);

                // 记录风险金调整变化
                $this->dividendService->addRiskReserveAdjustLog(
                    $storeId,
                    $storeInfo['id'],
                    $beforeAmount,
                    $afterAmount,
                    $remark,
                    $this->uid
                );

                Db::commit();

                // 记录操作日志
                add_log('edit', $storeId, json_encode($param, JSON_UNESCAPED_UNICODE), '[门店分红]-[门店分红配置表]：调整门店风险金');

                // 获取门店名称用于日志记录
                $storeName = Db::name('Department')
                    ->where('id', $storeId)
                    ->value('title') ?: '未知门店';
                Log::info('[门店分红]-[门店分红配置表]：调整门店风险金，门店：' . $storeName . '，调整类型：' . ($adjustType === 'increase' ? '增加' : '减少') . '，调整金额：' . $adjustAmount . '，用户ID：' . $this->uid);

            } catch (\Exception $e) {
                Db::rollback();
                Log::info('[门店分红]-[门店分红配置表]：调整门店风险金失败，错误信息：' . $e->getMessage() . '，用户ID：' . $this->uid);
                return to_assign(1, '调整失败：' . $e->getMessage());
            }

            return to_assign(0, '风险金调整成功');
        }

        return to_assign(1, '请求方式错误');
    }
}
