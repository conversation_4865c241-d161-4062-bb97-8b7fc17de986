{extend name="../../base/view/common/base" /}
<!-- 主体 -->
{block name="body"}
<form class="layui-form p-4">
	{if condition="$id eq 0"}
	<h3 class="pb-3">添加员工信息</h3>
	<table class="layui-table layui-table-form">
		<tr>
			<td class="layui-td-gray">所在部门<font>*</font></td>
			<td>
				<select name="did" lay-verify="required" lay-reqText="请选择所属部门" lay-search="">
					<option value="">请选择所属部门</option>
					{volist name="department" id="v"}
						<option value="{$v.id}">{$v.title}</option>
					{/volist}
				</select>
			</td>

		</tr>
		<tr>
			<td class="layui-td-gray">租金增长周期（年）<font>*</font></td>
			<td>
				<input type="number" name="increase" placeholder="输入租金增长周期" class="layui-input">
			</td>
			<td class="layui-td-gray">租金增长金额（百分比）<font>*</font></td>
			<td>
				<input type="number" name="increase_per" placeholder="输入租金增长金额" class="layui-input">
			</td>
		</tr>
		<tr>
			<td class="layui-td-gray">合同起始日期<font>*</font></td>
			<td>
				<input type="text" id="con_start_date" name="con_start_date"
					   readonly lay-verify="required" placeholder="请选择合同起始日期" lay-reqText="请选择合同起始日期"
					   autocomplete="off" class="layui-input">
			</td>
			<td class="layui-td-gray">合同终止日期<font>*</font></td>
			<td>
				<input type="text" id="con_end_date" name="con_end_date"
					   readonly lay-verify="required" placeholder="请选择合同终止日期" lay-reqText="请选择合同终止日期"
					   autocomplete="off" class="layui-input">
			</td>
		</tr>
		<tr>
			<td class="layui-td-gray">合同金额<font>*</font></td>
			<td>
				<input type="number" name="con_amount" placeholder="输入合同金额" class="layui-input">
			</td>
		</tr>
		<tr>
			<td class="layui-td-gray">首次付款日期<font>*</font></td>
			<td>
				<input type="text" id="pay_date" name="pay_date"
					   readonly lay-verify="required" placeholder="请选择首次付款日期" lay-reqText="请选择首次付款日期"
					   autocomplete="off" class="layui-input">
			</td>
			<td class="layui-td-gray">付款周期（年）<font>*</font></td>
			<td>
				<input type="number" name="pay_cycle" placeholder="输入付款周期" class="layui-input">
			</td>
		</tr>
		<tr>
			<td class="layui-td-gray">租金起始日期<font>*</font></td>
			<td>
				<input type="text" id="rent_start_date" name="rent_start_date"
					   readonly lay-verify="required" placeholder="请选择租金起始日期" lay-reqText="请选择租金起始日期"
					   autocomplete="off" class="layui-input">
			</td>
		</tr>
	</table>

	{else/}
	<h3 class="pb-3">编辑员工信息</h3>
	<table class="layui-table layui-table-form">
		<tr>
			<td class="layui-td-gray">员工姓名<font>*</font></td>
			<td>
				<input type="text" name="name" value="{$detail.name}" lay-verify="required" placeholder="请输入员工姓名" lay-reqText="请输入员工姓名" autocomplete="off" class="layui-input">
			</td>
			<td class="layui-td-gray">登录账号</td>
			<td>
				{$detail.username}
			</td>
			<td class="layui-td-gray">初始密码</td>
			<td>
				{empty name="$detail.reg_pwd"}
					密码已重置
				{else/}
					{$detail.reg_pwd}
				{/empty}
			</td>
		</tr>
		<tr>
			<td class="layui-td-gray">手机号码<font>*</font></td>
			<td>
				<input type="text" name="mobile" value="{$detail.mobile}" lay-verify="required" placeholder="可使用手机号码登录" lay-reqText="请输入手机" autocomplete="off" class="layui-input">
			</td>
			<td class="layui-td-gray">电子邮箱</td>
			<td>
				<!-- <input type="text" name="email" value="{$detail.email}" 
				lay-verify="required" placeholder="请输入电子邮箱" lay-reqText="请输入电子邮箱" autocomplete="off" class="layui-input"> -->
				<input type="text" name="email" value="{$detail.email}" placeholder="请输入电子邮箱" 
				autocomplete="off" class="layui-input">
			</td>
			<td rowspan="3" class="layui-td-gray">头像<br /><span style="font-size: 12px;">(如若不上传<br />系统将自动生成)</span></td>
			<td rowspan="3" valign="top" style="width: 100px;">
				<div class="layui-upload" style="width: 100px;">
					<div class="layui-upload-list" id="demo1" style="width: 100%; height:100px; overflow: hidden;">
						<img src="{$detail.thumb}" height="100" style="max-width: 100%; width: 100%;" />
						<input type="hidden" name="thumb" value="{$detail.thumb}">
					</div>
					<button type="button" class="layui-btn layui-btn-normal" style="width: 100%;" id="test1">上传头像</button>
				</div>
			</td>
		</tr>
		<tr>
			<td class="layui-td-gray">所在部门<font>*</font></td>
<!--			<td>-->
<!--				<select name="did" lay-verify="required" lay-reqText="请选择所属部门" lay-search>-->
<!--					<option value="">请选择所属部门</option>-->
<!--					{volist name="department" id="v"}-->
<!--						<option value="{$v.id}" {eq name="$v.id" value="$detail.did"}selected{/eq}>{$v.title}</option>-->
<!--					{/volist}-->
<!--				</select>-->
<!--			</td>-->
			<td>
				<div id="did"></div>
			</td>
			<td class="layui-td-gray">岗位职称<font>*</font></td>
			<td>
				<select name="position_id" lay-verify="required" lay-reqText="请选择岗位职称" lay-search="">
					<option value="">请选择岗位职称</option>
					{volist name="position" id="v"}
						<option value="{$v.id}" {eq name="$v.id" value="$detail.position_id"}selected{/eq}>{$v.title}</option>
					{/volist}
				</select>				
			</td>
		</tr>
		<tr>
			<td class="layui-td-gray">入职日期<font>*</font></td>
			<td>
				<input type="text" id="entry_time" 
				value="{$detail.entry_time|time_format=###,'Y-m-d'}" 
				name="entry_time" readonly lay-verify="required" 
				placeholder="请选择入职日期" lay-reqText="请选择入职日期" autocomplete="off" class="layui-input">
			</td>
			<td class="layui-td-gray">员工类型<font>*</font></td>
			<td>
				<input type="radio" name="type" value="3" title="实习" {eq name="$detail.type" value="3"}checked{/eq}>
				<input type="radio" name="type" value="2" title="试用" {eq name="$detail.type" value="2"}checked{/eq}>
				<input type="radio" name="type" value="1" title="正式" {eq name="$detail.type" value="1"}checked{/eq}>
			</td>
		</tr>
		<tr>
			<td class="layui-td-gray">账号状态</td>
			<td>
				{eq name="$detail.status" value="0"}<span style="color:#FF5722">禁止登录</span>{/eq}
				{eq name="$detail.status" value="1"}<span style="color:#009688">正常</span>{/eq}
				{eq name="$detail.status" value="2"}<span style="color:#FFB800">已离职</span>{/eq}
			</td>
			<td class="layui-td-gray">员工性别<font>*</font></td>
			<td colspan="3">
				<input type="radio" name="sex" value="1" title="男" {eq name="$detail.sex" value="1"}checked{/eq}>
				<input type="radio" name="sex" value="2" title="女" {eq name="$detail.sex" value="2"}checked{/eq}>
			</td>
		</tr>
		<tr>
			<td class="layui-td-gray">员工简介</td>
			<td colspan="7">
				<textarea name="desc" placeholder="请输入员工简介" class="layui-textarea">{$detail.desc|default=''}</textarea>
			</td>
		</tr>
	</table>

	{/if}

	<div class="py-3">
		<input type="hidden" value="{$id}" name="id">
		<button class="layui-btn layui-btn-normal" lay-submit="" lay-filter="webform">立即提交</button>
		<button type="reset" class="layui-btn layui-btn-primary">重置</button>
	</div>
</form>
<style>
	.layui-td-gray, .layui-td-gray-2, .layui-td-gray-3, .layui-td-gray-4{
		width: 180px;
	}
</style>
{/block}
<!-- /主体 -->

<!-- 脚本 -->
{block name="script"}
<script>
	const moduleInit = ['tool'];
	function gouguInit() {
		var form = layui.form,tool=layui.tool,laydate = layui.laydate,upload = layui.upload;
		//日期选择
		laydate.render({
			elem: '#con_start_date,#con_end_date,#pay_date,#rent_start_date',
			showBottom: false
		});

		//监听提交
		form.on('submit(webform)', function(data){
			let callback = function (e) {
				layer.msg(e.msg);
				if (e.code == 0) {
					tool.sideClose(1000);
				}
			}
			tool.post("/marketing/rent/add", data.field, callback);
			return false;
		});
	
	}

</script>
{/block}
<!-- /脚本 -->