<?php
/**
 * @copyright Copyright (c) 2021 仲正堂工作室
 * @license https://opensource.org/licenses/GPL-3.0
 * @link https://www.gougucms.com
 */

declare (strict_types=1);

namespace app\cross\controller;

use app\base\BaseController;
use app\cross\model\StoreNameMapping;
use app\cross\validate\StoreNameMappingCheck;
use think\exception\ValidateException;
use think\facade\Db;
use think\facade\View;
use think\facade\Log;
use think\App;

class StoreMapping extends BaseController
{
    public function __construct(App $app)
    {
        parent::__construct($app);
    }

    /**
     * 门店名称映射表列表
     */
    public function index()
    {
        if (request()->isAjax()) {
            $param = get_params();

            // 构建查询条件
            $where = [];

            // 按门店名称搜索
            if (!empty($param['oa_name'])) {
                $where[] = ['snm.oa_name', 'like', '%' . $param['oa_name'] . '%'];
            }

            // 按门店搜索（支持多选）
            if (!empty($param['department_ids']) && is_array($param['department_ids'])) {
                $where[] = ['snm.department_id', 'in', $param['department_ids']];
            }

            // 移除分页，获取所有数据
            $list = StoreNameMapping::getList($where);

            return table_assign(0, '', $list);
        } else {
            // 获取门店列表
            $departmentList = StoreNameMapping::getDepartmentList();
            View::assign('department_list', $departmentList);

            // 获取状态选项
            $statusOptions = StoreNameMapping::getStatusOptions();
            View::assign('status_options', $statusOptions);

            return View::fetch();
        }
    }

    /**
     * 查看门店名称映射详情
     */
    public function view()
    {
        $param = get_params();
        $id = isset($param['id']) ? intval($param['id']) : 0;

        if ($id <= 0) {
            return to_assign(1, '参数错误');
        }

        $detail = StoreNameMapping::getDetail($id);
        if (empty($detail)) {
            return to_assign(1, '记录不存在');
        }

        View::assign('detail', $detail);
        return View::fetch();
    }

    /**
     * 编辑门店名称映射
     */
    public function edit()
    {
        $param = get_params();

        if (request()->isPost()) {
            // 验证数据
            try {
                validate(StoreNameMappingCheck::class)->scene('edit')->check($param);
            } catch (ValidateException $e) {
                return to_assign(1, $e->getError());
            }

            $id = isset($param['id']) ? intval($param['id']) : 0;

            // 检查门店是否已存在映射（排除当前记录）
            if (StoreNameMapping::checkDepartmentExists($param['department_id'], $id)) {
                return to_assign(1, '该门店已存在映射关系，不能重复添加');
            }

            try {
                if ($id > 0) {
                    // 编辑
                    $result = StoreNameMapping::where('id', $id)->update($param);
                    if ($result !== false) {
                        add_log('edit', $id, $param, '门店名称映射表');
                        return to_assign(0, '编辑成功');
                    } else {
                        return to_assign(1, '编辑失败');
                    }
                } else {
                    // 新增
                    $result = StoreNameMapping::create($param);
                    if ($result) {
                        add_log('add', $result->id, $param, '门店名称映射表');
                        return to_assign(0, '新增成功');
                    } else {
                        return to_assign(1, '新增失败');
                    }
                }
            } catch (\think\exception\HttpResponseException $e) {
                // 重新抛出HttpResponseException，这是to_assign函数正常的响应机制
                throw $e;
            } catch (\Exception $e) {
                Log::error('门店名称映射表操作失败：' . $e->getMessage());
                return to_assign(1, '操作失败：' . $e->getMessage());
            }
        } else {
            $id = isset($param['id']) ? intval($param['id']) : 0;
            $detail = [];

            if ($id > 0) {
                $detail = StoreNameMapping::getDetail($id);
                if (empty($detail)) {
                    return to_assign(1, '记录不存在');
                }
                // 编辑时获取所有门店列表
                $departmentList = StoreNameMapping::getDepartmentList();
            } else {
                // 新增时仅获取未配置映射的门店列表
                $departmentList = StoreNameMapping::getAvailableDepartmentList();
                // 设置新增记录的默认值
                $detail = [
                    'status' => 1  // 默认启用状态
                ];
            }

            View::assign('department_list', $departmentList);

            // 获取状态选项
            $statusOptions = StoreNameMapping::getStatusOptions();
            View::assign('status_options', $statusOptions);

            View::assign('detail', $detail);
            return View::fetch();
        }
    }

    /**
     * 删除门店名称映射
     */
    public function delete()
    {
        $param = get_params();
        $id = isset($param['id']) ? intval($param['id']) : 0;

        if ($id <= 0) {
            return to_assign(1, '参数错误');
        }

        try {
            $detail = StoreNameMapping::find($id);
            if (empty($detail)) {
                return to_assign(1, '记录不存在');
            }

            $result = StoreNameMapping::destroy($id);
            if ($result) {
                add_log('delete', $id, [], '门店名称映射表');
                return to_assign(0, '删除成功');
            } else {
                return to_assign(1, '删除失败');
            }
        } catch (\think\exception\HttpResponseException $e) {
            // 重新抛出HttpResponseException，这是to_assign函数正常的响应机制
            throw $e;
        } catch (\Exception $e) {
            Log::error('删除门店名称映射失败：' . $e->getMessage());
            return to_assign(1, '删除失败：' . $e->getMessage());
        }
    }
}