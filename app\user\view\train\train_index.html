{extend name="../../base/view/common/base" /}
{block name="style"}
  <style>
	.layui-tree-entry{font-size:15px; line-height:24px}
	.layui-tree-set{padding:2px 0}
	.layui-tree-iconClick .layui-icon{color:#1E9FFF}
	.layui-icon layui-icon-file{font-size:16px;}
	.layui-tree-icon {height: 15px;width: 15px; text-align: center;border: 1px solid #1E9FFF; color:#1E9FFF}
	.layui-tree-line .layui-tree-set .layui-tree-set:after{top:18px;}
	.tree-left{width:200px; float:left; height:calc(100% - 30px); overflow: scroll; border:1px solid #eeeeee; background-color:#FAFAFA; padding:12px 12px 12px 5px;}
	.tree-left h3{font-size:16px; height:30px; padding-left:10px; font-weight:800}
	
	.gougu-upload-files{background-color: #ffffff; border:1px solid #e4e7ed;color: #c0c4cc;cursor: not-allowed; padding:0 12px; width:180px; box-sizing: border-box; display: inline-block; font-size: inherit; height: 38px; line-height: 35px; margin-right:8px; border-radius:2px;}
	.gougu-upload-tips{color:#969696}
	.layui-form-item{margin-bottom:8px;}
	.layui-input-block{min-height:24px;}
  </style>
{/block}
<!-- 主体 -->
{block name="body"}
<div class="p-3" style="height:100%; box-sizing: border-box;">

	<div class="body-table" style="overflow:hidden;">
		<form class="layui-form gg-form-bar border-t border-x" lay-filter="barsearchform">
			<div class="layui-input-inline" style="width:136px">
				<select name="type">
					<option value="">--培训类型--</option>
					<option value="1" {if condition="($type == '1')"}selected{/if}>线上</option>
					<option value="2" {if condition="($type == '2')"}selected{/if}>大师傅培训</option>
					<option value="3" {if condition="($type == '3')"}selected{/if}>技术指导培训</option>
				</select>
			</div>
			<div class="layui-input-inline" style="width:150px;">
				<button class="layui-btn layui-btn-normal" lay-submit="" lay-filter="webform"><i class="layui-icon layui-icon-search mr-1"></i>搜索</button>
				<button type="reset" class="layui-btn layui-btn-reset" lay-filter="reset">清空</button>
			</div>
		</form>
		<table class="layui-hide" id="test" lay-filter="test"></table>
	</div>
</div>

<script type="text/html" id="thumb">
	<img src="{{d.thumb}}" width="30" height="30" />
</script>
<script type="text/html" id="toolbara">
	<div class="layui-btn-group">
		<button class="layui-btn layui-btn-sm" lay-event="add"><i class="layui-icon">&#xe61f;</i>添加</button>
		<button class="layui-btn layui-btn-sm" lay-event="sync"><i class="layui-icon layui-icon-refresh-3"></i>同步培训列表</button>
	</div>
</script>

{/block}
<!-- /主体 -->

<!-- 脚本 -->
{block name="script"}
<script>
	const moduleInit = ['tool','tablePlus','customSelect'];
	function gouguInit() {
		var table = layui.tablePlus, tool = layui.tool,upload = layui.upload;
		var custom_select = layui.customSelect
		var form = layui.form;
		var tree = layui.tree;

		layui.pageTable = table.render({
			elem: '#test',
			title: '员工列表',
			toolbar: '#toolbara',
			defaultToolbar: [
				'filter',
				{
					title: '导出',
					layEvent: 'LAYTABLE_export',
					icon: 'layui-icon-export'
				}
			],
			url: "/user/train/train_index", //数据接口
			where:{
				type:$('[name="type"]').val()
			},
			page: true, //开启分页
			limit: 20,
			height: 'full-85',
			cols: [
				[
					{type: 'numbers', title: '序号', width: 100,fixed:true}
					, {
						field: 'trainName',
						title: '培训名称',
						align: 'center',
						width: 300,fixed:true
					},{
						field: 'count',
						title: '培训人员',
						align: 'center',
						width: 120,
						templet: function (d) {
							let html = `<a class="layui-btn layui-btn-normal layui-btn-xs" style="width: 50px" lay-event="progress">${d.count}</a>`;
							return html;
						}
					},{
						field: 'department',
						title: '培训时间',
						align: 'center',
						width: 300,
						templet: function (d) {
							let html = `--`;
							if (d.startTime){
								html = `${d.startTime} ~ ${d.endTime}`;
							}
							return html;
						}
					}, {
						field: 'position',
						title: '状态',
						align: 'center',
						width: 110,
						templet: function (d) {
							let html = `进行中`;
							if (d.startTime){
								html = `已结束`;
							}
							return html;
						}
					}, {
						field: 'categoryName',
						title: '培训分类',
						align: 'center',
						width: 110
					}, {
						field: 'createTime',
						title: '添加时间',
						align: 'center',
						width: 200
					}, {
						field: 'creatorName',
						title: '发布人',
						align: 'center',
						width: 100
					}, {
						field: 'type',
						title: '类型',
						align: 'center',
						width: 100,
						templet: function (d) {
							let html = '<span class="layui-btn layui-btn-normal layui-btn-xs" style = "background-color:#535353" >线上</span>';
							if (d.type == 2){
								html = '<span class="layui-btn layui-btn-success layui-btn-xs">线下</span>';
							}
							return html;
						}
					},{
						fixed: 'right',
						field: 'right',
						title: '操作',
						align: 'center',
						width: 240,
						templet: function (d) {
							var html = '';
							if (d.type == 2){
								html += '<span class="layui-btn layui-btn-xs layui-btn-normal" lay-event="result">成绩</span>';
								html += '<span class="layui-btn layui-btn-xs layui-btn-warm" lay-event="add">编辑</span>';
								html += '<span class="layui-btn layui-btn-xs layui-btn-danger" lay-event="del">删除</span>';
							}
							return html;
						}						
					}
				]
			]
		});

		//监听行工具事件
		table.on('tool(test)', function (obj) {
			var data = obj.data;
			if (obj.event === 'add') {
				tool.side("/user/train/train_add?id=" + data.id);
				return;
			}
			if (obj.event === 'progress') {
				if (data.type == 1){
					tool.side('/user/train/train_progress?trainId='+data.trainId);
				}else{
					tool.side('/user/train/train_sign?id='+data.id);
				}

				return;
			}
			if (obj.event === 'result') {
				window.open("/user/train/train_sign_result?id="+data.id, '_blank');
				return;
			}
			if (obj.event === 'del') {
				layer.confirm('确定要删除吗?', {icon: 3, title:'提示'}, function(index){
					$.ajax({
						url: "/user/train/train_del",
						type:'post',
						data:{id:data.id},
						success:function(e){
							layer.msg(e.msg);
							if (e.code == 0) {
								obj.del();
							}
						}
					})
					layer.close(index);
				});
				return;
			}
		});


		//表头工具栏事件
		table.on('toolbar(test)', function(obj){
			if (obj.event === 'add') {
				tool.side("/user/train/train_add");
				return;
			}
			if (obj.event === 'sync') {
				var loadIndex = layer.load(0);
				$.ajax({
					type: 'get',
					url: '/api/moxueyuan/syn_train',           //数据接口
					success: function (e) {
						layer.msg(e.msg);
						layui.pageTable.reload()
						layer.close(loadIndex);
					}
				});
			}
		});

	}	

</script>
{/block}
<!-- /脚本 -->