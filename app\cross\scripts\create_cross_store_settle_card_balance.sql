-- 跨店结算门店卡余额表建表脚本
-- 创建时间：2025-07-22
-- 说明：用于存储门店会员卡余额快照，支持门店类型判断和结算逻辑

CREATE TABLE `oa_cross_store_settle_card_balance` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `period` varchar(7) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '快照月份 (格式: YYYY-MM)',
  `store_id` int unsigned NOT NULL COMMENT '门店ID (关联 oa_department.id)',
  `card_balance` decimal(15,2) NOT NULL COMMENT '本月导入的会员卡总余额',
  `logical_store_type` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '本月根据余额计算的逻辑门店类型 (新店/老店)',
  `previous_month_logical_type` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '上月逻辑门店类型 (冗余字段, 方便前端展示)',
  `settlement_store_type` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '本月结算时实际使用的门店类型 (新店/老店)',
  `create_time` int unsigned NOT NULL DEFAULT '0' COMMENT '记录创建时间',
  `update_time` int unsigned NOT NULL DEFAULT '0' COMMENT '记录更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_period_store` (`period`,`store_id`) COMMENT '每个月每个门店只能有一条记录',
  KEY `idx_store_id` (`store_id`)
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='跨店结算门店卡余额表';