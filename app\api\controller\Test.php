<?php
/**
 * @copyright Copyright (c) 2021 仲正堂工作室
 * @license https://opensource.org/licenses/GPL-3.0
 * @link https://www.gougucms.com
 */
declare (strict_types=1);

namespace app\api\controller;

use app\api\BaseController;
use app\api\service\IndexService;
use app\finance\service\ExpenseService;
use app\message\service\QiWeiService;
use app\oa\service\ApproveService;
use app\store\service\StoreSalaryService;
use app\store\service\StoreService;
use app\user\model\DepartmentChange;
use app\user\service\AdminViewService;
use think\App;
use think\facade\Db;
use think\facade\View;
use think\facade\Log;

class Test extends BaseController
{


    /***
     * @return void
     * 加班工资
     */
    public function work_overtime()
    {
        $did = '68';
        $sdate = "2025-06";
        $sdate_time = strtotime($sdate);
        $last_month = getLastMonth("$sdate-01");

        $sql = "
SELECT ss.id,ss.overtime_date, concat(ifnull ( a.yz_uid , '' ) , ifnull ( concat(',' , a.yz_uid2) , '' ) ) as yz_uid, d.kdt_id 
from oa_store_salary ss 
INNER JOIN `oa_admin` `a` ON `a`.`id`=`ss`.`aid` 
INNER JOIN `oa_department` `d` ON `d`.`id`=`ss`.`did` 
where 
ss.date_time = $sdate_time
        ";

        $list = Db::query($sql);

        $StoreSalaryService = new StoreSalaryService();

        foreach ($list as $k => $v) {
            //加班工资
            $overtime_price = $StoreSalaryService->work_overtime(
                $v['yz_uid'],
                $v['kdt_id'] ,
                $sdate ,
                $last_month,
                $v['overtime_date']
            );

            Db::name("store_salary")->where(['id' => $v['id']])->update(
                ['overtime_price' => $overtime_price]
            );
        }
    }


    public function test2()
    {
        $store_salary = Db::name("store_salary")
            ->where([
                'date_time' => **********
            ])
            ->select()
            ->toArray();


        foreach ($store_salary as $k => $v){

            if (empty($v['employee_name']) || empty($v['bank_number']) || empty($v['bank_name'])) continue;

            Db::name("admin_expand")->where(
                ['id' => $v['aid']]
            )->update([
                'salary_card_number' => $v['bank_number'],
                'bank_name' => $v['bank_name'],
                'employee_name' => $v['employee_name'],
            ]);

        }

    }

}









