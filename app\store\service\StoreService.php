<?php

namespace app\store\service;

use app\home\service\YouzanService;
use app\store\model\StoreRank;
use DateTime;
use think\facade\Db;

class StoreService
{

    protected $inauguration_type = [
        ['title' => '20天以上全月指标', 'day' => 20, 'target' => ''],
        ['title' => '15-20天半个月指标', 'day' => 15, 'target' => '/2'],
        ['title' => '15天内无指标', 'day' => 15, 'target' => '0'],
    ];

    public function getTotalRow($database, $where)
    {
        $totalRow = Db::name($database)
            ->field("
                    sum(project_268) as project_268_tot,
                    sum(guasha) as guasha_tot,
                    sum(dianzhong) as dianzhong_tot,
                    sum(haoping) as haoping_tot,
                    sum(dy_haoping) as dy_haoping_tot,
                    
                    sum(sanke) as sanke_tot,
                    sum(sanke2) as sanke2_tot,
                    sum(sanke_yj) as sanke_yj_tot,
                    sum(deal_num) as deal_num_tot,
                    sum(service_num) as service_num_tot,
                    sum(second_service) as second_service_tot,
                    
                    sum(cika_num) as cika_num_tot,
                    sum(cika_amount_1) as cika_amount_1_tot,
                    sum(cika_amount_2) as cika_amount_2_tot,
                    
                    sum(xka_num_1) as xka_num_1_tot,
                    sum(xka_amount_1) as xka_amount_1_tot,
                    sum(xka_num_2) as xka_num_2_tot,
                    sum(xka_amount_2) as xka_amount_2_tot,
                    
                    sum(xcz_num_1) as xcz_num_1_tot,
                    sum(xcz_amount_1) as xcz_amount_1_tot,
                    sum(xcz_num_2) as xcz_num_2_tot,
                    sum(xcz_amount_2) as xcz_amount_2_tot,
                    
                    sum(total_yj) as total_yj_tot,
                    sum(haoka_yj) as haoka_yj_tot
                ")->where($where)->find();

        $totalRow['haoping_tot'] = round(($totalRow['haoping_tot']), 1);
        $totalRow['xka_num_1_tot'] = round(($totalRow['xka_num_1_tot']), 1);
        $totalRow['xka_num_2_tot'] = round(($totalRow['xka_num_2_tot']), 1);
        $totalRow['xcz_num_1_tot'] = round(($totalRow['xcz_num_1_tot']), 1);
        $totalRow['xcz_num_2_tot'] = round(($totalRow['xcz_num_2_tot']), 1);

        $totalRow['deal_num_tot'] = round(($totalRow['deal_num_tot']), 1);

        $totalRow['sanke_kdj_tot'] = floatBc("/", $totalRow['sanke_yj_tot'], $totalRow['sanke_tot']);
        $totalRow['sanke_deal_rate_tot'] = floatBc("*", floatBc("/", $totalRow['deal_num_tot'], $totalRow['sanke_tot'], 4), 100);
        $totalRow['total_kdj_tot'] = floatBc("/", floatBc("+", $totalRow['haoka_yj_tot'], $totalRow['sanke_yj_tot']), $totalRow['service_num_tot']);

        return $totalRow;

    }


    /***
     * @return boolean
     * 判断是否可以进行修改
     */
    public function isEditData($uid, $did, $sdate)
    {

        if (empty($uid) || empty($did) || empty($sdate)) return false;

        //隔天数据不能修改
        $noon_time = strtotime(date("Y-m-d 12:00:00"));
        $now_time = strtotime(date("Y-m-d H:i:s"));

        if ($now_time < $noon_time) {
            $now_time = $noon_time;
        }

        $sdate_time = strtotime('+1 day', strtotime("$sdate 12:00:00"));

        $now_date = date("Y-m-d");
        //不是门店员工 不能修改
        if ($now_date == $sdate) {
            return true;
        }

        //不是门店员工 不能修改
        $admin = Db::name("admin")->where(['id' => $uid])->find();
        if (!empty($admin)) {
            $dep = Db::name("department")->where([['id', 'in', $admin['did']], 'remark' => '门店'])->find();
            if (empty($dep)) {
                return false;
            }
        }

        if ($now_time == $sdate_time) {
            return true;
        } else {
            return false;
        }

//        //查看是否当天提交过已通过申请  flow_id 36
//        $approve = Db::name("Approve")->where([
//            'flow_id' => 36,
//            'mdid' => $did,
//            'admin_id' => $uid,
//            'check_status' => 2,
//            'detail_time' => strtotime($sdate)
//        ])->order('create_time desc')->find();
//
//        if (empty($approve)) return false;
//
//        //创建时间
//        $finish_time = date("Y-m-d", $approve['finish_time']);
//        if ($finish_time != date("Y-m-d")) return false;
//        //要修改日期
//        $detail_time = date("Y-m-d", $approve['detail_time']);
//        if ($detail_time != $sdate) return false;

        return true;
    }

    public function instore($sdate, $did, $aid)
    {

    }

    public function outstore($sdate, $did, $aid)
    {
        //调入日期  2025-04-02 中山南 -> 梅川
//        $store_transfer_in = Db::name('store_transfer')->where([
//            'status' => 2, 'n_did' => $did, 'aid' => $aid
//        ])->order("transfer_date desc")->find();

        //调离日期 2025-04-03 梅川 -> 中山南
        $store_transfer = Db::name('store_transfer')->where([
            'status' => 2, 'o_did' => $did, 'aid' => $aid
        ])->order("transfer_date desc")->find();

        if (!empty($store_transfer)) {

            $sdate_time = strtotime($sdate);
            $transfer_time = strtotime($store_transfer['transfer_date']);

            if ($sdate_time >= $transfer_time) {

                $where = [
                    ['aid', '=', $aid],
                    ['sdate', '>=', $store_transfer['transfer_date']],
                    ['did', '=', $did]
                ];

                //查看当前人员 是否重新调回
                $admin = Db::name('admin')->where(['id' => $aid])->find();
                if (in_array($did, explode(",", $admin['did']))) { //did 包含人员现在门店
                    //查询什么时候调回记录
                    $recall_store_transfer = Db::name('store_transfer')->where([
                        'status' => 2, 'n_did' => $did, 'aid' => $aid, ['transfer_date', '>', $store_transfer['transfer_date']],
                    ])->order("transfer_date desc")->find();
                    if (!empty($recall_store_transfer)){
                        $where[] =  ['sdate', '<', $recall_store_transfer['transfer_date']];
                    }
                }
                $where[] = ['total_yj' , '=' , 0];
                //删除调店日期以后得所有每日数据
                Db::name('store_business')->where($where)->update(['status' => -1]);

                return true;
            }
        }
        return false;
    }

    //调店离职导致数据 删除先检测还原 每日数据
    public function re_store_data($sdate, $did, $aid, $database)
    {
        $del_list = Db::name($database)
            ->where(
                [
                    ['sdate', '=', $sdate],
                    ['did', '=', $did],
                    ['status', '=', -1],
                ])
            ->select();
        foreach ($del_list as $k => $v) {
            $re = $this->outstore($v['sdate'], $v['did'], $v['aid']);
            if ($re === false) {
                Db::name('store_business')->where(['id' => $v['id']])->update(['status' => 1]);
            }
        }
    }

    //store表 添加月份的门店基础数据
    public function addstorerecently($sdate)
    {
        $store_count = Db::name("store")->where(['sdate' => $sdate])->select();
        $store = getmd("门店");

        if (count($store_count) >= count($store)) {
            $this->ratio($sdate);
            return;
        }

        foreach ($store as $k => $v) {
            $store = Db::name("store")->where(['sdate' => $sdate, 'did' => $v['id']])->find();
            if (!empty($store)) {
                continue;
            }

            $recently_store = Db::name("store")->where(['did' => $v['id']])->order("sdate_time desc")->find();

            $in_data = [
                'create_time' => time(),
                'did' => $v['id'],
                'dname' => Db::name("department")->where(['id' => $v['id']])->value('title'),
                'sdate' => $sdate,
                'sdate_time' => strtotime("{$sdate}-01"),
                'amount' => 0,
                'nstatus' => 0,
                'rstatus' => 0,
                'grade' => 0,
                'grade_amount' => '',
                'ratio' => 1,
            ];

            if (!empty($recently_store)) {

                $in_data['nstatus'] = $recently_store['nstatus'];
                $in_data['rstatus'] = $recently_store['rstatus'];
                $in_data['aid'] = $recently_store['aid'];
                $in_data['aname'] = $recently_store['aname'];
                $in_data['amount'] = $recently_store['amount'];

                $in_data['grade'] = $recently_store['grade'];
                $in_data['grade_amount'] = $recently_store['grade_amount'];
                $in_data['target_amount'] = $recently_store['target_amount'];
                $in_data['ratio'] = $recently_store['ratio'];
                $in_data['rent_amount'] = $recently_store['rent_amount'];
                $in_data['holder_id'] = $recently_store['holder_id'];
            } else {
                $department = Db::name("department")->where(['id' => $v['id']])->find();
                if ($department['leader_id'] != 0) {
                    $in_data['aid'] = $department['leader_id'];
                    $admin = Db::name("admin")->where(['id' => $department['leader_id']])->find();
                    $in_data['aname'] = $admin['name'];
                }
            }

            Db::name("store")->insertGetId($in_data);
        }
    }

    //八月以后 业绩指标可根据 等级中间档*系数来 指定
    public function ratio($sdate, $did = 0)
    {
        $where = array();
        if (!empty($did)) {
            $where['did'] = $did;
        }
        $where['sdate'] = $sdate;

        $store_count = Db::name("store")->where($where)->select();
        //八月份以后 按照分级 中间档*系数 规定业绩指标
        if (strtotime($sdate) >= strtotime('2024-08')) {
            foreach ($store_count as $k => $v) {
                if ($v['target_amount'] == 0 && !empty($v['grade_amount'])) {
                    $grade_amount = explode(",", $v['grade_amount']);
                    $target_amount = round($grade_amount[1] * $v['ratio'], 2);
                    Db::name("store")->where(['id' => $v['id']])->update(['target_amount' => $target_amount]);
                }
            }
        }
    }

    public function bonus_cal2($rname = '', $ce = 0, $price = 0, $tit = '', $tot = 0, $nstatus = 0, $rstatus = 0, $sdate = "")
    {
        $sdate_time = strtotime("{$sdate}-01");

        if ($tit == 'project_268') {

            if ($rname == '技术指导' || $rname == '学徒及无指标人员' || $rname == '店长') {
                return 0;
            } else if ($rname == '大师傅' || $rname == '普通老师' || $rname == '两个月新员工' || $rname == '一个月新员工') {
                if ($tot >= 45) {
                    return 200;
                } else if ($tot >= 40) {
                    return 0;
                } else {
                    return floatBc("*", floatBc("-", $tot, 40), $price);
                }
            }
        } elseif ($tit == 'guasha') {
            if ($rname == '技术指导' || $rname == '大师傅' || $rname == '普通老师' || $rname == '两个月新员工' || $rname == '一个月新员工') {
                if ($tot >= 35) {
                    return 50;
                } else if ($tot <= 30) {
                    return floatBc("*", floatBc("-", $tot, 30), $price);
                } else {
                    return 0;
                }
            } else {
                return 0;
            }

        } elseif ($tit == 'dianzhong') {
            if ($rname == '技术指导' || $rname == '大师傅') {
                if ($tot >= 100) {
                    return $tot * 9 + 50;
                } else if ($tot >= 90) {
                    return $tot * 7 + 50;
                } else {
                    return floatBc("*", floatBc("-", $tot, 90), $price);
                }
            } else if ($rname == '普通老师') {
                if ($tot >= 100) {
                    return $tot * 9 + 50;
                } else if ($tot >= 80) {
                    return $tot * 7 + 50;
                } else if ($tot >= 70) {
                    return $tot * 5 + 50;
                } else if ($tot >= 60) {
                    return $tot * 5;
                } else {
                    return floatBc("*", floatBc("-", $tot, 60), $price);
                }
            } else if ($rname == '两个月新员工') {
                if ($tot >= 100) {
                    return $tot * 9 + 50;
                } else if ($tot >= 80) {
                    return $tot * 7 + 50;
                } else if ($tot >= 70) {
                    return $tot * 5 + 50;
                } else if ($tot >= 60) {
                    return $tot * 5;
                } else if ($tot >= 30) {
                    return 0;
                } else {
                    return floatBc("*", floatBc("-", $tot, 30), $price);
                }
            } else if ($rname == '一个月新员工') {
                if ($tot >= 100) {
                    return $tot * 9 + 50;
                } else if ($tot >= 80) {
                    return $tot * 7 + 50;
                } else if ($tot >= 70) {
                    return $tot * 5 + 50;
                } else if ($tot >= 60) {
                    return $tot * 5;
                } else if ($tot >= 15) {
                    return 0;
                } else {
                    return floatBc("*", floatBc("-", $tot, 15), $price);
                }
            } else {
                return 0;
            }
        } elseif ($tit == 'haoping') {
            if ($rname == '技术指导' || $rname == '大师傅' || $rname == '普通老师' || $rname == '两个月新员工' || $rname == '一个月新员工') {
                if ($tot >= 7) {
                    return $tot * 30 + 50;
                } else if ($tot >= 4) {
                    return $tot * 30;
                } else {
                    return floatBc("*", floatBc("+", $tot, $ce), $price);
                }
            } else {
                return 0;
            }
        } elseif ($tit == 'kaj') {
            $s = 30000 - $tot;
            if ($rname == '技术指导' || $rname == '大师傅') {
                if ($s > 4000) {
                    return ($tot - 26000) / 1000 * 50;
                }
            } else if ($rname == '普通老师') {
                if ($s > 8000) {
                    return ($tot - 22000) / 1000 * 50;
                }
            } else if ($rname == '两个月新员工') {
                if ($s > 13000) {
                    return ($tot - 17000) / 1000 * 50;
                }
            } else if ($rname == '一个月新员工') {
                if ($s > 18000) {
                    return ($tot - 12000) / 1000 * 50;
                }
            } else {
                return 0;
            }

            if ($s <= 0) {
                return 100;
            } else {
                return 0;
            }

        } elseif ($tit == 'kat') {
            if ($rstatus == 0) { //是否参与改革
                return $tot * 0.05;
            } else {
                if ($tot < 22000) {
                    return $tot * 0.03;
                } elseif ($tot < 30000) {
                    return $tot * 0.05;
                } elseif ($tot < 40000) {
                    return $tot * 0.06;
                } else {
                    return $tot * 0.07;
                }
            }
            return 0;
        }

    }


    public function bonus_cal($rid = 0, $store_business_t, $tit = '', $rstatus = 0)
    {
        $in_tit = $tit;
        $tit = str_replace('_jj', "", $tit);
        $tit = str_replace('_kk', "", $tit);

        if ($tit == 'level'){
            $tit = 'kat';
        }



        $sdate = $store_business_t['sdate'];
        $tot = !empty($store_business_t[$tit]) ? $store_business_t[$tit] : 0;
        $inauguration_type = !empty($store_business_t['inauguration_type']) ? $store_business_t['inauguration_type'] : 0;

        if ($tit == 'kat' && $rstatus == 0) {
            if ($rid == 7) {
                return 0;
            }
            if (strtotime($sdate) < strtotime("2024-11")){
                return $tot * 0.05;
            }
        } else if ($tit == 'kat' && $rstatus == 1 && $rid == 6) {
            if ($in_tit == 'level'){
                return 0;
            }
            return $tot * 0.05;
        }

        if ($tit == 'kaj' && $rstatus == 1 && strtotime($sdate) >= strtotime("2024-07")) return 0;

        $rc_where = [
            ['', 'exp', Db::raw("FIND_IN_SET('{$rid}',r_id)")],
            ['sdate', '=', $sdate],
            ['project', '=', $tit]
        ];
        if (strtotime($sdate) == strtotime("2025-01") && $tit == 'kat'){
            if ($store_business_t['ratio'] == 2){
                $rc_where['ratio'] = 2;
            }else{
                $rc_where['ratio'] = 1;
            }
        }

        $rank_calculate = Db::name("StoreRankCalculate")->where($rc_where)->order("sort asc")->select()->toArray();

        $target = array();

        if ($tit == 'haoping') { // 好评 及 折叠好评
            $haoping_zd = $store_business_t['haoping_zd'];
            $real_tot = $tot - $haoping_zd;

            $haop_t = 4 ;

            if ($inauguration_type == 2){
                $haop_t = 0;
            }elseif ($inauguration_type == 1){
                $haop_t = 2;
            }

            //24年11月 - 25年2月份 考核大众指标
            if (strtotime($sdate) < strtotime("2024-11") || strtotime($sdate) > strtotime("2025-02")) {
                if (strtotime($sdate) < strtotime("2024-11")){
                    $t_j = 30;
                }else if(strtotime($sdate) > strtotime("2025-02")){
                    $t_j = 20;
                }
                if ($rid == 8 || $rid == 6 || $rid == 9) {
                    if (strpos($in_tit, "_jj") !== false) {
                        if ($rid == 6 && $real_tot >= 7) {
                            return $real_tot * $t_j + 50;
                        } else {
                            return $real_tot * $t_j;
                        }
                    }
                } elseif ($rid == 7) {
                    return 0;
                } else {
                    if ($tot < $haop_t) {
                        //return ($tot * 2 - $haoping_zd - 4) * $t_j;
                        if (strpos($in_tit, "_jj") !== false) {
                            return ($tot - $haoping_zd) * $t_j;
                        }

                        if (strpos($in_tit, "_kk") !== false) {
                            return ($tot - $haop_t) * $t_j;
                        }
                    } else {
                        if ($real_tot < 7) {
                            if (strpos($in_tit, "_jj") !== false) {
                                return $real_tot * $t_j;
                            }
                        } else {
                            if (strpos($in_tit, "_jj") !== false) {
                                return $real_tot * $t_j + 50;
                            }
                        }
                    }
                }
                return 0;
            } else {
                $tot = $real_tot;
            }
        }

        if ($in_tit == 'dy_haoping_jj'){
            if ($rid == 7) {
                return 0;
            }

//            if ($tot >= 7 && strtotime($sdate) != strtotime("2025-02")  ){
//                return $tot * 20 + 50;
//            }else{
//                return $tot * 20;
//            }
            return $tot * 20;
        }

        if (empty($rank_calculate)) return 0;

        foreach ($rank_calculate as $k => $v) {
            if (!empty($rank_calculate['target'])) {
                $target = $v;
                break;
            } else {
                if ($k == 0) {
                    if ($tot >= $rank_calculate[$k]['target']) {
                        $target = $v;
                        break;
                    }
                } else if ($k > 0 && $k + 1 < count($rank_calculate)) {
                    if ($rank_calculate[$k - 1]['target'] > $tot && $tot >= $rank_calculate[$k]['target']) {
                        $target = $v;
                        break;
                    }
                } else {
                    $target = $v;
                    break;
                }
            }
        }


        if ($in_tit == 'level'){

            if ($tot < intval($target['target'] ) && $v['symbol'] != 0){
                $target['symbol'] = 1;
            }
            return $target['symbol'];
        }

        if (isset($target['amount']) && $target['amount'] !== null) {
            $result = $target['amount'];
        } else {
            $formula = str_replace('{$tot}', $tot, $target['formula']);
            $formula = str_replace('{$target}', $target['target'], $formula);
            eval("\$result = $formula;");
        }

        if ($result < 0) {
            if ($inauguration_type == 2) {
                $result = 0;
            } else if ($inauguration_type == 1) {

                if (ceil($target['target'] / 2) > $tot ) {
                    $formula = str_replace('{$tot}', $tot, $target['formula']);
                    $formula = str_replace('{$target}', ceil($target['target'] / 2), $formula);
                    eval("\$result = $formula;");
                }else{
                    $result = 0;
                }
            }
        }

        if (strpos($in_tit, "_jj") !== false) {
            if ($result > 0) {
                return $result;
            } else {
                return 0;
            }
        } else if (strpos($in_tit, "_kk") !== false) {
            if ($result < 0) {
                return $result;
            } else {
                return 0;
            }
        } else {
            return $result;
        }

    }


    public function rank_data($rank, $store_business_t)
    {
        $nstatus = 0;
        $rstatus = 0;
        //查询门店是否参与薪资改革
        $store = DB::name("store")->where(['did' => $store_business_t['did'], 'sdate' => $store_business_t['sdate']])->find();

        if (!empty($store)) {
            $nstatus = $store['nstatus'];
            $rstatus = $store['rstatus'];
            $store_business_t['ratio'] = $store['ratio'];
        }
        $store_business_t['kat'] = $store_business_t['kaj'];

        $u_data = [
            'r_id' => $rank['id'],
            'r_name' => $rank['rank'],
            'project_268_ce' => $rank['project_268_limit'] == 0 ? 0 : $store_business_t['project_268'] - $rank['project_268_limit'],
            'guasha_ce' => $rank['guasha_limit'] == 0 ? 0 : $store_business_t['guasha'] - $rank['guasha_limit'],
            'dianzhong_ce' => $rank['dianzhong_limit'] == 0 ? 0 : $store_business_t['dianzhong'] - $rank['dianzhong_limit'],
            'second_service_ce' => $rank['second_service_limit'] == 0 ? 0 : $store_business_t['second_service'] - $rank['second_service_limit'],
            'haoping_ce' => $rank['haoping_limit'] == 0 ? 0 : $store_business_t['haoping'] - $rank['haoping_limit'],
            'dy_haoping_ce' => $rank['dy_haoping_limit'] == 0 ? 0 : $store_business_t['dy_haoping'] - $rank['dy_haoping_limit'],
            'kaj_ce' => $rank['kat_limit'] == 0 ? 0 : $store_business_t['kaj'] - $rank['kat_limit'],

            'project_268_jj' => $this->bonus_cal($rank['id'], $store_business_t, 'project_268_jj'),
            'project_268_kk' => $this->bonus_cal($rank['id'], $store_business_t, 'project_268_kk'),

            'guasha_jj' => $this->bonus_cal($rank['id'], $store_business_t, 'guasha_jj'),
            'guasha_kk' => $this->bonus_cal($rank['id'], $store_business_t, 'guasha_kk'),

            'dianzhong_jj' => $this->bonus_cal($rank['id'], $store_business_t, 'dianzhong_jj'),
            'dianzhong_kk' => $this->bonus_cal($rank['id'], $store_business_t, 'dianzhong_kk'),

            'second_service_jj' => $this->bonus_cal($rank['id'], $store_business_t, 'second_service_jj'),
            'second_service_kk' => $this->bonus_cal($rank['id'], $store_business_t, 'second_service_kk'),

            'haoping_jj' => $this->bonus_cal($rank['id'], $store_business_t, 'haoping_jj', $rstatus),
            'haoping_kk' => $this->bonus_cal($rank['id'], $store_business_t, 'haoping_kk', $rstatus),

            'dy_haoping_jj' => $this->bonus_cal($rank['id'], $store_business_t, 'dy_haoping_jj', $rstatus),
            'dy_haoping_kk' => $this->bonus_cal($rank['id'], $store_business_t, 'dy_haoping_kk', $rstatus),

            'kaj_jj' => $this->bonus_cal($rank['id'], $store_business_t, 'kaj_jj', $rstatus),
            'kaj_kk' => $this->bonus_cal($rank['id'], $store_business_t, 'kaj_kk', $rstatus),

            'kaj_kt' => $this->bonus_cal($rank['id'], $store_business_t, 'kat', $rstatus),

            'level' => $this->bonus_cal($rank['id'], $store_business_t, 'level', $rstatus),

            'project_268_t' => $rank['project_268_limit'],
            'guasha_t' => $rank['guasha_limit'],
            'haoping_t' => $rank['haoping_limit'],
            'second_service_t' =>  isset($rank['second_service_limit']) ? $rank['second_service_limit'] : 0,
            'dy_haoping_t' => $rank['dy_haoping_limit'],

            'target' => json_encode([
                'project_268' => $rank['project_268_limit'],
                'guasha' => $rank['guasha_limit'],
                'dianzhong' => $rank['dianzhong_limit'],
                'dy_haoping' => $rank['dy_haoping_limit'],
                'haoping' => $rank['haoping_limit'],
                'kat_limit' => isset($rank['kat_limit']) ? $rank['kat_limit'] : 0,
                'second_service' => isset($rank['second_service_limit']) ? $rank['second_service_limit'] : 0,
            ])

        ];

        return $u_data;
    }

    /***
     * @param $where
     * @param $param
     * @return void
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * 每日数据老师计算
     */
    public function record_store_business_t($where, $param)
    {
        $sum = Db::name("store_business")
            ->field('
                                sum(project_268) as project_268 , 
                                sum(guasha) as guasha, 
                                sum(dianzhong) as dianzhong,
                                sum(haoping) as haoping,
                                sum(dy_haoping) as dy_haoping,
                                sum(sanke) as sanke,
                                sum(sanke2) as sanke2,
                                sum(sanke_yj) as sanke_yj,
                                sum(deal_num) as deal_num,
                                sum(service_num) as service_num,
                                sum(cika_num) as cika_num,
                                sum(cika_amount_1) as cika_amount_1,
                                sum(cika_amount_2) as cika_amount_2,
                                sum(xka_num_1) as xka_num_1,
                                sum(xka_amount_1) as xka_amount_1,
                                sum(xka_num_2) as xka_num_2,
                                sum(xka_amount_2) as xka_amount_2,
                                sum(xcz_num_1) as xcz_num_1,
                                sum(xcz_amount_1) as xcz_amount_1,
                                sum(xcz_num_2) as xcz_num_2,
                                sum(xcz_amount_2) as xcz_amount_2,
                                sum(haoka_yj) as haoka_yj,
                                sum(second_service) as second_service,
                                aid,aname,did,dname
                            ')
            ->where($where)
            ->find();

        if ( strtotime($param['sdate'])  >= strtotime(date("2025-04"))){
            $sum['dianzhong'] = 0;
        }

        //二次服务数
        $store_second = Db::name("store_second")->where([
            'aid' => $where['aid'] ,
            'sdate' => $param['sdate'] ,
            'did' => $sum['did']
        ])->find();

        $sum['second_service'] = empty($store_second) ? 0 :
            (
                $store_second['num_2'] + $store_second['num_3'] + $store_second['num_4'] +
                $store_second['num_5'] + $store_second['num_6'] + $store_second['num_7'] +
                $store_second['num_8'] + $store_second['num_9']
            );

        $re_s = Db::name("store_business_t")->where([
            'sdate' => $param['sdate'],
            'aid' => $where['aid'],
            'did' => $sum['did'],
        ])->find();

        $store = Db::name("store")->where([
            'sdate' => $param['sdate'],
            'did' => $sum['did'],
        ])->find();


        $sum['sdate'] = $param['sdate'];
        $sum['create_time'] = time();

        //卡金
        $sum['kaj'] = $sum['cika_amount_1'] + $sum['cika_amount_2']
            + $sum['xka_amount_1'] + $sum['xka_amount_2']
            + $sum['xcz_amount_1'] + $sum['xcz_amount_2'];

        //业绩变更
        $store_business_diff = Db::name("store_business_diff")->where([
            'sdate' => $sum['sdate'],
            'aid' => $sum['aid'],
            'did' => $sum['did'],
            'status' => 1
        ])->field("sum(minus_amount) as minus_amount")->find();

        if (!empty($store_business_diff) && !empty($store_business_diff['minus_amount'])) {
            $sum['kaj'] = $sum['kaj'] - $store_business_diff['minus_amount'];
        }

        $store_business_diff = Db::name("store_business_diff")->where([
            'sdate' => $sum['sdate'],
            's_aid' => $sum['aid'],
            's_did' => $sum['did'],
            'status' => 1
        ])->field("sum(add_amount) as add_amount")->find();

        if (!empty($store_business_diff) && !empty($store_business_diff['add_amount'])) {
            $sum['kaj'] = $sum['kaj'] + $store_business_diff['add_amount'];
        }

        $sum['total_yj'] = $sum['kaj'] + $sum['sanke_yj'];
        $sum['sanke_kdj'] = 0;
        $sum['sanke_deal_rate'] = 0;
        if ($sum['sanke'] != 0) {
            $sum['sanke_kdj'] = $sum['sanke_yj'] / $sum['sanke'];
            $sum['sanke_deal_rate'] = $sum['deal_num'] / $sum['sanke'] * 100;
        }
        $sum['total_kdj'] = 0;
        if ($sum['service_num'] != 0) {
            $sum['total_kdj'] = ($sum['sanke_yj'] + $sum['haoka_yj']) / $sum['service_num'];
        }

        $sum['haoping_ce'] = 0;

        $sum['inauguration_type'] = $this->staff_target($param['sdate'], $where['aid'], $sum['did']);

        if (!empty($re_s)) {

            $sum['haoping_zd'] = $re_s['haoping_zd'];
            //查看师傅指标
            if (!empty($re_s['r_id'])) {
                $store_record_model = new StoreRank();

                $rank = $store_record_model->getMonthTarget($param['sdate'], $re_s['r_id'], $sum['inauguration_type'] , $store);

                if (empty($rank['id'])) return;
                //计算 项目提成金额

                /***
                 * 获取职位 对应的奖罚 公式
                 */
                $u_data = $this->rank_data($rank, $sum);

                unset($u_data['r_id']);
                unset($u_data['r_name']);

                $sum = array_merge($sum, $u_data);
            } else {
                $sum['haoping_ce'] = 0;
            }
            $sum['update_time'] = time();

            //删除离职人员
            $s_sdate = strtotime("{$param['sdate']}-01");
            $e_sdate = strtotime(getLastMonth("{$param['sdate']}-01"));
            $admin = Db::name('admin')->where(['id' => $re_s['aid']])->find();

            if (!empty($admin['res_date']) && $admin['status'] == 2) {
                $res_time = strtotime($admin['res_date']);
                $day = explode("-", $admin['res_date'])[2];
                $res_date = date("Y-m", $res_time);
                if ($day > 1) {
                    $res_date = date("Y-m", strtotime("+1 month", $res_time));
                }
                Db::name('store_business_t')->where(['id' => $re_s['id']])->update($sum);
                Db::name('store_business_t')->where([
                    ['aid', '=', $re_s['aid']], ['sdate', '>=', $res_date], ['did', '=', $sum['did']]
                ])->update(['status' => -1]);
            } else {
                //查询每日数据是否有这个人
                $sb_data = Db::name("store_business")->where([
                    ['sdate' , 'like' , "%{$param['sdate']}%"] ,
                    'aid' => $sum['aid'],
                    'did' => $sum['did'],
                    'status' => 1
                ])->find();

                if (!empty($sb_data)){
                    $sum['status'] = 1;
                    Db::name('store_business_t')->where(['id' => $re_s['id']])->update($sum);
                }else{
                    $sum['status'] = -1;
                    Db::name('store_business_t')->where(['id' => $re_s['id']])->update($sum);
                }

                //调离记录
//                $re = $this->outstore("{$param['sdate']}-01", $sum['did'], $re_s['aid']);
//                var_dump($re);
//                if ($re === true) {
//                    $sum['status'] = -1;
//                    Db::name('store_business_t')->where(['id' => $re_s['id']])->update($sum);
//                } else {
//                    $sum['status'] = 1;
//                    Db::name('store_business_t')->where(['id' => $re_s['id']])->update($sum);
//                }
            }

        } else {
            $sum['sdate'] = $param['sdate'];

            $sum['haoping_zd'] = 0;
            $sum['create_time'] = time();

            $sum['s_sdate'] = "{$param['sdate']}-01";
            Db::name("store_business_t")->insertGetId($sum);
        }
    }


    public function store_business_t_inauguration_type($inauguration_type)
    {
        return $this->inauguration_type[$inauguration_type];
    }


    /***
     * @param $where
     * @return array|mixed|Db|\think\Model
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * 每日数据门店计算
     */
    public function record_store_business_m($where)
    {
        $sum = Db::name("store_business")
            ->field('
                                sum(project_268) as project_268 ,
                                sum(guasha) as guasha,
                                sum(dianzhong) as dianzhong,
                                sum(haoping) as haoping,
                                sum(sanke) as sanke,
                                sum(sanke2) as sanke2,
                                sum(sanke_yj) as sanke_yj,
                                sum(deal_num) as deal_num,
                                sum(service_num) as service_num,
                                sum(second_service) as second_service,
                                sum(cika_num) as cika_num,
                                sum(cika_amount_1) as cika_amount_1,
                                sum(cika_amount_2) as cika_amount_2,
                                sum(xka_num_1) as xka_num_1,
                                sum(xka_amount_1) as xka_amount_1,
                                sum(xka_num_2) as xka_num_2,
                                sum(xka_amount_2) as xka_amount_2,
                                sum(xcz_num_1) as xcz_num_1,
                                sum(xcz_amount_1) as xcz_amount_1,
                                sum(xcz_num_2) as xcz_num_2,
                                sum(xcz_amount_2) as xcz_amount_2,
                                sum(total_yj) as total_yj,
                                sum(haoka_yj) as haoka_yj,
                                did,dname,sdate
                            ')
            ->where($where)
            ->find();

        $s_b_m = Db::name("store_business_m")->where([
            'sdate' => $where['sdate'],
            'did' => $sum['did']
        ])->find();

        $sum['sanke_kdj'] = 0;
        $sum['sanke_deal_rate'] = 0;

        /**业绩总和
         * 'hk_yj' => !empty($hk_yj['real_pay']) ? $hk_yj['real_pay'] : 0,
         * 'xj_yj' => !empty($xj_yj['real_pay']) ? $xj_yj['real_pay'] : 0,
         * 'cika_xh_yj' => !empty($cika_xh_yj['real_pay']) ? $cika_xh_yj['real_pay'] : 0,
         * 'kk_yj' => !empty($kk_yj['real_pay']) ? $kk_yj['real_pay'] : 0,
         * 'hk_reverse_yj' => !empty($hk_reverse_yj['real_pay']) ? $hk_reverse_yj['real_pay'] : 0,
         * 'xj_reverse_yj' => !empty($xj_reverse_yj['real_pay']) ? $xj_reverse_yj['real_pay'] : 0,
         * 'cika_xh_reverse_yj' => !empty($cika_xh_reverse_yj['real_pay']) ? $cika_xh_reverse_yj['real_pay'] : 0,
         */
        if (strtotime($where['sdate']) >= strtotime("2024-10-01")) {
            $yj_total = $this->store_m_yj($where['sdate'], date("Y-m-d", strtotime('+1 day', strtotime($where['sdate']))), $sum['did']);
            $sum['total_yj'] = round(($yj_total['xj_yj'] - $yj_total['xj_reverse_yj']) / 100, 2);
            $sum['haoka_yj'] = round((
                    $yj_total['hk_yj'] - $yj_total['hk_reverse_yj'] +
                    $yj_total['cika_xh_yj'] - $yj_total['cika_xh_reverse_yj']
                ) / 100, 2);
        } else {
            $sum['total_yj'] = $sum['sanke_yj'] +
                $sum['cika_amount_1'] + $sum['cika_amount_2'] +
                $sum['xka_amount_1'] + $sum['xka_amount_2'] +
                $sum['xcz_amount_1'] + $sum['xcz_amount_2'];
        }

        $sum['total_yj'] = round($sum['total_yj'], 2);

        $sum['deal_num'] = round($sum['deal_num'], 1);
        $sum['xka_num_1'] = round($sum['xka_num_1'], 1);
        $sum['xka_num_2'] = round($sum['xka_num_2'], 1);
        $sum['xcz_num_1'] = round($sum['xcz_num_1'], 1);
        $sum['xcz_num_2'] = round($sum['xcz_num_2'], 1);

        $sum['xka_amount_1'] = round($sum['xka_amount_1'], 1);
        $sum['xka_amount_2'] = round($sum['xka_amount_2'], 1);
        $sum['xcz_amount_1'] = round($sum['xcz_amount_1'], 1);
        $sum['xcz_amount_2'] = round($sum['xcz_amount_2'], 1);


        if ($sum['sanke'] != 0) {
            $sum['sanke_kdj'] = bcdiv($sum['sanke_yj'], $sum['sanke'], 2);
            $sum['sanke_deal_rate'] = bcdiv($sum['deal_num'] * 100, $sum['sanke'], 2);
        }

        $sum['total_kdj'] = 0;
        if ($sum['service_num'] != 0) {
            $sum['total_kdj'] = bcdiv(($sum['sanke_yj'] + $sum['haoka_yj']), $sum['service_num'], 2);
        }

        if (!empty($s_b_m)) {
            $sum['update_time'] = time();
            Db::name("store_business_m")->where(['id' => $s_b_m['id']])->update($sum);
        } else {
            $sum['create_time'] = time();
            Db::name("store_business_m")->insertGetId($sum);
        }
        return $sum;
    }


    /***
     * @param $param
     * @return void
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * 一系列离职操作
     */
    //调转 离职记录
    public function add_store_transfer($param)
    {
        $param['create_time'] = time();
        $param['create_date'] = date("Y-m-d");

        $admin = Db::name('admin')->where(['id' => $param['aid']])->find();
//        $admin_expand = Db::name('admin_expand')->where(['id' => $param['aid']])->find();

        //调转门店
        if (!empty($param['n_did'])) {
            $param['status'] = 2;
            if (!empty($admin)) {
                $admin_did = explode(",", $admin['did']);
                $n_dep = array();
                foreach ($admin_did as $k => $v) {
                    $dep = Db::name('department')->where(['id' => $v, 'remark' => '门店'])->find();
                    if (!empty($dep)) {
                        $n_dep[] = $param['n_did'];
                    } else {
                        $n_dep[] = $v;
                    }
                }
                Db::name('admin')->where(['id' => $param['aid']])->update(
                    ['did' => implode(",", $n_dep)]
                );
            }

        } else { //离职门店
            //修改员工状态
            Db::name('admin')->where(['id' => $param['aid']])->update(
                [
                    'status' => 2,
                    'res_date' => $param['transfer_date'] ,
                    'is_black' => isset($param['is_black']) ? $param['is_black'] : 0
                ]
            );
        }

        if ($param['id'] > 0) {
            Db::name('store_transfer')->where(['id' => $param['id']])->update($param);
        } else {
            Db::name('store_transfer')->strict(false)->field(true)->insertGetId($param);
        }
    }

    //添加人才中心 离职记录
    public function add_admin_view($data, $uid, $uname)
    {
        $aadmin = Db::name("admin")->where(['id' => $data['aid']])->find();
        $admin_view_data = [
            'name' => $aadmin['name'],
            'age' => $aadmin['age'],
            'id_card' => !empty($aadmin_expand) ? $aadmin_expand['id_card_number'] : '',
            'mobile' => $aadmin['mobile'],
            'sex' => $aadmin['sex'],
            'did' => $data['o_did'],
            'dname' => $data['o_dname'],
            'dstatus' => $data['dstatus'],
            'status' => 6,
            'create_time' => time(),
            'edu_background' => !empty($aadmin_expand) ? $aadmin_expand['education'] : '',
            'edu_school' => !empty($aadmin_expand) ? $aadmin_expand['graduation_school'] : '',
            'edu_major' => !empty($aadmin_expand) ? $aadmin_expand['major'] : '',
            'certificate' => !empty($aadmin_expand) ? $aadmin_expand['certificates'] : ''
        ];
        $re_admin_view = Db::name("admin_view")->where(['aid' => $data['aid']])->find();
        if (!empty($re_admin_view)) {
            $admin_view_id = $re_admin_view['id'];
            Db::name("admin_view")->where(['id' => $re_admin_view['id']])->update(['status' => 6]);
        } else {
            $admin_view_id = Db::name("admin_view")->insertGetId($admin_view_data);
        }

        $end_time = date("Y-m-d");
        $admin_view_record_data = [
            'aview_id' => $admin_view_id,
            'aid' => $uid,
            'aname' => $uname,
            'create_time' => time(),
            'remark' => "离职时间：{$end_time}，离职原因：修改离职状态",
            's_str' => "修改离职状态",
            'type' => -1,
        ];
        Db::name("admin_view_record")->insertGetId($admin_view_record_data);
    }

    //还原离职人员数据
    public function lizhi_restore($aid)
    {
        //还原人才中心数据


    }

    public function store_m_yj($start_date, $end_date, $did)
    {

        $store = Db::name("department")->where(['id' => $did])->find();

        $hk_type = '120';
        $hk_yj = Db::name("order")
            ->field('sum(op.real_pay) as real_pay')
            ->alias('o')
            ->join('order_pay op', 'op.tid = o.tid')
            ->where([
                ['o.sale_kdt_id', '=', $store['kdt_id']],
                ['o.finish_time', 'between', [strtotime("$start_date 00:00:00") * 1000, strtotime("$start_date 23:59:59") * 1000]],
                ['o.order_state', '=', 40],
//                 ['o.is_reverse', '=', 0],
                ['op.pay_channel', 'in', $hk_type],
            ])->find();
//        getLastSql("order");


//        $xj_type = '1000,1001,1014,1015,1003,201,202,1018,28,1002,303,1020,1021'; //pos
        $xj_type = '1000,1001,1014,1015,1003,201,1018,28,1002,303,1020,1021,1022'; //pos
//        $xj_yj = Db::name("order")
//            ->field('sum(op.real_pay) as real_pay')
//            ->alias('o')
//            ->join('order_pay op', 'op.tid = o.tid')
//            ->where([
//                ['o.sale_kdt_id', '=', $store['kdt_id']],
//                ['o.finish_time', 'between', [strtotime("$start_date 00:00:00") * 1000, strtotime("$start_date 23:59:59") * 1000]],
//                ['o.order_state', '=', 40],
//                ['op.pay_channel', 'in', $xj_type],
//            ])->group("op.transaction_no")->find();

        $xj_yj = Db::query("SELECT sum(a.real_pay) as real_pay from (
	SELECT op.real_pay FROM `oa_order` `o` 
	INNER JOIN `oa_order_pay` `op` ON `op`.`tid`=`o`.`tid` 
	WHERE `o`.`sale_kdt_id` = {$store['kdt_id']}
	AND `o`.`finish_time` BETWEEN '".strtotime("$start_date 00:00:00") * 1000 ."' AND '".strtotime("$start_date 23:59:59") * 1000 ."' 
	AND `o`.`order_state` = '40' 
	AND `op`.`pay_channel` IN ($xj_type) 
	GROUP BY `op`.`transaction_no` 
) as a");

        $cika_order = Db::name("order")
            ->field("oi.*")
            ->alias('o')
            ->join('order_item oi', 'oi.tid = o.tid')
            ->where([
                ['o.sale_kdt_id', '=', $store['kdt_id']],
                ['o.finish_time', 'between', [strtotime("$start_date 00:00:00") * 1000, strtotime("$start_date 23:59:59") * 1000]],
                ['o.order_state', '=', 40],
//                ['o.is_reverse', '=', 0],
                ['oi.promotion_type', '=', 1],
            ])->select()->toArray();

        $empty = array();
        $cika_xh_yj['real_pay'] = 0;
        foreach ($cika_order as $k => $v) {
            if (!in_array($v['item_no'], $empty)) {
                $cika_xh_yj['real_pay'] += $v['worth'];
                $empty[] = $v['item_no'];
            }
        }

        $kk_yj = Db::name("order")
            ->field('sum(o.pay_real_pay) as real_pay')
            ->alias('o')
            ->join('order_item oi', 'oi.tid = o.tid')
            ->where([
                ['o.sale_kdt_id', '=', $store['kdt_id']],
                ['o.finish_time', 'between', [strtotime("$start_date 00:00:00") * 1000, strtotime("$start_date 23:59:59") * 1000]],
                ['o.order_state', '=', 40],
                ['o.is_reverse', '=', 0],
                ['oi.item_type', 'in', '2,4,9'],
            ])->find();


        //耗卡退款
        $hk_reverse_yj = Db::name("order")
            ->field('sum(op.real_pay) as real_pay')
            ->alias('o')
            ->join('order_pay op', 'op.tid = o.tid')
            ->join('order_reverse ors', 'ors.tid = o.tid')
            ->where([
                ['o.sale_kdt_id', '=', $store['kdt_id']],
                ['ors.reverse_finish_time', 'between', ["$start_date 00:00:00", "$start_date 23:59:59"]],
                ['o.order_state', '=', 40],
                ['o.is_reverse', '=', 1],
                ['op.pay_channel', 'in', $hk_type],
            ])->find();


        $xj_reverse_yj = Db::name("order")
            ->field('sum(op.real_pay) as real_pay')
            ->alias('o')
            ->join('order_pay op', 'op.tid = o.tid')
            ->join('order_reverse ors', 'ors.tid = o.tid')
            ->where([
                ['o.sale_kdt_id', '=', $store['kdt_id']],
                ['ors.reverse_finish_time', 'between', ["$start_date 00:00:00", "$start_date 23:59:59"]],
                ['o.order_state', '=', 40],
                ['o.is_reverse', '=', 1],
                ['op.pay_channel', 'in', $xj_type],
            ])->find();


//        $cika_xh_reverse_yj = Db::name("order")
//            ->field('sum(oi.worth) as real_pay')
//            ->alias('o')
//            ->join('order_item oi', 'oi.tid = o.tid')
//            ->join('order_reverse ors', 'ors.tid = o.tid')
//            ->where([
//                ['o.sale_kdt_id', '=', $store['kdt_id']],
//                ['ors.reverse_finish_time', 'between', ["$start_date 00:00:00" , "$end_date 00:00:00"]],
//                ['o.order_state', '=', 40],
//                ['o.is_reverse', '=', 1],
//                ['oi.promotion_type', '=', 1],
//            ])->find();

        $cika_reverse_order = Db::name("order")
            ->field("oi.*")
            ->alias('o')
            ->join('order_item oi', 'oi.tid = o.tid')
            ->join('order_reverse ors', 'ors.tid = o.tid')
            ->where([
                ['o.sale_kdt_id', '=', $store['kdt_id']],
                ['ors.reverse_finish_time', 'between', ["$start_date 00:00:00", "$start_date 23:59:59"]],
                ['o.order_state', '=', 40],
                ['o.is_reverse', '=', 1],
                ['oi.promotion_type', '=', 1],
            ])->select()->toArray();

        $empty_reverse = array();
        $cika_xh_reverse_yj['real_pay'] = 0;
        foreach ($cika_reverse_order as $k => $v) {
            if (!in_array($v['item_no'], $empty_reverse)) {
                $cika_xh_reverse_yj['real_pay'] += $v['worth'];
                $empty_reverse[] = $v['item_no'];
            }
        }

        return [
            'hk_yj' => !empty($hk_yj['real_pay']) ? $hk_yj['real_pay'] : 0,
            'xj_yj' => !empty($xj_yj[0]['real_pay']) ? $xj_yj[0]['real_pay'] : 0,
            'cika_xh_yj' => !empty($cika_xh_yj['real_pay']) ? $cika_xh_yj['real_pay'] : 0,
            'kk_yj' => !empty($kk_yj['real_pay']) ? $kk_yj['real_pay'] : 0,
            'hk_reverse_yj' => !empty($hk_reverse_yj['real_pay']) ? $hk_reverse_yj['real_pay'] : 0,
            'xj_reverse_yj' => !empty($xj_reverse_yj['real_pay']) ? $xj_reverse_yj['real_pay'] : 0,
            'cika_xh_reverse_yj' => !empty($cika_xh_reverse_yj['real_pay']) ? $cika_xh_reverse_yj['real_pay'] : 0,
        ];

    }


    /***
     * @param $sdate
     * @param $did
     * @return float|int|mixed
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * 门店总业绩
     */
    public function md_zyj($sdate, $did)
    {
        //收入 -
        Db::name("store_bill");

        $store_bill = DB::name("store_bill")->where([
            'sdate_date' => $sdate, 'did' => $did
        ])->find();

        $types = Db::name("type")->where([
            'pid' => 1
        ])->select();
        $income = 0;

//        if (empty($store_bill['in_tg']) || $store_bill['in_tg'] == '0.00') {
//            return $income;
//        }
        if (empty($store_bill)){
            return 0;
        }

        foreach ($types as $tk => $tv) {

            if (!isset($store_bill[$tv['title']])){
                continue;
            }

            if ($tv['title'] == 'in_tktk' || $tv['title'] == 'in_sxf') {
                $income -= abs($store_bill[$tv['title']]);
            } else if ($tv['title'] == 'in_kdzz') {
                continue;
            } else {
                $income += $store_bill[$tv['title']];
            }
        }

        return round($income, 2);
    }

    public function staff_target($sdate, $aid, $did)
    {
        //查询 是否有调离记录 根据当前门店的调离日期 确定指标
        $diffDays = Db::name("store_business")->where([
            ['sdate', 'between', ["{$sdate}-01", getLastMonth($sdate)]],
            'aid' => $aid,
            'did' => $did,
            'status' => 1,
        ])->count();

        //var_dump($diffDays);
        //$inauguration_type = 0;
//        if (!empty($store_transfer)) {
////            $date1 = new DateTime($store_transfer['transfer_date']);
////            $date2 = new DateTime(getLastMonth($sdate));
////            $interval = $date1->diff($date2);
////            $diffDays = $interval->days;
//            if ($diffDays >= 20) {
//                $inauguration_type = 0;
//            } else if ($diffDays >= 15) {
//                $inauguration_type = 1;
//            } else {
//                $inauguration_type = 2;
//            }
//        }

        if ( strtotime(date("Y-m-d")) < strtotime(date("Y-m-21" , strtotime($sdate)) ) ){

            if ($diffDays + 1 >= intval(date("d" , strtotime($sdate) ))){
                return 0;
            }
        }

        if ($diffDays >= 20) {
            $inauguration_type = 0;
        } else if ($diffDays >= 15) {
            $inauguration_type = 1;
        } else {
            $inauguration_type = 2;
        }

        //金谊广场 2025-06 按照半个月指标计算
        if (  $sdate == '2025-06' && $did == 107){
            $inauguration_type = 1;
        }

        return $inauguration_type;
    }


    public function updateStore($param)
    {
        $data = [];

        if (isset($param['target_amount'])) {
            if (empty($param['target_amount'])) {
                $data['target_amount'] = 0;
            } else {
                $data['target_amount'] = $param['target_amount'];
            }
        }
        if (isset($param['rstatus'])) {
            $data['rstatus'] = $param['rstatus'];
        }
        if (isset($param['nstatus'])) {
            $data['nstatus'] = $param['nstatus'];
        }
        if (isset($param['aid'])) {
            if (empty($param['aid'])) {
                $data['aid'] = 0;
                $data['aname'] = '';
            } else {
                $data['aid'] = $param['aid'];
                $data['aname'] = $param['aname'];
            }
        }

        $store = DB::name("store")->where(['id' => $param['id']])->find();

        if (isset($param['grade']) && $param['grade'] != $store['grade']) {
            $store_grade = DB::name("store_grade")->where(['grade' => $param['grade'], 'sdate' => $store['sdate']])->find();
            $data['grade'] = $store_grade['grade'];
            $data['grade_amount'] = $store_grade['grade_amount'];
            $data['ratio'] = $store_grade['ratio'];

            $grade_amount = explode(",", $store_grade['grade_amount']);
            $data['target_amount'] = round($grade_amount[1] * $store['ratio'], 2);

        }

        if ( isset($param['leader_id']) ){
            DB::name("department")->where(['id' => $store['did']])->update(['leader_id' => $param['leader_id']]);
        }

        $data['rent_amount'] = !empty($param['rent_amount']) ? $param['rent_amount'] : 0;
        $data['wuye_amount'] = !empty($param['wuye_amount']) ? $param['wuye_amount'] : 0;
        $data['sushe_amount'] = !empty($param['sushe_amount']) ? $param['sushe_amount'] : 0;
        $data['ratio'] = !empty($param['ratio']) ? $param['ratio'] : 1;
        $data['amount'] = $data['rent_amount'] + $data['wuye_amount'];

        if (isset($param['holder_id'])){
            $data['holder_id'] = $param['holder_id'];
        }

        $re = DB::name("store")->where(['id' => $param['id']])->update($data);

    }

}

