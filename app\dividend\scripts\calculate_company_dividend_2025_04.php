<?php
/**
 * 公司股东分红表计算脚本 - 2025年4月
 * 
 * 使用方法：
 * php calculate_company_dividend_2025_04.php
 */

// 引入ThinkPHP框架
require_once __DIR__ . '/../../../vendor/autoload.php';

use think\facade\Db;
use think\facade\Config;
use app\dividend\service\DividendCompanyCalculateService;

// 初始化应用
$app = new think\App();
$app->initialize();

// 设置数据库配置（如果需要）
// 这里假设使用默认配置，如果需要特殊配置可以在这里设置

echo "=== 公司股东分红表计算脚本 ===\n";
echo "计算周期：2025年4月\n";
echo "开始时间：" . date('Y-m-d H:i:s') . "\n";
echo "================================\n\n";

try {
    // 执行计算
    $period = '2025-04';
    $result = DividendCompanyCalculateService::calculateCompanyDividend($period);
    
    if ($result['success']) {
        echo "✅ 计算成功！\n";
        echo "消息：{$result['message']}\n\n";
        
        if (!empty($result['data'])) {
            echo "📊 计算结果详情：\n";
            echo str_repeat('-', 100) . "\n";
            printf("%-15s %-20s %-12s %-12s %-8s\n", 
                '门店ID', '门店名称', '分红利润(元)', '公司持股比例', '分红人数');
            echo str_repeat('-', 100) . "\n";
            
            foreach ($result['data'] as $item) {
                printf("%-15s %-20s %-12s %-12s %-8s\n",
                    $item['store_id'],
                    mb_substr($item['store_name'], 0, 18, 'UTF-8'),
                    number_format($item['dividend_profit'], 2),
                    number_format($item['company_shareholding_ratio'], 2) . '%',
                    $item['person_count']
                );
            }
            echo str_repeat('-', 100) . "\n";
        }
        
        // 获取统计信息
        $statistics = DividendCompanyCalculateService::getCalculateStatistics($period);
        echo "\n📈 汇总统计：\n";
        echo "门店数量：{$statistics['store_count']} 个\n";
        echo "总分红利润：" . number_format($statistics['total_dividend_profit'], 2) . " 元\n";
        
    } else {
        echo "❌ 计算失败！\n";
        echo "错误信息：{$result['message']}\n";
    }
    
} catch (\Exception $e) {
    echo "❌ 脚本执行异常！\n";
    echo "异常信息：" . $e->getMessage() . "\n";
    echo "异常文件：" . $e->getFile() . " 第 " . $e->getLine() . " 行\n";
}

echo "\n================================\n";
echo "结束时间：" . date('Y-m-d H:i:s') . "\n";
echo "=== 公司股东分红表计算完成 ===\n";
