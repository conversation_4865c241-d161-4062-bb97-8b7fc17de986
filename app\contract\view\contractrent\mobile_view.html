{extend name="../../base/view/common/base" /}

{block name="style"}
<style>
/* 移动端合同详情页面样式 - 参考合同选择页面的卡片式设计 */
* {
    box-sizing: border-box;
}

html, body {
    background-color: #f5f5f5;
    margin: 0;
    padding: 0;
    overflow-x: hidden;
    width: 100%;
    max-width: 100%;
}

/* 头部样式 - 参考合同选择页面 */
.mobile-header {
    background: #fff;
    padding: 16px 20px;
    border-bottom: 1px solid #e6e6e6;
    position: sticky;
    top: 0;
    z-index: 100;
    margin: -16px -16px 16px -16px;
}

.mobile-header h1 {
    margin: 0;
    font-size: 18px;
    font-weight: 500;
    color: #333;
    text-align: center;
}

.back-btn {
    position: absolute;
    left: 20px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    font-size: 16px;
    color: #666;
    cursor: pointer;
    padding: 10px;
    min-width: 40px;
    min-height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.back-btn:hover {
    background: #f5f5f5;
    border-radius: 4px;
}

/* 合同编号展示卡片 */
.contract-code-card {
    background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
    border-radius: 12px;
    margin: 30px 20px 16px 20px;
    padding: 20px;
    color: #fff;
    box-shadow: 0 4px 12px rgba(24, 144, 255, 0.15);
}

.contract-code-title {
    font-size: 14px;
    opacity: 0.9;
    margin-bottom: 8px;
}

.contract-code-value {
    font-size: 20px;
    font-weight: 600;
    letter-spacing: 1px;
}

/* 信息卡片样式 - 参考合同选择页面 */
.info-card {
    background: #fff;
    border-radius: 12px;
    margin: 0 20px 12px 20px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.08);
    overflow: hidden;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.info-card-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    padding: 16px 20px;
    border-bottom: 1px solid #e6e6e6;
}

.info-card-title {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: #333;
    display: flex;
    align-items: center;
    gap: 8px;
}

.info-card-title::before {
    content: '';
    width: 4px;
    height: 16px;
    background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
    border-radius: 2px;
}

.info-card-content {
    padding: 0;
}

/* 信息行样式 */
.info-row {
    display: flex;
    padding: 16px 20px;
    border-bottom: 1px solid #f0f0f0;
    align-items: flex-start;
    min-height: 56px;
}

.info-row:last-child {
    border-bottom: none;
}

.info-label {
    color: #666;
    font-size: 14px;
    font-weight: 500;
    width: 80px;
    flex-shrink: 0;
    line-height: 1.5;
    padding-top: 2px;
}

.info-value {
    color: #333;
    font-size: 14px;
    flex: 1;
    line-height: 1.5;
    word-break: break-all;
    padding-left: 12px;
}

/* 金额显示样式 */
.amount-row .info-value {
    color: #ff4d4f;
    font-weight: 600;
    font-size: 16px;
}

/* 备注信息特殊样式 */
.remark-content {
    padding: 20px;
    background: #fafafa;
    border-radius: 8px;
    margin: 16px 20px;
    color: #333;
    font-size: 14px;
    line-height: 1.6;
    word-break: break-all;
    white-space: pre-wrap;
}

/* 移动端优化 */
@media (max-width: 768px) {
    body {
        background: #f5f5f5;
        font-size: 14px;
    }

    .layui-form {
        padding: 0;
        background: #f5f5f5;
        width: 100%;
        max-width: 100%;
        overflow-x: hidden;
    }

    .mobile-header {
        padding: 14px 16px;
    }

    .mobile-header h1 {
        font-size: 16px;
    }

    .contract-code-card {
        margin: 32px 16px 12px 16px;
        padding: 16px;
    }

    .contract-code-value {
        font-size: 18px;
    }

    .info-card {
        margin: 0 16px 10px 16px;
    }

    .info-card-header {
        padding: 12px 16px;
    }

    .info-card-title {
        font-size: 15px;
    }

    .info-row {
        padding: 12px 16px;
        min-height: 48px;
    }

    .info-label {
        width: 70px;
        font-size: 13px;
    }

    .info-value {
        font-size: 13px;
        padding-left: 10px;
    }

    .amount-row .info-value {
        font-size: 15px;
    }

    .remark-content {
        margin: 12px 16px;
        padding: 16px;
        font-size: 13px;
    }
}

/* 超小屏幕优化 */
@media (max-width: 480px) {
    .mobile-header {
        padding: 12px 14px;
    }

    .mobile-header h1 {
        font-size: 15px;
    }

    .back-btn {
        left: 14px;
        font-size: 14px;
    }

    .contract-code-card {
        margin: 24px 14px 10px 14px;
        padding: 14px;
    }

    .contract-code-title {
        font-size: 13px;
    }

    .contract-code-value {
        font-size: 16px;
    }

    .info-card {
        margin: 0 14px 8px 14px;
    }

    .info-card-header {
        padding: 10px 14px;
    }

    .info-card-title {
        font-size: 14px;
    }

    .info-row {
        padding: 10px 14px;
        min-height: 44px;
    }

    .info-label {
        width: 65px;
        font-size: 12px;
    }

    .info-value {
        font-size: 12px;
        padding-left: 8px;
    }

    .amount-row .info-value {
        font-size: 14px;
    }

    .remark-content {
        margin: 10px 14px;
        padding: 14px;
        font-size: 12px;
    }
}
</style>
{/block}

{block name="body"}
<div class="layui-form">
    <!-- 移动端头部 - 参考合同选择页面样式 -->
    <div class="mobile-header">
        <button class="back-btn" onclick="goBack()">← 返回</button>
        <h1>合同详情</h1>
    </div>

    <!-- 合同编号卡片 -->
    <div class="contract-code-card">
        <div class="contract-code-title">合同编号</div>
        <div class="contract-code-value">{$contract_rent.code|default='未设置'}</div>
    </div>

    <!-- 甲方信息卡片 -->
    <div class="info-card">
        <div class="info-card-header">
            <h3 class="info-card-title">甲方信息</h3>
        </div>
        <div class="info-card-content">
            <div class="info-row">
                <div class="info-label">公司名称</div>
                <div class="info-value">{$contract_rent.party_a_name|default='未设置'}</div>
            </div>
            <div class="info-row">
                <div class="info-label">联系人</div>
                <div class="info-value">{$contract_rent.party_a_person|default='未设置'}</div>
            </div>
            <div class="info-row">
                <div class="info-label">联系电话</div>
                <div class="info-value">{$contract_rent.party_a_mobile|default='未设置'}</div>
            </div>
            <div class="info-row">
                <div class="info-label">地址</div>
                <div class="info-value">{$contract_rent.party_a_add|default='未设置'}</div>
            </div>
        </div>
    </div>

    <!-- 乙方信息卡片 -->
    <div class="info-card">
        <div class="info-card-header">
            <h3 class="info-card-title">乙方信息</h3>
        </div>
        <div class="info-card-content">
            <div class="info-row">
                <div class="info-label">公司名称</div>
                <div class="info-value">{$contract_rent.party_b_name|default='未设置'}</div>
            </div>
            <div class="info-row">
                <div class="info-label">联系人</div>
                <div class="info-value">{$contract_rent.party_b_person|default='未设置'}</div>
            </div>
            <div class="info-row">
                <div class="info-label">联系电话</div>
                <div class="info-value">{$contract_rent.party_b_mobile|default='未设置'}</div>
            </div>
            <div class="info-row">
                <div class="info-label">地址</div>
                <div class="info-value">{$contract_rent.party_b_add|default='未设置'}</div>
            </div>
        </div>
    </div>

    <!-- 门店信息卡片 -->
    <div class="info-card">
        <div class="info-card-header">
            <h3 class="info-card-title">门店信息</h3>
        </div>
        <div class="info-card-content">
            <div class="info-row">
                <div class="info-label">门店名称</div>
                <div class="info-value">{$contract_rent.dname|default='未设置'}</div>
            </div>
            <div class="info-row">
                <div class="info-label">门店地址</div>
                <div class="info-value">{$contract_rent.address|default='未设置'}</div>
            </div>
            <div class="info-row">
                <div class="info-label">店长姓名</div>
                <div class="info-value">{$contract_rent.dz_name|default='未设置'}</div>
            </div>
            <div class="info-row">
                <div class="info-label">店长电话</div>
                <div class="info-value">{$contract_rent.dz_mobile|default='未设置'}</div>
            </div>
        </div>
    </div>

    <!-- 基本信息卡片 -->
    <div class="info-card">
        <div class="info-card-header">
            <h3 class="info-card-title">基本信息</h3>
        </div>
        <div class="info-card-content">
            <div class="info-row">
                <div class="info-label">合同类型</div>
                <div class="info-value">{$contract_rent.cate_name|default='未设置'}</div>
            </div>
            <div class="info-row">
                <div class="info-label">开始日期</div>
                <div class="info-value">{$contract_rent.start_date|default='未设置'}</div>
            </div>
            <div class="info-row">
                <div class="info-label">结束日期</div>
                <div class="info-value">{$contract_rent.end_date|default='未设置'}</div>
            </div>
            <div class="info-row amount-row">
                <div class="info-label">月租金</div>
                <div class="info-value">¥{$contract_rent.rent_amount|default='0'}</div>
            </div>
            <div class="info-row amount-row">
                <div class="info-label">押金</div>
                <div class="info-value">¥{$contract_rent.deposit_amount|default='0'}</div>
            </div>
            <div class="info-row amount-row">
                <div class="info-label">物业费</div>
                <div class="info-value">¥{$contract_rent.wyf_amount|default='0'}</div>
            </div>
            <div class="info-row">
                <div class="info-label">付款周期</div>
                <div class="info-value">{$contract_rent.pay_cycle|default='未设置'}个月</div>
            </div>
            <div class="info-row">
                <div class="info-label">付款日期</div>
                <div class="info-value">每月{$contract_rent.pay_date|default='未设置'}号</div>
            </div>
        </div>
    </div>

    <!-- 账户信息卡片 -->
    {if condition="!empty($contract_rent.account_name)"}
    <div class="info-card">
        <div class="info-card-header">
            <h3 class="info-card-title">收款账户信息</h3>
        </div>
        <div class="info-card-content">
            <div class="info-row">
                <div class="info-label">账户名</div>
                <div class="info-value">{$contract_rent.account_name}</div>
            </div>
            <div class="info-row">
                <div class="info-label">账号</div>
                <div class="info-value">{$contract_rent.account_number}</div>
            </div>
            <div class="info-row">
                <div class="info-label">开户行</div>
                <div class="info-value">{$contract_rent.account_bank}</div>
            </div>
        </div>
    </div>
    {/if}

    <!-- 备注信息 -->
    {if condition="!empty($contract_rent.remark)"}
    <div class="info-card">
        <div class="info-card-header">
            <h3 class="info-card-title">备注信息</h3>
        </div>
        <div class="remark-content">{$contract_rent.remark}</div>
    </div>
    {/if}
</div>
{/block}

{block name="script"}
<script>
function gouguInit() {
    // 返回按钮功能
    window.goBack = function() {
        // 直接使用浏览器回退功能
        window.history.back();
    };

    // 移动端优化：防止页面缩放
    var viewport = document.querySelector('meta[name="viewport"]');
    if (!viewport) {
        viewport = document.createElement('meta');
        viewport.name = 'viewport';
        viewport.content = 'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no';
        document.head.appendChild(viewport);
    }
}
</script>
{/block}
