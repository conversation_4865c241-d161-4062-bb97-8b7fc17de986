{extend name="../../base/view/common/base" /}
<!-- 主体 -->
{block name="body"}
<style>
	/*
     * 基于复选框和单选框的卡片风格多选组件
     * 需要具备一些基础的 CSS 技能，以下样式均为外部自主实现。
     */
	/* 主体 */
	.layui-form-checkbox>.lay-skin-checkcard,
	.layui-form-radio>.lay-skin-checkcard {
		display: table;
		display: flex;
		padding: 12px;
		white-space: normal;
		border-radius: 10px;
		border: 1px solid #e5e5e5;
		color: #000;
		background-color: #fff;
	}
	.layui-form-checkbox>.lay-skin-checkcard>*,
	.layui-form-radio>.lay-skin-checkcard>* {
		/* display: table-cell; */  /* IE */
		vertical-align: top;
	}
	/* 悬停 */
	.layui-form-checkbox:hover>.lay-skin-checkcard,
	.layui-form-radio:hover>.lay-skin-checkcard {
		border-color: #16b777;
	}
	/* 选中 */
	.layui-form-checked>.lay-skin-checkcard,
	.layui-form-radioed[lay-skin="none"]>.lay-skin-checkcard {
		color: #000;
		border-color: #16b777;
		background-color: rgb(22 183 119 / 10%) !important;
		/* box-shadow: 0 0 0 3px rgba(22, 183, 119, 0.08); */
	}
	/* 禁用 */
	.layui-checkbox-disabled>.lay-skin-checkcard,
	.layui-radio-disabled>.lay-skin-checkcard {
		box-shadow: none;
		border-color: #e5e5e5 !important;
		background-color: #eee !important;
	}
	/* card 布局 */
	.lay-skin-checkcard-avatar {
		padding-right: 8px;
	}
	.lay-skin-checkcard-detail {
		overflow: hidden;
		width: 100%;
	}
	.lay-skin-checkcard-header {
		font-weight: 500;
		font-size: 16px;
		white-space: nowrap;
		margin-bottom: 4px;
	}
	.lay-skin-checkcard-description {
		font-size: 13px;
		color: #5f5f5f;
	}
	.layui-disabled  .lay-skin-checkcard-description{
		color: #c2c2c2! important;
	}
	/* 选中 dot */
	.layui-form-checked>.lay-check-dot:after,
	.layui-form-radioed>.lay-check-dot:after {
		position: absolute;
		content: "";
		top: 2px;
		right: 2px;
		width: 0;
		height: 0;
		display: inline-block;
		vertical-align: middle;
		border-width: 10px;
		border-style: dashed;
		border-color: transparent;
		border-top-left-radius: 0px;
		border-top-right-radius: 6px;
		border-bottom-right-radius: 0px;
		border-bottom-left-radius: 6px;
		border-top-color: #16b777;
		border-top-style: solid;
		border-right-color: #16b777;
		border-right-style: solid;
		overflow: hidden;
	}
	.layui-checkbox-disabled>.lay-check-dot:after,
	.layui-radio-disabled>.lay-check-dot:after {
		border-top-color: #d2d2d2;
		border-right-color: #d2d2d2;
	}
	/* 选中 dot-2 */
	.layui-form-checked>.lay-check-dot-2:before,
	.layui-form-radioed>.lay-check-dot-2:before {
		position: absolute;
		font-family: "layui-icon";
		content: "\e605";
		color: #fff;
		bottom: 4px;
		right: 3px;
		font-size: 9px;
		z-index: 12;
	}
	.layui-form-checked>.lay-check-dot-2:after,
	.layui-form-radioed>.lay-check-dot-2:after {
		position: absolute;
		content: "";
		bottom: 2px;
		right: 2px;
		width: 0;
		height: 0;
		display: inline-block;
		vertical-align: middle;
		border-width: 10px;
		border-style: dashed;
		border-color: transparent;
		border-top-left-radius: 6px;
		border-top-right-radius: 0px;
		border-bottom-right-radius: 6px;
		border-bottom-left-radius: 0px;
		border-right-color: #16b777;
		border-right-style: solid;
		border-bottom-color: #16b777;
		border-bottom-style: solid;
		overflow: hidden;
	}
	.layui-checkbox-disabled>.lay-check-dot-2:before,
	.layui-radio-disabled>.lay-check-dot-2:before {
		color: #eee !important;
	}
	.layui-checkbox-disabled>.lay-check-dot-2:after,
	.layui-radio-disabled>.lay-check-dot-2:after {
		border-bottom-color: #d2d2d2;
		border-right-color: #d2d2d2;
	}
	.lay-ellipsis-multi-line {
		overflow: hidden;
		word-break: break-all;
		display: -webkit-box;
		-webkit-box-orient: vertical;
		-webkit-line-clamp: 3;
	}
</style>
<style>
	.layui-form-radio>.lay-skin-tag,
	.layui-form-checkbox>.lay-skin-tag {
		font-size: 13px;
		border-radius: 100px;
	}
	.layui-form-checked>.lay-skin-tag,
	.layui-form-radioed>.lay-skin-tag {
		color: #fff !important;
		background-color: #16b777 !important;
	}
	/* 主体 */
	.layui-form-radio>.lay-skin-color-picker {
		border-radius: 50%;
		border-width: 1px;
		border-style: solid;
		width: 20px;
		height: 20px;
	}
	/* 选中 */
	.layui-form-radioed>.lay-skin-color-picker {
		box-shadow: 0 0 0 1px #ffffff, 0 0 0 4px currentColor;
	}
	font{
		color: #FF6347;
		margin-left: 2px;
	}

	.input-content input,.input-content div{
		flex: 1;
	}

	.pb-3{
		background-color: #fff;
		padding: 10px;
	}

	.layui-td-gray{
		width: 50px;
		background-color: #fff;
	}

	.layui-card-body-content{
		border-top: 1px solid #f8f8f8;
	}

	.layui-card-body-fraction{
		border-top: 1px solid #f8f8f8;
		display: flex;
		justify-content: center;
		align-items: center;
	}

	.layui-card-body-fraction .layui-input-wrap{
		flex: 1;
	}

	.layui-bg-gray{
		background-color: #fff !important;
	}

	.layui-card-header{
		font-size: 18px;
	}

	.w-200{
		width: 100px;
	}

	.w-80{
		width: 100px;
	}

	.s-color{
		color: #0000cc;
	}

	.score{
		font-weight: bolder;
		color: #0a001f;
	}

</style>
<form class="layui-form p-12">
	<h3 class="pb-3">考核记录表</h3>
	<div class="layui-tabs layui-tabs-card layui-panel">
		<ul class="layui-tabs-header layui-bg-tint">
			{foreach name="$examine" item="a"}
			<li >{$a.start_sdate} 【{$a.dname}】</li>
			{/foreach}
		</ul>

		<div class="layui-tabs-body">
			{foreach name="$examine" key="k" item="a"}

			<div class="layui-tabs-item">

				<h3>
					考核结果：
					{if condition="($a.check_status == '1')"}
					<span style="color: green">通过</span>
					{else/}
					<span style="color: red">不通过</span>
					{/if}
				</h3>

				<table class="layui-table layui-table-form">

					<tr>
						<td class="layui-td-gray w-200">姓名</td>
						<td >{$a['a_view_name']}</td>
					</tr>
					<tr>
						<td class="layui-td-gray w-200">性别</td>
						<td>
							{if condition="($a['sex'] == 1)"}
							男
							{else/}
							女
							{/if}
						</td>
					</tr>
					<tr>
						<td class="layui-td-gray w-200">开始学习时间<br>（到店时间）</td>
						<td >{$a['start_sdate']}</td>
					</tr>
					<tr>
						<td class="layui-td-gray w-200">所属门店</td>
						<td>{$a['dname']}</td>
					</tr>
					<tr>
						<td class="layui-td-gray w-200">门店店长<br>（带教责任人）</td>
						<td>{$a['aname']}</td>
					</tr>
				</table>

				<table class="layui-table layui-table-form">
					<tr >
						<td class="layui-td-gray " colspan="3" style="text-align: left;color: #000"><h2>线上理论</h2></td>
					</tr>
					<tr>
						<td class="layui-td-gray w-80">视频课件完成情况</td>
						<td>{$a['courseware_status']}</td>
					</tr>
					<tr>
						<td class="layui-td-gray w-80">理论考核成绩</td>
						<td>{$a['courseware_score']}</td>
					</tr>
					<tr>
						<td class="layui-td-gray w-80">理论考试次数</td>
						<td>{$a['courseware_times']}</td>
					</tr>
				</table>

				<h2 style="margin: 10px 0">线下实操</h2>
				<div class="layui-tabs layui-tabs-card" lay-options="{index: 0}">
					<ul class="layui-tabs-header">
						<li>关元灸</li>
						<li>刮痧</li>
						<li>拔罐</li>
						<li>推拿</li>
					</ul>
					<div class="layui-tabs-body" style="padding: 0">
						<div class="layui-tabs-item">
							<h3 class="details">关元灸实操考核（25分）</h3>
							<table class="layui-table layui-table-form">
								<tr>
									<td style="width: 60px">考核项</td>
									<td>考核重点</td>
									<td>得分</td>
								</tr>

								<tr>
									<td rowspan="2">铺姜（10分）</td>
									<td >厚度、距离（5分） </td>
									<td class="score">{$a['score0'][0]} </td>
								</tr>
								<tr>
									<td >整洁度、时间把控（5分）</td>
									<td class="score">{$a['score0'][1]}</td>
								</tr>
								<tr>
									<td rowspan="3">施灸流程（15分）</td>
									<td >灸前（5分）</td>
									<td class="score">{$a['score0'][2]}</td>
								</tr>
								<tr>
									<td>灸中（5分）</td>
									<td class="score">{$a['score0'][3]}</td>
								</tr>

								<tr>
									<td>灸后（5分）</td>
									<td class="score">{$a['score0'][4]}</td>
								</tr>
								<tr>
									<td>平均分</td>
									<td colspan="2" class="score">{$a['average0']}</td>
								</tr>
							</table>
						</div>
						<div class="layui-tabs-item">
							<h3>刮痧实操考核（15分）</h3>
							<table class="layui-table layui-table-form">
								<tr>
									<td>考核重点</td>
									<td>得分</td>
								</tr>

								<tr>
									<td>刮痧前检查准备（5分）</td>
									<td class="score">{$a['score1'][0]}</td>
								</tr>
								<tr>
									<td >刮痧中的操作事项（5分）（痛感、位置）</td>
									<td class="score">{$a['score1'][1]}</td>
								</tr>
								<tr>
									<td >刮痧后处理（5分）</td>
									<td class="score">{$a['score1'][2]}</td>
								</tr>
								<tr>
									<td>平均分</td>
									<td class="score">{$a['average1']}</td>
								</tr>
							</table>
						</div>
						<div class="layui-tabs-item">
							<h3>拔罐实操考核（15分）</h3>
							<table class="layui-table layui-table-form">
								<tr>
									<td>考核重点</td>
									<td>得分</td>
								</tr>

								<tr>
									<td>拔罐前检查准备（5分）</td>
									<td class="score">{$a['score2'][0]}</td>
								</tr>

								<tr>
									<td >拔罐中的操作事项（5分）</td>
									<td class="score">{$a['score2'][1]}</td>
								</tr>

								<tr>
									<td >拔罐后处理（5分）</td>
									<td class="score">{$a['score2'][2]}</td>
								</tr>

								<tr>
									<td>平均分</td>
									<td class="score">{$a['average2']}</td>
								</tr>

							</table>
						</div>
						<div class="layui-tabs-item">
							<h3>推拿实操考核（30分）</h3>
							<table class="layui-table layui-table-form">
								<tr>
									<td style="width: 60px">考核项</td>
									<td>考核重点</td>
									<td>得分</td>
								</tr>

								<tr>
									<td rowspan="3">肩颈背（15分）</td>
									<td >位置的精准度（5分）</td>
									<td class="score">{$a['score3'][0]}</td>

								</tr>
								<tr>
									<td >手法力度（5分）</td>
									<td class="score">{$a['score3'][1]}</td>
								</tr>

								<tr>
									<td >老师姿势及发力方向（5分）</td>
									<td class="score">{$a['score3'][2]}</td>
								</tr>

								<tr>
									<td rowspan="3">腰臀腿（15分）</td>
									<td >位置的精准度（5分）</td>
									<td class="score">{$a['score3'][3]}</td>
								</tr>

								<tr>
									<td>手法力度（5分）</td>
									<td class="score">{$a['score3'][4]}</td>
								</tr>

								<tr>
									<td>老师姿势及发力方向（5分）</td>
									<td class="score">{$a['score3'][5]}</td>
								</tr>
								<tr>
									<td>平均分</td>
									<td colspan="2" class="score">{$a['average3']}</td>
								</tr>
							</table>
						</div>
					</div>
				</div>

				<table class="layui-table layui-table-form">
					<tr>
						<td class="layui-td-gray s-color">总平均分</td>
						<td class="layui-td-gray">
							{$a['total_score']}
						</td>
					</tr>
					<tr>
						<td class="layui-td-gray s-color">实操考试时间</td>
						<td class="layui-td-gray">
							{$a['exam_sdate']}
						</td>
					</tr>
					<tr>
						<td class="layui-td-gray s-color">实操考官</td>
						<td class="layui-td-gray">
							{$a['exam_name']}
						</td>
					</tr>
					<tr>
						<td class="layui-td-gray">考核点评</td>
						<td class="layui-td-gray" colspan="2">
							{$a['remark']}
						</td>
					</tr>
					<tr>
						<td class="layui-td-gray">现场照片</td>
						<td class="layui-td-gray">
							{volist name="$a['file_array']" id="vo"}
							<img src="{$vo.filepath}" data-href="{$vo.filepath}" alt="{vo.name}" class="file-view-img">
							{/volist}
						</td>
					</tr>
				</table>
			</div>
			{/foreach}
		</div>



	</div>
</form>
{/block}
<!-- /主体 -->

<!-- 脚本 -->
{block name="script"}
<script>
	const moduleInit = ['tool','oaTool'];
	function gouguInit() {
		var tabs = layui.tabs;

		$('body').on('click', '.file-view-img', function () {

			var data = []

			$('.file-view-img').map(function() {
				data.push({
					"alt": i,
					"pid": i,
					"src": $(this).attr('data-href'),
				})
			});

			if (data) {
				layer.photos({
					photos: {
						"title": "Photos Demo",
						"start": 0,
						"data": data
					},
					footer: true // 是否显示底部栏 --- 2.8.16+
				});
			}


		});


		// 方法渲染
		tabs.render({
			elem: '#demoTabs2',
			header: [
				{ title: 'Tab1' },
				{ title: 'Tab2' },
				{ title: 'Tab3' }
			],
			body: [
				{ content: 'Tab content 1' },
				{ content: 'Tab content 2' },
				{ content: 'Tab content 3' }
			],




			// index: 1, // 初始选中项
			// className: 'layui-tabs-card',
			// closable: true
		});

	}

</script>
{/block}
<!-- /脚本 -->