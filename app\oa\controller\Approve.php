<?php
/**
 * @copyright Copyright (c) 2021 仲正堂工作室
 * @license https://opensource.org/licenses/GPL-3.0
 * @link https://www.gougucms.com
 */

declare (strict_types=1);

namespace app\oa\controller;

use app\base\BaseController;
use app\message\service\QiWeiService;
use app\oa\service\ApproveService;
use app\user\model\DepartmentChange;
use app\user\service\AdminService;
use think\facade\Db;
use think\facade\Request;
use think\facade\Session;
use think\facade\View;
use think\facade\Log;
use think\facade\Config;
use systematic\ConfigManager;

class Approve extends BaseController
{
    /**
     * 支持移动端的审批流程列表
     * 在此数组中配置的流程将在移动设备上使用专用的移动端模板
     * 配置流程表示数组，如：'mdssgh'
     */
    private $mobile_supported_flows = [
    ];

    /**
     * 检测是否为移动设备
     * @return bool
     */
    private function isMobileDevice()
    {
        // 方法1：从Session中获取移动端标识
        $is_mobile_session = Session::get('is_mobile');

        // 方法2：基于User-Agent检测
        $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? '';
        $is_mobile_ua = preg_match('/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i', $user_agent);

        // 使用OR逻辑：任一方式判断为移动端即认为是移动端
        return !empty($is_mobile_session) || $is_mobile_ua;
    }

    /**
     * 获取模板路径（支持移动端适配）
     * @param string $flow_name 流程名称
     * @param string $action 操作类型（add/view）
     * @return string
     */
    private function getTemplatePath($flow_name, $action = 'add')
    {
        $module = strtolower(app('http')->getName());
        $class = strtolower(app('request')->controller());

        // 检查是否为移动设备且流程支持移动端
        if ($this->isMobileDevice() && in_array($flow_name, $this->mobile_supported_flows)) {
            $mobile_template = $module . '/view/' . $class . '/' . $action . '_' . $flow_name . '_mobile.html';

            // 检查移动端模板是否存在
            if (isTemplate($mobile_template)) {
                return $mobile_template;
            }
        }

        // 返回默认桌面端模板
        return $module . '/view/' . $class . '/' . $action . '_' . $flow_name . '.html';
    }

    public function index()
    {
        $is_mobile = Session::get("is_mobile");

        if (request()->isAjax()) {
            $param = get_params();
            $where = [];
            if (isset($param['status'])) {
                if ($param['status'] == 1) {
                    $where[] = ['f.check_status', '<', 2];
                }
                if ($param['status'] == 2) {
                    $where[] = ['f.check_status', '=', 2];
                }
                if ($param['status'] == 3) {
                    $where[] = ['f.check_status', '>', 2];
                }
            }

            if (isset($param['type']) && !empty($param['type']) && empty($param['flow_cate']) ) {
                $ids = Db::name("flow")->where(['type' => $param['type'] , 'status' => 1 ])->column(['id']);
                $where[] = ['f.type', 'in', $ids];
            }

            if (isset($param['flow_cate']) && !empty($param['flow_cate'])) {
                $where[] = ['f.type', '=', $param['flow_cate']];
            }

            if (isset($param['sdate']) && !empty($param['sdate'])) {
                $sdate = explode(" - ",$param['sdate']);
                $where[] = ['f.create_time', 'between', [strtotime($sdate[0]) , strtotime($sdate[1])] ];
            }

            if (isset($param['did']) && !empty($param['did'])) {
                $where[] = ['f.department_id', '=', $param['did'] ];
            }

            if (!empty($param['id'])){
                // 如果ID以"WX"开头，则从备注字段模糊搜索（物品维修流程编号）
                if (strpos($param['id'], 'WX') === 0) {
                    $where[] = ['f.remark', 'like', '%' . $param['id'] . '%'];
                } else {
                    $where[] = ['f.id', '=', $param['id']];
                }
            }

            if (isset($param['name']) && !empty($param['name'])) {
                $admin = Db::name("admin")->where([ ['name' , 'like' , "%{$param['name']}%"] ])->column('id');
                if (!empty($admin)){
                    $where[] = ['f.admin_id', 'in', $admin ];
                }
            }

            // 添加表单内容模糊搜索功能
            if (isset($param['form_content']) && !empty($param['form_content'])) {
                $where[] = ['f.jsoncode', 'like', '%' . $param['form_content'] . '%'];
            }

            //按时间检索
            if (!empty($param['finish_time'])) {
                $finish_time = explode(' - ', $param['finish_time']);
                $where[] = ['f.finish_time', 'between', [strtotime(urldecode($finish_time[0])), strtotime(urldecode($finish_time[1]))]];
            }


            if (
                (isset($param['status']) && $param['status'] == 4) ||
                ($this->uid !== 878 &&
                $this->uid !== 880 &&
                $this->uid !== 2202 &&
                $this->uid !== 2311 &&
                $this->uid !== 2357 &&
                $this->uid !== 2259 &&
                $this->uid !== 1)
            ){
                $where[] = ['f.admin_id', '=', $this->uid];
            }

            //区店 可以看到管理门店下的 宿舍 房租 物业审批
//            $DepartmentChangeService = new DepartmentChange();
//            $dep_fenguan = $DepartmentChangeService->getDirectlyDepartment($this->uid);
//            if (!empty($dep_fenguan)){
//                $where[] = ['f.department_id', 'in',$dep_fenguan];
//            }

            $rows = empty($param['limit']) ? get_config('app.page_size') : $param['limit'];
            $list = Db::name('Approve')
                ->field('f.*,a.name,d.title as department_name,t.title as flow_type')
                ->alias('f')
                ->join('Admin a', 'a.id = f.admin_id', 'left')
                ->join('Department d', 'd.id = f.department_id', 'left')
                ->join('FlowType t', 't.id = f.type', 'left')
                ->where($where)
                ->order('f.id desc')
                ->paginate(['list_rows' => $rows, 'query' => $param])
                ->each(function ($item, $key) {
                    $item['create_time'] = date('Y-m-d H:i', $item['create_time']);
                    $item['finish_time'] = empty($item['finish_time']) ? '' :  date('Y-m-d H:i', $item['finish_time']);
                    $item['check_user'] = '-';
                    if ($item['check_status'] < 2 && !empty($item['check_admin_ids'])) {
                        $check_user = Db::name('Admin')->where('id', 'in', $item['check_admin_ids'])->column('name');
                        $item['check_user'] = implode(',', $check_user);
                    }

                    $item['aname'] = '-';

                    if (!empty($item['jsoncode'])){
                        $jsoncode = unserialize($item['jsoncode']);
                        if (isset($jsoncode['did'])){
                            $item['department_name'] = Db::name("department")->where(['id'=>$jsoncode['did']])->value('title');
                        }
                        if (isset($jsoncode['aid'])){
                            $item['aname'] = Db::name("admin")->where(['id'=>$jsoncode['aid']])->value('name');
                        }


                    }

                    return $item;
                });

            // 获取催审标识数据
            $list_array = $list->toArray();
            $approve_ids = array_column($list_array['data'], 'id');
            $urge_counts = [];

            if (!empty($approve_ids)) {
                // 查询当前用户收到的催审消息次数
                $urge_data = Db::name('Message')
                    ->field('action_id, COUNT(id) as urge_count')
                    ->where([
                        ['action_name', '=', 'urge'],
                        ['module_name', '=', 'oa'],
                        ['controller_name', '=', 'approve'],
                        ['action_id', 'in', $approve_ids],
                        ['to_uid', '=', $this->uid]  // 关键：只统计发给当前用户的催审消息
                    ])
                    ->group('action_id')
                    ->select()
                    ->toArray();

                // 转换为以action_id为键的数组
                foreach ($urge_data as $item) {
                    $urge_counts[$item['action_id']] = $item['urge_count'];
                }
            }

            // 添加催审次数字段
            foreach ($list_array['data'] as $k => $v) {
                $list_array['data'][$k]['urge_count'] = isset($urge_counts[$v['id']]) ? $urge_counts[$v['id']] : 0;
            }

            return table_assign(0, '', $list_array);
        } else {
            $uid = $this->uid;
            $department = $this->did;
            if ($uid == 1) {
                $list = Db::name('FlowType')->where(['status' => 1])->select()->toArray();
            } else {
                $arr = array();
                $arr[] = ['status', '=', 1];

                $DepartmentChange = new DepartmentChange();
                $department = $DepartmentChange->getDirectlyDepartment($uid);

                $ordepartment = [['department_ids', '=', '']];
                $department_arr = $department;
                foreach ($department_arr as $k => $v) {
                    $ordepartment[] = [
                        ['', 'exp', Db::raw("FIND_IN_SET('{$v}',department_ids)")]
                    ];
                }

                $orposition = [['position_ids', '=', '']];
                $position_arr = explode(",", $this->pid);
                foreach ($position_arr as $k => $v) {
                    $orposition[] = [
                        ['', 'exp', Db::raw("FIND_IN_SET('{$v}',position_ids)")]
                    ];
                }

                $list = Db::name('FlowType')->where($arr)
                    ->where(function ($query) use ($ordepartment) {
                        $query->whereOr($ordepartment);
                    })
                    ->where(function ($query) use ($orposition) {
                        $query->whereOr($orposition);
                    })->select()->toArray();
            }

            View::assign('uid', $this->uid);
            View::assign('list', $list);
            View::assign('type', get_config('approve.type'));

            if (!empty($is_mobile)) {
                return View("../../mobile/view/index/index");
            }

            return view();
        }
    }

    public function list()
    {
        $is_mobile = Session::get("is_mobile");

        if (request()->isAjax()) {
            $param = get_params();
            $status = isset($param['status']) ? $param['status'] : 0;

            if ($status == 3) {
                $status = 2;
            }

            $user_id = $this->uid;
            //查询条件
            $map = [];
            $map1 = [];
            $map2 = [];
            $map1[] = ['', 'exp', Db::raw("FIND_IN_SET('{$user_id}',f.check_admin_ids)")];
            $map2[] = ['', 'exp', Db::raw("FIND_IN_SET('{$user_id}',f.flow_admin_ids)")];

            if (!empty($param['type'])) {
                $map1[] = ['f.type', '=', $param['type']];
                $map2[] = ['f.type', '=', $param['type']];
            }
            if (!empty($param['uid'])) {
                $map1[] = ['f.admin_id', '=', $param['uid']];
                $map2[] = ['f.admin_id', '=', $param['uid']];
            }
            //按时间检索
            if (!empty($param['apply_time'])) {
                $apply_time = explode('~', $param['apply_time']);
                $map1[] = ['f.create_time', 'between', [strtotime(urldecode($apply_time[0])), strtotime(urldecode($apply_time[1]))]];
                $map2[] = ['f.create_time', 'between', [strtotime(urldecode($apply_time[0])), strtotime(urldecode($apply_time[1]))]];
            }

            //按时间检索
            if (!empty($param['finish_time'])) {
                $finish_time = explode('~', $param['finish_time']);
                $map1[] = ['f.finish_time', 'between', [strtotime(urldecode($finish_time[0])), strtotime(urldecode($finish_time[1]))]];
                $map2[] = ['f.finish_time', 'between', [strtotime(urldecode($finish_time[0])), strtotime(urldecode($finish_time[1]))]];
            }

            if (!empty($param['id'])){
                // 如果ID以"WX"开头，则从备注字段模糊搜索（物品维修流程编号）
                if (strpos($param['id'], 'WX') === 0) {
                    $map1[] = ['f.remark', 'like', '%' . $param['id'] . '%'];
                    $map2[] = ['f.remark', 'like', '%' . $param['id'] . '%'];
                } else {
                    $map1[] = ['f.id', '=', $param['id']];
                    $map2[] = ['f.id', '=', $param['id']];
                }
            }

            //按时间检索
            if (!empty($param['aname'])) {
                $admin_ids = Db::name("admin")->where([['name','like','%'.$param['aname'].'%']])->column('id');
                $map1[] = ['f.aid', 'in', implode(",",$admin_ids)];
                $map2[] = ['f.aid', 'in', implode(",",$admin_ids)];
            }

            // 添加表单内容模糊搜索功能
            if (!empty($param['form_content'])) {
                $map1[] = ['f.jsoncode', 'like', '%' . $param['form_content'] . '%'];
                $map2[] = ['f.jsoncode', 'like', '%' . $param['form_content'] . '%'];
            }

            $rows = empty($param['limit']) ? get_config('app.page_size') : $param['limit'];

            $whereor = array();
            if ($status == 0) {
                $whereor = [$map1, $map2];
            }
            if ($status == 1) {
                $map1[] = ['f.check_status', '<', 3];
                $map = $map1;
            }
            if ($status == 2) {
                $map = $map2;
            }

//            $map[] = ['f.admin_id','<>',$this->uid];

            $list = Db::name('Approve')
                ->field('f.*,a.name,d.title as department_name,t.title as flow_type')
                ->alias('f')
                ->join('Admin a', 'a.id = f.admin_id', 'left')
                ->join('Department d', 'd.id = f.department_id', 'left')
                ->join('FlowType t', 't.id = f.type', 'left')
                ->where($map)
//                ->whereOr($whereor)
                ->where(function ($query) use ($whereor) {
                    $query->whereOr($whereor);
                })
                ->order('f.id desc')
                ->group('f.id')
                ->paginate(['list_rows' => $rows, 'query' => $param])
                ->each(function ($item, $key) {
                    $item['create_time'] = date('Y-m-d H:i', $item['create_time']);
                    $item['finish_time'] = empty($item['finish_time']) ? '' : date('Y-m-d H:i', $item['finish_time']);
                    $item['check_user'] = '-';
                    if ($item['check_status'] < 2 && !empty($item['check_admin_ids'])) {
                        $check_user = Db::name('Admin')->where('id', 'in', $item['check_admin_ids'])->column('name');
                        $item['check_user'] = implode(',', $check_user);
                    }

                    if (!empty($item['jsoncode'])){
                        $jsoncode = unserialize($item['jsoncode']);
                        if (isset($jsoncode['a_view_name']))
                        $item['aname'] = $jsoncode['a_view_name'];
                    }
                    return $item;
                });

//            getLastSql("Approve");

            $list = $list->toArray();

            // 获取所有审批ID，用于批量查询催审次数
            $approve_ids = array_column($list['data'], 'id');
            $urge_counts = [];

            if (!empty($approve_ids)) {
                // 查询当前用户收到的催审消息次数
                $urge_data = Db::name('Message')
                    ->field('action_id, COUNT(id) as urge_count')
                    ->where([
                        ['action_name', '=', 'urge'],
                        ['module_name', '=', 'oa'],
                        ['controller_name', '=', 'approve'],
                        ['action_id', 'in', $approve_ids],
                        ['to_uid', '=', $this->uid]  // 关键：只统计发给当前用户的催审消息
                    ])
                    ->group('action_id')
                    ->select()
                    ->toArray();

                // 转换为以action_id为键的数组
                foreach ($urge_data as $item) {
                    $urge_counts[$item['action_id']] = $item['urge_count'];
                }
            }

            foreach ($list['data'] as $k => $v){
                if (!empty($v['jsoncode'])){
                    $jsoncode = unserialize($v['jsoncode']);
                    if (isset($jsoncode['aid'])){
                        $list['data'][$k]['aname'] = Db::name("admin")->where(['id'=>$jsoncode['aid']])->value('name');
                    }
                    if (isset($jsoncode['did'])){
                        $list['data'][$k]['department_name'] = Db::name("department")->where(['id'=>$jsoncode['did']])->value('title');
                    }
                }

                // 添加催审次数字段
                $list['data'][$k]['urge_count'] = isset($urge_counts[$v['id']]) ? $urge_counts[$v['id']] : 0;
            }

            return table_assign(0, '', $list);
        } else {
            $type = Db::name('FlowType')->whereOr('status', 1)->select()->toArray();

            // 计算待审批数量
            $user_id = $this->uid;
            $pending_count = Db::name('Approve')
                ->alias('f')
                ->where([
                    ['', 'exp', Db::raw("FIND_IN_SET('{$user_id}',f.check_admin_ids)")],
                    ['f.check_status', '<', 2]
                ])
                ->count();

            View::assign('type', $type);
            View::assign('uid', $this->uid);
            View::assign('pending_count', $pending_count);

            if (!empty($is_mobile)) {
                return View("../../mobile/view/index/list");
            }

            return view();
        }
    }

    public function copy()
    {
        $is_mobile = Session::get("is_mobile");

        if (request()->isAjax()) {
            $param = get_params();
            $user_id = $this->uid;
            //查询条件
            $map = [];
            $map[] = ['', 'exp', Db::raw("FIND_IN_SET('{$user_id}',f.copy_uids)")];
            if (!empty($param['type'])) {
                $map[] = ['f.type', '=', $param['type']];
            }
            if (!empty($param['uid'])) {
                $map[] = ['f.admin_id', '=', $param['uid']];
            }
            $whereor = array();
            if (isset($param['keywords']) && !empty($param['keywords'])) {
                $key_admin = Db::name("admin")->where([
                    ['name' , 'like' , "%{$param['keywords']}%"]
                ])->select()->column("id");
                $whereor[] = ['f.admin_id', 'in', $key_admin];
                $whereor[] = ['f.aid', 'in', $key_admin];
            }
            //按时间检索
            if (!empty($param['apply_time'])) {
                $apply_time = explode('~', $param['apply_time']);
                $map[] = ['f.create_time', 'between', [strtotime(urldecode($apply_time[0])), strtotime(urldecode($apply_time[1]))]];
            }

            if (isset($param['check_status']) && $param['check_status'] != "") {
                $map[] = ['f.check_status', '=', $param['check_status']];
            }

            $rows = empty($param['limit']) ? get_config('app.page_size') : $param['limit'];
            $list = Db::name('Approve')
                ->field('f.*,a.name,d.title as department_name,t.title as flow_type')
                ->alias('f')
                ->join('Admin a', 'a.id = f.admin_id', 'left')
                ->join('Department d', 'd.id = f.department_id', 'left')
                ->join('FlowType t', 't.id = f.type', 'left')
                ->where($map)
                ->where(function ($query) use ($whereor) {
                    $query->whereOr($whereor);
                })
                ->order('f.id desc')
                ->group('f.id')
                ->paginate(['list_rows' => $rows, 'query' => $param])
                ->each(function ($item, $key) {
                    $item['create_time'] = date('Y-m-d H:i', $item['create_time']);
                    $item['check_user'] = '-';
                    if ($item['check_status'] < 2 && !empty($item['check_admin_ids'])) {
                        $check_user = Db::name('Admin')->where('id', 'in', $item['check_admin_ids'])->column('name');
                        $item['check_user'] = implode(',', $check_user);
                    }

                    $item['aname'] = '-';
                    if (!empty($item['jsoncode'])){
                        $jsoncode = unserialize($item['jsoncode']);
                        if (isset($jsoncode['aid'])){
                            $item['aname'] = Db::name("admin")->where(['id'=>$jsoncode['aid']])->value('name');
                        }
                    }
                    return $item;
                });

            return table_assign(0, '', $list);
        } else {
            $type = Db::name('FlowType')->whereOr('status', 1)->select()->toArray();
            View::assign('type', $type);

            if (!empty($is_mobile)) {
                return View("../../mobile/view/index/copy");
            }

            return view();
        }
    }

    //添加新增/编辑
    public function add()
    {
        $param = get_params();

        if (request()->isAjax()) {

            $admin = Db::name("admin")->where(['id' => $this->uid])->find();

            if ($admin['position_id'] == '85' && $admin['is_virtually'] == 1 && $param['type'] != 36){
                return to_assign(1, "前台预约账号不能提交此审批");
            }

            if (empty($admin) || $admin['status'] !== 1 ){
                return to_assign(1, "账号异常，请联系管理员");
            }

            // 定义要修改补充 aid 的 con 标识的列表
            $modify_aid_con_list = ['yzzzhkt', 'pxssysq', 'bgypsq', 'mdssgh'];
            // 如果未设置，或者为空，修改 aid
            if ((!isset($param['aid']) || empty($param['aid'])) && in_array($param['con'], $modify_aid_con_list)) {
                $param['aid'] = $this->uid;
            }

            $param['jsoncode'] = serialize($param);
            $ApproveService = new ApproveService();

            if (isset($param['con']) && ($param['con'] == 'transtore'
                    || $param['con'] == 'lizhi'
                    || $param['con'] == 'zhuangang'
                    || $param['con'] == 'jinsheng'
                    || $param['con'] == 'zhuanzheng'
                    || $param['con'] == 'waichu'
                    || $param['con'] == 'jiaban'
                    || $param['con'] == 'chuchai'
                    || $param['con'] == 'qingjia'
                    || $param['con'] == 'zhaopinjj'
                    || $param['con'] == 'gongguanfei'
                    || $param['con'] == 'shebao'
                    || $param['con'] == 'gongjijin'
                    || $param['con'] == 'storelift'
                    || $param['con'] == 'tingxin'
                    || $param['con'] == 'fukuan'
                    || $param['con'] == 'peifufei'
                    || $param['con'] == 'baoxiao'
                    || $param['con'] == 'zhuangu'
                    || $param['con'] == 'zanzhi'
                    || $param['con'] == 'tuigu'
                    || $param['con'] == 'jjsq'
                    || $param['con'] == 'contract'
                    || $param['con'] == 'purchase'
                    || $param['con'] == 'tzkbx'
                    || $param['con'] == 'hytk'
                    || $param['con'] == 'zzsq'
                    || $param['con'] == 'mdzk'
                    || $param['con'] == 'lxcg'
                    || $param['con'] == 'yzzzhkt'
                    || $param['con'] == 'pxssysq'
                    || $param['con'] == 'mdssgh'
                    || $param['con'] == 'ruzhi'
                )) { //调店
                if ($param['con'] == 'transtore') {
                    $param['did'] = $param['o_did'];
                } else if ($param['con'] == 'lizhi'
                    || $param['con'] == 'tingxin'
                    || $param['con'] == 'shebao'
                    || $param['con'] == 'zhaopinjj'
                    || $param['con'] == 'storelift'
                ) { //离职
                    $param['did'] = $param['department_type'];
                } else if ($param['con'] == 'zhuangang') {// 转岗
                    $param['did'] = get_admin($param['aid'])['did'];
                } else if ($param['con'] == 'yzzzhkt') { // 有赞总账号开通
                    Log::info('=== 有赞总账号开通流程-特殊处理-开始 ===');
                    // 添加当前用户的部门负责人作为抄送人（如果不是自己）
                    // 获取当前用户的部门负责人
                    $leader = get_department_leader($this->uid);
                    // 判断部门负责人不是自己且不为0
                    if ($leader != 0 && $leader != $this->uid) {
                        // 如果已有抄送人，则添加部门负责人到抄送列表，去除重复
                        if (!empty($param['copy_uids'])) {
                            $uidsArr = explode(',', $param['copy_uids']);
                            $uidsArr[] = $leader;
                            $uidsArr = array_unique($uidsArr);
                            $param['copy_uids'] = implode(',', $uidsArr);
                        } else {
                            // 如果没有抄送人，则直接设置部门负责人作为抄送人
                            $param['copy_uids'] = $leader;
                        }
                        Log::info("有赞总账号开通，操作人：{$this->uid}，部门负责人：{$leader}，最终抄送人列表：{$param['copy_uids']}");
                    } else if ($leader == 0) {
                        Log::warning("有赞总账号开通，未找到部门负责人，操作人：{$this->uid}");
                    }
                    Log::info('=== 有赞总账号开通流程-特殊处理-结束 ===');
                }

                $ApproveService->transtoreadd($param , $this->uid);
                return to_assign(0, "操作成功", ['is_mobile' => Session::get("is_mobile")]);
            }
            if (isset($param['con']) && $param['con'] == 'gongguanfei') {
                //数据校验
                $pay_method = $param['pay_method'];
                if ($pay_method == '银行转账') {
                    if (empty($param['payee'])) {
                        return to_assign(1, "请填写转账收款人");
                    } else if (empty($param['transfer_bcn'])) {
                        return to_assign(1, "请填写转账银行卡号");
                    } else if (empty($param['transfer_aob'])) {
                        return to_assign(1, "请填写转账开户行");
                    }
                } else {
                    if (empty($param['member_name'])) {
                        return to_assign(1, "请填写会员卡姓名");
                    } else if (empty($param['member_mobile'])) {
                        return to_assign(1, "请填写会员卡手机号");
                    }
                }
                $ApproveService->transtoreadd($param , $this->uid);
                return to_assign(0, "操作成功", ['is_mobile' => Session::get("is_mobile")]);
            }

            if (isset($param['detail_time'])) {
                $param['detail_time'] = strtotime($param['detail_time']);
            }
            if (isset($param['start_time'])) {
                $param['start_time'] = strtotime($param['start_time']);
            }
            if (isset($param['end_time'])) {
                $param['end_time'] = strtotime($param['end_time']);
                if ($param['end_time'] < $param['start_time']) {
                    return to_assign(1, "结束时间不能小于开始时间");
                }
            }

            if (isset($param['duration'])) {
                if ($param['duration'] <= 0) {
                    return to_assign(1, "时间区间选择错误");
                }
            }
            $flow_list = Db::name('Flow')->where('id', $param['flow_id'])->value('flow_list');
            $flow = unserialize($flow_list);

            if ($param['id'] > 0) {
                $param['update_time'] = time();
                $param['check_status'] = 0;
                $param['check_step_sort'] = 0;
                //删除原来的审核流程和审核记录
                Db::name('FlowStep')->where(['action_id' => $param['id'], 'type' => 1, 'delete_time' => 0])->update(['delete_time' => time()]);
                Db::name('FlowRecord')->where(['action_id' => $param['id'], 'type' => 1, 'delete_time' => 0])->update(['delete_time' => time()]);

                if (!isset($param['check_admin_ids'])) {
                    if ($flow[0]['flow_type'] == 1) {
                        //部门负责人
                        $leader = get_department_leader($this->uid);
                        if ($leader == 0) {
                            return to_assign(1, '审批流程设置有问题：当前部门负责人还未设置，请联系HR或者管理员');
                        } else {
                            $param['check_admin_ids'] = $leader;
                        }
                    } else if ($flow[0]['flow_type'] == 2) {
                        //上级部门负责人
                        $leader = get_department_leader($this->uid, 1);
                        if ($leader == 0) {
                            return to_assign(1, '审批流程设置有问题：上级部门负责人还未设置，请联系HR或者管理员');
                        } else {
                            $param['check_admin_ids'] = $leader;
                        }
                    } else {
                        $param['check_admin_ids'] = $flow[0]['flow_uids'];
                    }
                    Db::name('Approve')->strict(false)->field(true)->update($param);
                    foreach ($flow as $key => &$value) {
                        $value['action_id'] = $param['id'];
                        $value['sort'] = $key;
                        $value['create_time'] = time();
                    }
                    $res = Db::name('FlowStep')->strict(false)->field(true)->insertAll($flow);
                } else {
                    Db::name('Approve')->strict(false)->field(true)->update($param);
                    $flow_step = array(
                        'action_id' => $param['id'],
                        'flow_uids' => $param['check_admin_ids'],
                        'create_time' => time()
                    );
                    $res = Db::name('FlowStep')->strict(false)->field(true)->insertGetId($flow_step);
                }

                //添加提交申请记录
                $checkData = array(
                    'action_id' => $param['id'],
                    'check_user_id' => $this->uid,
                    'content' => '重新提交申请',
                    'check_time' => time(),
                    'create_time' => time()
                );
                $record_id = Db::name('FlowRecord')->strict(false)->field(true)->insertGetId($checkData);
                add_log('edit', $param['id'], $param);
                //发送消息通知
                $msg = [
                    'from_uid' => $this->uid,
                    'title' => Db::name('FlowType')->where('id', $param['type'])->value('title'),
                    'action_id' => $param['id']
                ];
                $users = $param['check_admin_ids'];
                sendMessage($users, 21, $msg, '', 0);

                //将我本人之前 的消息记录标记为已读
                Db::name("message")->where([
                    'action_id' => $param['id'],
                    'to_uid' => $this->uid,
                    ['create_time', '<', time()]
                ])->update(['read_time' => time()]);
            } else {
                $param['admin_id'] = $this->uid;
                $param['department_id'] = $this->did;
                $param['create_time'] = time();

                if (!isset($param['check_admin_ids'])) {

                    if ($flow[0]['flow_type'] == 1) {
                        //部门负责人
                        $leader = get_department_leader($this->uid);
                        if ($leader == 0) {
                            return to_assign(1, '当前部门负责人还未设置，请联系HR或者管理员');
                        } else {
                            $param['check_admin_ids'] = $leader;
                        }
                    } else if ($flow[0]['flow_type'] == 2) {
                        //上级部门负责人
                        $leader = get_department_leader($this->uid, 1);
                        if ($leader == 0) {
                            return to_assign(1, '上级部门负责人还未设置，请联系HR或者管理员');
                        } else {
                            $param['check_admin_ids'] = $leader;
                        }
                    } else {
                        $param['check_admin_ids'] = $flow[0]['flow_uids'];
                    }
                    $aid = Db::name('Approve')->strict(false)->field(true)->insertGetId($param);
                    foreach ($flow as $key => &$value) {
                        $value['action_id'] = $aid;
                        $value['sort'] = $key;
                        $value['create_time'] = time();
                        if (empty($value['flow_title'])) {
                            unset($flow[$key]['flow_title']);
                        }
                        if (empty($value['id'])) {
                            unset($flow[$key]['id']);
                        }
                    }

                    $res = Db::name('FlowStep')->strict(false)->field(true)->insertAll($flow);

                } else {
                    $aid = Db::name('Approve')->strict(false)->field(true)->insertGetId($param);
                    $flow_step = array(
                        'action_id' => $aid,
                        'flow_uids' => $param['check_admin_ids'],
                        'create_time' => time()
                    );
                    $step_id = Db::name('FlowStep')->strict(false)->field(true)->insertGetId($flow_step);
                }

                //添加提交申请记录
                $checkData = array(
                    'action_id' => $aid,
                    'check_user_id' => $this->uid,
                    'content' => '提交申请',
                    'check_time' => time(),
                    'create_time' => time()
                );
                $record_id = Db::name('FlowRecord')->strict(false)->field(true)->insertGetId($checkData);
                add_log('add', $aid, $param);
                //给审核人发送消息通知
                $msg = [
                    'from_uid' => $this->uid,
                    'title' => Db::name('FlowType')->where('id', $param['type'])->value('title'),
                    'action_id' => $aid
                ];
                $users = $param['check_admin_ids'];
                sendMessage($users, 21, $msg);
            }
            return to_assign(0, "操作成功", ['is_mobile' => Session::get("is_mobile")]);
        } else {
            $id = isset($param['id']) ? $param['id'] : 0;
            $type = isset($param['type']) ? $param['type'] : 0;

            // 特殊处理：如果是维修流程类型，重定向到维修流程详情页
            // 从数据库获取维修流程类型ID
            $repair_config = ConfigManager::get('flow_repair', []);
            $repair_type_id = isset($repair_config['type_id']) ? $repair_config['type_id'] : 0;

            // 如果数据库中没有配置，则从配置文件中读取（兼容旧版本）
            if (empty($repair_type_id)) {
                $repair_type_id = Config::get('flow-config.repair.type_id');
            }

            if ($type == $repair_type_id) {
                return redirect((string)url('repair/process/add'));
            }

            $jsoncode = "";
            if ($id > 0) {
                $detail = Db::name('Approve')->where('id', $id)->find();

                $detail['start_time_a'] = isset($detail['start_time']) ? date('Y-m-d', $detail['start_time']) : '';
                $detail['start_time_b'] = isset($detail['start_time']) ? date('H:i', $detail['start_time']) : '' ;
                $detail['end_time_a'] = isset($detail['end_time']) ? date('Y-m-d', $detail['end_time']) : '';
                $detail['end_time_b'] = isset($detail['end_time']) ? date('H:i', $detail['end_time']) : '';
                $detail['detail_time'] = isset($detail['detail_time']) ? date('Y-m-d', $detail['detail_time']) : '';

                $detail['days'] = floor($detail['duration'] * 10 / 80);
                $detail['hours'] = (($detail['duration'] * 10) % 80) / 10;

                $copy_names = Db::name("admin")->field("name")->where([['id', 'in', $detail['copy_uids']]])->select()
                    ->each(function ($item) {
                        $item = (string)$item['name'];
                        return $item;
                    })->toArray();
                $im = implode(",", $copy_names);
                $detail['copy_names'] = $im;

                $type = $detail['type'];
                if ($detail['file_ids'] != '') {
                    $fileArray = Db::name('File')->where('id', 'in', $detail['file_ids'])->select();
                    $detail['fileArray'] = $fileArray;
                }

                View::assign('detail', $detail);

                if (!empty($detail['jsoncode'])) {
                    $jsoncode = unserialize($detail['jsoncode']);

                    if (isset($jsoncode['aid'])) {
                        $jsoncode['aname'] = Db::name("admin")->where(['id' => $jsoncode['aid']])->value("name");
                    }

                    if (isset($jsoncode['to_aid'])) {
                        $jsoncode['to_aname'] = Db::name("admin")->where(['id' => $jsoncode['to_aid']])->value("name");
                    }

                    if (isset($jsoncode['o_did'])) {
                        $jsoncode['o_dname'] = Db::name("Department")->where(['id' => $jsoncode['o_did']])->value("title");
                    }
                    if (isset($jsoncode['n_did'])) {
                        $jsoncode['n_dname'] = Db::name("Department")->where(['id' => $jsoncode['n_did']])->value("title");
                    }

                    if ($detail['type'] == 23 ){
                        $jsoncode['arr']  = array();
                        foreach ($jsoncode['pay_type'] as $k => $v){
                            $detail_file = array();
                            if (!empty($jsoncode['detailsList'][$k])) {
                                $detail_file = Db::name('file')->where([['id', 'in', $jsoncode['detailsList'][$k]]])->select()->toArray();
                            }
                            $jsoncode['arr'][] = [
                                'pay_type' => $jsoncode['pay_type'][$k],
                                'pay_time' => $jsoncode['pay_time'][$k],
                                'pay_amount' => $jsoncode['pay_amount'][$k],
                                'pay_text' => $jsoncode['pay_text'][$k],
                                'detailsList' => $jsoncode['detailsList'][$k],
                                'detail_file' => $detail_file
                            ];
                        }
                    }

                    if ($detail['type'] == 13 ){
                        if (isset($jsoncode['payee'])){
                            $jsoncode['payee_name'] = $jsoncode['payee'];
                        }
                        if (isset($jsoncode['open_bank'])){
                            $jsoncode['bank_name'] = $jsoncode['open_bank'];
                        }

                        if (isset($jsoncode['rent_id'])){
                            $contract_rent = Db::name("rent")
                                ->field(" cr.is_invoice ")
                                ->alias('r')
                                ->join('contract_rent cr', 'r.cr_id = cr.id', 'left')
                                ->where(['r.id' => $jsoncode['rent_id']])
                                ->find();
                            $jsoncode['is_invoice'] = $contract_rent['is_invoice'] == 1 ? '是' : '否';
                        }
                    }

                }
            }
            else if ($type == 17 && isset($param['a_view_id']) && !empty($param['a_view_id'])) {
                $jsoncode =
                    Db::name("admin_view")
                        ->field("av.*, av.name as a_view_name , av.id as a_view_id ")
                        ->alias('av')
                        ->where(['av.id' => $param['a_view_id']])
                        ->find();

                $interview = Db::name("interview")->where(['a_view_id' => $param['a_view_id']])->order("create_time desc")->find();
                $examine = Db::name("examine")->where(['a_view_id' => $param['a_view_id']])->order("create_time desc")->find();

                $jsoncode['remark'] = '擅长项：推拿/关元灸/刮痧/拔罐';

                $jsoncode['interview_check_status'] = $interview['check_status'];
                $jsoncode['average_score'] = $interview['average_score'];
                $jsoncode['in_aname'] = $interview['aname'];
                $jsoncode['position'] = $interview['position'];

                $jsoncode['ex_aname'] = $examine['aname'];
                $jsoncode['ex_check_status'] = $examine['check_status'];

            }

            $adminService = new AdminService();
            $param = $adminService->getParam();
            View::assign('equ_background', $param['equ_background']);

            $department = $this->did;

            //获取审批流程
            $DepartmentChange = new DepartmentChange();
            $dep = $DepartmentChange->getDirectlyDepartment($this->uid);
            $flows = get_cate_department_flows($type, implode(",",$dep), $this->uid);

            if ($type == 19) {
                $re_dep = Db::name("department")->where([['id', 'in', $department], ['remark', '=', '门店']])->select()->toArray();

                if (empty($re_dep)) {
                    $flows = array_reverse($flows);
                }
            }

            $moban = Db::name('FlowType')->where('id', $type)->value('name');
            $template = $this->getTemplatePath($moban, 'add');

            //获取店长
            $dz_opsition = Db::name("position")->where([['title', 'like', '%店长%']])->column('id');

            foreach ($dz_opsition as $dzp_k => $dzp_v){
                $map[] = ['', 'exp', Db::raw("FIND_IN_SET('{$dzp_v}',position_id)")];
            }

            $dz_admin = Db::name("admin")->field("id,name,mobile")->where(function ($query) use ($map) {
                $query->whereOr($map);
            })->select();

            $xdkys = Db::name("approve")->where(['flow_id' => 26, "check_status" => 1])->select();

            //查看一下是否分管
            $dep_model = new DepartmentChange();
            $dep_fenguan = $dep_model->get_dep_fenguan(0, $this->uid);

            $admin = get_admin($this->uid);

            $department_detail = Db::name("department")->where([
                ['id', 'in', $department],
                ['title','<>','前台预约微信']
            ])->order("id desc")
                ->select()
                ->toArray();

            foreach ($department_detail as $k => $v){
               $store = Db::name("store")->where(['did' => $v['id'] , 'sdate' => date("Y-m") ])->find();

               if (!empty($store)){
                   $department_detail[$k]['grade'] = $store['grade'];
               }else{
                   $department_detail[$k]['grade'] = 0;
               }
            }

            $dep_store = Db::name("department")->where([['id', 'in', $department],['remark' , '=' , '门店' ]])->order("id desc")->find();

            foreach ( $dep_fenguan as $k => $v ){
                $store = Db::name("store")->where([
                    'did' => $v['id'] , 'sdate' => date("Y-m")
                ])->find();

                if (!empty($store)){
                    $dep_fenguan[$k]['grade'] = $store['grade'];
                }else{
                    $dep_fenguan[$k]['grade'] = 0;
                }
            }

            $position = Db::name("position")->where(['type' => 2, 'status' => 1])->select()->toArray();

            $code_edit_flag = true;
            if ($type == 40) {
                $code_edit_flag = false;
            }

//            $ranks = Db::name("StoreRank")->where([ ['', 'exp', Db::raw("FIND_IN_SET('{$param['type']}',flow_type)")] ])->select()->toArray();
//            $labels = Db::name("StoreLabel")->select()->toArray();

//            View::assign('ranks', $ranks);
//            View::assign('labels', $labels);

            $flow_type = Db::name("flow_type")->where(['id' => $type])->find();

            View::assign('flow_type', $flow_type);
            View::assign('position', $position);
            View::assign('jsoncode', $jsoncode);
            View::assign('dep', $dep_fenguan);
            View::assign('name', $this->name);


            View::assign('dep_detail', $department_detail);
            View::assign('dep_store', $dep_store);

            View::assign('code_edit_flag', $code_edit_flag);

            View::assign('is_mobile', Session::get("is_mobile"));



            // 处理用户默认门店选择逻辑
            $default_store_id = null;
            if (!empty($admin['did'])) {
                // 处理多部门情况，did可能是逗号分隔的多个ID
                $department_ids = explode(',', $admin['did']);
                if (!empty($department_ids)) {
                    // 查找用户所在的门店部门（remark='门店'）
                    $user_stores = Db::name('department')
                        ->where('id', 'in', $department_ids)
                        ->where('remark', '门店')
                        ->where('status', 1)
                        ->field('id')
                        ->select()
                        ->toArray();

                    // 如果用户有门店部门，选择第一个作为默认值
                    if (!empty($user_stores)) {
                        $default_store_id = $user_stores[0]['id'];
                    }
                }
            }

            View::assign([
                'flows' => $flows,
                'id' => $id,
                'type' => $type,
                'dz_admin' => $dz_admin,
                'xdkys' => $xdkys,
                'admin_info' => $admin,
                'mds' => getmd("门店"),
                'hqs' => getmd("后勤"),
                'default_store_id' => $default_store_id,
            ]);

            if (isTemplate($template)) {
                // 根据模板路径确定视图名称
                if ($this->isMobileDevice() && in_array($moban, $this->mobile_supported_flows)) {
                    $mobile_template = 'add_' . $moban . '_mobile';
                    if (isTemplate('oa/view/approve/' . $mobile_template . '.html')) {
                        return view($mobile_template);
                    }
                }
                return view('add_' . $moban);
            } else {
                return view('../../base/view/common/errortemplate', ['file' => $template]);
            }
        }
    }

    //查看
    public function view()
    {
        $param = get_params();
        $id = isset($param['id']) ? $param['id'] : 0;

        if (empty($id)) {
            $url = Request::url();
            $pos = strpos($url, "view");
            $poss = strpos($url, "?");

            if ($pos !== false) {
                if ($poss !== false) {
                    $substr = substr($url, $pos + 5);
                    $param['id'] = strstr($substr, "?", true);
                } else {
                    $param['id'] = substr($url, $pos + 5);
                }
            }
        }

        $detail = Db::name('Approve')->where('id', $param['id'])->find();

        // 特殊处理：如果是维修流程类型，重定向到维修流程详情页
        // 从数据库获取维修流程类型ID
        $repair_config = ConfigManager::get('flow_repair', []);
        $repair_type_id = isset($repair_config['type_id']) ? $repair_config['type_id'] : 0;

        // 如果数据库中没有配置，则从配置文件中读取（兼容旧版本）
        if (empty($repair_type_id)) {
            $repair_type_id = Config::get('flow-config.repair.type_id');
        }

        if ($detail && $detail['type'] == $repair_type_id) {
            // 从remark中提取维修流程ID
            $repair_id = 0;
            if (strpos($detail['remark'], 'repair_') === 0) {
                $repair_id = (int)substr($detail['remark'], 7);
            } else if (!empty($detail['jsoncode'])) {
                $jsoncode = unserialize($detail['jsoncode']);
                if (isset($jsoncode['process_id'])) {
                    $repair_id = (int)$jsoncode['process_id'];
                }
            }

            if ($repair_id > 0) {
                // 重定向到维修流程详情页
                return redirect((string)url('repair/process/view', ['id' => $repair_id]));
            }
        }

        $flow_name = Db::name('flow')->where('id', $detail['flow_id'])->value('name');

        if ($detail['type'] == 29 || $detail['type'] == 34){
            $check_record2 = Db::name('FlowRecord')->field('f.*,a.name,a.thumb')
                ->alias('f')
                ->join('Admin a', 'a.id = f.check_user_id', 'left')
                ->where(['f.action_id' => $detail['a_id'], 'f.type' => 1])
                ->order('check_time desc')
                ->select()->toArray();
            foreach ($check_record2 as $kk => &$vv) {
                $vv['check_time_str'] = date('Y-m-d H:i', $vv['check_time']);
                $vv['status_str'] = '提交';
                if ($vv['status'] == 1) {
                    $vv['status_str'] = '审核通过';
                } else if ($vv['status'] == 2) {
                    $vv['status_str'] = '审核拒绝';
                }
                if ($vv['status'] == 3) {
                    $vv['status_str'] = '撤销';
                }
                if ($vv['status'] == 4) {
                    $vv['status_str'] = '无法处理';
                }
                if (!empty($vv['file_ids'])) {
                    $fileArray = Db::name('file')->where([['id', 'in', $vv['file_ids']]])->select();
                    $check_record2[$kk]['fileArray'] = $fileArray;
                }
            }

            View::assign('check_record2', $check_record2);
        }

        if ($detail['type'] == 37) { //调店
            $detail = $this->transtore_details($detail);
        } else if ($detail['type'] == 19) { //调店
            $detail = $this->lizhi_details($detail);
        } else if ($detail['type'] == 20
            || $detail['type'] == 38
            || $detail['type'] == 18
        ) { //转岗
            $detail = $this->zhuangang_details($detail);

            //$ApproveService = new ApproveService();
            //$store_business_t = $ApproveService->getStoreBusinessT($detail['aid']);
            //View::assign('store_business_t', $store_business_t);

        }else if ($detail['type'] == 1 || $detail['type'] == 42 || $detail['type'] == 43 || $detail['type'] == 44){ //请假
            $detail = $this->jsoncode($detail);
        } else if ($detail['type'] == 40
            || $detail['type'] == 3
            || $detail['type'] == 4
            || $detail['type'] == 2
            || $detail['type'] == 45
            || $detail['type'] == 45
        ) {
            $detail = $this->jsoncode($detail);
        }else if ($detail['type'] == 41) {
            $detail = $this->zhaopinjj_details($detail);
        }else if (isset($detail['jsoncode'])){
            $jsoncode = unserialize($detail['jsoncode']);

            foreach ($jsoncode as $k => $v){
                if ($k == 'id') continue;
                if ($k == 'did'){
                    $detail['dname'] = Db::name("department")->where(['id' => $v])->value("title");
                }
                if ($k == 'store'){
                    $detail['store_name'] = Db::name("department")->where(['id' => $v])->value("title");
                }
                if ($k == 'pay_type'){
                    $detail['pay_type_name'] = Db::name("type_pay")->where(['id' => $v])->value("name");
                }
                $detail[$k] = $v;
            }
        }

        if ($detail['type'] == 13 ){
            if (isset($jsoncode['rent_id'])){
                $contract_rent = Db::name("rent")
                    ->field(" cr.is_invoice ")
                    ->alias('r')
                    ->join('contract_rent cr', 'r.cr_id = cr.id', 'left')
                    ->where(['r.id' => $jsoncode['rent_id']])
                    ->find();
                $detail['is_invoice'] = $contract_rent['is_invoice'] == 1 ? '是' : '否';
            }
        }



        if ($detail['type'] == 23 ){
            $detail['arr']  = array();
            foreach ($detail['pay_type'] as $k => $v){
                $detail_file = array();
                if (!empty($detail['detailsList'][$k])) {
                    $detail_file = Db::name('file')->where([['id', 'in', $detail['detailsList'][$k]]])->select();

                }
                $detail['arr'][] = [
                    'pay_type' => $detail['pay_type'][$k],
                    'pay_time' => $detail['pay_time'][$k],
                    'pay_amount' => $detail['pay_amount'][$k],
                    'pay_text' => $detail['pay_text'][$k],
                    'detailsList' => $detail['detailsList'][$k],
                    'detail_file' => $detail_file
                ];
            }
        }

        $check_record = [];
        if (is_numeric($detail['start_time']) && $detail['start_time'] > 0) {
            $detail['start_time_hour'] = date('Y-m-d H:i:s', $detail['start_time']);
            $detail['start_time'] = date('Y-m-d', $detail['start_time']);
        }
        if (is_numeric($detail['end_time']) && $detail['end_time'] > 0) {
            $detail['end_time_hour'] = date('Y-m-d H:i:s', $detail['end_time']);
            $detail['end_time'] = date('Y-m-d', $detail['end_time']);
        }
        if (is_numeric($detail['detail_time']) && $detail['detail_time'] > 0) {
            $detail['detail_time_hour'] = date('Y-m-d H:i:s', $detail['detail_time']);
            $detail['detail_time'] = date('Y-m-d', $detail['detail_time']);
        }

        $detail['days'] = floor($detail['duration'] * 10 / 80);
        $detail['hours'] = (($detail['duration'] * 10) % 80) / 10;

        $detail['create_user'] = Db::name('Admin')->where('id', $detail['admin_id'])->value('name');

        $detail['create_user'] = $detail['create_user'] . "【{$flow_name}】";

        $flows = Db::name('FlowStep')->where(['action_id' => $detail['id'], 'type' => 1, 'sort' => $detail['check_step_sort'], 'delete_time' => 0])->find();
        $detail['check_user'] = '-';
        $detail['copy_user'] = '-';
        $check_user_ids = [];

//        if (!empty($check_user_ids)){
//
//        }
        if ($detail['check_status'] < 2) {
            if ($flows['flow_type'] == 1) {
                $detail['check_user'] = '部门负责人';
                $check_user_ids = explode(',', $detail['check_admin_ids']);
            } else if ($flows['flow_type'] == 2) {
                $detail['check_user'] = '上级部门负责人';
                $check_user_ids = explode(',', $detail['check_admin_ids']);
            } else {
                $check_user_ids = explode(',', $flows['flow_uids']);
                $check_user = Db::name('Admin')->where('id', 'in', $flows['flow_uids'])->column('name');
                $detail['check_user'] = implode(',', $check_user);
            }
        }
        if ($detail['copy_uids'] != '') {
            $copy_user = Db::name('Admin')->where('id', 'in', $detail['copy_uids'])->column('name');
            $detail['copy_user'] = implode(',', $copy_user);
        }
        if ($detail['file_ids'] != '') {
            $fileArray = Db::name('File')->where('id', 'in', $detail['file_ids'])->select();
            $detail['fileArray'] = $fileArray;
        }else{
            $detail['fileArray'] = [];
        }

        if ($detail['type'] == 17 ){
            $fileArray_deu = Db::name('File')->where('id', 'in', $detail['file_ids_edu'])->select();
            $detail['fileArrayEdu'] = $fileArray_deu;
        }

        $is_check_admin = 0;
        $is_create_admin = 0;
        if ($detail['admin_id'] == $this->uid && $detail['check_status'] != 1) {
            $is_create_admin = 1;
        }
        if (in_array($this->uid, $check_user_ids)) {
            $is_check_admin = 1;
            //当前审核节点详情
            $step = Db::name('FlowStep')->where(['action_id' => $detail['id'], 'type' => 1, 'sort' => $detail['check_step_sort'], 'delete_time' => 0])->find();
            if ($step['flow_type'] == 4) {
                $check_count = Db::name('FlowRecord')->where(['action_id' => $detail['id'], 'type' => 1, 'step_id' => $step['id'], 'check_user_id' => $this->uid])->count();
                if ($check_count > 0) {
                    $is_check_admin = 0;
                }
            }
        }
        $check_record = Db::name('FlowRecord')->field('f.*,a.name,a.thumb')
            ->alias('f')
            ->join('Admin a', 'a.id = f.check_user_id', 'left')
            ->where(['f.action_id' => $detail['id'], 'f.type' => 1])
            ->order('check_time desc')
            ->select()->toArray();
        foreach ($check_record as $kk => &$vv) {
            $vv['check_time_str'] = date('Y-m-d H:i', $vv['check_time']);
            $vv['status_str'] = '提交';
            if ($vv['status'] == 1) {
                $vv['status_str'] = '审核通过';
            } else if ($vv['status'] == 2) {
                $vv['status_str'] = '审核拒绝';
            }
            if ($vv['status'] == 3) {
                $vv['status_str'] = '撤销';
            }
            if ($vv['status'] == 4) {
                $vv['status_str'] = '无法处理';
            }
            if (!empty($vv['file_ids'])) {
                $fileArray = Db::name('file')->where([['id', 'in', $vv['file_ids']]])->select();
                $check_record[$kk]['fileArray'] = $fileArray;
            }
        }
        $moban = Db::name('FlowType')->where('id', $detail['type'])->value('name');
        $template = $this->getTemplatePath($moban, 'view');

        //设置附件标题 以及是否必须上传附件
//        var_dump($detail['fileArray']);

        //评价
        $eval_p = [];
        $step_p = Db::name('FlowStep')->where(['action_id' => $detail['id'], 'sort' => 13])->find();
        if (!empty($step_p)) {
            $eval_p = Db::name('FlowRecord')->where(['action_id' => $detail['id'], 'step_id' => $step_p['id']])->find();
        }

        if (empty($detail['node_tit'])) {
            $detail['node_tit'] = $detail['fz_name'];
        }

        //设计交底确认 添加确认开业时间
        $step_p = Db::name('FlowStep')->where(['action_id' => $detail['id'], 'flow_title' => "设计交底确认"])->find();
        $ky_date = false;
        if (!empty($step_p) && $step_p['sort'] == $detail['check_step_sort']) {
            $ky_date = true;
        }

        $ky_date_text = "";
        $step_pp = Db::name('FlowStep')->where(['action_id' => $detail['id'], 'sort' => 10])->find();
        if (!empty($step_pp)) {
            $eval_pp = Db::name('FlowRecord')->where(['action_id' => $detail['id'], 'step_id' => $step_pp['id']])->find();
            if (!empty($eval_pp)) {
                $ky_date_text = $eval_pp['ky_date'];
            }
        }

        //添加责任部门
        $approve_s = Db::name('Approve_s')->where(['id' => $detail['id']])->find();
        if (!empty($approve_s)) {
            $detail['zrbm_id'] = $approve_s['zrbm_id'];
        }

        //问题反馈用 问题分配
        $is_wtfk_wtfp = 0;
        if ($detail['check_step_sort'] == '2') {
            $is_wtfk_wtfp = 1;
        }

        $is_wtfk_zrbm = 0;
        if ($detail['check_step_sort'] < 1 || $detail['check_step_sort'] == 2) {
            $is_wtfk_zrbm = 1;
        }

        $detail['admin_name'] = Db::name("admin")->where(['id' => $detail['admin_id']])->value('name');

        if (isset($jsoncode['crent_id']) && !empty($jsoncode['crent_id'])){
            $contract_rent = Db::name('contract_rent')->where(['id' => $jsoncode['crent_id']])->field("id,code")->find();
        }

        if ($detail['type'] == 17 && $detail['check_status'] == 2){
            $QiWeiService = new QiWeiService();
            $qiwei_qrcode = $QiWeiService->get_join_qrcode();
            View::assign('join_qrcode', isset($qiwei_qrcode->join_qrcode) ? $qiwei_qrcode->join_qrcode : '' );
        }

        $is_edit = 0;
        if ($this->uid == 2259 || $this->uid == 2357 ){
            $is_edit = 1;
        }
        View::assign('is_edit', $is_edit);

        View::assign('contract_rent', isset($contract_rent) ? $contract_rent : array());

        View::assign('is_wtfk_wtfp', $is_wtfk_wtfp);
        View::assign('is_wtfk_zrbm', $is_wtfk_zrbm);
        View::assign('ky_date_text', $ky_date_text);
        View::assign('ky_date', $ky_date);
        View::assign('eval_p', $eval_p);
        View::assign('is_create_admin', $is_create_admin);
        View::assign('is_check_admin', $is_check_admin);
        View::assign('check_record', $check_record);
        View::assign('detail', $detail);
        View::assign('flows', $flows);
        View::assign('hqs', getmd('后勤'));
        if (isTemplate($template)) {
            // 根据模板路径确定视图名称
            if ($this->isMobileDevice() && in_array($moban, $this->mobile_supported_flows)) {
                $mobile_template = 'view_' . $moban . '_mobile';
                if (isTemplate('oa/view/approve/' . $mobile_template . '.html')) {
                    return view($mobile_template);
                }
            }
            return view('view_' . $moban);
        } else {
            return view('../../base/view/common/errortemplate', ['file' => $template]);
        }
    }

    //新店开业
    public function xdkyadd()
    {
        $param = get_params();
        if (request()->isAjax()) {

            $flow_list = Db::name('Flow')->where('id', $param['flow_id'])->value('flow_list');
            $flow = unserialize($flow_list);

            if ($param['id'] > 0) {

                $param['update_time'] = time();
                $param['check_status'] = 0;
                $param['check_step_sort'] = 0;

                $param['check_admin_ids'] = $flow[0]['flow_uids'];
                $param['node_tit'] = $flow[0]['flow_title'];
                //删除原来的审核流程和审核记录
                Db::name('FlowStep')->where(['action_id' => $param['id'], 'type' => 1, 'delete_time' => 0])->update(['delete_time' => time()]);
                Db::name('FlowRecord')->where(['action_id' => $param['id'], 'type' => 1, 'delete_time' => 0])->update(['delete_time' => time()]);

                Db::name('Approve')->strict(false)->field(true)->update($param);
                //添加审批节点
                foreach ($flow as $key => &$value) {
                    $value['action_id'] = $param['id'];
                    $value['sort'] = $key;
                    $value['create_time'] = time();
                    if ($value['flow_uids'] == 0) {
                        $value['flow_uids'] = $param['dzid'];
                    }
                    unset($flow[$key]['id']);
                }

                $res = Db::name('FlowStep')->strict(false)->field(true)->insertAll($flow);
                //添加提交申请记录
                $checkData = array(
                    'action_id' => $param['id'],
                    'check_user_id' => $this->uid,
                    'content' => '提交申请',
                    'check_time' => time(),
                    'create_time' => time()
                );
                $record_id = Db::name('FlowRecord')->strict(false)->field(true)->insertGetId($checkData);
                add_log('add', $param['id'], $param);
                //给审核人发送消息通知
                $msg = [
                    'from_uid' => $this->uid,
                    'title' => Db::name('FlowType')->where('id', $param['type'])->value('title'),
                    'action_id' => $param['id']
                ];
                $users = $param['check_admin_ids'];
                sendMessage($users, 21, $msg);

            } else {
                $param['admin_id'] = $this->uid;
                $param['department_id'] = $this->did;
                $param['create_time'] = time();

                $param['check_admin_ids'] = $flow[0]['flow_uids'];
                $param['node_tit'] = $flow[0]['flow_title'];

                $copy_uids = Db::name('Flow')->where('id', $param['flow_id'])->value('copy_uids');
                if (!empty($copy_uids)){
                    $param['copy_uids'] = $param['copy_uids'] . ',' . $copy_uids;
                }

                $aid = Db::name('Approve')->strict(false)->field(true)->insertGetId($param);
                //添加审批节点
                foreach ($flow as $key => &$value) {
                    $value['action_id'] = $aid;
                    $value['sort'] = $key;
                    $value['create_time'] = time();
                    if ($value['flow_uids'] == 0) {
                        $value['flow_uids'] = $param['dzid'];
                    }
                    unset($flow[$key]['id']);
                }

                $res = Db::name('FlowStep')->strict(false)->field(true)->insertAll($flow);
                //添加提交申请记录
                $checkData = array(
                    'action_id' => $aid,
                    'check_user_id' => $this->uid,
                    'content' => '提交申请',
                    'check_time' => time(),
                    'create_time' => time()
                );
                $record_id = Db::name('FlowRecord')->strict(false)->field(true)->insertGetId($checkData);
                add_log('add', $aid, $param);
                //给审核人发送消息通知
                $msg = [
                    'from_uid' => $this->uid,
                    'title' => Db::name('FlowType')->where('id', $param['type'])->value('title'),
                    'action_id' => $aid
                ];
                $users = $param['check_admin_ids'];
                sendMessage($users, 21, $msg);
            }
            return to_assign(0, "操作成功", ['is_mobile' => Session::get("is_mobile")]);
        }
    }

    public function wtfkAdd()
    {
        $param = get_params();

        $mdid = $param['mdid']; //问题门店

        $flow_list = Db::name('Flow')->where('id', $param['flow_id'])->value('flow_list');
        $flow = unserialize($flow_list);

        //获取上级分管部门
        $qz_id = 0;
        $qz_name = '';
        $in_dep = Db::name("Department")->where(['id' => $mdid])->find();
        if (!empty($in_dep)) {
            $l_dep = Db::name("Department")->where(['id' => $in_dep['pid']])->find(); //上级部门
            if (!empty($l_dep) && strpos($l_dep['title'], '分管') !== false && !empty($l_dep['leader_id'])) {
                $l_admin = Db::name("admin")->where(['id' => $l_dep['leader_id'], 'status' => 1])->find();
                if (!empty($l_admin)) {
                    $qz_id = $l_admin['id'];
                    $qz_name = $l_admin['name'];
                }
            }
        }

        if (empty($qz_id)) {
            return to_assign(1, '当前门店未设置区总，请联系HR或者管理员');
        }

        //添加审批
        $param['admin_id'] = $this->uid;
        $param['department_id'] = $this->did;
        $param['check_admin_ids'] = $qz_id;
        $param['create_time'] = time();

        $param['node_tit'] = "区域店长-问题分析";

        $aid = Db::name('Approve')->strict(false)->field(true)->insertGetId($param);

        $approve_s = ['id' => $aid, 'zrbm_id' => $param['zrbm_id']];
        Db::name('Approve_s')->strict(false)->field(true)->insertGetId($approve_s);

        foreach ($flow as $key => &$value) {
            if ($key == 0) {
                $value['flow_uids'] = $qz_id;
            }
            $value['action_id'] = $aid;
            $value['sort'] = $key;
            $value['create_time'] = time();
            $value['flow_title'] = $value['flow_name'];
            if (empty($value['id'])) {
                unset($flow[$key]['id']);
            }
        }
        Db::name('FlowStep')->strict(false)->field(true)->insertAll($flow);


        $record = [
            'action_id' => $aid,
            'check_user_id' => $this->uid,
            'check_time' => time(),
            'content' => '提交申请',
            'flow_title' => $param['node_tit'],
            'zrbm_id' => $param['zrbm_id'],
        ];
        $record_id = Db::name('FlowRecord')->strict(false)->field(true)->insertGetId($record);
        add_log('add', $aid, $param);

        //给审核人发送消息通知
        $msg = [
            'from_uid' => $this->uid,
            'title' => Db::name('FlowType')->where('id', $param['type'])->value('title'),
            'action_id' => $aid
        ];
        sendMessage($qz_id, 21, $msg);
        return to_assign(0, "操作成功", ['is_mobile' => Session::get("is_mobile")]);
    }

    public function jsoncode($detail)
    {
        $jsoncode = unserialize($detail['jsoncode']);
        if (empty($jsoncode)) {
            return $detail;
        }
        foreach ($jsoncode as $k => $v) {
            if ($k == 'id') continue;
            if ($k == 'department_type') {
                $detail['dname'] =  Db::name("department")->where(['id' => $v])->value('title');
            }
            $detail[$k] = $v;
        }
        return $detail;
    }


    public function transtore_details($detail)
    {
        $jsoncode = unserialize($detail['jsoncode']);
        if (empty($jsoncode)) {
            return $detail;
        }
        $detail['aname'] = Db::name("admin")->where(['id' => $jsoncode['aid']])->value("name");
        $detail['o_dname'] = Db::name("Department")->where(['id' => $jsoncode['o_did']])->value("title");
        $detail['n_dname'] = Db::name("Department")->where(['id' => $jsoncode['n_did']])->value("title");
        $position = Db::name("position")->where([['id', 'in', $jsoncode['select']]])->column("title");
        $detail['position'] = implode(",", $position);
        return $detail;
    }

    public function lizhi_details($detail)
    {
        $jsoncode = unserialize($detail['jsoncode']);
        if (empty($jsoncode)) {
            return $detail;
        }
        $detail['aname'] = Db::name("admin")->where(['id' => $jsoncode['aid']])->value("name");

        $detail['dname'] = Db::name("Department")->where(['id' => $jsoncode['department_type']])->value("title");

        $detail['dz_name'] = $jsoncode['dz_name'];
        $detail['position'] = $jsoncode['position'];
        $detail['id_card_number'] = $jsoncode['id_card_number'];
        $detail['salary'] = $jsoncode['salary'];
        $detail['l_salary'] = $jsoncode['l_salary'];
        $detail['bank_number'] = $jsoncode['bank_number'];
        $detail['open_bank'] = $jsoncode['open_bank'];
        $detail['is_grant'] = isset($jsoncode['is_grant']) ? $jsoncode['is_grant'] : 0;
        $detail['is_gufen'] = isset($jsoncode['is_gufen']) ? $jsoncode['is_gufen'] : '2';
        $detail['payment_time'] = isset($jsoncode['payment_time']) ? $jsoncode['payment_time'] : '';
        $detail['payee'] = isset($jsoncode['payee']) ? $jsoncode['payee'] : '';
        $detail['is_black'] = isset($jsoncode['is_black']) ? $jsoncode['is_black'] : 2;


        return $detail;
    }

    public function zhuangang_details($detail)
    {
        $jsoncode = unserialize($detail['jsoncode']);
        if (empty($jsoncode)) {
            return $detail;
        }

        foreach ($jsoncode as $k => $v) {
            if ($k == 'detail_time' || $k == 'id') continue;
            $detail[$k] = $v;
        }

        $detail['aname'] = Db::name("admin")->where(['id' => $jsoncode['aid']])->value("name");

        if (isset($jsoncode['position'])) {
            $detail['position'] = $jsoncode['position'];
            $in_position = Db::name("position")->where([['id', 'in', $jsoncode['select']]])->column("title");
            $detail['in_position'] = implode(",", $in_position);
        }


        return $detail;
    }

    public function zhaopinjj_details($detail)
    {
        $jsoncode = unserialize($detail['jsoncode']);
        if (empty($jsoncode)) {
            return $detail;
        }

        foreach ($jsoncode as $k => $v) {
            if ($k == 'detail_time' || $k == 'id') continue;
            $detail[$k] = $v;
        }

        $detail['aname'] = Db::name("admin")->where(['id' => $jsoncode['aid']])->value("name");
        $detail['to_aname'] = Db::name("admin")->where(['id' => $jsoncode['to_aid']])->value("name");
        $detail['dname'] = Db::name("Department")->where(['id' => $jsoncode['department_type']])->value("title");


        return $detail;
    }

    /**
     * 催审功能
     * 向当前节点的审批人发送催审通知
     */
    public function urge()
    {
        $param = get_params();
        $approve_id = $param['id'] ?? 0;

        if (empty($approve_id)) {
            return to_assign(1, '参数错误');
        }

        // 查询审批记录
        $approve = Db::name('Approve')->where('id', $approve_id)->find();
        if (empty($approve)) {
            return to_assign(1, '审批记录不存在');
        }

        // 检查权限：只有申请人可以催审
        if ($approve['admin_id'] != $this->uid) {
            return to_assign(1, '只有申请人可以催审');
        }

        // 完善审批状态检查：只有待审批(0)或审批中(1)的流程可以催审
        // 状态说明：0-待审批, 1-审批中, 2-已通过, 3-已拒绝, 4-已撤销
        if (!in_array($approve['check_status'], [0, 1])) {
            $status_map = [
                0 => '待审批',
                1 => '审批中',
                2 => '已通过',
                3 => '已拒绝',
                4 => '已撤销'
            ];
            $current_status = isset($status_map[$approve['check_status']]) ? $status_map[$approve['check_status']] : '未知状态';
            return to_assign(1, "当前审批状态为「{$current_status}」，只有待审批或审批中的流程可以催审");
        }

        // 检查是否有当前审批人
        if (empty($approve['check_admin_ids'])) {
            return to_assign(1, '当前没有审批人，无法催审');
        }

        // 验证当前审批人是否存在且有效
        $check_admin_ids = explode(',', $approve['check_admin_ids']);
        $check_admin_ids = array_filter($check_admin_ids);

        if (empty($check_admin_ids)) {
            return to_assign(1, '当前审批人信息无效，无法催审');
        }

        // 检查审批人是否都是有效用户
        $valid_admin_count = Db::name('Admin')
            ->where('id', 'in', $check_admin_ids)
            ->where('status', 1) // 1为正常状态：状态：-1删除,0禁止登录,1正常,2离职
            ->count();

        if ($valid_admin_count == 0) {
            return to_assign(1, '当前审批人账户异常，无法催审');
        }

        // 获取流程类型名称
        $flow_type = Db::name('FlowType')->where('id', $approve['type'])->value('title');

        // 获取申请人信息
        $applicant = Db::name('Admin')->where('id', $approve['admin_id'])->find();

        // 准备消息数据
        $msg_data = [
            'action_id' => $approve_id,
            'from_uid' => $this->uid,
            'title' => $flow_type,
            'create_time' => date('Y-m-d H:i', $approve['create_time'])
        ];

        // 使用前面已验证的审批人ID列表

        // 发送催审消息（模板ID：100）
        $result = sendMessage($check_admin_ids, 100, $msg_data);

        if ($result) {
            return to_assign(0, '催审通知已发送');
        } else {
            return to_assign(1, '催审通知发送失败');
        }
    }

}