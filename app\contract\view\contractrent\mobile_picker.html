<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <title>选择租聘合同</title>
    <link rel="stylesheet" href="/static/assets/layui/css/layui.css">
    <style>
        body {
            background-color: #f5f5f5;
            margin: 0;
            padding: 0;
        }
        
        .mobile-header {
            background: #fff;
            padding: 16px 20px;
            border-bottom: 1px solid #e6e6e6;
            position: sticky;
            top: 0;
            z-index: 100;
        }
        
        .mobile-header h1 {
            margin: 0;
            font-size: 18px;
            font-weight: 500;
            color: #333;
            text-align: center;
        }
        
        .back-btn {
            position: absolute;
            left: 20px;
            top: 50%;
            transform: translateY(-50%);
            background: none;
            border: none;
            font-size: 16px;
            color: #666;
            cursor: pointer;
        }
        
        .search-container {
            background: #fff;
            padding: 16px 20px;
            margin-bottom: 10px;
        }
        
        .search-box {
            display: flex;
            gap: 10px;
            align-items: center;
        }
        
        .search-input {
            flex: 1;
            height: 40px;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 0 12px;
            font-size: 14px;
        }
        
        .search-btn {
            height: 40px;
            padding: 0 20px;
            background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
            color: #fff;
            border: none;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
            box-shadow: 0 2px 4px rgba(24, 144, 255, 0.2);
        }

        .search-btn:active {
            background: linear-gradient(135deg, #096dd9 0%, #1890ff 100%);
            transform: translateY(1px);
            box-shadow: 0 1px 2px rgba(24, 144, 255, 0.2);
        }
        
        .contract-list {
            padding: 0 20px calc(100px + env(safe-area-inset-bottom));
        }
        
        .contract-card {
            background: #fff;
            border-radius: 12px;
            margin-bottom: 12px;
            padding: 16px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.08);
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            border: 2px solid transparent;
        }

        .contract-card:active {
            transform: scale(0.98);
        }
        
        .contract-card.selected {
            border: 2px solid #1890ff;
            background: linear-gradient(135deg, #f0f9ff 0%, #e6f7ff 100%);
            box-shadow: 0 4px 12px rgba(24, 144, 255, 0.15);
        }
        
        .contract-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;
            padding-right: 30px; /* 为右侧单选按钮留出空间 */
        }

        .contract-left {
            display: flex;
            align-items: center;
            gap: 12px;
            flex: 1;
            min-width: 0; /* 允许flex项目收缩 */
        }

        .contract-code {
            font-size: 16px;
            font-weight: 500;
            color: #333;
            flex-shrink: 0;
        }

        .contract-status {
            font-size: 12px;
            color: #52c41a;
            background: linear-gradient(135deg, #f6ffed 0%, #e6f7ff 100%);
            padding: 3px 10px;
            border-radius: 14px;
            border: 1px solid #b7eb8f;
            flex-shrink: 0;
            font-weight: 500;
            box-shadow: 0 1px 2px rgba(82, 196, 26, 0.1);
        }
        
        .contract-info {
            margin-bottom: 8px;
        }
        
        .info-row {
            display: flex;
            margin-bottom: 6px;
            font-size: 14px;
        }
        
        .info-label {
            color: #666;
            width: 60px;
            flex-shrink: 0;
        }
        
        .info-value {
            color: #333;
            flex: 1;
            word-break: break-all;
        }
        
        .contract-amounts {
            display: flex;
            justify-content: space-between;
            padding-top: 12px;
            border-top: 1px solid #f0f0f0;
        }
        
        .amount-item {
            text-align: center;
        }
        
        .amount-label {
            font-size: 12px;
            color: #666;
            margin-bottom: 4px;
        }
        
        .amount-value {
            font-size: 16px;
            font-weight: 500;
            color: #ff4d4f;
        }
        
        .radio-icon {
            position: absolute;
            top: 16px;
            right: 16px;
            width: 22px;
            height: 22px;
            border: 2px solid #d9d9d9;
            border-radius: 50%;
            background: #fff;
            transition: all 0.2s;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }

        .contract-card.selected .radio-icon {
            border-color: #1890ff;
            background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
            box-shadow: 0 2px 6px rgba(24, 144, 255, 0.3);
        }

        .contract-card.selected .radio-icon::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 8px;
            height: 8px;
            background: #fff;
            border-radius: 50%;
        }
        
        .bottom-actions {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: #fff;
            padding: 12px 16px calc(20px + env(safe-area-inset-bottom));
            border-top: 1px solid #e6e6e6;
            display: flex;
            gap: 12px;
            box-shadow: 0 -2px 8px rgba(0,0,0,0.1);
        }



        .btn-cancel, .btn-confirm {
            flex: 1;
            height: 48px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
            position: relative;
            overflow: hidden;
        }

        .btn-cancel {
            background: #f8f9fa;
            color: #6c757d;
            border: 1px solid #dee2e6;
        }

        .btn-cancel:active {
            background: #e9ecef;
            transform: translateY(1px);
        }

        .btn-confirm {
            background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
            color: #fff;
            box-shadow: 0 2px 4px rgba(24, 144, 255, 0.3);
        }

        .btn-confirm:active {
            background: linear-gradient(135deg, #096dd9 0%, #1890ff 100%);
            transform: translateY(1px);
            box-shadow: 0 1px 2px rgba(24, 144, 255, 0.3);
        }

        .btn-confirm:disabled {
            background: #f5f5f5;
            color: #bfbfbf;
            cursor: not-allowed;
            box-shadow: none;
            border: 1px solid #d9d9d9;
        }

        .btn-confirm:disabled:active {
            transform: none;
        }
        
        .loading {
            text-align: center;
            padding: 40px 20px;
            color: #666;
            font-size: 14px;
        }

        .loading::before {
            content: '';
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 2px solid #f3f3f3;
            border-top: 2px solid #1890ff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: 8px;
            vertical-align: middle;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* 小屏幕设备优化 */
        @media (max-width: 360px) {
            .contract-header {
                padding-right: 26px; /* 小屏幕上减少右边距 */
            }

            .contract-left {
                gap: 8px; /* 减少间距 */
            }

            .contract-code {
                font-size: 15px; /* 稍微减小字体 */
            }

            .contract-status {
                font-size: 11px; /* 稍微减小字体 */
                padding: 1px 6px; /* 减少内边距 */
            }

            .radio-icon {
                width: 20px;
                height: 20px;
                top: 14px;
                right: 14px;
            }
        }
        
        .no-data {
            text-align: center;
            padding: 40px 20px;
            color: #999;
        }
        
        .load-more {
            text-align: center;
            padding: 20px;
        }
        
        .load-more-btn {
            background: #f5f5f5;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            color: #666;
            cursor: pointer;
        }
    </style>
</head>
<body>
    <div class="mobile-header">
        <button class="back-btn" onclick="goBack()">← 返回</button>
        <h1>选择租聘合同</h1>
    </div>
    
    <div class="search-container">
        <div class="search-box">
            <input type="text" class="search-input" id="searchInput" placeholder="输入合同编号、房东姓名或地址">
            <button class="search-btn" onclick="searchContracts()">搜索</button>
        </div>
    </div>
    
    <div class="contract-list" id="contractList">
        <div class="loading">正在加载合同数据...</div>
    </div>
    
    <div class="bottom-actions">
        <button class="btn-cancel" onclick="goBack()">取消</button>
        <button class="btn-confirm" id="confirmBtn" onclick="confirmSelection()" disabled>确定选择</button>
    </div>

    <script src="/static/assets/layui/layui.js"></script>
    <script>
        let selectedContract = null;

        // 页面加载时检查是否应该在历史记录中
        window.addEventListener('load', function() {
            // 如果是通过replace方式进入的，确保不会在历史记录中留下痕迹
            const returnUrl = sessionStorage.getItem('contractRentPickerReturnUrl');
            if (returnUrl && window.history && window.history.replaceState) {
                // 替换当前历史状态，确保返回时不会回到这个页面
                window.history.replaceState({isContractPicker: true}, document.title, window.location.href);
            }
        });

        // 监听浏览器返回按钮，确保正确的导航行为
        window.addEventListener('popstate', function(event) {
            const returnUrl = sessionStorage.getItem('contractRentPickerReturnUrl');
            if (returnUrl) {
                // 如果用户按了浏览器返回按钮，直接跳转到原页面
                sessionStorage.removeItem('contractRentPickerReturnUrl');
                sessionStorage.removeItem('contractRentPickerCallback');
                window.location.replace(returnUrl);
            }
        });
        let currentPage = 1;
        let isLoading = false;
        let hasMore = true;
        
        // 页面加载完成后获取合同数据
        document.addEventListener('DOMContentLoaded', function() {
            loadContracts();
            
            // 搜索框回车事件
            document.getElementById('searchInput').addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    searchContracts();
                }
            });
        });
        
        // 加载合同数据
        function loadContracts(keywords = '', page = 1, append = false) {
            if (isLoading) return;
            isLoading = true;
            
            const params = new URLSearchParams({
                page: page,
                limit: 20
            });
            
            if (keywords) {
                params.append('keywords', keywords);
            }
            
            fetch('/contract/contractrent/mobile_picker?' + params.toString(), {
                method: 'GET',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => response.json())
            .then(data => {
                isLoading = false;
                
                if (data.code === 0) {
                    renderContracts(data.data, append);
                    hasMore = data.data.length >= 20;
                    currentPage = page;
                } else {
                    showError('加载失败：' + (data.msg || '未知错误'));
                }
            })
            .catch(error => {
                isLoading = false;
                showError('网络错误，请重试');
                console.error('Error:', error);
            });
        }
        
        // 渲染合同列表
        function renderContracts(contracts, append = false) {
            const listContainer = document.getElementById('contractList');
            
            if (!append) {
                listContainer.innerHTML = '';
            }
            
            if (contracts.length === 0 && !append) {
                listContainer.innerHTML = '<div class="no-data">暂无合同数据</div>';
                return;
            }
            
            contracts.forEach(contract => {
                const cardHtml = createContractCard(contract);
                listContainer.insertAdjacentHTML('beforeend', cardHtml);
            });
            
            // 添加加载更多按钮
            if (hasMore && !document.querySelector('.load-more')) {
                listContainer.insertAdjacentHTML('beforeend', 
                    '<div class="load-more"><button class="load-more-btn" onclick="loadMore()">加载更多</button></div>'
                );
            }
        }
        
        // 创建合同卡片HTML
        function createContractCard(contract) {
            return `
                <div class="contract-card" onclick="selectContract(${contract.id})" data-id="${contract.id}">
                    <div class="radio-icon"></div>
                    <div class="contract-header">
                        <div class="contract-left">
                            <div class="contract-code">${contract.code}</div>
                            <div class="contract-status">生效中</div>
                        </div>
                    </div>
                    <div class="contract-info">
                        <div class="info-row">
                            <div class="info-label">房东：</div>
                            <div class="info-value">${contract.display_name || ''}</div>
                        </div>
                        <div class="info-row">
                            <div class="info-label">电话：</div>
                            <div class="info-value">${contract.party_a_mobile || ''}</div>
                        </div>
                        <div class="info-row">
                            <div class="info-label">地址：</div>
                            <div class="info-value">${contract.address || ''}</div>
                        </div>
                    </div>
                    <div class="contract-amounts">
                        <div class="amount-item">
                            <div class="amount-label">月租金</div>
                            <div class="amount-value">¥${contract.rent_amount_formatted}</div>
                        </div>
                        <div class="amount-item">
                            <div class="amount-label">押金</div>
                            <div class="amount-value">¥${contract.deposit_amount_formatted}</div>
                        </div>
                    </div>
                </div>
            `;
        }
        
        // 选择合同
        function selectContract(contractId) {
            // 移除之前的选中状态
            document.querySelectorAll('.contract-card').forEach(card => {
                card.classList.remove('selected');
            });
            
            // 添加当前选中状态
            const selectedCard = document.querySelector(`[data-id="${contractId}"]`);
            if (selectedCard) {
                selectedCard.classList.add('selected');
                
                // 获取合同数据
                selectedContract = getContractData(contractId);
                
                // 启用确定按钮
                document.getElementById('confirmBtn').disabled = false;
            }
        }
        
        // 获取合同数据
        function getContractData(contractId) {
            const card = document.querySelector(`[data-id="${contractId}"]`);
            if (!card) return null;
            
            const codeElement = card.querySelector('.contract-code');
            const infoRows = card.querySelectorAll('.info-row');
            const amountValues = card.querySelectorAll('.amount-value');
            
            return {
                id: contractId,
                code: codeElement.textContent,
                party_a_name: infoRows[0]?.querySelector('.info-value')?.textContent || '',
                party_a_mobile: infoRows[1]?.querySelector('.info-value')?.textContent || '',
                address: infoRows[2]?.querySelector('.info-value')?.textContent || '',
                rent_amount: amountValues[0]?.textContent.replace('¥', '').replace(',', '') || '0',
                deposit_amount: amountValues[1]?.textContent.replace('¥', '').replace(',', '') || '0'
            };
        }
        
        // 搜索合同
        function searchContracts() {
            const keywords = document.getElementById('searchInput').value.trim();
            currentPage = 1;
            hasMore = true;
            loadContracts(keywords, 1, false);
        }
        
        // 加载更多
        function loadMore() {
            if (hasMore && !isLoading) {
                const keywords = document.getElementById('searchInput').value.trim();
                loadContracts(keywords, currentPage + 1, true);
                
                // 移除加载更多按钮
                const loadMoreBtn = document.querySelector('.load-more');
                if (loadMoreBtn) {
                    loadMoreBtn.remove();
                }
            }
        }
        
        // 确认选择
        function confirmSelection() {
            if (!selectedContract) {
                alert('请先选择一个合同');
                return;
            }
            
            // 将选中的合同数据存储到sessionStorage
            sessionStorage.setItem('selectedContractData', JSON.stringify(selectedContract));
            
            // 返回上一页
            goBack();
        }
        
        // 返回上一页
        function goBack() {
            const returnUrl = sessionStorage.getItem('contractRentPickerReturnUrl');
            if (returnUrl) {
                sessionStorage.removeItem('contractRentPickerReturnUrl');
                // 使用replace返回，替换当前历史记录
                window.location.replace(returnUrl);
            } else {
                history.back();
            }
        }
        
        // 显示错误信息
        function showError(message) {
            document.getElementById('contractList').innerHTML = 
                `<div class="no-data" style="color: #ff4d4f;">${message}</div>`;
        }
    </script>
</body>
</html>
