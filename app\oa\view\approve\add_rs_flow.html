<table class="layui-table layui-table-form" style="margin-top:12px">
	<tr>
		<td class="layui-td-gray-2">选择审批流程<font>*</font></td>
		<td>
			<select name="flow_id" lay-verify="required" lay-filter="flowtype" lay-reqText="请选择审批流程" id="flow_id">
				{volist name="flows" id="vo"}
				<option value="{$vo.id}" title="{$vo.check_type}" {if condition="(isset($detail) && $detail['flow_id'] == $vo.id )"}selected{/if}>{$vo.name}</option>
				{/volist}
			</select>
		</td>
	</tr>
	<tr id="flow_tr">
		<td class="layui-td-gray">审核人<font>*</font></td>
		<td>
			<input type="text" name="check_admin_name" value="" autocomplete="off" placeholder="请选择审核人" lay-verify="required" lay-reqText="请选择审核人" class="layui-input picker-one" readonly><input type="hidden" name="check_admin_ids" value="" readonly>
		</td>
	</tr>

	{eq name="$id" value="0"}
	<tr>
		<td class="layui-td-gray">抄送人</td>
		<td>
			<input type="text" name="copy_names" value="" autocomplete="off" placeholder="请选择审核人" class="layui-input {if condition='$code_edit_flag != false'}picker-more{/if} copy_names" readonly>
			<input type="hidden" name="copy_uids" value="" readonly class="copy_uids">
		</td>
	</tr>
	{else/}
	<tr>
		<td class="layui-td-gray">抄送人</td>
		<td>
			<input type="text" name="copy_names" value="{$detail.copy_names}" autocomplete="off" placeholder="请选择审核人" class="layui-input picker-more copy_names" readonly>
			<input type="hidden" name="copy_uids" value="{$detail.copy_uids}" readonly class="copy_uids">
		</td>
	</tr>
	{/eq}
</table>

<div style="padding: 10px 0">
	<input type="hidden" name="id" value="{$id}">
	<input type="hidden" name="type" value="{$type}">
	<button class="layui-btn layui-btn-normal" lay-submit="" lay-filter="webform" id="webform">立即提交</button>
</div>
<script>
function flowStep(){
		let tool=layui.tool,form = layui.form,upload = layui.upload,employeepicker = layui.employeepicker;

		shenheren();

		form.on('select(flowtype)', function(data){
			shenheren();
		});

		$('#input_payee_name').on('input', function() {
			get_account(this.value)
		});

		//获取输入人的账号信息
		function get_account(payee_name){
			$.ajax({
				type: 'post',
				url: '/api/index/get_account',           //数据接口
				data: {
					payee_name: payee_name,
				},               //搜素框里的值
				dataType: 'json',
				success: function (data) {
					if (data){
						$('[name="bank_number"]').val(data.bank_number)
						$('[name="bank_name"]').val(data.bank_name)
					}else{
						$('[name="bank_number"]').val('')
						$('[name="bank_name"]').val('')
					}
				}
			});
		}

		function shenheren(){
			var check_type = 1;
			var formHtml='<td class="layui-td-gray">审核人<font>*</font></td>\
		<td colspan="5">\
			<input type="text" name="check_admin_name" value="" autocomplete="off" placeholder="请选择审核人" lay-verify="required" lay-reqText="请选择审核人" class="layui-input picker-one"><input type="hidden" name="check_admin_ids" value="">\
		</td>';
			$.ajax({
				url: "/api/index/get_flow_users",
				type:'get',
				data:{id:$('[name="flow_id"]').val()},
				success: function (e) {
					if (e.code == 0) {

						var flowLi='';
						var flow_data = e.data.flow_data;
						if(e.data.copy_uids && e.data.copy_uids !=''){
							$('[name="copy_names"]').val(e.data.copy_unames);
							$('[name="copy_uids"]').val(e.data.copy_uids.split(','));
						}
						if(check_type == 1 || check_type == 3){
							for(var a=0;a<flow_data.length;a++){
								var userList='',sign_type = '';
								if(check_type == 1){
									if(flow_data[a].flow_type==1){
										userList+= '<li style="padding:3px 0">当前部门负责人</li>';
									}
									else if(flow_data[a].flow_type==2){
										userList+= '<li style="padding:3px 0">上级部门负责人</li>';
									}
									else{
										if(flow_data[a].flow_type==3 || flow_data[a].flow_type==5){
											sign_type= ' <span class="layui-badge layui-bg-blue">或签</span>';
										}
										if(flow_data[a].flow_type==4 || flow_data[a].flow_type==6){
											sign_type= ' <span class="layui-badge layui-bg-blue">会签</span>';
										}

										if (flow_data[a].user_id_info.length == 0){
											userList+= '<li style="padding:3px 0">--</li>';
										}
										for(var b=0;b<flow_data[a].user_id_info.length;b++){
											userList+= '<li style="padding:3px 0"><img src="'+flow_data[a].user_id_info[b].thumb+'" style="width:24px; height:24px; border-radius:50%; margin-right:8px;" />'+flow_data[a].user_id_info[b].name+'</li>';
										}

										if(flow_data[a].flow_type==5 || flow_data[a].flow_type==6){
											userList= '<li style="padding:3px 0">'+flow_data[a].flow_dep_info.title+'</li>';
										}

										if(flow_data[a].flow_type==7){
											flow_data[a].flow_title = '门店负责人';
											userList = '<li style="padding:3px 0">门店负责人</li>';
										}

									}
								}
								else if(check_type == 3){
									sign_type= ' <span class="layui-badge layui-bg-blue">'+flow_data[a].flow_title+'</span>'
									for(var b=0;b<flow_data[a].user_id_info.length;b++){
										userList+= '<li style="padding:3px 0"><img src="'+flow_data[a].user_id_info[b].thumb+'" style="width:24px; height:24px; border-radius:50%; margin-right:8px;" />'+flow_data[a].user_id_info[b].name+'</li>';
									}

								}
								flowLi+='<li class="layui-timeline-item">\
								<i class="layui-icon layui-timeline-axis">&#xe63f;</i>\
								<div class="layui-timeline-content">\
								  <p class="layui-timeline-title"><strong>'+flow_data[a].flow_title+'</strong>'+sign_type+'</p>\
								  <ul>'+userList+'</ul>\
								</div>\
							</li>';
							}
							formHtml = '<td class="layui-td-gray">审批流程</td>\
									<td colspan="7">\
										<ul id="flowList" class="layui-timeline">'+flowLi+'</ul>\
									</td>';
							$('#flow_tr').html(formHtml);
						}
					}
				},error: function (e){
					$('#flow_tr').empty();
				}
			})
		}

		//监听提交
		form.on('submit(webform)', function(data){

			$("#webform").addClass('layui-btn-disabled');

			if (data.field.con == 'lizhi'){
				if (data.field.is_gufen == '' || data.field.is_gufen == null){
					layer.msg("请选择是否有股份");
					return false;
				}
			}

			if (data.field.con == 'ruzhi'){
				let edu_background = $("#edu_background").val();
				let file_ids_edu = $('[name="file_ids_edu"]').val()

				if ((edu_background != '初中及以下' || edu_background != '高中') && !file_ids_edu){
					layer.msg("请上传学历证书附件");
					return false;
				}

				let certificate = $('[name="certificate"]').val();
				let file_ids = $('[name="file_ids"]').val();
				if (certificate != '无' && !file_ids){
					layer.msg("请上传技能证书附件");
					return false;
				}
			}


			if (data.field.con == 'qingjia'){
				let is_days = $("#is_days").text()
				let duration = $("#duration").val()
				console.log(is_days)
				console.log(duration)
				if (is_days != null && is_days != '' && duration > is_days){
					layer.msg("可用假期不足");
					return false;
				}
			}

			let flow_text = $("#flow_id option:selected").text();

			if (data.field.con == 'lizhi'){

				let payment_time = $('[name="payment_time"]').val()

				let currentDate = new Date();
				let year = currentDate.getFullYear();
				let month = currentDate.getMonth() + 1; // 月份从0开始，需要加1
				if (month < 10){
					month = `0${month}`
				}
				const day = currentDate.getDate();
				let today = year + "-" + month + "-" + day;

				if (payment_time == today && flow_text.includes('无需')){
					layer.msg("付款日期为今天，请选择及时打款审批!!!");
					// layer.confirm(`打款日期为今天，选择的审批流程是 【${flow_text}】?请选择及时发放审批`, {
					// 	icon: 3,
					// 	title: '提示'
					// }, function (index) {
					// 	add_flow(data)
					// 	layer.close(index);
					// });
					return false;
				}
			}
			add_flow(data)
			return false;
		});


		function add_flow(data){

			if (data.field.payee_name){
				data.field.payee_name = data.field.payee_name.replace(/\s+/g, '')
			}

			if (data.field.bank_number){
				data.field.bank_number = data.field.bank_number.replace(/\s+/g, '')
			}

			if (data.field.bank_name){
				data.field.bank_name = data.field.bank_name.replace(/\s+/g, '')
			}

			if (data.field.open_bank){
				data.field.open_bank = data.field.open_bank.replace(/\s+/g, '')
			}

			$.ajax({
				url: "/oa/approve/add",
				type:'post',
				data:{...data.field},
				success: function (e) {
					layer.msg(e.msg);
					let is_mobile = e.data.is_mobile
					if (e.code == 0) {
						if (is_mobile != null && is_mobile != ""){
							window.location.pathname = "/oa/approve/list";
						}else{
							tool.sideClose(1000);
						}
					}
					$("#webform").removeClass('layui-btn-disabled');
				},error: function (e){
					$("#webform").removeClass('layui-btn-disabled');
				}
			})
		}

	$('body').on('click', '.file-view-img', function () {

		let href = $(this).parents(".layui-row");

		var data = []

		href.find('.file-view-img').map(function() {
			data.push({
				"alt": i,
				"pid": i,
				"src": $(this).attr('data-href'),
			})
		});

		if (data) {
			layer.photos({
				photos: {
					"title": "Photos Demo",
					"start": 0,
					"data": data
				},
				footer: true // 是否显示底部栏 --- 2.8.16+
			});
		}


	});

	}

function getDate( i ){
	const currentDate = new Date();
	currentDate.setMonth(currentDate.getMonth() + i);

	const year = currentDate.getFullYear();
	const month = currentDate.getMonth() + 1;
	const day = currentDate.getDate();
	return [year,month,day]
}

</script>
