{extend name="../../base/view/common/base" /}
<!-- 主体 -->
{block name="body"}
<form class="layui-form p-4">
	<h3 class="pb-3">邮箱配置</h3>
	<table class="layui-table layui-table-form">
		<tr>
			<td class="layui-td-gray-2">SMTP地址<font>*</font>
			</td>
			<td>
				<input type="hidden" name="id" value="{$id}">
				<input type="text" name="smtp" lay-verify="required" autocomplete="off" placeholder="请输入SMTP服务器地址" lay-reqText="请输入SMTP服务器地址" class="layui-input" value="{$config.smtp|default=''}" style="width:240px;display: inline-block;">
				<span style="color:#999; font-size:12px;">如：QQ邮箱的SMTP服务器地址是smtp.qq.com，163邮箱的SMTP服务器地址是smtp.163.com</span>
			</td>
		</tr>
		<tr>
			<td class="layui-td-gray-2">协议端口号<font>*</font>
			</td>
			<td>
				<input type="text" name="smtp_port" lay-verify="required" autocomplete="off" placeholder="请输入端口" lay-reqText="请输入端口" class="layui-input" value="{$config.smtp_port|default=''}"  style="width:240px;display: inline-block;"><span style="color:#999; font-size:12px;">如：QQ邮箱的ssl协议方式端口号是465/587，163邮箱的ssl协议方式端口号是465/994</span>
			</td>
		</tr>
		<tr>
			<td class="layui-td-gray">邮箱账户<font>*</font></td>
			<td><input type="text" name="smtp_user" autocomplete="off" lay-verify="required" placeholder="请输入邮箱用户名" lay-reqText="请输入邮箱用户名" class="layui-input" value="{$config.smtp_user|default=''}"style="width:240px;display: inline-block;"><span style="color:#999; font-size:12px;">如：<EMAIL></span>
			</td>
		</tr>
		<tr>
			<td class="layui-td-gray">邮箱密码<font>*</font></td>
			<td><input type="password" name="smtp_pwd" lay-verify="required" autocomplete="off" placeholder="请输入邮箱密码" class="layui-input" value="{$config.smtp_pwd|default=''}" style="width:240px;display: inline-block;"><span style="color:#999; font-size:12px;">不一定是登录密码，如QQ邮箱的是第三方授权登录码，要自己去开启，在邮箱的设置->账户->POP3/IMAP/SMTP/Exchange/CardDAV/CalDAV服务</span>
			</td>
		</tr>

		<tr>
			<td class="layui-td-gray">发送人<font>*</font></td>
			<td>
				<input type="text" name="from" autocomplete="off" lay-verify="required" placeholder="请输入要显示的发送者" lay-reqText="请输入要显示的发送者" class="layui-input" value="{$config.from|default=''}" style="width:240px; display: inline-block;">
				<span style="color:#999; font-size:12px;">用于展示给发送方，如：仲正堂CMS系统管理员</span>
			</td>
		</tr>
		<tr>
			<td class="layui-td-gray">显示的邮箱<font>*</font></td>
			<td>
				<input type="text" name="email" lay-verify="required" autocomplete="off" placeholder="请输入要显示的发送者邮箱" lay-reqText="请输入要显示的发送者邮箱" class="layui-input" value="{$config.email|default=''}" style="width:240px;display: inline-block;">
				<span style="color:#999;font-size:12px;">可以不同于上面的账户，用于展示给发送方的邮箱，如：<EMAIL></span>
			</td>
		</tr>
		<tr>
			<td class="layui-td-gray">邮件模板</td>
			<td><textarea name="template" placeholder="" class="layui-textarea" id="container">{$config.template|default=''}</textarea>
			</td>
		</tr>
	</table>
	<div class="py-3">
		<button class="layui-btn layui-btn-normal" lay-submit="" lay-filter="webform">立即提交</button>
		<button lay-event="email" class="layui-btn">发送测试</button>
		<button type="reset" class="layui-btn layui-btn-primary">重置</button>
	</div>
</form>
{/block}
<!-- /主体 -->

<!-- 脚本 -->
{block name="script"}
<script>
	const moduleInit = ['tool', 'tinymce'];
	function gouguInit() {
		var form = layui.form, tool = layui.tool, tinymce = layui.tinymce;

		var edit = tinymce.render({
			selector: "#container",
			height: 320
		});
		//监听提交
		form.on('submit(webform)', function (data) {
			data.field.template = tinyMCE.editors['container'].getContent();
			let callback = function (e) {
				layer.msg(e.msg);
				if (e.code == 0) {
					tool.sideClose(1000);
				}
			}
			tool.post("/home/<USER>/edit", data.field, callback);
			return false;
		});

		$('body').on('click', '[lay-event="email"]', function () {
			layer.prompt({
				formType: 0,
				value: '',
				title: '输入接收测试邮件的邮箱',
				id: 'email_to'
			}, function (value, index, elem) {
				var isEmail = /^\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*$/;
				if (value.length < 6 || !(isEmail.test(value))) {
					layer.tips('请正确输入邮箱', elem);
					return false;
				}
				$.ajax({
					url: "/api/index/email_test",
					data: { email: value },
					type: "post",
					beforeSend: function () {
						// 禁用按钮防止重复提交
						$("#email_to input").val('');
					},
					success: function (e) {
						layer.msg(e.msg);
						if (e.code == 0) {
							layer.close(index);
						}
					}
				})
			});
			return false;
		})
	}
</script>
{/block}
<!-- /脚本 -->