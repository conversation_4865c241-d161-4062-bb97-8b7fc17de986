{extend name="../../base/view/common/base" /}
{block name="style"}
<style>
.layui-form-pane .layui-form-label{color:#999; width:80px; padding:8px 3px;}
.layui-form-item .layui-inline{margin-right:3px; margin-bottom:10px;}
.layui-form-item{margin-bottom:5px;}
.layui-form-item .layui-btn-danger{display:none; margin-top:-8px}
.layui-form-item:hover .layui-btn-danger{display:inline-block;}

.select-1,.select-2,.select-5,.select-6,.select-7,.select-8{display:none;}

.select-1-1,.select-2-1,.select-3-1,.select-4-1 ,.select-7-1,.select-8-1{display:none;}
</style>
{/block}
<!-- 主体 -->
{block name="body"}
<form class="layui-form p-4">
	<h3 class="pb-3">审批流程</h3>
	{eq name="$id" value="0"}
	<table class="layui-table">
		<tr>
			<td class="layui-td-gray">流程名称<font>*</font></td>
			<td>
				<input type="text" name="name" autocomplete="off" placeholder="请输入审批流程名称" lay-verify="required" lay-reqText="请输入审批流程名称" class="layui-input">
			</td>
			<td class="layui-td-gray">应用模块<font>*</font></td>
			<td>
				<select name="type" lay-filter="type" lay-verify="required" lay-reqText="请选择应用模块">
				  <option value="">--请选择--</option>
				  {volist name="$type" id="vo"}
				  <option value="{$vo.id}">{$vo.title}</option>
				  {/volist}
				</select>
			</td>
			<td class="layui-td-gray">审批类型<font>*</font></td>
			<td>
				<select name="flow_cate" lay-filter="flowcate" lay-verify="required" lay-reqText="请选择审批类型">
				  <option value="">--请先选择审批类型--</option>
				</select>
			</td>
		</tr>
		<tr>
			<td class="layui-td-gray">应用部门</td>
			<td colspan="5">
				<div class="layui-input-inline" style="width:360px;">
					<select id="department_ids" name="department_ids" xm-selected="" xm-select="select1" xm-select-skin="default"></select>
				</div>
				<span class="red" style="font-size:12px;">（如果不选，默认是全公司）</span>
			</td>
		</tr>
		<tr>
			<td class="layui-td-gray">应用部门属性</td>
			<td colspan="5">
				<div class="layui-input-inline" style="width:360px;">
					<select id="department_remark" name="department_remark" >
						<option value="">请选择</option>
						<option value="门店">门店</option>
						<option value="后勤">后勤</option>
					</select>
				</div>
				<span class="red" style="font-size:12px;">（如果不选，默认是全部部门属性）</span>
			</td>
		</tr>
		<tr>
			<td class="layui-td-gray">应用岗位职称</td>
			<td colspan="5">
				<div class="layui-input-inline" style="width:360px;">
					<select id="position_ids" name="position_ids" xm-selected="" xm-select="select2" xm-select-skin="default"></select>
				</div>
				<span class="red" style="font-size:12px;">（如果不选，默认是全部岗位）</span>
			</td>
		</tr>

		<tr id="position_ids_js_tr" hidden="hidden">
			<td class="layui-td-gray">晋升岗位</td>
			<td colspan="5" >
				<div class="layui-input-inline" style="width:360px;">
					<select id="position_ids_js" name="position_ids_js" xm-selected="" xm-select="selcted_js" xm-select-skin="default"></select>
				</div>
				<span class="red" style="font-size:12px;">（如果不选，默认是全部岗位）</span>
			</td>
		</tr>

		<tr>
			<td class="layui-td-gray">指定员工</td>
			<td colspan="5">
				<div class="layui-input-inline" style="width:360px;">
					<input type="text" name="employee_names[]" value="" autocomplete="off" readonly class="layui-input picker-more" placeholder="选择指定员工">
					<input type="hidden" name="employee_ids[]" value="">
				</div>
				<span class="red" style="font-size:12px;">（如果不选，默认是全部员工）</span>
			</td>
		</tr>

		<tr id="pay_amount_tr" hidden="hidden">
			<td class="layui-td-gray">付款金额</td>
			<td colspan="5" >
				<div class="layui-input-inline" style="width:360px;">
					<input type="text" name="pay_amount" placeholder="付款金额" class="layui-input" />
				</div>
				<span class="red" style="font-size:12px;">（如果不填写，不限制付款金额）</span>
			</td>
		</tr>

		<tr id="pay_type_tr" hidden="hidden">
			<td class="layui-td-gray">付款类型</td>
			<td colspan="5" >
				<div class="layui-input-inline" style="width:360px;">
					<select id="pay_types" name="pay_types" xm-selected="" xm-select="select4" xm-select-skin="default"></select>
				</div>
				<span class="red" style="font-size:12px;">（如果不选，默认是全部付款类型）</span>
			</td>
		</tr>


		<tr id="vacation_type_tr" hidden="hidden">
			<td class="layui-td-gray">请假类型</td>
			<td colspan="5">
				<div class="layui-input-inline" style="width:360px;">
					<select id="vacation_type" name="vacation_type" xm-selected="" xm-select="select3" xm-select-skin="default"></select>
				</div>
				<span class="red" style="font-size:12px;">（如果不选，默认是全部类型）</span>
			</td>
		</tr>
		<tr id="time_conditions_tr" hidden="hidden">
			<td class="layui-td-gray">请假时长</td>
			<td colspan="5">
				<input type="text" name="time_conditions" placeholder="请假时长" class="layui-input"></input>
			</td>
		</tr>
		<tr>
			<td class="layui-td-gray">流程说明</td>
			<td colspan="5">
				<textarea name="remark" placeholder="请输入流程说明" class="layui-textarea"></textarea>
			</td>
		</tr>
		<tr>
			<td class="layui-td-gray-2">审批流类型<font>*</font></td>
			<td colspan="5">
				<input type="radio" name="check_type" lay-filter="checktype" value="1" title="固定审批流" checked>
				<input type="radio" name="check_type" lay-filter="checktype" value="2" title="自由审批流">
				<input type="radio" name="check_type" lay-filter="checktype" value="3" title="可回退的审批流">
			</td>
		</tr>
		<tr id="flowTr1">
			<td class="layui-td-gray">审批流程<font>*</font></td>
			<td colspan="5">
				<div id="flowList1">
					<div class="layui-form-item layui-form-pane">
						<div class="layui-inline">
						  <label class="layui-form-label">第1级</label>
						  <div class="layui-input-inline">
							<select name="flowType[]" lay-filter="flowtype">
								<option value="1">当前部门负责人</option>
								<option value="2">上一级部门负责人</option>
								<option value="3">指定人员(多人或签)</option>
								<option value="4">指定人员(多人会签)</option>
								<option value="5">指定部门(多人会签)</option>
								<option value="6">指定部门(多人或签)</option>
								<option value="7">门店负责人</option>
								<option value="8">股东店长</option>
							</select>
						  </div>
						</div>
						<div class="layui-inline">
							<label class="layui-form-label">节点名称</label>
							<div class="layui-input-inline" style="width:360px;">
								<input type="text" name="flowTitle[]" value="" autocomplete="off" class="layui-input">
								<input type="hidden" name="ids[]" value="" autocomplete="off" class="layui-input">
							</div>
						</div>

						<div class="layui-inline select-1">
						  <label class="layui-form-label">指定人员</label>
						  <div class="layui-input-inline" style="width:360px;">
							<input type="text" name="flowUnamesA[]" value="" autocomplete="off" readonly class="layui-input picker-more">
							<input type="hidden" name="flowUidsA[]" value="">
						  </div>
						</div>

						<div class="layui-inline select-1-1">
							<label class="layui-form-label">指定部门</label>
							<div class="layui-input-inline" style="width:360px;">
								<select name="flow_dep[]" lay-filter="select_flow_dep" lay-search="">
									<option value="">--请选择--</option>
									{volist name=":getmd('后勤')" id="vo"}
									<option value="{$vo.id}">{$vo.title}</option>
									{/volist}
								</select>
							</div>
						</div>

					</div>
				</div>
				<span id="addFlow1" class="layui-btn layui-btn-xs layui-btn-normal">+ 添加审批层级</span>
				<div style="padding:10px; margin-top:10px; font-size:12px; background-color:#fffcf0">
					<p><strong>温馨提示</strong></p>
					<p>1、当选择<strong> “当前部门负责人” </strong>审批时。系统仅会通知当前部门的负责人。</p>
					<p>2、当选择<strong> “上一级部门负责人” </strong>审批时。系统仅会通知当前部门的上一级部门的负责人。</p>
					<p>3、当选择<strong> “指定人员(多人或签)” </strong>时，表示指定用户中任意一人审批即可，可单选或多选。</p>
					<p>4、当选择<strong> “指定人员(多人会签)” </strong>时，表示指定人员中所有人都需要审批，可单选或多选。</p>
					<p>5、如果指定用户没有分配查看审批模块的功能权限，系统会通知其审批，但是他无法查看此审批数据信息。</p>
				</div>
			</td>
		</tr>
		<tr id="flowTr2" style="display:none">
			<td class="layui-td-gray">审批流程</td>
			<td colspan="5">
				<div style="padding:10px; font-size:12px; background-color:#fffcf0">
					<p><strong>温馨提示</strong></p>
					<p>无需配置审批人，审批时，根据实际情况选择审批人即可，自由度最高。</p>
				</div>
			</td>
		</tr>
		<tr id="flowTr3" style="display:none;">
			<td class="layui-td-gray">审批流程<font>*</font></td>
			<td colspan="5">
				<div id="flowList3">
					<div class="layui-form-item layui-form-pane">
						<div class="layui-inline">
						  <label class="layui-form-label">第1级</label>
						  <div class="layui-input-inline">
							<input type="text" name="flowName[]" value="" autocomplete="off" placeholder="请输入流程名称" class="layui-input">
						  </div>
						</div>
						<div class="layui-inline select-3">
						  <label class="layui-form-label">指定人员</label>
						  <div class="layui-input-inline" style="width:360px;">
							<input type="text" name="flowUnamesB[]" value="" autocomplete="off" readonly class="layui-input picker-one">
							<input type="hidden" name="flowUidsB[]" value="">
						  </div>
						</div>
					</div>
				</div>
				<span id="addFlow3" class="layui-btn layui-btn-xs layui-btn-normal">+ 添加审批层级</span>
				<div style="padding:10px; margin-top:10px; font-size:12px; background-color:#fffcf0">
					<p><strong>温馨提示</strong></p>
					<p>1、<strong>指定人员</strong>单选。后期审批的时候，审批人只能该指定人员。</p>
					<p>2、该审批流程可<strong>回退</strong>，当拒绝审核时，会自动回退到上一位审批人节点。</p>
				</div>
			</td>
		</tr>
		<tr>
			<td class="layui-td-gray">抄送人</td>
			<td colspan="5">
				<input type="text" name="copy_unames" value="" autocomplete="off" readonly class="layui-input picker-more">
				<input type="hidden" name="copy_uids" value="">
			</td>
		</tr>
	</table>
	{else/}
	<table class="layui-table">
		<tr>
			<td class="layui-td-gray">流程名称<font>*</font></td>
			<td>
				<input type="text" name="name" value="{$detail.name}" autocomplete="off" placeholder="请输入审批流程名称" lay-verify="required" lay-reqText="请输入审批流程名称" class="layui-input">
			</td>
			<td class="layui-td-gray">应用模块<font>*</font></td>
			<td>
				<select name="type" lay-filter="type" lay-verify="required" lay-reqText="请选择应用模块">
				  <option value="">--请选择--</option>
				  {volist name="$type" id="vo"}
				  <option value="{$vo.id}" {eq name="$detail.type" value="$vo.id"}selected=""{/eq}>{$vo.title}</option>
				  {/volist}
				</select>
			</td>
			<td class="layui-td-gray">审批类型<font>*</font></td>
			<td>
				<select name="flow_cate" lay-filter="flowcate" lay-verify="required" lay-reqText="请选择审批类型">
				  <option value="">--请选择--</option>
				  {volist name="$detail.flow_cate_list" id="vo"}
				  <option value="{$vo.id}" {eq name="$detail.flow_cate" value="$vo.id"}selected=""{/eq}>{$vo.title}</option>
				  {/volist}
				</select>
			</td>
		</tr>
		<tr>
			<td class="layui-td-gray">应用部门</td>
			<td colspan="5">
				<div class="layui-input-inline" style="width:360px;">
					<select id="department_ids" name="department_ids" xm-selected="{$detail.department_ids}" xm-select="select1" xm-select-skin="default"></select>
				</div>
				<span class="red" style="font-size:12px;">（如果不选，默认是全公司）</span>
			</td>
		</tr>
		<tr>
			<td class="layui-td-gray">应用部门属性</td>
			<td colspan="5">
				<div class="layui-input-inline" style="width:360px;">
					<select id="department_remark" name="department_remark" >
						<option value="">请选择</option>
						<option value="门店" {if condition="$detail.department_remark == '门店'"}selected{/if}>门店</option>
						<option value="后勤" {if condition="$detail.department_remark == '后勤'"}selected{/if}>后勤</option>
					</select>
				</div>
				<span class="red" style="font-size:12px;">（如果不选，默认是全部部门属性）</span>
			</td>
		</tr>
		<tr>
			<td class="layui-td-gray">应用岗位职称</td>
			<td colspan="5">
				<div class="layui-input-inline" style="width:360px;">
					<select id="position_ids" name="position_ids" xm-selected="{$detail.position_ids}" xm-select="select2" xm-select-skin="default"></select>
				</div>
				<span class="red" style="font-size:12px;">（如果不选，默认是全部岗位）</span>
			</td>
		</tr>

		<tr id="position_ids_js_tr" {if condition="($detail['flow_cate'] != 38)"}hidden="hidden"{/if}>
			<td class="layui-td-gray">晋升岗位</td>
			<td colspan="5" >
				<div class="layui-input-inline" style="width:360px;">
					<select id="position_ids_js" name="position_ids_js" xm-selected="{$detail.position_ids_js}" xm-select="selcted_js" xm-select-skin="default"></select>
				</div>
				<span class="red" style="font-size:12px;">（如果不选，默认是全部岗位）</span>
			</td>
		</tr>

		<tr>
			<td class="layui-td-gray">指定员工</td>
			<td colspan="5">
				<div class="layui-input-inline" style="width:360px;">
					<input type="text" name="employee_names[]" value="{$detail.employee_names}" autocomplete="off" readonly class="layui-input picker-more">
					<input type="hidden" name="employee_ids[]" value="{$detail.employee_ids}">
				</div>
				<span class="red" style="font-size:12px;">（如果不选，默认是全部员工）</span>
			</td>
		</tr>

		<tr id="pay_amount_tr" {if condition="($detail['type'] != 3)"}hidden="hidden"{/if}>
			<td class="layui-td-gray">付款金额</td>
			<td colspan="5" >
				<input type="text" name="pay_amount" value="{$detail.pay_amount}" placeholder="付款金额" class="layui-input" style="width:360px;"/>
			</td>
		</tr>

		<tr id="pay_type_tr" {if condition="($detail['type'] != 3)"}hidden="hidden"{/if}>
			<td class="layui-td-gray">付款类型</td>
			<td colspan="5" >
				<div class="layui-input-inline" style="width:360px;">
					<select id="pay_types" name="pay_types" xm-selected="{$detail.pay_types}" xm-select="select4" xm-select-skin="default"></select>
				</div>
				<span class="red" style="font-size:12px;">（如果不选，默认是全部岗位）</span>
			</td>
		</tr>


		{if condition="($detail['flow_cate'] == 1)"}
		<tr>
			<td class="layui-td-gray">请假类型</td>
			<td colspan="5">
				<div class="layui-input-inline" style="width:360px;">
					<select id="vacation_type" name="vacation_type" xm-selected="{$detail.vacation_type}" xm-select="select3" xm-select-skin="default"></select>
				</div>
				<span class="red" style="font-size:12px;">（如果不选，默认是全部类型）</span>
			</td>
		</tr>
		<tr>
			<td class="layui-td-gray">请假时长</td>
			<td colspan="5">
				<input type="text" name="time_conditions" value="{$detail.time_conditions}" placeholder="请假时长" class="layui-input"></input>
			</td>
		</tr>
		{/if}
		<tr>
			<td class="layui-td-gray">流程说明</td>
			<td colspan="5">
				<textarea name="remark" placeholder="请输入流程说明" class="layui-textarea">{$detail.remark}</textarea>
			</td>
		</tr>
		<tr>
			<td class="layui-td-gray-2">审批流类型<font>*</font></td>
			<td colspan="5">
				<input type="radio" name="check_type" lay-filter="checktype" value="1" title="固定审批流" {eq name="$detail.check_type" value="1"}checked{/eq}>
				<input type="radio" name="check_type" lay-filter="checktype" value="2" title="自由审批流" {eq name="$detail.check_type" value="2"}checked{/eq}>
				<input type="radio" name="check_type" lay-filter="checktype" value="3" title="可回退的审批流" {eq name="$detail.check_type" value="3"}checked{/eq}>
			</td>
		</tr>
		<tr id="flowTr1" {neq name="$detail.check_type" value="1"}style="display:none"{/neq}>
			<td class="layui-td-gray">审批流程<font>*</font></td>
			<td colspan="5">
				<div id="flowList1">
					{eq name="$detail.check_type" value="1"}
					{volist name="detail.flow_list" id="vo"}
					<div class="layui-form-item layui-form-pane">
						<div class="layui-inline">
						  <label class="layui-form-label">第{$key+1}级</label>
						  <div class="layui-input-inline">
							<select name="flowType[]" lay-filter="flowtype">
								<option value="1" {eq name="$vo.flow_type" value="1"}selected=""{/eq}>当前部门负责人</option>
								<option value="2" {eq name="$vo.flow_type" value="2"}selected=""{/eq}>上一级部门负责人</option>
								<option value="3" {eq name="$vo.flow_type" value="3"}selected=""{/eq}>指定人员(多人或签)</option>
								<option value="4" {eq name="$vo.flow_type" value="4"}selected=""{/eq}>指定人员(多人会签)</option>
								<option value="5" {eq name="$vo.flow_type" value="5"}selected=""{/eq}>指定部门(多人或签)</option>
								<option value="6" {eq name="$vo.flow_type" value="6"}selected=""{/eq}>指定部门(多人会签)</option>
								<option value="7" {eq name="$vo.flow_type" value="7"}selected=""{/eq}>门店负责人</option>
								<option value="8" {eq name="$vo.flow_type" value="8"}selected=""{/eq}>股东店长</option>
							</select>
						  </div>
						</div>


						<div class="layui-inline">
							<label class="layui-form-label">节点名称</label>
							<div class="layui-input-inline" style="width:360px;">
								{if condition="isset($vo.flow_title)"}
								<input type="text" name="flowTitle[]" value="{$vo.flow_title}" autocomplete="off" class="layui-input">
								<input type="hidden" name="ids[]" value="{$vo.id}" autocomplete="off" class="layui-input">
								{else/}
								<input type="text" name="flowTitle[]" value="" autocomplete="off" class="layui-input">
								<input type="hidden" name="ids[]" value="" autocomplete="off" class="layui-input">
								{/if}
							</div>
						</div>

						<div class="layui-inline select-{$vo.flow_type}">
						  <label class="layui-form-label">指定人员</label>
						  <div class="layui-input-inline" style="width:360px;">
							<input type="text" name="flowNamesA[]" value="{$vo.flow_unames}" autocomplete="off" readonly class="layui-input picker-more">
							<input type="hidden" name="flowUidsA[]" value="{$vo.flow_uids}">
						  </div>
						</div>

						<div class="layui-inline select-{$vo.flow_type}-1">
							<label class="layui-form-label">指定部门</label>
							<div class="layui-input-inline" style="width:360px;">
								<select name="flow_dep[]" lay-filter="select_flow_dep" lay-search="">
									<option value="">--请选择--</option>
									{volist name=":getmd('后勤')" id="vvo"}
									<option value="{$vvo.id}" {if condition="(isset($vo.flow_dep) && $vvo.id == $vo.flow_dep)"}selected{/if}>{$vvo.title}</option>
									{/volist}
								</select>
							</div>
						</div>


						{gt name="$key" value="0"}
						<span class="layui-btn layui-btn-danger layui-btn-sm">删除</span>
						{/gt}
					</div>
					{/volist}
					{else/}
					<div class="layui-form-item layui-form-pane">
						<div class="layui-inline">
						  <label class="layui-form-label">第1级</label>
						  <div class="layui-input-inline">
							<select name="flowType[]" lay-filter="flowtype">
								<option value="1">当前部门负责人</option>
								<option value="2">上一级部门负责人</option>
								<option value="3">指定人员(多人或签)</option>
								<option value="4">指定人员(多人会签)</option>
								<option value="5">指定部门(多人或签)</option>
								<option value="6">指定部门(多人会签)</option>
								<option value="7">门店负责人</option>
								<option value="8">股东店长</option>

							</select>
						  </div>
						</div>
						<div class="layui-inline select-1">
						  <label class="layui-form-label">指定人员</label>
						  <div class="layui-input-inline" style="width:360px;">
							<input type="text" name="flowNamesA[]" value="" autocomplete="off" readonly class="layui-input picker-more">
							<input type="hidden" name="flowUidsA[]" value="">
						  </div>
						</div>



					</div>
					{/eq}
				</div>
				<span id="addFlow1" class="layui-btn layui-btn-xs layui-btn-normal">+ 添加审批层级</span>
				<div style="padding:10px; margin-top:10px; font-size:12px; background-color:#fffcf0">
					<p><strong>温馨提示</strong></p>
					<p>1、当选择<strong> “当前部门负责人” </strong>审批时。系统仅会通知当前部门的负责人。</p>
					<p>2、当选择<strong> “上一级部门负责人” </strong>审批时。系统仅会通知当前部门的上一级部门的负责人。</p>
					<p>3、当选择<strong> “指定人员(多人或签)” </strong>时，表示指定用户中任意一人审批即可，可单选或多选。</p>
					<p>4、当选择<strong> “指定人员(多人会签)” </strong>时，表示指定人员中所有人都需要审批，可单选或多选。</p>
					<p>5、如果指定用户没有分配查看审批模块的功能权限，系统会通知其审批，但是他无法查看此审批数据信息。</p>
				</div>
			</td>
		</tr>
		<tr id="flowTr2" {neq name="$detail.check_type" value="2"}style="display:none"{/neq}>
			<td class="layui-td-gray">审批流程<font>*</font></td>
			<td colspan="5">
				<div style="padding:10px; font-size:12px; background-color:#fffcf0">
					<p><strong>温馨提示</strong></p>
					<p>无需配置审批人，审批时，根据实际情况选择审批人即可，自由度最高。</p>
				</div>
			</td>
		</tr>
		<tr id="flowTr3" {neq name="$detail.check_type" value="3"}style="display:none"{/neq}>
			<td class="layui-td-gray">审批流程<font>*</font></td>
			<td colspan="5">
				<div id="flowList3">
					{eq name="$detail.check_type" value="3"}
					{volist name="detail.flow_list" id="vo"}
					<div class="layui-form-item layui-form-pane">
						<div class="layui-inline">
						  <label class="layui-form-label">第{$key+1}级</label>
						  <div class="layui-input-inline">
							<input type="text" name="flowName[]" value="{$vo.flow_name}" autocomplete="off" placeholder="请输入流程名称" class="layui-input">
						  </div>
						</div>
						<div class="layui-inline select-3">
						  <label class="layui-form-label">指定人员</label>
						  <div class="layui-input-inline" style="width:360px;">
							<input type="text" name="flowNamesB[]" value="{$vo.flow_unames}" autocomplete="off" readonly class="layui-input picker-one">
							<input type="hidden" name="flowUidsB[]" value="{$vo.flow_uids}">
						  </div>
						</div>
						{gt name="$key" value="0"}
						<span class="layui-btn layui-btn-danger layui-btn-sm">删除</span>
						{/gt}
					</div>
					{/volist}
					{else/}
					<div class="layui-form-item layui-form-pane">
						<div class="layui-inline">
						  <label class="layui-form-label">第1级</label>
						  <div class="layui-input-inline">
							<input type="text" name="flowName[]" value="" autocomplete="off" placeholder="请输入流程名称" class="layui-input">
						  </div>
						</div>
						<div class="layui-inline select-3">
						  <label class="layui-form-label">指定人员</label>
						  <div class="layui-input-inline" style="width:360px;">
							<input type="text" name="flowNamesB[]" value="" autocomplete="off" readonly class="layui-input picker-one">
							<input type="hidden" name="flowUidsB[]" value="">
						  </div>
						</div>
					</div>
					{/eq}
				</div>
				<span id="addFlow3" class="layui-btn layui-btn-xs layui-btn-normal">+ 添加审批层级</span>
				<div style="padding:10px; margin-top:10px; font-size:12px; background-color:#fffcf0">
					<p><strong>温馨提示</strong></p>
					<p>1、<strong>指定人员</strong>单选。后期审批的时候，审批人只能该指定人员。</p>
					<p>2、该审批流程可<strong>回退</strong>，当拒绝审核时，会自动回退到上一位审批人节点。</p>
				</div>
			</td>
		</tr>
		<tr>
			<td class="layui-td-gray">抄送人</td>
			<td colspan="5">
				<input type="text" name="copy_unames" value="{$detail.copy_unames}" autocomplete="off" readonly class="layui-input picker-more">
				<input type="hidden" name="copy_uids" value="{$detail.copy_uids}">
			</td>
		</tr>
	</table>
	{/eq}
	<div class="py-3">
		<input type="hidden" name="id" value="{$id}">
		<button class="layui-btn layui-btn-normal" lay-submit="" lay-filter="webform">立即提交</button>
		<button type="reset" class="layui-btn layui-btn-primary">重置</button>
    </div>
</form>
{/block}
<!-- /主体 -->

<!-- 脚本 -->
{block name="script"}

<script>
	const moduleInit = ['tool','formSelects','employeepicker'];
	function gouguInit() {
		var form = layui.form,tool=layui.tool,formSelects = layui.formSelects,employeepicker = layui.employeepicker;
		//选择应用模块
		form.on('select(type)', function(data){
			let callback = function (e) {
				if (e.code == 0) {
					if(e.data.length>0){
						let ops='<option value="">--请选择--</option>';
						for(var i=0;i<e.data.length;i++){
							ops+='<option value="'+e.data[i].id+'">'+e.data[i].title+'</option>';
						}
						$('[name="flow_cate"]').html(ops);
						form.render();
					}				
				}
			}
			tool.get("/api/index/get_flow_cate", {type:data.value}, callback);

			if (data.value == 3){
				$('#pay_amount_tr').show();
				$('#pay_type_tr').show();
			}else{
				$('#pay_amount_tr').hide();
				$('#pay_type_tr').hide();
			}

		})

		//flowcate
		form.on('select(flowcate)', function(data){

			let value = data.value
			console.log(value)
			if (value == 1){
				$('#vacation_type_tr').show();
				$('#time_conditions_tr').show();
			}else{
				$('#vacation_type_tr').hide();
				$('#time_conditions_tr').hide();
			}

			if (value == 38){
				$('#position_ids_js_tr').show();
			}else{
				$('#position_ids_js_tr').hide();
			}

		})

		
		//选择应用部门
		var selcted = $('#department_ids').attr('xm-selected');
		formSelects.data('select1', 'server', {
			url: '/api/index/get_department_select',
			keyword: selcted,
		});

		//选择岗位职称
		var selcted2 = $('#position_ids').attr('xm-selected');
		formSelects.data('select2', 'server', {
			url: '/api/index/get_positionList',
			keyword: selcted2,
		});

		//选择晋升岗位职称
		var selcted_js = $('#position_ids_js').attr('xm-selected');
		formSelects.data('selcted_js', 'server', {
			url: '/api/index/get_positionList',
			keyword: selcted_js,
		});

		//
		var selcted3 = $('#vacation_type').attr('xm-selected');
		formSelects.data('select3', 'server', {
			url: '/api/index/getVacationTypeList',
			keyword: selcted3,
		});

		//选择付款类型
		var selcted4 = $('#pay_types').attr('xm-selected');
		formSelects.data('select4', 'server', {
			url: '/api/index/getPayTypeList?controller=fukuan',
			keyword: selcted4,
		});
		
		
		form.on('radio(checktype)', function(data){
		  if(data.value==1){
			$('#flowTr1').show();
			$('#flowTr2').hide();
			$('#flowTr3').hide();
		  }
		  else if(data.value==2){
			$('#flowTr1').hide();
			$('#flowTr2').show();
			$('#flowTr3').hide();
		  }
		  else{
			$('#flowTr1').hide();
			$('#flowTr2').hide();
			$('#flowTr3').show();
		  }
		});
		
		form.on('select(flowtype)', function(data){
			console.log('layui-inline select-'+data.value)
		  	$(data.elem).parents('.layui-form-item').find('.layui-inline').eq(2).attr('class','layui-inline select-'+data.value);

			$(data.elem).parents('.layui-form-item').find('.layui-inline').eq(3).attr('class','layui-inline select-'+data.value+'-1');
			// $(data.elem).parents('.layui-form-item').find('.layui-inline').eq(3).attr('class','layui-inline select-'+data.value+'-2');
		});

		//监听提交
		form.on('submit(webform)', function(data){
			let callback = function (e) {
				layer.msg(e.msg);
				if (e.code == 0) {
					tool.sideClose(1000);					
				}
			}
			tool.post("/home/<USER>/add", data.field, callback);
			return false;
		});
    
		
		$('#addFlow1').on('click',function(){
			var len = $('#flowList1').find('.layui-form-item').length;
			var index = len+1;
			var timestamp=new Date().getTime();
			var tem=`<div class="layui-form-item  layui-form-pane">
						<div class="layui-inline">
						  <label class="layui-form-label label-index">第${index}级</label>
						  <div class="layui-input-inline">
							<select name="flowType[]" lay-filter="flowtype">
								<option value="1">当前部门负责人</option>
								<option value="2">上一级部门负责人</option>
								<option value="3">指定人员(多人或签)</option>
								<option value="4">指定人员(多人会签)</option>
								<option value="5">指定部门(多人或签)</option>
								<option value="6">指定部门(多人会签)</option>
								<option value="7">门店负责人</option>
								<option value="8">股东店长</option>
							</select>
						  </div>
						</div>
						<div class="layui-inline">
							<label class="layui-form-label">节点名称</label>
							<div class="layui-input-inline" style="width:360px;">
								<input type="text" name="flowTitle[]" value="" autocomplete="off" class="layui-input">
								<input type="hidden" name="ids[]" value="" autocomplete="off" class="layui-input">
							</div>
						</div>
						<div class="layui-inline select-1">
						  <label class="layui-form-label">指定人员</label>
						  <div class="layui-input-inline" style="width:360px;">
							<input type="text" name="flowNamesA[]" value="" autocomplete="off" readonly class="layui-input picker-more">
							<input type="hidden" name="flowUidsA[]" value="">
						  </div>
						</div>
						<div class="layui-inline select-1-1">
						<label class="layui-form-label">指定部门</label>
							<div class="layui-input-inline" style="width:360px;">
								<select name="flow_dep[]" lay-filter="select_flow_dep" lay-search="">
									<option value="">--请选择--</option>
									{volist name=":getmd('后勤')" id="vo"}
										<option value="{$vo.id}">{$vo.title}</option>
									{/volist}
								</select>
							</div>
						</div>
						<span class="layui-btn layui-btn-danger layui-btn-sm">删除</span>
					</div>`;
			$('#flowList1').append(tem);
			form.render();
		});
		
		$('#flowList1').on('click','.layui-btn-danger',function(){
			$(this).parents('.layui-form-item').remove();
			var items = $('.label-index').length;
			if(items>0){
				$('.label-index').each(function(index,item){
					$(this).html('第'+(index+2)+'级');
				})
			}
		});			
		
		//================================
		$('#addFlow3').on('click',function(){
			var len = $('#flowList3').find('.layui-form-item').length;
			var index = len+1;
			var timestamp=new Date().getTime();
			var tem='<div class="layui-form-item  layui-form-pane">\
						<div class="layui-inline">\
						  <label class="layui-form-label label-index">第'+index+'级</label>\
						  <div class="layui-input-inline">\
							<input type="text" name="flowName[]" value="" autocomplete="off" placeholder="请输入流程名称" class="layui-input">\
						  </div>\
						</div>\
						<div class="layui-inline select-3">\
						  <label class="layui-form-label">指定人员</label>\
						  <div class="layui-input-inline" style="width:360px;">\
							<input type="text" name="flowNamesB[]" value="" autocomplete="off" readonly class="layui-input picker-one">\
							<input type="hidden" name="flowUidsB[]" value="0">\
						  </div>\
						</div>\
						<span class="layui-btn layui-btn-danger layui-btn-sm">删除</span>\
					</div>';
			$('#flowList3').append(tem);
			form.render();
		});
		
		$('#flowList3').on('click','.layui-btn-danger',function(){
			$(this).parents('.layui-form-item').remove();
			var items = $('.label-index').length;
			if(items>0){
				$('.label-index').each(function(index,item){
					$(this).html('第'+(index+2)+'级');
				})
			}
		});
	}
</script>
{/block}
<!-- /脚本 -->