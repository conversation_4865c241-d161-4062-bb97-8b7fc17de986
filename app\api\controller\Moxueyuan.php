<?php
/**
 * @copyright Copyright (c) 2021 仲正堂工作室
 * @license https://opensource.org/licenses/GPL-3.0
 * @link https://www.gougucms.com
 */
declare (strict_types=1);

namespace app\api\controller;

use app\api\BaseController;
use app\api\service\IndexService;
use app\contract\service\ContractrentService;
use app\oa\controller\Approve;
use app\oa\service\TypeService;
use app\store\model\StoreBill;
use app\store\service\StoreBillService;
use app\store\service\StoreBusinessService;
use app\store\service\StoreSalaryService;
use app\store\service\StoreService;
use app\user\service\TrainService;
use PhpOffice\PhpSpreadsheet\Calculation\Logical\Boolean;
use SNMP;
use think\facade\Db;
use think\Session;

class Moxueyuan
{

    public function syn_train()
    {
        set_time_limit(0);
        $TrainService = new TrainService();
        $TrainService->syn_train();
        return to_assign(0,"同步成功");
    }


    public function syn_train_exam()
    {
        set_time_limit(0);
        $TrainService = new TrainService();
        $TrainService->syn_train_exam();
        return to_assign(0,"同步成功");
    }

}


