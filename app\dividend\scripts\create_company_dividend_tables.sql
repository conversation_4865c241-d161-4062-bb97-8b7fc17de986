-- 公司股东分红明细主表
CREATE TABLE `oa_dividend_company_detail` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `store_id` int unsigned NOT NULL DEFAULT '0' COMMENT '门店ID（关联oa_department表）',
  `period` varchar(20) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '统计周期（如：2024-05）',
  `dividend_profit` decimal(15,2) NOT NULL DEFAULT '0.00' COMMENT '分红利润（元）',
  `company_shareholding_ratio` decimal(6,3) NOT NULL DEFAULT '0.000' COMMENT '公司持股门店比例（%，保留三位小数）',
  `remark` text COLLATE utf8mb4_general_ci COMMENT '备注信息',
  `is_delete` tinyint(1) NOT NULL DEFAULT '0' COMMENT '删除标记(0:正常,1:已删除)',
  `delete_time` int NOT NULL DEFAULT '0' COMMENT '删除时间',
  `create_time` int NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` int NOT NULL DEFAULT '0' COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uk_store_period` (`store_id`,`period`) USING BTREE COMMENT '门店周期唯一索引',
  KEY `idx_store_id` (`store_id`) USING BTREE COMMENT '门店ID索引',
  KEY `idx_period` (`period`) USING BTREE COMMENT '统计周期索引',
  KEY `idx_is_delete` (`is_delete`) USING BTREE COMMENT '删除标记索引',
  KEY `idx_create_time` (`create_time`) USING BTREE COMMENT '创建时间索引',
  KEY `idx_store_delete` (`store_id`,`is_delete`) USING BTREE COMMENT '门店删除状态组合索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='公司股东分红明细主表';

-- 公司股东分红人子表
CREATE TABLE `oa_dividend_company_detail_person` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `company_detail_id` int unsigned NOT NULL DEFAULT '0' COMMENT '公司股东分红明细主表ID（关联oa_dividend_company_detail表）',
  `shareholder_id` int unsigned NOT NULL DEFAULT '0' COMMENT '分红人ID（关联oa_dividend_shareholder表）',
  `shareholder_name` varchar(50) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '分红人名称',
  `shareholding_ratio` decimal(6,3) NOT NULL DEFAULT '0.000' COMMENT '持股比例（%，保留三位小数）',
  `actual_shareholding` decimal(6,3) NOT NULL DEFAULT '0.000' COMMENT '实际持股（%，保留三位小数）',
  `amount` decimal(15,2) NOT NULL DEFAULT '0.00' COMMENT '金额（元）',
  `adjustment_amount` decimal(15,2) NOT NULL DEFAULT '0.00' COMMENT '调整金额（元）',
  `payable_amount` decimal(15,2) NOT NULL DEFAULT '0.00' COMMENT '应付金额（元）',
  `remark` text COLLATE utf8mb4_general_ci COMMENT '备注信息',
  `is_delete` tinyint(1) NOT NULL DEFAULT '0' COMMENT '删除标记(0:正常,1:已删除)',
  `delete_time` int NOT NULL DEFAULT '0' COMMENT '删除时间',
  `create_time` int NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` int NOT NULL DEFAULT '0' COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_company_detail_id` (`company_detail_id`) USING BTREE COMMENT '公司分红明细表ID索引',
  KEY `idx_shareholder_id` (`shareholder_id`) USING BTREE COMMENT '分红人ID索引',
  KEY `idx_is_delete` (`is_delete`) USING BTREE COMMENT '删除标记索引',
  KEY `idx_company_delete` (`company_detail_id`,`is_delete`) USING BTREE COMMENT '公司分红明细删除状态组合索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='公司股东分红人子表';
