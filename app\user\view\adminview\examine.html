{extend name="../../base/view/common/base" /}
<!-- 主体 -->
{block name="body"}
<style>
	/*
     * 基于复选框和单选框的卡片风格多选组件
     * 需要具备一些基础的 CSS 技能，以下样式均为外部自主实现。
     */
	/* 主体 */
	.layui-form-checkbox>.lay-skin-checkcard,
	.layui-form-radio>.lay-skin-checkcard {
		display: table;
		display: flex;
		padding: 12px;
		white-space: normal;
		border-radius: 10px;
		border: 1px solid #e5e5e5;
		color: #000;
		background-color: #fff;
	}
	.layui-form-checkbox>.lay-skin-checkcard>*,
	.layui-form-radio>.lay-skin-checkcard>* {
		/* display: table-cell; */  /* IE */
		vertical-align: top;
	}
	/* 悬停 */
	.layui-form-checkbox:hover>.lay-skin-checkcard,
	.layui-form-radio:hover>.lay-skin-checkcard {
		border-color: #16b777;
	}
	/* 选中 */
	.layui-form-checked>.lay-skin-checkcard,
	.layui-form-radioed[lay-skin="none"]>.lay-skin-checkcard {
		color: #000;
		border-color: #16b777;
		background-color: rgb(22 183 119 / 10%) !important;
		/* box-shadow: 0 0 0 3px rgba(22, 183, 119, 0.08); */
	}
	/* 禁用 */
	.layui-checkbox-disabled>.lay-skin-checkcard,
	.layui-radio-disabled>.lay-skin-checkcard {
		box-shadow: none;
		border-color: #e5e5e5 !important;
		background-color: #eee !important;
	}
	/* card 布局 */
	.lay-skin-checkcard-avatar {
		padding-right: 8px;
	}
	.lay-skin-checkcard-detail {
		overflow: hidden;
		width: 100%;
	}
	.lay-skin-checkcard-header {
		font-weight: 500;
		font-size: 16px;
		white-space: nowrap;
		margin-bottom: 4px;
	}
	.lay-skin-checkcard-description {
		font-size: 13px;
		color: #5f5f5f;
	}
	.layui-disabled  .lay-skin-checkcard-description{
		color: #c2c2c2! important;
	}
	/* 选中 dot */
	.layui-form-checked>.lay-check-dot:after,
	.layui-form-radioed>.lay-check-dot:after {
		position: absolute;
		content: "";
		top: 2px;
		right: 2px;
		width: 0;
		height: 0;
		display: inline-block;
		vertical-align: middle;
		border-width: 10px;
		border-style: dashed;
		border-color: transparent;
		border-top-left-radius: 0px;
		border-top-right-radius: 6px;
		border-bottom-right-radius: 0px;
		border-bottom-left-radius: 6px;
		border-top-color: #16b777;
		border-top-style: solid;
		border-right-color: #16b777;
		border-right-style: solid;
		overflow: hidden;
	}
	.layui-checkbox-disabled>.lay-check-dot:after,
	.layui-radio-disabled>.lay-check-dot:after {
		border-top-color: #d2d2d2;
		border-right-color: #d2d2d2;
	}
	/* 选中 dot-2 */
	.layui-form-checked>.lay-check-dot-2:before,
	.layui-form-radioed>.lay-check-dot-2:before {
		position: absolute;
		font-family: "layui-icon";
		content: "\e605";
		color: #fff;
		bottom: 4px;
		right: 3px;
		font-size: 9px;
		z-index: 12;
	}
	.layui-form-checked>.lay-check-dot-2:after,
	.layui-form-radioed>.lay-check-dot-2:after {
		position: absolute;
		content: "";
		bottom: 2px;
		right: 2px;
		width: 0;
		height: 0;
		display: inline-block;
		vertical-align: middle;
		border-width: 10px;
		border-style: dashed;
		border-color: transparent;
		border-top-left-radius: 6px;
		border-top-right-radius: 0px;
		border-bottom-right-radius: 6px;
		border-bottom-left-radius: 0px;
		border-right-color: #16b777;
		border-right-style: solid;
		border-bottom-color: #16b777;
		border-bottom-style: solid;
		overflow: hidden;
	}
	.layui-checkbox-disabled>.lay-check-dot-2:before,
	.layui-radio-disabled>.lay-check-dot-2:before {
		color: #eee !important;
	}
	.layui-checkbox-disabled>.lay-check-dot-2:after,
	.layui-radio-disabled>.lay-check-dot-2:after {
		border-bottom-color: #d2d2d2;
		border-right-color: #d2d2d2;
	}
	.lay-ellipsis-multi-line {
		overflow: hidden;
		word-break: break-all;
		display: -webkit-box;
		-webkit-box-orient: vertical;
		-webkit-line-clamp: 3;
	}
</style>

<style>
	.layui-form-radio>.lay-skin-tag,
	.layui-form-checkbox>.lay-skin-tag {
		font-size: 13px;
		border-radius: 100px;
	}
	.layui-form-checked>.lay-skin-tag,
	.layui-form-radioed>.lay-skin-tag {
		color: #fff !important;
		background-color: #16b777 !important;
	}
	/* 主体 */
	.layui-form-radio>.lay-skin-color-picker {
		border-radius: 50%;
		border-width: 1px;
		border-style: solid;
		width: 20px;
		height: 20px;
	}
	/* 选中 */
	.layui-form-radioed>.lay-skin-color-picker {
		box-shadow: 0 0 0 1px #ffffff, 0 0 0 4px currentColor;
	}
	font{
		color: #FF6347;
		margin-left: 2px;
	}

	.input-content input,.input-content div{
		flex: 1;
	}

	.pb-3{
		background-color: #fff;
		padding: 10px;
	}

	.layui-td-gray{
		width: 50px;
		background-color: #fff;
	}

	.layui-card-body-content{
		border-top: 1px solid #f8f8f8;
	}

	.layui-card-body-fraction{
		border-top: 1px solid #f8f8f8;
		display: flex;
		justify-content: center;
		align-items: center;
	}

	.layui-card-body-fraction .layui-input-wrap{
		flex: 1;
	}

	.layui-bg-gray{
		background-color: #fff !important;
	}

	.layui-card-header{
		font-size: 18px;
	}

	.w-200{
		width: 100px;
	}

	.w-80{
		width: 15px;
	}

	.s-color{
		color: #0000cc;
	}

</style>
<form class="layui-form p-12">
	<h3 class="pb-3">技师考核表</h3>
	<table class="layui-table layui-table-form">
		<tr>
			<td class="layui-td-gray w-200">姓名</td>
			<td >
				<input name="a_view_name" type="text" class="layui-input" value="{$admin_view['name']}"/>
				<input name="a_view_id" type="hidden" class="layui-input" value="{$admin_view['id']}"/>
			</td>
		</tr>
		<tr>
			<td class="layui-td-gray w-200">性别</td>
			<td>
				{if condition="($admin_view['sex'] == 1)"}
				男
				{else/}
				女
				{/if}
				<input name="sex" type="hidden" class="layui-input" value="{$admin_view['sex']}"/>
			</td>
		</tr>
		<tr>
			<td class="layui-td-gray w-200">岗位</td>
			<td>
				<input name="position" readonly type="text" class="layui-input" value="{$interview['position']}"/>
			</td>
		</tr>
		<tr>
			<td class="layui-td-gray w-200">开始学习时间<br>（到店时间）</td>
			<td >
				<input id="sdate" name="start_sdate" type="text" class="layui-input" value="{$sdate}" readonly/>
			</td>
		</tr>
		<tr>
			<td class="layui-td-gray w-200">所属门店</td>
			<td>
				<select name="did" id="did" lay-verify="required" lay-reqText="请选择所在部门" lay-search="" lay-filter="select-did">
					<option value="">--请选择--</option>
					{volist name=":getmd('门店')" id="vo"}
					<option value="{$vo.id}"
							data-leader-id="{$vo.leader_id}"
							data-leader-name="{$vo.leader_name}"
							{if condition="( ($admin_view.did == $vo.id) )"} selected {/if}>{$vo.title}</option>
					{/volist}
				</select>
			</td>
		</tr>
		<tr>
			<td class="layui-td-gray w-200">门店店长<br>（带教责任人）</td>
			<td>
				<input name="aname" type="text" class="layui-input" readonly/>
				<input name="aid" type="hidden" class="layui-input" readonly/>
			</td>
		</tr>
	</table>

	<table class="layui-table layui-table-form">
		<tr >
			<td class="layui-td-gray " colspan="3" style="text-align: left;color: #000"><h2>线上理论</h2></td>
		</tr>
		<tr>
			<td class="layui-td-gray w-80">视频课件完成情况</td>
			<td class="layui-td-gray ">
				<input placeholder="视频课件完成情况" type="text" name="courseware_status" readonly
					   {if condition="(isset($admin_view.courseware_status))"} value="{$admin_view.courseware_status}" {/if}
					class="layui-input" >
			</td>
		</tr>
		<tr>
			<td class="layui-td-gray w-80">理论考核成绩</td>
			<td class="layui-td-gray ">
				<input placeholder="理论考核成绩" type="text" name="courseware_score" readonly
					   {if condition="(isset($admin_view.courseware_score))"} value="{$admin_view.courseware_score}" {/if}
					   class="layui-input" >
			</td>
		</tr>
		<tr>
			<td class="layui-td-gray w-80">理论考试次数</td>
			<td class="layui-td-gray ">
				<input placeholder="理论考试次数" lay-verify="required" readonly lay-reqText="请填写得分" type="text" name="courseware_times"
					   {if condition="(isset($admin_view.courseware_times))"} value="{$admin_view.courseware_times}" {/if}
					   class="layui-input" >
			</td>
		</tr>
<!--		<tr >-->
<!--			<td class="layui-td-gray" colspan="3" style="text-align: left;color: #000">-->
<!--				<h2>线下实操（满分100,60分合格）</h2></br>-->
<!--				<span style="color: red;font-weight: bolder">注意：实操考核共6门，考核合格需通过4门科目，其中推拿为必过科目</span>-->
<!--			</td>-->
<!--		</tr>-->



<!--		<tr>-->
<!--			<td class="layui-td-gray s-color w-80">得分<font>*</font></td>-->
<!--			<td class="layui-td-gray">-->
<!--				<input placeholder="得分" lay-affix="number" type="number"-->
<!--					   lay-verify="required|number" lay-reqText="请填写得分"-->
<!--					   name="score[]"  class="layui-input" step="0.1" min="0" max="5">-->
<!--			</td>-->
<!--		</tr>-->
<!--		<tr>-->
<!--			<td class="layui-td-gray s-color w-80">关元灸分数</td>-->
<!--			<td class="layui-td-gray">-->
<!--				<input  placeholder="关元灸分数" lay-affix="number" type="number" name="score[]" value="" class="layui-input" step="0.1" min="0" max="5">-->
<!--			</td>-->
<!--		</tr>-->
<!--		<tr>-->
<!--			<td class="layui-td-gray s-color w-80">悬灸分数</td>-->
<!--			<td class="layui-td-gray">-->
<!--				<input placeholder="悬灸分数" lay-affix="number" type="number" name="score[]" value="" class="layui-input" step="0.1" min="0" max="5">-->
<!--			</td>-->
<!--		</tr>-->
<!--		<tr>-->
<!--			<td class="layui-td-gray s-color w-80">刮痧分数</td>-->
<!--			<td class="layui-td-gray">-->
<!--				<input placeholder="刮痧分数" lay-affix="number" type="number" name="score[]" value="" class="layui-input" step="0.1" min="0" max="5">-->
<!--			</td>-->
<!--		</tr>-->
<!--		<tr>-->
<!--			<td class="layui-td-gray s-color w-80">拔罐分数</td>-->
<!--			<td class="layui-td-gray">-->
<!--				<input placeholder="拔罐分数" lay-affix="number" type="number" name="score[]" value="" class="layui-input" step="0.1" min="0" max="5">-->
<!--			</td>-->
<!--		</tr>-->
<!--&lt;!&ndash;		<tr>&ndash;&gt;-->
<!--&lt;!&ndash;			<td class="layui-td-gray s-color w-80">拉伸分数</td>&ndash;&gt;-->
<!--&lt;!&ndash;			<td class="layui-td-gray">&ndash;&gt;-->
<!--&lt;!&ndash;				<input placeholder="拉伸分数" lay-affix="number" type="number" name="score[]" value="" class="layui-input" step="0.1" min="0" max="5">&ndash;&gt;-->
<!--&lt;!&ndash;			</td>&ndash;&gt;-->
<!--&lt;!&ndash;		</tr>&ndash;&gt;-->
<!--		<tr>-->
<!--			<td class="layui-td-gray s-color w-80">实操考试时间</td>-->
<!--			<td class="layui-td-gray">-->
<!--				<input id="exam_sdate" name="exam_sdate" type="text" class="layui-input" value="{$sdate}" readonly/>-->
<!--			</td>-->
<!--		</tr>-->
<!--		<tr>-->
<!--			<td class="layui-td-gray s-color w-80">实操考官</td>-->
<!--			<td class="layui-td-gray">-->
<!--				<input name="exam_name" type="text" class="layui-input"/>-->
<!--			</td>-->
<!--		</tr>-->
<!--		<tr>-->
<!--			<td class="layui-td-gray w-80">考核点评</td>-->
<!--			<td class="layui-td-gray" colspan="2">-->
<!--				<input name="remark" type="text" class="layui-input" value="" />-->
<!--			</td>-->
<!--		</tr>-->
	</table>

	<h2 style="margin: 10px 0">线下实操</h2>
	<div class="layui-tabs layui-tabs-card" lay-options="{index: 0}">
		<ul class="layui-tabs-header">
			<li>关元灸</li>
			<li>刮痧</li>
			<li>拔罐</li>
			<li>推拿</li>
		</ul>
		<div class="layui-tabs-body" style="padding: 0">
			<div class="layui-tabs-item">
				<h3 class="details">关元灸实操考核（25分）</h3>
				<table class="layui-table layui-table-form">
					<tr>
						<td style="width: 60px">考核项</td>
						<td>考核重点 <span style="font-size: 12px;color: red">点击考核重点可查看详细内容</span></td>
					</tr>

					<tr>
						<td rowspan="4">铺姜（10分）</td>
						<td class="detail_title" data-index="0">厚度、距离（5分）</td>

					</tr>
					<tr>
						<td ><input placeholder="厚度、距离（5分）得分" lay-affix="number" type="number"
									lay-verify="required|number" lay-reqText="请填写得分" data-index="0"
									name="score_0[]"  class="layui-input input-score" step="1" min="0" max="5"></td>
					</tr>
					<tr>
						<td class="detail_title" data-index="1">整洁度、时间把控（5分）</td>
					</tr>
					<tr>
						<td ><input placeholder="整洁度、时间把控（5分）得分" lay-affix="number" type="number"
									lay-verify="required|number" lay-reqText="请填写得分" data-index="1"
									name="score_0[]"  class="layui-input input-score" step="1" min="0" max="5"></td>
					</tr>
					<tr>
						<td rowspan="6">施灸流程（15分）</td>
						<td class="detail_title" data-index="2">灸前（5分）</td>
					</tr>
					<tr>
						<td ><input placeholder="灸前（5分）得分" lay-affix="number" type="number"
									lay-verify="required|number" lay-reqText="请填写得分" data-index="2"
									name="score_0[]"  class="layui-input input-score" step="1" min="0" max="5"></td>
					</tr>
					<tr>
						<td class="detail_title" data-index="3">灸中（5分）</td>
					</tr>
					<tr>
						<td ><input placeholder="灸中（5分）得分" lay-affix="number" type="number"
									lay-verify="required|number" lay-reqText="请填写得分" data-index="3"
									name="score_0[]"  class="layui-input input-score" step="1" min="0" max="5"></td>
					</tr>
					<tr>
						<td class="detail_title" data-index="4">灸后（5分）</td>
					</tr>
					<tr>
						<td ><input placeholder="灸后（5分）得分" lay-affix="number" type="number"
									lay-verify="required|number" lay-reqText="请填写得分" data-index="4"
									name="score_0[]"  class="layui-input input-score" step="1" min="0" max="5"></td>
					</tr>
					<tr>
						<td>平均分</td>
						<td ><input type="text" id="average_0" readonly placeholder="关元灸实操考核平均分" name="average[]"  class="layui-input" ></td>
					</tr>
				</table>
			</div>
			<div class="layui-tabs-item">
				<h3>刮痧实操考核（15分）</h3>
				<table class="layui-table layui-table-form">
					<tr>
						<td>考核重点</td>
					</tr>

					<tr>
						<td class="detail_title" data-index="5">刮痧前检查准备（5分）</td>
					</tr>
					<tr>
						<td ><input placeholder="刮痧前检查准备（5分）得分" lay-affix="number" type="number"
									lay-verify="required|number" lay-reqText="请填写得分" data-index="1"
									name="score_1[]"  class="layui-input input-score" step="1" min="0" max="5"></td>
					</tr>
					<tr>
						<td class="detail_title" data-index="6">刮痧中的操作事项（5分）（痛感、位置）</td>
					</tr>
					<tr>
						<td ><input placeholder="刮痧中的操作事项（5分）得分" lay-affix="number" type="number"
									lay-verify="required|number" lay-reqText="请填写得分" data-index="1"
									name="score_1[]"  class="layui-input input-score" step="1" min="0" max="5"></td>
					</tr>
					<tr>
						<td class="detail_title" data-index="7">刮痧后处理（5分）</td>
					</tr>
					<tr>
						<td ><input placeholder="刮痧后处理（5分）得分" lay-affix="number" type="number"
									lay-verify="required|number" lay-reqText="请填写得分" data-index="1"
									name="score_1[]"  class="layui-input input-score" step="1" min="0" max="5"></td>
					</tr>
					<tr>
						<td>平均分</td>
					</tr>
					<tr>
						<td ><input type="text" id="average_1" readonly placeholder="刮痧实操考核平均分" name="average[]"  class="layui-input" ></td>
					</tr>
				</table>
			</div>
			<div class="layui-tabs-item">
				<h3>拔罐实操考核（15分）</h3>
				<table class="layui-table layui-table-form">
					<tr>
						<td>考核重点</td>
					</tr>

					<tr>
						<td class="detail_title" data-index="8">拔罐前检查准备（5分）</td>
					</tr>
					<tr>
						<td ><input placeholder="拔罐前检查准备（5分）得分" lay-affix="number" type="number"
									lay-verify="required|number" lay-reqText="请填写得分" data-index="2"
									name="score_2[]"  class="layui-input input-score" step="1" min="0" max="5"></td>
					</tr>
					<tr>
						<td class="detail_title" data-index="9">拔罐中的操作事项（5分）</td>
					</tr>
					<tr>
						<td ><input placeholder="拔罐中的操作事项（5分）得分" lay-affix="number" type="number"
									lay-verify="required|number" lay-reqText="请填写得分" data-index="2"
									name="score_2[]"  class="layui-input input-score" step="1" min="0" max="5"></td>
					</tr>
					<tr>
						<td class="detail_title" data-index="10">拔罐后处理（5分）</td>
					</tr>
					<tr>
						<td ><input placeholder="拔罐后处理（5分）得分" lay-affix="number" type="number"
									lay-verify="required|number" lay-reqText="请填写得分" data-index="2"
									name="score_2[]"  class="layui-input input-score" step="1" min="0" max="5"></td>
					</tr>
					<tr>
						<td>平均分</td>
					</tr>
					<tr>
						<td ><input type="text" id="average_2" readonly placeholder="拔罐实操考核平均分" name="average[]"  class="layui-input" ></td>
					</tr>
				</table>
			</div>
			<div class="layui-tabs-item">
				<h3>推拿实操考核（30分）</h3>
				<table class="layui-table layui-table-form">
					<tr>
						<td style="width: 60px">考核项</td>
						<td>考核重点</td>
					</tr>

					<tr>
						<td rowspan="6">肩颈背（15分）</td>
						<td class="detail_title" data-index="11">位置的精准度（5分）</td>

					</tr>
					<tr>
						<td ><input placeholder="位置的精准度（5分）得分" lay-affix="number" type="number"
									lay-verify="required|number" lay-reqText="请填写得分" data-index="3"
									name="score_3[]"  class="layui-input input-score" step="1" min="0" max="5"></td>
					</tr>
					<tr>
						<td class="detail_title" data-index="11">手法力度（5分）</td>
					</tr>
					<tr>
						<td ><input placeholder="手法力度（5分）得分" lay-affix="number" type="number"
									lay-verify="required|number" lay-reqText="请填写得分" data-index="3"
									name="score_3[]"  class="layui-input input-score" step="1" min="0" max="5"></td>
					</tr>
					<tr>
						<td class="detail_title" data-index="11">老师姿势及发力方向（5分）</td>
					</tr>
					<tr>
						<td ><input placeholder="老师姿势及发力方向（5分）得分" lay-affix="number" type="number"
									lay-verify="required|number" lay-reqText="请填写得分" data-index="3"
									name="score_3[]"  class="layui-input input-score" step="1" min="0" max="5"></td>
					</tr>
					<tr>
						<td rowspan="6">腰臀腿（15分）</td>
						<td class="detail_title" data-index="12">位置的精准度（5分）</td>
					</tr>
					<tr>
						<td ><input placeholder="位置的精准度（5分）得分" lay-affix="number" type="number"
									lay-verify="required|number" lay-reqText="请填写得分" data-index="3"
									name="score_3[]"  class="layui-input input-score" step="1" min="0" max="5"></td>
					</tr>
					<tr>
						<td class="detail_title" data-index="12">手法力度（5分）</td>
					</tr>
					<tr>
						<td ><input placeholder="手法力度（5分）得分" lay-affix="number" type="number"
									lay-verify="required|number" lay-reqText="请填写得分" data-index="3"
									name="score_3[]"  class="layui-input input-score" step="1" min="0" max="5"></td>
					</tr>
					<tr>
						<td class="detail_title" data-index="12">老师姿势及发力方向（5分）</td>
					</tr>
					<tr>
						<td ><input placeholder="老师姿势及发力方向（5分）得分" lay-affix="number" type="number"
									lay-verify="required|number" lay-reqText="请填写得分" data-index="3"
									name="score_3[]"  class="layui-input input-score" step="1" min="0" max="5"></td>
					</tr>
					<tr>
						<td>平均分</td>
						<td ><input type="text" id="average_3" readonly placeholder="推拿实操考核平均分" name="average[]"  class="layui-input" ></td>
					</tr>
				</table>
			</div>
		</div>
	</div>


	<table class="layui-table layui-table-form">
		<tr>
			<td class="layui-td-gray s-color w-80">总平均分</td>
			<td class="layui-td-gray">
				<input  name="total_score" type="text" class="layui-input" readonly/>
			</td>
		</tr>
		<tr>
			<td class="layui-td-gray s-color w-80">实操考试时间</td>
			<td class="layui-td-gray">
				<input id="exam_sdate" name="exam_sdate" type="text" class="layui-input" value="{$sdate}" readonly/>
			</td>
		</tr>
		<tr>
			<td class="layui-td-gray s-color w-80">实操考官</td>
			<td class="layui-td-gray">
				<input name="exam_name" lay-verify="required" lay-reqText="请填写实操考官" type="text" class="layui-input"/>
			</td>
		</tr>
		<tr>
			<td class="layui-td-gray w-80">考核点评</td>
			<td class="layui-td-gray" colspan="2">
				<input name="remark" type="text" class="layui-input" value="" />
			</td>
		</tr>
		<tr>
			<td class="layui-td-gray w-80"><div class="layui-input-inline">现场照片</div> <div class="layui-input-inline"><button type="button" class="layui-btn layui-btn-xs" id="upFile"><i class="layui-icon"></i></button></div></td>
			<td colspan="5" style="line-height:inherit">
				<div class="layui-row" id="fileList">
					<input type="hidden" data-type="file" name="file_ids" value=""  lay-verify="required" lay-reqText="请上传现场照片">
				</div>
			</td>
		</tr>
	</table>


	<div id="formBtn" class="py-3" style="padding: 10px">
<!--		<span class="layui-btn layui-btn-normal" data-status="1"><i class="layui-icon layui-icon-ok"></i> 通过</span>-->
<!--		<span class="layui-btn layui-btn-danger" data-status="2"><i class="layui-icon layui-icon-close"></i> 不通过</span>-->
		<span class="layui-btn layui-btn-normal" data-status="0" >提交</span>
<!--		<button class="layui-btn layui-btn-danger" data-status="2" lay-submit="" lay-filter="webform">不通过</button>-->
	</div>
</form>
{/block}
<!-- /主体 -->

<!-- 脚本 -->
{block name="script"}
<script>
	const moduleInit = ['tool','oaTool'];
	function gouguInit() {
		var form = layui.form,tool=layui.tool,laydate = layui.laydate,upload = layui.upload,oaTool=layui.oaTool;

		//日期时间范围
		laydate.render({
			elem: '#sdate,#exam_sdate',
			type: 'date',
			format: 'yyyy-MM-dd',
			showBottom: false
		});

		var currentPath = window.location.pathname;

		oaTool.addFile({
			btn: 'upFile',
			box: 'fileList'
		});

		//监听提交
		// form.on('submit(webform)', function(data){
		// 	var selectedValues = [];
		// 	$('input[type=radio]:checked').each(function() {
		// 		selectedValues.push($(this).val());
		// 	});
		// 	console.log(selectedValues);
		// 	return false;
		// 	// tool.post("/user/adminview/add", {...data.field,'flag' : 0}, callback);
		// 	// return false;
		// });

		$('body').on('click', '.file-view-img', function () {

			let href = $(this).parents(".layui-row");

			var data = []

			href.find('.file-view-img').map(function() {
				data.push({
					"alt": i,
					"pid": i,
					"src": $(this).attr('data-href'),
				})
			});

			if (data) {
				layer.photos({
					photos: {
						"title": "Photos Demo",
						"start": 0,
						"data": data
					},
					footer: true // 是否显示底部栏 --- 2.8.16+
				});
			}


		});

		let h = [
				`姜：</br>
	1、铺生姜厚度2.5cm-3cm</br>
	2、生姜四边距离灸具1~1.5cm</br>
	艾绒：</br>
	1、铺艾绒厚度0.5cm-1cm</br>
	2、艾绒四边距离姜边距离0.5cm`,
				`1、生姜整体平整度：相对无高低</br>
	2、灸具上整洁度：四周无散落姜末、艾绒</br>
	3、时间控制在3分钟内</br>`,
				`1、自我介绍</br>
	2、顾客主诉（症状）</br>
	3、望诊（面诊、舌诊）</br>
	4、问诊（饮食、作息加睡眠、二便、出汗、运动等）</br>
	5、触诊（体表温度、柔软度、腹部是否胀气）</br>
	6、告知顾客所作项目名称与项目介绍，告知施灸顺序及施灸穴位，腹部，腰部</br>
	7、客户仰卧除施灸部位其他部位用毛毯覆盖</br>
	8、准备生姜、艾绒，铺设是否整齐，生姜厚度，艾绒在生姜里避免掉落产生烫伤</br>
	9、施灸前告知顾客施灸部位需要配合，避免误会，告知艾灸调理过程中有可能会出现哪些调理反应</br>`,
				`10、轻轻把灸具放在顾客身体上，四周保持平整。</br>
	11、询问温度是否适合。烫或不烫的原因和处理。</br>
	12、询问是否做过艾灸。</br>
	13、是否了解艾灸的作用？介绍艾灸的好处。</br>
	14、灸感引导。</br>
	15、强调头疗的好处，以及与关元灸的巧妙搭配。</br>
	16、分享一些类似问题的调理案例。</br>
	注意：</br>
	①翻面时给顾客喝一杯温开水。</br>
	②灸中加绒的时间点和加绒的细节`,
				`17、根据顾客身体情况，铺垫调理疗程。调理项目+调理周期。</br>
18、分享一些灸后反应，顺便让顾客加店里微信。</br>
19、告知灸后注意事项。</br>
20、预约下次时间。`,
				`1、检查工具有无破损，消毒。</br>
2、颈部刮痧的站姿老师马步或弓步。</br>
3、禁忌：避开痣、疣、痘等。`,
				`4、角度为30度或小于30度。</br>
5、刮痧的顺序，先刮督脉，再刮内外膀胱经，肩胛骨缝，胆经三焦（角度为45度）。</br>
6、刮痧的规范细节，频率、速度快慢均匀，力度不轻不重，一般30次左右，不出痧就换位置。询问顾客痛感。</br>
7、刮完痧颈部，搓大椎，背部搓督脉。</br>`,
				`8、整理衣服，保暖，说明注意事项和禁忌症。`,
				`1、检查罐子口有无破损或裂口，消毒。</br>
2、检查有无禁忌症，检查罐子的温度是否合适。`,
				`3、火把粘酒精后，甩出多余的酒精。</br>
4、点燃火把，火把在客户的身体侧面（不要放在身体上面）。</br>
5、距离顾客皮肤保持5-6公分，火把快速放入罐内1周后拔出，吸拔在相应的穴位上。询问顾客感受。`,
				`6、拔好后，火把粘下酒精再放回原位，盖好毯子，保暖。</br>
7、时间控制在10分钟左右，特殊体质5-7分钟（预防出水泡），告知禁忌和注意事项。`,
				`首先让顾客俯卧，调理师站姿（马步或弓步），并将按摩巾盖在顾客按摩的位置，开始推拿放松</br>
1、掌揉上背部(双掌叠加、 每个位置3次），从上到下，从肩胛冈到胸十，掌揉放松</br>
2、拿捏斜方肌（站姿前后弓步），用手发力，拿捏大概20下左右，</br>
3、拇指压揉发力枕后肌群三条线(单手拇指侧峰从下向上发力)，</br>
4、拇指压揉颈椎椎板，从上往下揉，手法不用太重，可以略微轻一点，一直到大椎穴位置，每个位置7~8下</br>
5、拇指压揉颈椎横突，从颈3到颈7位置，每个位置7~8下</br>
肩：</br>
6、将顾客的手臂放到顾客头部两侧，用拇指压揉肩颈角（后斜角肌）、冈上肌</br>
7、背部：T1-12</br>
①指压背部椎板，每个位置7~8下，</br>
②指压胸椎横突，每个位置7~8下，</br>
③拇指侧峰按压肩胛骨内上角半圈，每个位置7~8下</br>
④指压冈下肌、大圆肌、小圆肌 （肩胛骨外侧缘）</br>
⑤松解胸锁乳突肌、喙突，5分钟</br>
8、最后拉伸环节：胸大肌拉伸，拉3次</br>
（剩余10分钟，可以问顾客还有哪里不舒服或者之前的问题解决了几分，可根据情况，进行加强调理，若顾客有表示调理不满意，调理师需要立即寻求店长或大师傅等技术较好的老师协助调理）`,
				`首先让顾客俯卧，老师站姿（马步或弓步），面朝顾客侧后，并将按摩巾盖在顾客按摩的位置，开始推拿放松</br>
1.叠掌揉背部T11-L5,至骶骨面由上⽽下 (双掌叠加、 每个位置三次)</br>
2.压揉腰椎椎板，由上⽽下(双指拇指从上到下竖向压揉)</br>
3.拇指压揉按压腰方肌L1-4横突背面</br>
4.拇指压揉L2-4横突间，三个方向</br>
5.拇指压揉髂骨内缘,从髂后上棘到髂骨最高处，沿着髂骨边缘进行压揉</br>
6.掌揉臀部-骶骨八髎、 髂后上棘，每个位置三次</br>
7.体位俯卧屈髋</br>
拇指压揉骶骨外侧缘臀⼤肌从髂后上棘到尾骨大约5个位置，每个位置7~8下 ；臀中肌、臀小肌  、阔筋膜张肌，髂骨最高点到大转子和髂前上棘之间，大约5个位置，每个位置7~8下；</br>
8.掌揉腘绳肌、小腿三头肌，从上到下，每个位置7~8下</br>
该手法为补充手法，若顾客有腿前侧与膝盖不适情况，可以进行髂腰肌处理；若无症状，不处理</br>
9.髂腰肌手法、 肚脐外三寸，上下内外三个方向、髂窝，无症状，不处理</br>
(髂前上棘内侧1.5寸、以及向上的三个位置) (双手四肢叠加,手指微屈)</br>
10.臀大肌、 腘绳肌 、髂腰肌拉伸  5分钟</br>
（剩余10分钟，可以问顾客还有哪里不舒服或者之前的问题解决了几分，可根据情况，进行加强调理，若顾客有表示调理不满意，调理师需要立即寻求店长或大师傅等技术较好的老师协助调理）</br>`
		]


		$('body').on('click', '.detail_title', function () {

			let index = $(this).data('index');
			let text = $(this).text();
			console.log(index)
			console.log(text)

			var viewportWidth = $(window).width();
			layer.open({
				type: 1,
				title:text,
				shadeClose: true, // 点击遮罩关闭层
				area: [viewportWidth - 100], // 宽高
				content: `
				<div style="padding: 11px;">
${h[index]}
				</div>
				`
			});

		})

		$("input[name='score_0[]']").on('input', function() {
			var currentValue = $(this).val();
			count('score_0','average_0');
		});

		$("input[name='score_1[]']").on('input', function() {
			var currentValue = $(this).val();
			count('score_1','average_1');
		});

		$("input[name='score_2[]']").on('input', function() {
			var currentValue = $(this).val();
			count('score_2','average_2');
		});

		$("input[name='score_3[]']").on('input', function() {
			var currentValue = $(this).val();
			count('score_3','average_3');
		});

		function count(score_name , average_name){
			let score = 0;
			let index = $('[name="'+score_name+'[]"]').length;
			$('[name="'+score_name+'[]"]').each(function (index,el){
				score += $(el).val() ? (parseFloat($(el).val()) > 5 ? 5 : parseFloat($(el).val())) : 0;
			})

			let average = parseFloat(score / index).toFixed(2);

			$("#"+average_name+"").val(average)

			let total_score = 0;

			$('[name="average[]"]').each(function (index,el){
				total_score += $(el).val() ? (parseFloat($(el).val()) > 5 ? 5 : parseFloat($(el).val())) : 0;
			})

			$('[name="total_score"]').val(parseFloat(total_score / 4).toFixed(2))
		}


		//监听提交
		$('#formBtn').on('click','span', function(data) {
			let check_status = $(this).data('status');

			isajax(check_status);
		})

		form.on('select(select-did)', function(data){
			get_leader()
		});

		get_leader()
		function get_leader(){
			let leader_id = $('#did').find('option:selected').data('leader-id')
			let leader_name = $('#did').find('option:selected').data('leader-name')
			$('[name="aid').val(leader_id)
			$('[name="aname').val(leader_name)
		}

		function layer_msg(val , elem){

		}

		function isajax(check_status){

			let score = {
				score_0 : [],
				score_1 : [],
				score_2 : [],
				score_3 : [],
			}

			let flag = true;
			$('.input-score').each(function (index,el){

				if ($(el).val() == null || $(el).val() == ''){
					let placeholderText = $(this).attr('placeholder');
					layer.msg("请填写：" + placeholderText);
					flag = false
					return false
				}

				let name = $(this).attr('name').substring(7,-1)
				score[name].push($(el).val() ? (parseFloat($(el).val()) > 5 ? 5 : parseFloat($(el).val())) : 0)
			})


			if (!flag){
				return false;
			}

			let data = {
				a_view_name:$('[name="a_view_name"]').val(),
				a_view_id:$('[name="a_view_id"]').val(),
				position:$('[name="position"]').val(),
				sex:$('[name="sex"]').val(),
				did:$('[name="did"]').val(),
				start_sdate:$('[name="start_sdate"]').val(),
				aname:$('[name="aname"]').val(),
				aid:$('[name="aid"]').val(),
				courseware_status:$('[name="courseware_status"]').val(),
				courseware_score:$('[name="courseware_score"]').val(),
				courseware_times:$('[name="courseware_times"]').val(),
				score0:score.score_0,
				score1:score.score_1,
				score2:score.score_2,
				score3:score.score_3,
				total_score:$('[name="total_score"]').val(),
				exam_sdate:$('[name="exam_sdate"]').val(),
				exam_name:$('[name="exam_name"]').val(),
				remark:$('[name="remark"]').val(),
				file_ids:$('[name="file_ids"]').val(),
				check_status:check_status
			};

			let callback = function (e) {
				layer.msg(e.msg);
				let is_mobile = e.data.is_mobile

				if (e.code == 0) {
					if (is_mobile != null && is_mobile != ""){
						window.location.pathname = "/user/adminview/search";
					}else{
						parent.layui.pageTable2.reload();
						tool.sideClose(1000);
					}
				}

			}
			tool.post("/user/adminview/examine", data, callback);

			// $.ajax({
			// 	url: "/user/adminview/examine",
			// 	type:'post',
			// 	data: data,
			// 	success: function (e) {
			// 		layer.msg(e.msg);
			// 	}
			// })
		}

	}

</script>
{/block}
<!-- /脚本 -->