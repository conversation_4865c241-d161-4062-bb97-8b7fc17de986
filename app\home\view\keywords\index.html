{extend name="../../base/view/common/base" /}
<!-- 主体 -->
{block name="body"}
<div class="p-3">
	<form class="layui-form gg-form-bar border-x border-t">
		<div class="layui-input-inline" style="width:300px;">
			<input type="text" name="keywords" placeholder="关键字名称" class="layui-input" autocomplete="off" />
		</div>
		<div class="layui-input-inline" style="width:150px;">
			<button class="layui-btn layui-btn-normal" lay-submit="" lay-filter="webform"><i class="layui-icon layui-icon-search mr-1"></i>搜索</button>
			<button type="reset" class="layui-btn layui-btn-reset" lay-filter="reset">清空</button>
		</div>
	</form>
    <table class="layui-hide" id="test" lay-filter="test"></table>
</div>
<script type="text/html" id="status">
	<i class="layui-icon {{#  if(d.status == 1){ }}green layui-icon-ok{{#  } else { }}yellow layui-icon-close{{#  } }}"></i>
</script>
<script type="text/html" id="toolbarDemo">
  <div class="layui-btn-container">
  	<button class="layui-btn layui-btn-sm" lay-event="add">+ 添加关键字</button>
  </div>
</script>
{/block}
<!-- /主体 -->

<!-- 脚本 -->
{block name="script"}
	<script>
	const moduleInit = ['tool'];
	function gouguInit() {
		var table = layui.table, tool = layui.tool, form = layui.form;
		layui.pageTable = table.render({
			elem: '#test',
			title: '关键字列表',
			toolbar: '#toolbarDemo',
			defaultToolbar: false,
			url: "/home/<USER>/index",
			page: true, //开启分页				
			limit: 20,
			cols: [
				[{
					field: 'id',
					width: 80,
					title: 'ID编号',
					align: 'center'
				}, {
					field: 'sort',
					width: 80,
					title: '排序',
					align: 'center',
					edit: "text"
				}, {
					field: 'title',
					title: '关键字名称',
					edit: "text"
				}, {
					field: 'status',
					width: 80,
					title: '状态',
					templet: '#status',
					align: 'center'
				}, {
					width: 100,
					title: '操作',
					align: 'center',
					templet: function (d) {
						var html = '<div class="layui-btn-group"><button class="layui-btn layui-btn-normal layui-btn-xs" lay-event="edit">编辑</button><button class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">删除</button></div>';
						return html;
					}
				}]
			]
		});

		//监听搜索提交
		form.on('submit(webform)', function (data) {
			if (data.field.keywords) {
				layui.pageTable.reload({
					where: {
						keywords: data.field.keywords
					},
					page: {
						curr: 1
					}
				});
			} else {
				location.reload();
			}
			return false;
		});

		//表头工具栏事件
		table.on('toolbar(test)', function(obj){
			if (obj.event === 'add') {
				tool.side("/home/<USER>/add");
				return;
			}
		});
		//监听行工具事件
		table.on('tool(test)', function (obj) {
			var data = obj.data;
			if (obj.event === 'edit') {
				tool.side('/home/<USER>/add?id='+obj.data.id);
				return;
			}
			if (obj.event === 'del') {
				layer.confirm('确定要删除吗？', { icon: 3, title: '提示' }, function (index) {
					let callback = function (e) {
						layer.msg(e.msg);
						if (e.code == 0) {
							obj.del();
						}
					}
					tool.delete("/home/<USER>/delete", { id: data.id }, callback);
					layer.close(index);
				});
			}
		});
	}
</script>
{/block}
<!-- /脚本 -->