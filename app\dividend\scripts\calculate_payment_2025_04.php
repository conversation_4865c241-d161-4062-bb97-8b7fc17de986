<?php
/**
 * 分红人清单数据计算脚本 - 2025年4月
 * 
 * 使用方法：
 * php calculate_payment_2025_04.php
 */

// 引入ThinkPHP框架
require_once __DIR__ . '/../../../vendor/autoload.php';

use think\facade\Db;
use think\facade\Config;
use app\dividend\service\DividendPaymentCalculateService;

// 初始化应用
$app = new think\App();
$app->initialize();

// 设置数据库配置（如果需要）
// 这里假设使用默认配置，如果需要特殊配置可以在这里设置

echo "=== 分红人清单数据计算脚本 ===\n";
echo "计算周期：2025年4月\n";
echo "开始时间：" . date('Y-m-d H:i:s') . "\n";
echo "================================\n\n";

try {
    // 执行计算
    $period = '2025-04';
    $result = DividendPaymentCalculateService::calculatePaymentData($period);
    
    if ($result['success']) {
        echo "✅ 计算成功！\n";
        echo "消息：{$result['message']}\n\n";
        
        if (!empty($result['data'])) {
            echo "📊 计算结果详情：\n";
            echo str_repeat('-', 120) . "\n";
            printf("%-20s %-15s %-15s %-10s %-10s\n", 
                '分红人姓名', '统计周期', '应付金额(元)', '门店数量', '股东记录数');
            echo str_repeat('-', 120) . "\n";
            
            foreach ($result['data'] as $item) {
                printf("%-20s %-15s %-15s %-10s %-10s\n",
                    mb_substr($item['shareholder_name'], 0, 18, 'UTF-8'),
                    $item['period'],
                    number_format($item['payable_amount'], 2),
                    $item['store_count'],
                    $item['shareholder_count']
                );
            }
            echo str_repeat('-', 120) . "\n";
        }
        
        // 获取统计信息
        $statistics = DividendPaymentCalculateService::getCalculateStatistics($period);
        echo "\n📈 汇总统计：\n";
        echo "分红人数量：{$statistics['shareholder_count']} 人\n";
        echo "总应付金额：" . number_format($statistics['total_payable_amount'], 2) . " 元\n";
        echo "总调整金额：" . number_format($statistics['total_adjustment_amount'], 2) . " 元\n";
        echo "总实际应付金额：" . number_format($statistics['total_actual_payable_amount'], 2) . " 元\n";
        echo "总实付金额：" . number_format($statistics['total_paid_amount'], 2) . " 元\n";
        echo "总未付金额：" . number_format($statistics['total_unpaid_amount'], 2) . " 元\n";
        
    } else {
        echo "❌ 计算失败！\n";
        echo "错误信息：{$result['message']}\n";
    }
    
} catch (\Exception $e) {
    echo "❌ 脚本执行异常！\n";
    echo "异常信息：" . $e->getMessage() . "\n";
    echo "异常文件：" . $e->getFile() . " 第 " . $e->getLine() . " 行\n";
}

echo "\n================================\n";
echo "结束时间：" . date('Y-m-d H:i:s') . "\n";
echo "=== 分红人清单数据计算完成 ===\n";
