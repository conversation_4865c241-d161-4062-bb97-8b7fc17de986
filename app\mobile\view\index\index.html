{extend name="../../base/view/common/base" /}
{block name="style"}
<link rel="stylesheet" href="{__GOUGU__}/gougu/css/layout.css" media="all">
{/block}
<!-- 主体 -->
{block name="body"}
<style>

    body {
        background-color: #f5f6f7;
    }

    .uni-page {
        display: block;
        width: 100%;
        height: 100%;
    }

    .uni-page-head-main {
        display: block;
        box-sizing: border-box;
    }

    .uni-page-head .uni-page-head-hd {
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-align: center;
        -webkit-align-items: center;
        align-items: center;
        font-size: 16px;
    }

    .uni-page-head-main .uni-page-head {
        position: fixed;
        left: var(--window-left);
        right: var(--window-right);
        height: 44px;
        height: calc(44px + constant(safe-area-inset-top));
        height: calc(44px + env(safe-area-inset-top));
        padding: 7px 3px;
        padding-top: calc(7px + constant(safe-area-inset-top));
        padding-top: calc(7px + env(safe-area-inset-top));
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        overflow: hidden;
        -webkit-box-pack: justify;
        -webkit-justify-content: space-between;
        justify-content: space-between;
        box-sizing: border-box;
        z-index: 998;
        color: #fff;
        background-color: #000;
        -webkit-transition-property: all;
        transition-property: all;
        width: 100%;
    }

    .uni-page-head .uni-page-head-bd {
        position: absolute;
        left: 70px;
        right: 70px;
        min-width: 0;
    }

    .uni-page-head .uni-page-head__title {
        font-weight: 700;
        font-size: 16px;
        line-height: 30px;
        text-align: center;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
    }

    .mine-container .header-section[data-v-1716ef58] {
        padding: 15px 15px 45px 15px;
        background-color: #3c96f3;
        color: #fff;
    }

    .uni-page-head-main .uni-page-head ~ .uni-placeholder {
        width: 100%;
        height: 44px;
        height: calc(44px + constant(safe-area-inset-top));
        height: calc(44px + env(safe-area-inset-top));
    }

    .mine-container .header-section .cu-avatar[data-v-1716ef58] {
        border: 2px solid #eaeaea;
    }

    .layui-layout-body {
        overflow-x: visible;
    }

    .layui-card-header {
        height: 40px;
        line-height: 40px;
        border-bottom: none;
    }

    .layui-card-header {
        position: relative;
        padding: 0 12px;
    }

    .layui-card-header {
        position: relative;
        height: 42px;
        line-height: 42px;
        padding: 0 15px;
        border-bottom: 1px solid #f8f8f8;
        color: #333;
        border-radius: 2px 2px 0 0;
        font-size: 14px;
    }

    .square .layui-col-xs4 {
        border: 1px solid #f5f5f5;
        border-left-width: 1px;
        border-left-style: solid;
        border-left-color: rgb(245, 245, 245);
        cursor: pointer;
    }

    .square .layui-col-xs4 div {
        padding: 15px 0;
        text-align: center;
        background-color: #fff;
        color: #666;
    }

    .square .layui-col-xs4 i {
        font-size: 24px;
        font-weight: 800;
        display: block;
        padding-bottom: 5px;
        color: #4285f4;
    }
</style>
<div class="layui-layout-body">
    <div id="GouguApp">
        <div class="layui-layout gg-layout layout-menu-{$web.menu_mode|default='classical'}">

            <div class="uni-page" data-page="pages/mine/index" >
                <div class="layui-row" id="toolbarDemo">
                    <div class="layui-col-md12" style="margin-bottom: 50px">
                        <div class="layui-card">
                            <div class="layui-card-header" style="border-bottom:1px solid #eee;"><h3 class="h3-title">发起申请</h3></div>
                            {volist name="type" id="v" offset="0" length='5'}
                            <div class="layui-card-header"><strong>{$v.title}</strong></div>
                            <div class="square">
                                <div class="layui-row">
                                    {volist name="list" id="vo"}
                                    {eq name="vo.type" value="$v.id"}
                                    <div class="layui-col-xs4" data-type="{$vo.id}" title="{$vo.title}"><div><i class="iconfont {$vo.icon}"></i>{$vo.title}</div></div>
                                    {/eq}
                                    {/volist}
                                </div>
                            </div>
                            {/volist}
                        </div>
                    </div>

                </div>
            </div>

            {include file="../../mobile/view/index/tabbar" /}


        </div>
    </div>
</div>


<!-- /主体 -->
{/block}
<!-- 脚本 -->
{block name="script"}
<script>

    function clickvoice(id) {

    }
    $('.square').on('click','.layui-col-xs4',function(){
        var type=$(this).data('type');

        window.location = '/oa/approve/add?type=' + type;

    });

</script>
{/block}
<!-- /脚本 -->