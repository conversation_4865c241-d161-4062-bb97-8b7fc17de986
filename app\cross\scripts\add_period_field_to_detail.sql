-- 为储值跨店结算明细表添加period字段的SQL脚本
-- 创建时间：2025-07-22
-- 说明：为现有的oa_cross_store_settle_detail表添加period字段和相关索引

-- 添加period字段
ALTER TABLE `oa_cross_store_settle_detail` 
ADD COLUMN `period` varchar(7) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '快照月份 (格式: YYYY-MM)' 
AFTER `customer_belong_store_id`;

-- 添加period字段的索引
ALTER TABLE `oa_cross_store_settle_detail` 
ADD INDEX `idx_period` (`period`) USING BTREE COMMENT '快照月份索引';

-- 可选：为现有数据设置默认的period值（根据settlement_time字段推算）
-- 注意：执行前请确认是否需要为现有数据设置period值
-- UPDATE `oa_cross_store_settle_detail` 
-- SET `period` = DATE_FORMAT(`settlement_time`, '%Y-%m') 
-- WHERE `period` = '' AND `settlement_time` IS NOT NULL;
