<?php
/**
 * @copyright Copyright (c) 2021 仲正堂工作室
 * @license https://opensource.org/licenses/GPL-3.0
 * @link https://www.gougucms.com
 */
declare (strict_types=1);

namespace app\api\controller;

use app\api\BaseController;
use app\oa\controller\Approve;
use app\store\service\StoreBusinessService;
use app\store\service\StoreSalaryService;
use app\store\service\StoreService;
use PhpOffice\PhpSpreadsheet\Calculation\Logical\Boolean;
use think\facade\Db;
use think\Session;

class douying
{

    protected $appid = "aw2qdipvotkvg8p0";
    protected $appSecrent = "2d4d6f9e93c4f82d8cb100665451674b";

    protected $token = "clt.fa6f2ba3de71abde0216ef43966d1eadwvqDeT43MzPO47MCjbTGdx8BRBNl_lf";

    protected $account_id = "7112712933498423310";

    public function __construct()
    {
        $this->gettoken();
    }

    //抖音token
    public function gettoken()
    {
        $data = [
            'client_key' => $this->appid,
            'client_secret' => $this->appSecrent,
            'grant_type' => 'client_credential',
        ];
        $response = $this->ajax('https://open.douyin.com/oauth/client_token/', $data);
        $data = json_decode($response)->data;
        $access_token = $data->access_token;

        $this->token = $access_token;
        return $access_token;
    }

    //获取抖音商铺信息
    public function douying_shop()
    {
        $url = "https://open.douyin.com/goodlife/v1/shop/poi/query/?account_id=" . $this->account_id . "&page=1&size=100";

        $response = $this->ajax($url, [], 'GET', true, array('content-type: application/json', "access-token: {$this->token}"));

        $res = json_decode($response);

        $store = getmd("门店");

        $array = array();

        Db::name("dy_pois")->where([['did', '>', 1]])->delete();

        if ($res && isset($res->data) && empty($res->data->error_code)) {
            $data = $res->data;
            $pois = $data->pois;

            foreach ($store as $k => $v) {

                $dname = str_replace('路', "", $v['title']);
                $dname = str_replace('店', "", $dname);

                foreach ($pois as $kk => $vv) {

                    $poi = $vv->poi;
                    $account = $vv->account;
                    if (strpos($poi->poi_name, $dname) !== false) {

                        $array[] = [
                            'did' => $v['id'],
                            'address' => $poi->address,
                            'latitude' => $poi->latitude,
                            'longitude' => $poi->longitude,
                            'poi_id' => $poi->poi_id,
                            'poi_name' => $poi->poi_name,

                            'poi_account_id' => $account->poi_account->account_id,
                            'poi_account_name' => $account->poi_account->account_name,

                            'create_time' => strtotime(date("Y-m-d H:i:s")),
                        ];
                        break;
                    }
                }
            }
            Db::name("dy_pois")->insertAll($array);

        } else {
            dump($res->data);
        }

    }


    //获取抖音账单
    public function douying_ledger()
    {

        $bill_date = "2025-05-04";
        $end_time = strtotime("2025-05-05");

        $cursor = 0;

        $res = $this->douying_ledger_for($bill_date, $cursor);

        $arr = array();

        Db::name("dy_ledger")->where(['create_time' => $bill_date])->delete();

        foreach ($res as $k => $v) {
            $ob = get_object_vars($v);
            $amount = get_object_vars($v->amount);
            $certificate = get_object_vars($v->certificate);
            $goods = get_object_vars($v->goods);
            $order_attrribute = get_object_vars($v->order_attrribute);
            $merged = array_merge($ob, $amount, $certificate, $goods, $order_attrribute);

            $merged['tid'] = $merged['id'];
            unset($merged['id']);
            unset($merged['amount']);
            unset($merged['certificate']);
            unset($merged['goods']);
            unset($merged['order_attrribute']);

            $merged['create_time'] = $bill_date;

            $arr[] = $merged;

        }

        Db::name("dy_ledger")->strict(false)->field(true)->insertAll($arr);

    }


    //抖音账单查询
    public function douying_ledger_for($bill_date, $cursor, $array = [])
    {
        $page = 1;
        $size = 2;

//        $url = "https://open.douyin.com/goodlife/v1/settle/ledger/query/?account_id=" . $this->account_id . "&page=$page&size=$size&bill_date={$bill_date}&cursor={$cursor}";
        $url = "https://open.douyin.com/goodlife/v1/settle/bill/composite_query/?account_id=" . $this->account_id . "&page=$page&size=$size&bill_date={$bill_date}&cursor={$cursor}";
        $response = $this->ajax($url, [], 'GET', true, array('content-type: application/json', "access-token: {$this->token}"));

        $res = json_decode($response);

        $data = $res->data;
        dump($data);
        $cursor = $data->cursor;

        if (!empty($cursor)) {
            $array = array_merge($array, $data->ledger_records);
            return $this->douying_ledger_for($bill_date, $cursor, $array);
        } else {
            return $array;
        }
    }

    //抖音验券历史查询

    public function douying_verify()
    {

        $start_day = date("Y-m-01");
        $start_date = strtotime($start_day . " 00:00:00");
        $end_time = strtotime(date('Y-m-d 23:59:59', strtotime('-1 day')));
        $end_time = strtotime("2025-05-01 23:59:59");

        $cursor = 0;

        $res = $this->douying_verify_for($start_date, $end_time, $cursor);
        dump($res);
        exit();
        $arr = array();

        Db::name("dy_verify")->where(['create_time' => $start_day])->delete();

        foreach ($res as $k => $v) {

            try {
                $item = [
                    'create_time' => $start_day,
                    'brand_ticket_amount' => $v->amount->brand_ticket_amount,
                    'coupon_pay_amount' => $v->amount->coupon_pay_amount,
                    'original_amount' => $v->amount->original_amount,
                    'pay_amount' => $v->amount->pay_amount,

                    'can_cancel' => $v->can_cancel ? 0 : 1,
                    'certificate_id' => $v->certificate_id,
                    'code' => isset($v->code) ? $v->code : '',
                    'account_id' => $v->sku->account_id,
                    'groupon_type' => $v->sku->groupon_type,
                    'market_price' => $v->sku->market_price,
                    'sku_id' => $v->sku->sku_id,
                    'sold_start_time' => $v->sku->sold_start_time,
                    'third_sku_id' => $v->sku->third_sku_id,
                    'title' => $v->sku->title,
                    'status' => $v->status,
                    'verify_id' => $v->verify_id,
                    'verify_time' => $v->verify_time,
                    'verify_type' => $v->verify_type,
                    'jsoncode' => serialize($v)
                ];
            } catch (\Exception $e) {
                dump($v);
                dump($e);
            }

            $arr[] = $item;
        }

        Db::name("dy_verify")->insertAll($arr);

    }

    public function douying_verify_for($start_time, $end_time, $cursor, $array = [])
    {
        $page = 1;
        $size = 1;

        $url = "https://open.douyin.com/goodlife/v1/fulfilment/certificate/verify_record/query/?account_id=" . $this->account_id .
            "&page=$page&size=$size&start_time={$start_time}&end_time={$end_time}&cursor={$cursor}";
        $response = $this->ajax($url, [], 'GET', true, array('content-type: application/json', "access-token: {$this->token}"));

        $res = json_decode($response);

        $data = $res->data;
        $records = $data->records_v2;

        if (!empty($records)) {
            $array = array_merge($array, $records);
            $end_arr = end($records);
            $cursor = $end_arr->cursor;
            return $this->douying_verify_for($start_time, $end_time, $cursor, $array);
        } else {
            return $array;
        }

    }


    //抖音综合账单
    public function douying_composite()
    {
        //每月1号的时候 获取上个月的数据
        $sdate = date("Y-m");
        $day = date("d");
        if ($day == '01'){
            $sdate = date("Y-m", strtotime("-1 day"));
        }

        $bill_date = "$sdate-01";
        $end_date = date("Y-m-d", strtotime("-1 day"));

        $cursor = 0;

        $between_days = getDatesBetween($bill_date, $end_date);

        $re_data = array();
        foreach ($between_days as $k => $v) {
            $res = $this->douying_composite_for($v, $cursor);
            $re_data = array_merge($re_data, $res);
        }

        $arr = array();

        Db::name("dy_composite")->where(['sdate' => $sdate])->delete();

        foreach ($re_data as $k => $v) {

            try {
                $item = [
                    'create_time' => date("Y-m-d" , $v->settle_time ),
                    'sdate' => $sdate,

                    'account_id' => $v->account_id,

                    'goods' => $v->amount->goods,
                    'ledger_platform_ticket' => $v->amount->ledger_platform_ticket,
                    'ledger_total' => $v->amount->ledger_total,
                    'original' => $v->amount->original,
                    'pay' => $v->amount->pay,
                    'total_commission' => $v->amount->total_commission,
                    'total_merchant_platform_service' => $v->amount->total_merchant_platform_service,

                    'biz_type' => $v->biz_type,
                    'certificate_id' => $v->certificate_id,
                    'code' => $v->code,
                    'fund_amount' => $v->fund_amount,
                    'fund_amount_type' => $v->fund_amount_type,
                    'group_name' => $v->group_name,
                    'item_order_id' => $v->item_order_id,
                    'ledger_id' => $v->ledger_id,
                    'poi_id' => $v->poi_id,
                    'settle_time' => $v->settle_time,
                    'settle_type' => $v->settle_type,
                    'settle_type_desc' => $v->settle_type_desc,
                    'shop_order_id' => $v->shop_order_id,
                    'sku_id' => $v->sku_id,
                    'verify_id' => $v->verify_id,
                    'verify_poi_account_id' => $v->verify_poi_account_id,
                    'jsoncode' => serialize($v)
                ];
            } catch (\Exception $e) {
                dump($v);
                dump($e);
            }

            $arr[] = $item;


        }

        Db::name("dy_composite")->strict(false)->field(true)->insertAll($arr);

    }
    public function douying_composite_for($bill_date, $cursor, $array = [])
    {
        $page = 1;
        $size = 2;

        $url = "https://open.douyin.com/goodlife/v1/settle/bill/composite_query/?account_id=" . $this->account_id . "&page=$page&size=$size&bill_date={$bill_date}&cursor={$cursor}";
        $response = $this->ajax($url, [], 'GET', true, array('content-type: application/json', "access-token: {$this->token}"));

        $res = json_decode($response);

        $data = $res->data;

        $cursor = $data->cursor;

        if (!empty($cursor)) {
            $array = array_merge($array, $data->ledger_records);
            return $this->douying_composite_for($bill_date, $cursor, $array);
        } else {
            return $array;
        }
    }

    public function douying_composite_md()
    {
        //每月1号的时候 获取上个月的数据
        $sdate = date("Y-m");
        $day = date("d");
        if ($day == '01'){
            $sdate = date("Y-m", strtotime("-1 day"));
        }

        Db::name("store_bill_s")->where(['sdate' => $sdate , 'aid' => 1 , 'field' => 'in_dy'])->delete();

        $sql = "select 
dp.did,dp.poi_id,
jo.total_income,jo.total_expense,jo.total_income_s,jo.total_expense_s,
jo_store.id as store_id,
jo_dep.title as dname
from
oa_dy_pois dp
left JOIN (
SELECT 
    SUM(CASE WHEN settle_type = 1 THEN ledger_total ELSE 0 END) AS total_income,
    SUM(CASE WHEN settle_type = 2 THEN ledger_total ELSE 0 END) AS total_expense,
    SUM(CASE WHEN settle_type = 1 THEN total_commission ELSE 0 END) AS total_income_s,
    SUM(CASE WHEN settle_type = 2 THEN total_commission ELSE 0 END) AS total_expense_s,
		poi_id
FROM oa_dy_composite
where sdate = '$sdate'
GROUP BY poi_id
) jo on jo.poi_id = dp.poi_id
left JOIN (
	select * from oa_store where sdate = '$sdate'
) jo_store on jo_store.did = dp.did
left JOIN (
	select * from oa_department
) jo_dep on jo_dep.id = dp.did
";

        $re = Db::query($sql);

        $arr = array();
        foreach ($re as $k => $v){
            $data = [
                'store_id' => $v['store_id'],
                'sdate' => $sdate,
                'abstract' => "{$sdate}抖音收入",
                'title_1' => '收入',
                'title_2' => '抖音',
                'amount' => round(($v['total_income'] - $v['total_expense']) / 100 , 2),
                'sxf' => round(($v['total_income_s'] - $v['total_expense_s']) / 100 , 2),
                'remark' => '自动获取',
                'did' => $v['did'],
                'dname' => $v['dname'],
                'status' => 1,
                'field' => 'in_dy',
                'is_lock' => '0',
                'aid' => '1',
                'create_time' => date("Y-m-d H:i:s"),
                'type' => 1,
            ];

            $arr[] = $data;
        }

        Db::name("store_bill_s")->insertAll($arr);

    }

    public function ajax($url, $data, $method = 'POST', $https = true, $headers = array())
    {
        //1.初始化url
        $ch = curl_init($url);
        //2.设置相关的参数
        //字符串不直接输出,进行一个变量的存储
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        //判断是否为https请求
        if ($https === true) {
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
            curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
        }
        //判断是否为post请求
        if ($method == 'POST') {
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($data));
        }
        if (!empty($headers)) {
            curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        } else {
            curl_setopt($ch, CURLOPT_HTTPHEADER, array('Content-type:application/x-www-form-urlencoded'));
        }

        //3.发送请求
        $str = curl_exec($ch);
        //4.关闭连接
        curl_close($ch);
        //6.返回请求到的结果
        return $str;

    }


}


