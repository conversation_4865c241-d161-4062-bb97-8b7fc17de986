{extend name="../../base/view/common/base" /}

{block name="style"}
<style>
    /* 查看页面样式 */
    .layui-card {
        border: 1px solid #e6e6e6;
        border-radius: 4px;
        margin-bottom: 15px;
    }

    .layui-card-header {
        background-color: #f8f8f8;
        padding: 10px 15px;
        border-bottom: 1px solid #e6e6e6;
        font-weight: bold;
    }

    .layui-card-body {
        padding: 15px;
    }
    
    /* 表格样式优化 */
    .layui-table {
        margin-bottom: 0;
    }

    .layui-table td {
        padding: 8px 12px;
        vertical-align: middle;
    }

    .layui-table th {
        background-color: #f8f8f8;
        font-weight: bold;
    }
    
    .layui-td-gray {
        background-color: #f5f5f5;
        font-weight: bold;
        text-align: left;
        vertical-align: middle;
        width: 140px;
        padding: 8px 12px;
    }

    /* 只读字段样式 */
    .readonly-value {
        background-color: #fff;
        color: #333;
        padding: 8px 12px;
    }

    /* 金额显示样式 - 参考门店分红明细表 */
    .amount-display {
        font-weight: bold;
        color: #4CAF50;
    }
    
    /* 页面内容区域 */
    .view-content {
        padding: 20px;
        height: 100%;
        box-sizing: border-box;
        overflow-y: auto;
    }
</style>
{/block}

{block name="body"}
<div class="view-content">
    <!-- 页面标题 -->
    <div style="margin-bottom: 20px;">
        <h3 style="margin: 0 0 15px 0; font-size: 18px; font-weight: bold; color: #333;">门店卡余额详情</h3>
    </div>

    <!-- 基础信息 -->
    <div class="layui-card">
        <div class="layui-card-header">基础信息</div>
        <div class="layui-card-body">
            <table class="layui-table">
                <tr>
                    <td class="layui-td-gray">月份</td>
                    <td style="width: 200px;">
                        <span style="font-weight: bold;">{$detail.period|default='-'}</span>
                    </td>
                    <td class="layui-td-gray">门店</td>
                    <td style="width: 200px;">
                        <span style="font-weight: bold;">{$detail.store_name|default='-'}</span>
                    </td>
                </tr>
                <tr>
                    <td class="layui-td-gray">卡余额</td>
                    <td>
                        <span style="font-weight: bold;">{$detail.card_balance|number_format=2|default='0.00'}</span>
                    </td>
                    <td class="layui-td-gray">逻辑类型<span style="font-size: 12px; color: #999; display: block;">（余额≥60万为老店，否则为新店）</span></td>
                    <td>
                        {if $detail.logical_store_type == '老店'}
                            <span style="color: #FF5722; font-weight: bold;">{$detail.logical_store_type}</span>
                        {else}
                            <span style="color: #2196F3; font-weight: bold;">{$detail.logical_store_type}</span>
                        {/if}
                    </td>
                </tr>
            </table>
        </div>
    </div>

    <!-- 门店类型信息 -->
    <div class="layui-card">
        <div class="layui-card-header">门店类型信息</div>
        <div class="layui-card-body">
            <table class="layui-table">
                <tr>
                    <td class="layui-td-gray">上月逻辑类型<span style="font-size: 12px; color: #999; display: block;">（上个月的逻辑门店类型）</span></td>
                    <td style="width: 200px;">
                        {if $detail.previous_month_logical_type == '老店'}
                            <span style="color: #FF5722; font-weight: bold;">{$detail.previous_month_logical_type}</span>
                        {else}
                            <span style="color: #2196F3; font-weight: bold;">{$detail.previous_month_logical_type}</span>
                        {/if}
                    </td>
                    <td class="layui-td-gray">结算类型<span style="font-size: 12px; color: #999; display: block;">（延迟一个月生效，一旦变老，永不降级）</span></td>
                    <td style="width: 200px;">
                        {if $detail.settlement_store_type == '老店'}
                            <span style="color: #FF5722; font-weight: bold;">{$detail.settlement_store_type}</span>
                        {else}
                            <span style="color: #2196F3; font-weight: bold;">{$detail.settlement_store_type}</span>
                        {/if}
                    </td>
                </tr>
            </table>
        </div>
    </div>

    <!-- 底部区域 -->
</div>
{/block}