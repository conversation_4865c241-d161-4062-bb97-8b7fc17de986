<?php
/**
 * @copyright Copyright (c) 2021 仲正堂工作室
 * @license https://opensource.org/licenses/GPL-3.0
 * @link https://www.gougucms.com
 */

declare (strict_types=1);

namespace app\api\controller;

use app\base\BaseController;
use think\facade\Db;
use think\facade\Log;
use app\repair\service\ApproveMapService;

class Repair extends BaseController
{
    /**
     * 获取用户所属门店列表（不需要权限验证）
     * @return \think\response\Json
     */
    public function get_user_stores()
    {
        try {
            // 直接使用BaseController中的用户信息
            $admin_id = $this->uid;
            
            if (empty($admin_id)) {
                return json(['code' => 1, 'msg' => '用户未登录']);
            }
            
            $admin_info = Db::name('admin')->where('id', $admin_id)->find();
            
            if (!$admin_info) {
                return json(['code' => 1, 'msg' => '获取用户信息失败']);
            }
            
            $stores = [];
            
            // 判断是否为区域店长（职位ID为88）
            $is_area_manager = $this->isAreaManager();
            // 判断是否为店长（包括店长和区域店长）
            $is_store_manager = $this->isStoreManager();
            
            if ($is_area_manager) {
                // 区域店长特殊处理 - 直接查询区域店长负责的分管部门
                $area_departments = Db::name('department')
                    ->where('leader_id', $admin_id) // 负责人是当前用户
                    ->where('status', 1) // 只获取启用状态的部门
                    ->field('id, title')
                    ->select()
                    ->toArray();
                
                // 找出区域部门（title字段以"分管"结尾）
                $area_department_id = 0;
                foreach ($area_departments as $department) {
                    if (mb_substr($department['title'], -2) === '分管') {
                        $area_department_id = $department['id'];
                        break;
                    }
                }
                
                if ($area_department_id > 0) {
                    // 获取该区域下的所有门店（上级部门ID为区域部门ID且remark为"门店"的部门）
                    $stores = Db::name('department')
                        ->where('pid', $area_department_id)
                        ->where('remark', '门店')
                        ->where('status', 1)
                        ->field('id, title')
                        ->select()
                        ->toArray();
                }
            } else if ($is_store_manager) {
                // 普通店长处理 - 调整为查询leader_id为自己的门店部门
                $stores = Db::name('department')
                    ->where('leader_id', $admin_id) // 负责人是当前用户
                    ->where('remark', '门店') // 只获取remark字段为"门店"的记录
                    ->where('status', 1) // 只获取启用状态的部门
                    ->field('id, title')
                    ->select()
                    ->toArray();
            } else {
                // 非店长、非区域店长处理：获取用户所在部门
                if (!empty($admin_info['did'])) {
                    // 处理多部门情况，did可能是逗号分隔的多个ID
                    $department_ids = explode(',', $admin_info['did']);
                    if (!empty($department_ids)) {
                        // 获取用户所在部门信息
                        $departments = Db::name('department')
                            ->where('id', 'in', $department_ids)
                            ->where('status', 1) // 只获取启用状态的部门
                            ->field('id, title')
                            ->select()
                            ->toArray();
                        
                        $stores = $departments;
                    }
                }
            }
            
            return json([
                'code' => 0, 
                'msg' => '获取成功', 
                'data' => $stores
            ]);
            
        } catch (\Exception $e) {
            // 记录错误日志
            Log::error('获取用户门店列表失败：' . $e->getMessage());
            return json(['code' => 1, 'msg' => '获取用户门店列表失败：' . $e->getMessage()]);
        }
    }
    
    /**
     * 验证用户是否为店长
     * @return bool
     */
    protected function isStoreManager()
    {
        // 店长职位ID数组（包括店长、区域店长）
        $storeManagerPositionIds = [12, 88];
        
        // 获取用户信息
        $admin = Db::name('admin')
            ->where('id', $this->uid)
            ->where('status', 1)
            ->field('position_id')
            ->find();
            
        if (!$admin) {
            return false;
        }
        
        // 判断用户职位是否在店长职位列表中
        return in_array($admin['position_id'], $storeManagerPositionIds);
    }
    
    /**
     * 验证用户是否为区域店长
     * @return bool
     */
    protected function isAreaManager()
    {
        // 区域店长职位ID
        $areaManagerPositionId = 88;
        
        // 获取用户信息
        $admin = Db::name('admin')
            ->where('id', $this->uid)
            ->where('status', 1)
            ->field('position_id')
            ->find();
            
        if (!$admin) {
            return false;
        }
        
        // 判断用户职位是否包含区域店长ID
        // position_id可能是逗号分隔的多个ID，如"12,88"
        $positionIds = explode(',', $admin['position_id']);
        return in_array($areaManagerPositionId, $positionIds);
    }

    /**
     * 获取维修人员列表（用于维修人选择，支持搜索，查询全部数据）
     * @return \think\response\Json
     */
    public function getRepairerList()
    {
        $param = get_params();
        $keywords = $param['keywords'] ?? '';

        try {
            $where = [
                ['status', '=', 1] // 只获取启用状态的维修人员
            ];

            // 关键词搜索
            if (!empty($keywords)) {
                $where[] = ['name', 'like', '%' . $keywords . '%'];
            }

            // 获取维修人员列表 - 查询全部数据，不分页
            $repairerList = Db::name('repair_personnel')
                ->where($where)
                ->field('id, name, phone')
                ->order('id desc')
                ->select()
                ->toArray();

            return json(['code' => 0, 'msg' => '获取成功', 'data' => $repairerList]);
        } catch (\Exception $e) {
            Log::error('获取维修人员列表失败：' . $e->getMessage());
            return json(['code' => 1, 'msg' => '获取维修人员列表失败：' . $e->getMessage()]);
        }
    }

    /**
     * 测试转审功能的流程同步
     * @return \think\response\Json
     */
    public function testTransferSync()
    {
        try {
            $param = get_params();
            $process_id = $param['process_id'] ?? 0;

            if (empty($process_id)) {
                return json(['code' => 1, 'msg' => '请提供流程ID']);
            }

            // 获取同步前的审批记录
            $before_approve = Db::name('approve')
                ->where('remark', 'like', '%repair_' . $process_id . '%')
                ->find();

            // 获取流程信息
            $process = Db::name('repair_process')->where('id', $process_id)->find();
            if (!$process) {
                return json(['code' => 1, 'msg' => '流程不存在']);
            }

            // 获取节点信息
            $nodes = Db::name('repair_process_nodes')
                ->where('process_id', $process_id)
                ->order('node_type asc, created_time asc')
                ->select()
                ->toArray();

            $result = [
                'process_info' => [
                    'id' => $process['id'],
                    'process_no' => $process['process_no'],
                    'store_name' => $process['store_name'],
                    'current_node' => $process['current_node'],
                    'status' => $process['status']
                ],
                'nodes' => [],
                'transfer_info' => [],
                'before_sync' => $before_approve ? [
                    'check_admin_ids' => $before_approve['check_admin_ids'],
                    'flow_admin_ids' => $before_approve['flow_admin_ids']
                ] : null,
                'sync_result' => null,
                'approve_record' => null,
                'flow_records' => []
            ];

            // 处理节点信息
            foreach ($nodes as $node) {
                $node_info = [
                    'node_type' => $node['node_type'],
                    'handler_id' => $node['handler_id'],
                    'handler_name' => $node['handler_name'],
                    'node_status' => $node['node_status'],
                    'is_transferred' => $node['is_transferred'] ?? 0
                ];

                if ($node['is_transferred'] == 1) {
                    $node_info['transfer_to_id'] = $node['transfer_to_id'];
                    $node_info['transfer_to_name'] = $node['transfer_to_name'];
                    $node_info['transfer_reason'] = $node['transfer_reason'];
                    $result['transfer_info'][] = $node_info;
                }

                $result['nodes'][] = $node_info;
            }

            // 执行同步
            $service = new ApproveMapService();
            $sync_result = $service->syncToApprove($process_id);
            $result['sync_result'] = $sync_result;

            if ($sync_result) {
                // 获取同步后的审批记录
                $approve_record = Db::name('approve')
                    ->where('remark', 'like', '%repair_' . $process_id . '%')
                    ->find();

                if ($approve_record) {
                    $result['approve_record'] = [
                        'id' => $approve_record['id'],
                        'check_admin_ids' => $approve_record['check_admin_ids'],
                        'flow_admin_ids' => $approve_record['flow_admin_ids'],
                        'check_status' => $approve_record['check_status'],
                        'check_step_sort' => $approve_record['check_step_sort']
                    ];

                    // 获取当前审核人姓名
                    if (!empty($approve_record['check_admin_ids'])) {
                        $current_admin = Db::name('admin')->where('id', $approve_record['check_admin_ids'])->find();
                        if ($current_admin) {
                            $result['approve_record']['current_admin_name'] = $current_admin['name'];
                        }
                    }

                    // 获取flow_record记录
                    $flow_records = Db::name('flow_record')
                        ->where('action_id', $approve_record['id'])
                        ->where('type', 1)
                        ->where('delete_time', 0)
                        ->order('check_time asc')
                        ->select()
                        ->toArray();

                    foreach ($flow_records as $record) {
                        $result['flow_records'][] = [
                            'check_user_id' => $record['check_user_id'],
                            'content' => $record['content'],
                            'status' => $record['status'],
                            'check_time' => date('Y-m-d H:i:s', $record['check_time'])
                        ];
                    }
                }
            }

            return json(['code' => 0, 'msg' => '测试完成', 'data' => $result]);

        } catch (\Exception $e) {
            Log::error('测试转审同步失败：' . $e->getMessage());
            return json(['code' => 1, 'msg' => '测试失败：' . $e->getMessage()]);
        }
    }
}