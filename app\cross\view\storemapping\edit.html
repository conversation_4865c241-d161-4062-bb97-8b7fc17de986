{extend name="../../base/view/common/base" /}

{block name="style"}
<style>
    /* 编辑页面样式 - 参考balance/edit.html */
    .layui-card {
        border: 1px solid #e6e6e6;
        border-radius: 4px;
        margin-bottom: 15px;
    }

    .layui-card-header {
        background-color: #f8f8f8;
        padding: 10px 15px;
        border-bottom: 1px solid #e6e6e6;
        font-weight: bold;
    }

    .layui-card-body {
        padding: 15px;
    }

    .layui-td-gray {
        background-color: #f5f5f5;
        font-weight: bold;
        text-align: left;
        vertical-align: middle;
        width: 140px;
        padding: 8px 12px;
    }

    /* 表格样式优化 */
    .layui-table {
        margin-bottom: 0;
    }

    .layui-table td {
        padding: 8px 12px;
        vertical-align: middle;
    }

    .layui-table th {
        background-color: #f8f8f8;
        font-weight: bold;
    }

    /* 必填项红色星号 */
    .required-mark {
        color: #FF6347;
        margin-left: 3px;
    }

    /* 页面内容区域 */
    .edit-content {
        padding: 20px;
        height: 100%;
        box-sizing: border-box;
        overflow-y: auto;
    }

    /* 表单输入框样式 */
    .layui-input,
    .layui-select,
    .layui-textarea {
        border: 1px solid #e6e6e6;
    }

    .layui-input:focus,
    .layui-select:focus,
    .layui-textarea:focus {
        border-color: #5FB878;
    }

    /* xmselect组件样式 */
    .xm-select-container {
        width: 200px;
        height: 38px;
        border: 1px solid #e6e6e6;
        border-radius: 2px;
    }

    /* 系统提示信息样式 */
    .system-info {
        color: #999;
        font-size: 12px;
        font-style: italic;
        padding: 5px 0;
    }
</style>
{/block}

{block name="body"}
<div class="edit-content">
    <!-- 页面标题 -->
    <div style="margin-bottom: 20px;">
        <h3 style="margin: 0 0 15px 0; font-size: 18px; font-weight: bold; color: #333;">
            {if condition="isset($detail.id) && $detail.id > 0"}编辑门店名称映射{else/}新增门店名称映射{/if}
        </h3>
    </div>

    <form class="layui-form" lay-filter="editForm">
        <input type="hidden" name="id" value="{$detail.id|default=0}">
        <input type="hidden" name="department_id" value="{$detail.department_id|default=0}">

        <!-- 基础信息卡片 -->
        <div class="layui-card">
            <div class="layui-card-header">基础信息</div>
            <div class="layui-card-body">
                <table class="layui-table">
                    <colgroup>
                        <col width="200">
                        <col width="200">
                        <col width="200">
                        <col width="200">
                    </colgroup>
                    <tbody>
                        <tr>
                            <td class="layui-td-gray">门店<span class="required-mark">*</span></td>
                            <td>
                                <div id="department-select-container" class="xm-select-container"></div>
                            </td>
                            <td class="layui-td-gray">OA门店名称<span class="required-mark">*</span></td>
                            <td>
                                <input type="text" name="oa_name" value="{$detail.oa_name|default=''}"
                                    lay-verify="required" placeholder="请输入OA门店名称" autocomplete="off"
                                    class="layui-input">
                            </td>
                        </tr>
                        <tr>
                            <td class="layui-td-gray">状态<span class="required-mark">*</span></td>
                            <td>
                                <select name="status" lay-verify="required">
                                    {volist name="status_options" id="status" key="k"}
                                    <option value="{$k}" {if
                                        condition="isset($detail.status) && $detail.status == $k || !isset($detail.status) && $k == 1"
                                        }selected{/if}>{$status}
                                    </option>
                                    {/volist}
                                </select>
                            </td>
                            <td class="layui-td-gray"></td>
                            <td></td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- 对账相关信息卡片 -->
        <div class="layui-card">
            <div class="layui-card-header">对账相关信息</div>
            <div class="layui-card-body">
                <table class="layui-table">
                    <colgroup>
                        <col width="200">
                        <col width="200">
                        <col width="200">
                        <col width="200">
                    </colgroup>
                    <tbody>
                        <tr>
                            <td class="layui-td-gray">对账表名称</td>
                            <td>
                                <input type="text" name="reconciliation_table"
                                    value="{$detail.reconciliation_table|default=''}" placeholder="请输入对账表名称"
                                    autocomplete="off" class="layui-input">
                            </td>
                            <td class="layui-td-gray">对账表2名称</td>
                            <td>
                                <input type="text" name="reconciliation_table2"
                                    value="{$detail.reconciliation_table2|default=''}" placeholder="请输入对账表2名称"
                                    autocomplete="off" class="layui-input">
                            </td>
                        </tr>
                        <tr>
                            <td class="layui-td-gray">跨店结算表名称</td>
                            <td>
                                <input type="text" name="cross_store_settlement"
                                    value="{$detail.cross_store_settlement|default=''}" placeholder="请输入跨店结算表名称"
                                    autocomplete="off" class="layui-input">
                            </td>
                            <td class="layui-td-gray">门店分红表名称</td>
                            <td>
                                <input type="text" name="store_dividend" value="{$detail.store_dividend|default=''}"
                                    placeholder="请输入门店分红表名称" autocomplete="off" class="layui-input">
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- 第三方平台信息卡片 -->
        <div class="layui-card">
            <div class="layui-card-header">第三方平台信息</div>
            <div class="layui-card-body">
                <table class="layui-table">
                    <colgroup>
                        <col width="200">
                        <col width="200">
                        <col width="200">
                        <col width="200">
                    </colgroup>
                    <tbody>
                        <tr>
                            <td class="layui-td-gray">美团门店ID</td>
                            <td>
                                <input type="text" name="meituan_id" value="{$detail.meituan_id|default=''}"
                                    placeholder="请输入美团门店ID" autocomplete="off" class="layui-input">
                            </td>
                            <td class="layui-td-gray">美团门店名称</td>
                            <td>
                                <input type="text" name="meituan_name" value="{$detail.meituan_name|default=''}"
                                    placeholder="请输入美团门店名称" autocomplete="off" class="layui-input">
                            </td>
                        </tr>
                        <tr>
                            <td class="layui-td-gray">有赞门店名称</td>
                            <td>
                                <input type="text" name="youzan_name" value="{$detail.youzan_name|default=''}"
                                    placeholder="请输入有赞门店名称" autocomplete="off" class="layui-input">
                            </td>
                            <td class="layui-td-gray">大众点评名称</td>
                            <td>
                                <input type="text" name="dianping_name" value="{$detail.dianping_name|default=''}"
                                    placeholder="请输入大众点评名称" autocomplete="off" class="layui-input">
                            </td>
                        </tr>
                        <tr>
                            <td class="layui-td-gray">抖音核销门店</td>
                            <td>
                                <input type="text" name="douyin_verification"
                                    value="{$detail.douyin_verification|default=''}" placeholder="请输入抖音核销门店名称"
                                    autocomplete="off" class="layui-input">
                            </td>
                            <td class="layui-td-gray">抖音广告名称</td>
                            <td>
                                <input type="text" name="douyin_ad" value="{$detail.douyin_ad|default=''}"
                                    placeholder="请输入抖音广告名称" autocomplete="off" class="layui-input">
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- 其他系统信息卡片 -->
        <div class="layui-card">
            <div class="layui-card-header">其他系统信息</div>
            <div class="layui-card-body">
                <table class="layui-table">
                    <colgroup>
                        <col width="200">
                        <col width="200">
                        <col width="200">
                        <col width="200">
                    </colgroup>
                    <tbody>
                        <tr>
                            <td class="layui-td-gray">惠美云终端匹配</td>
                            <td>
                                <input type="text" name="terminal_match" value="{$detail.terminal_match|default=''}"
                                    placeholder="请输入惠美云终端匹配" autocomplete="off" class="layui-input">
                            </td>
                            <td class="layui-td-gray">老板管账名称</td>
                            <td>
                                <input type="text" name="boss_account" value="{$detail.boss_account|default=''}"
                                    placeholder="请输入老板管账名称" autocomplete="off" class="layui-input">
                            </td>
                        </tr>
                        <tr>
                            <td class="layui-td-gray">采购系统名称</td>
                            <td>
                                <input type="text" name="purchase_system" value="{$detail.purchase_system|default=''}"
                                    placeholder="请输入采购系统名称" autocomplete="off" class="layui-input">
                            </td>
                            <td class="layui-td-gray">数据框架名称</td>
                            <td>
                                <input type="text" name="data_framework" value="{$detail.data_framework|default=''}"
                                    placeholder="请输入数据框架名称" autocomplete="off" class="layui-input">
                            </td>
                        </tr>
                        <tr>
                            <td class="layui-td-gray">访客收藏名称</td>
                            <td colspan="3">
                                <input type="text" name="visitor_collection"
                                    value="{$detail.visitor_collection|default=''}" placeholder="请输入访客收藏名称"
                                    autocomplete="off" class="layui-input">
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- 提交按钮 -->
        <div style="margin-top: 15px;">
            <button class="layui-btn layui-btn-normal" lay-submit lay-filter="submitForm">保存修改</button>
            <button type="button" class="layui-btn layui-btn-primary" onclick="parent.layer.closeAll()">取消</button>
        </div>
    </form>
</div>
{/block}

{block name="script"}
<script src="/static/admin/js/xmselect.js"></script>
<script>
    const moduleInit = ['form'];
    function gouguInit() {
        var form = layui.form;
        var departmentSelect;

        // 等待DOM完全加载后再初始化xmSelect组件
        $(document).ready(function() {
            // 检查元素是否存在
            if ($('#department-select-container').length === 0) {
                console.error('门店选择容器元素不存在');
                return;
            }

            // 初始化门店选择组件
            departmentSelect = xmSelect.render({
                el: '#department-select-container',
                radio: true,
                clickClose: true,
                filterable: true,
                placeholder: '请选择门店',
                data: [],
                on: function(data) {
                    // 同步隐藏字段值
                    if (data.arr && data.arr.length > 0) {
                        $('input[name="department_id"]').val(data.arr[0].value);
                    } else {
                        $('input[name="department_id"]').val('');
                    }
                }
            });

            // 加载门店数据
            loadDepartmentList();
        });

        // 加载门店列表数据
        function loadDepartmentList() {
            // 确保组件已经初始化
            if (!departmentSelect) {
                console.error('门店选择组件未初始化');
                return;
            }

            // 准备门店数据
            var departmentData = [
                {volist name="department_list" id="dept"}
                {name: '{$dept.title}', value: '{$dept.id}'},
                {/volist}
            ];

            // 更新组件数据
            departmentSelect.update({
                data: departmentData
            });

            // 设置默认选中值
            var departmentId = $('input[name="department_id"]').val();
            if (departmentId && departmentId != '0') {
                departmentSelect.setValue([departmentId]);
            }
        }

        // 表单提交
        form.on('submit(submitForm)', function(data) {
            var loadIndex = layer.load(2, {shade: 0.3});

            $.post('/cross/storemapping/edit', data.field, function(res) {
                layer.close(loadIndex);
                if (res.code == 0) {
                    layer.msg(res.msg, {icon: 1, time: 2000}, function() {
                        parent.layer.closeAll();
                        // 触发父页面刷新
                        if (parent.layui && parent.layui.pageTable) {
                            parent.layui.pageTable.reload();
                        }
                    });
                } else {
                    layer.msg(res.msg, {icon: 2, time: 3000});
                }
            }).fail(function() {
                layer.close(loadIndex);
                layer.msg('网络错误，请重试', {icon: 2});
            });

            return false;
        });

        // 表单渲染
        form.render();
    }
</script>
{/block}