-- 为oa_order表添加组合索引，提高按sale_kdt_id和finish_date筛选的查询性能
ALTER TABLE `oa_order` ADD INDEX `idx_sale_kdt_id_finish_date` (`sale_kdt_id`, `finish_date`, `order_state`);

-- 为oa_store_business表添加组合索引，提高按did和sdate筛选的查询性能
ALTER TABLE `oa_store_business` ADD INDEX `idx_did_sdate` (`did`, `sdate`, `status`);

-- 为oa_store_business_m表添加组合索引，提高汇总查询的性能
ALTER TABLE `oa_store_business_m` ADD INDEX `idx_did_sdate_range` (`did`, `sdate`);

-- 为oa_order_pay表添加索引，提高与order表关联查询的性能
ALTER TABLE `oa_order_pay` ADD INDEX `idx_tid_pay_channel` (`tid`, `pay_channel`);

-- 为oa_order_item表添加索引，提高与order表关联查询的性能
ALTER TABLE `oa_order_item` ADD INDEX `idx_tid_item_type` (`tid`, `item_type`);
ALTER TABLE `oa_order_item` ADD INDEX `idx_tid_promotion_type` (`tid`, `promotion_type`);

-- 为oa_store表添加索引，提高按sdate和did查询的性能
ALTER TABLE `oa_store` ADD INDEX `idx_sdate_did` (`sdate`, `did`);

-- 为oa_department表添加索引，提高按ID查询的性能
ALTER TABLE `oa_department` ADD INDEX `idx_id_title` (`id`, `title`, `kdt_id`); 