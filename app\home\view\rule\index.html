{extend name="../../base/view/common/base" /}
<!-- 主体 -->
{block name="body"}
<div class="p-3">
	<div class="gg-form-bar border-t border-x" style="padding-bottom:12px;">
		<button class="layui-btn layui-btn-sm add-menu">+ 添加菜单/节点</button>
	</div>
	<div>
		<table class="layui-hide" id="treeTable" lay-filter="treeTable"></table>
	</div>
</div>
<script type="text/html" id="toolbarDemo">
  <div class="layui-btn-container">
    <button class="layui-btn layui-btn-sm" lay-event="add">+ 添加权限组</button>
  </div>
</script>
{/block}
<!-- /主体 -->

<!-- 脚本 -->
{block name="script"}
<script>
	const moduleInit = ['tool'];
	function gouguInit() {
		var treeTable = layui.treeTable, tool = layui.tool;
		
		layui.trees = treeTable.render({
			elem: '#treeTable'
			,url: "/home/<USER>/index"
			,tree: { // treeTable 特定属性集
				customName: {name:'title'},
				data: {},
				view: {showIcon:false},
				async: {},
				callback: {}
			}
			, cellMinWidth: 80
			, cols: [[
				{ field: 'id', width: 80, title: 'ID号', align: 'center' }
				, { field: 'sort', width: 60, title: '排序', align: 'center' }
				,{field:'icon',title: '菜单图标',width: 80, align: 'center' ,templet: function(d){
					var html='<strong class="iconfont '+d.icon+'"></strong>';
					return html;
				}}
				, { field: 'title', width: 200, title: '菜单/节点名称' }
				, { field: 'pid', title: '父ID', width: 80, align: 'center' }
				, { field: 'src', title: 'URL链接' }
				, {
					field: 'menu', width: 100, title: '是否是菜单', align: 'center', templet: function (d) {
						var html = '<span style="color:#fbbc05">否</span>';
						if (d.menu == '1') {
							html = '<span style="color:#12bb37">是</span>';
						}
						return html;
					}
				}
				, { field: 'name', width: 110, title: '操作日志名称', align: 'center' }
				, {
					width: 188, title: '操作', align: 'center'
					, templet: function (d) {
						var html = '<span class="layui-btn-group"><button class="layui-btn layui-btn-xs" lay-event="add">添加子菜单/节点</button><button class="layui-btn layui-btn-normal  layui-btn-xs" lay-event="edit">编辑</button><button class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">删除</button>';
						return html;
					}
				}
			]]
			, page: false
		});
		//表头工具栏事件
		$('.add-menu').on('click', function () {
			tool.side("/home/<USER>/add");
			return;
		});

		//操作按钮
		treeTable.on('tool(treeTable)', function (obj) {
			console.log(obj);
			if (obj.event === 'add') {
				tool.side('/home/<USER>/add?pid=' + obj.data.id);
				return;
			}
			if (obj.event === 'edit') {
				tool.side('/home/<USER>/add?id=' + obj.data.id);
				return;
			}
			if (obj.event === 'del') {
				layer.confirm('确定要删除吗?', { icon: 3, title: '提示' }, function (index) {
					let callback = function (e) {
						layer.msg(e.msg);
						if (e.code == 0) {
							obj.del();
						}
					}
					tool.delete("/home/<USER>/delete", { id: obj.data.id }, callback);
					layer.close(index);
				});
			}
		});
	}
</script>
{/block}
<!-- /脚本 -->