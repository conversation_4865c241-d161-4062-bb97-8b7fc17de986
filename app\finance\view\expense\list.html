{extend name="../../base/view/common/base" /}
<!-- 主体 -->
{block name="body"}
<div class="p-3">
	<div class="layui-card">
	<div class="layui-tab layui-tab-brief" lay-filter="tab">
		<ul class="layui-tab-title">
			<li class="layui-this">全部</li>
			<li>待我审批</li>
			<li>我已审批</li>
		</ul>
		<table class="layui-hide" id="test" lay-filter="test"></table>
	</div>
</div>
</div>
<script type="text/html" id="toolbarDemo">
<div class="layui-btn-container">
    <h3 class="h3-title" style="height:32px;">报销列表</h3>
</div>
</script>
{/block}
<!-- /主体 -->

<!-- 脚本 -->
{block name="script"}
<script>
	const moduleInit = ['tool','employeepicker'];
	function gouguInit() {
		var form = layui.form,table = layui.table,tool=layui.tool, element = layui.element;
			
		element.on('tab(tab)', function(data){
			layui.pageTable.reload({where:{status:data.index},page:{curr:1}});
			return false;
		});

		layui.pageTable = table.render({
			elem: '#test',
			title: '报销管理列表',
			toolbar: '#toolbarDemo',
			defaultToolbar: false,
			url: "/finance/expense/list", //数据接口
			cellMinWidth: 100,			
			page: true, //开启分页
			limit: 20,
			cols: [
				[
					{
						field: 'id',
						title: 'ID号',
						align: 'center',
						width: 80
					}, {
						field: 'amount',
						title: '报销总金额(元)',
						align: 'right',
						width: 120,
					},{
						field: 'check_status',
						title: '状态',
						align: 'center',
						width: 120,
						templet:function(d){
							var html='<span class="red">审批不通过</span>';
							if(d.check_status==1){
								html='<span class="green">审批中</span>';
							}
							else if(d.check_status==2){
								html='<span class="blue">审批通过</span>';
							}
							else if(d.check_status==4){
								html='<span class="red">撤销</span>';
							}
							else if(d.check_status==5){
								html='<span class="yellow">已打款</span>';
							}
							return html;
						}
					},{
						field: 'admin_name',
						title: '报销人',
						align: 'center',
						width: 100
					},{
						field: 'department',
						title: '报销部门',
						align: 'center',
						width: 150
					},{
						field: 'code',
						title: '报销凭证编号',
						width: 150,
					},{
						field: 'expense_time',
						title: '原始单据日期',
						align: 'center',
						width: 120
					},{
						field: 'income_month',
						title: '入账月份',
						align: 'center',
						width: 90
					},{
						field: 'create_time',
						title: '录入时间',
						align: 'center',
						width: 150
					},{
						field: 'check_user',
						title: '当前审核人'
					},{
						field: 'pay_name',
						title: '打款人',
						align: 'center',
						width: 90
					},{
						field: 'pay_time',
						title: '打款确认时间',
						align: 'center',
						width: 150
					}, {
						field: 'right',
						fixed: 'right',
						title: '操作',
						width: 80,
						align: 'center',
						templet:function(d){
							var html='<span class="layui-btn layui-btn-normal layui-btn-xs" lay-event="view">详情</span>';
							return html;
						}
					}
				]
			]
		});
		
		//监听行工具事件
		table.on('tool(test)', function(obj) {
			var data = obj.data;
			if (obj.event === 'view') {
				tool.side("/finance/expense/view?id="+data.id);
				return;
			}
		});
	}
</script>
{/block}
<!-- /脚本 -->
