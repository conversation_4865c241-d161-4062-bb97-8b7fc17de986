{extend name="../../base/view/common/base" /}
<!-- 主体 -->
{block name="body"}
<form class="layui-form p-4">
    <h3 class="pb-3">【{$detail.trainName}】培训签到</h3>
    <table class="layui-table">
        <tr>
            <td class="layui-td-gray-2">员工<font>*</font></td>
            <td colspan="2">
                <div class="xm-select-demo" name="aid" id="demo5"></div>
            </td>
            <input type="hidden" readonly name="aname" class="layui-input">
            <input type="hidden" readonly name="aid" class="layui-input">
        </tr>
        <tr>
            <td class="layui-td-gray">门店<font>*</font></td>
            <td colspan="2">
                <select id="did" name="did" lay-verify="required" lay-reqText="请选择所在部门" lay-search="">
                    <option value="">--请选择--</option>
                    {volist name=":getmd('门店')" id="vo"}
                    <option value="{$vo.id}">{$vo.title}</option>
                    {/volist}
                </select>
            </td>
        </tr>
    </table>

    <div class="py-3">
        <input type="hidden" name="trainId" value="{$id}">
        <button class="layui-btn layui-btn-normal" lay-submit="" lay-filter="webform">立即提交</button>
    </div>
</form>
{/block}
<!-- /主体 -->

<!-- 脚本 -->
{block name="script"}
<script>
    const moduleInit = ['tool'];
    function gouguInit() {
        var form = layui.form,tool=layui.tool,laydate = layui.laydate;

        laydate.render({
            elem: "#ID-laydate-shortcut-range-date",
            range: true,
            shortcuts: [
                {
                    text: "上个月",
                    value: function(){
                        var date = new Date();
                        var year = date.getFullYear();
                        var month = date.getMonth();
                        return [
                            new Date(year, month - 1, 1),
                            new Date(year, month, 0)
                        ];
                    }
                },
                {
                    text: "这个月",
                    value: function(){
                        var date = new Date();
                        var year = date.getFullYear();
                        var month = date.getMonth();
                        return [
                            new Date(year, month, 1),
                            new Date(year, month + 1, 0)
                        ];
                    }
                },
                {
                    text: "下个月",
                    value: function(){
                        var date = new Date();
                        var year = date.getFullYear();
                        var month = date.getMonth();
                        return [
                            new Date(year, month + 1, 1),
                            new Date(year, month + 2, 0)
                        ];
                    }
                }
            ]
        });


        //监听提交
        form.on('submit(webform)', function(data){
            $.ajax({
                url: "/mobile/index/train_sign_in",
                type:'post',
                data:data.field,
                success: function (e) {
                    layer.msg(e.msg);
                    if (e.code == 0) {
                        window.open(`/successtemplate.html`);
                    }
                }
            })

            return false;
        });


        var demo4 = xmSelect.render({
            el: '#demo5',        //绑定页面上的id
            filterable: true,
            clickClose: true,
            name: 'aid',     //页面上的name
            remoteSearch: true,
            radio: true,      //单选
            showCount: 20,    //下拉展开显示的条数
            searchTips: "输入名字",
            remoteMethod: function (val, cb, show) {
                //需要回传一个数组
                $.ajax({
                    type: 'post',
                    url: '/api/index/get_personnel_dep',           //数据接口
                    data: { keywords: val },               //搜素框里的值
                    dataType: 'json',
                    success: function (data) {
                        var res_data = data.data;
                        let arr = [];
                        for (let i = 0; i < res_data.length; i++) {
                            arr.push({name: res_data[i].name, value: res_data[i].id})
                        }
                        cb(arr)
                    },
                    error: function myfunction() {
                        cb([]);
                    }
                });
            },
            on: function (data) {
                o_did(data)
            },
        });

        function o_did(data){
            //arr:  当前多选已选中的数据
            let arr = data.arr
            if (data.arr.length > 0) {
                // 从选中数据中提取value值
                let value = arr[0].value;
                aid = arr[0].value;

                $.ajax({
                    type: 'post',
                    url: '/api/index/get_admin_to_id',           //数据接口
                    data: { id: value },               //搜素框里的值
                    success: function (data) {
                        let re_data = data.data;
                        $('input[name="aname"]').val(re_data.name);
                        $('input[name="aid"]').val(re_data.id);

                        let did = re_data.did.split(',')

                        $("#did option[value='"+did[0]+"']").attr("selected","selected");

                        // 重新渲染下拉框
                        form.render('select');

                    }
                });
            }
        }


    }
</script>
{/block}
<!-- /脚本 -->
