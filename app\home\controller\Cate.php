<?php
/**
 * @copyright Copyright (c) 2021 仲正堂工作室
 * @license https://opensource.org/licenses/GPL-3.0
 * @link https://www.gougucms.com
 */

declare (strict_types = 1);

namespace app\home\controller;

use app\base\BaseController;
use app\home\validate\FlowTypeCheck;
use app\home\validate\ExpenseCateCheck;
use app\home\validate\CostCateCheck;
use app\home\validate\IndustryCheck;
use app\home\validate\ServicesCheck;
use app\home\validate\WorkCateCheck;
use app\home\validate\KeyworksCheck;
use app\home\validate\InvoiceSubjectCheck;
use think\exception\ValidateException;
use think\facade\Db;
use think\facade\View;

class Cate extends BaseController
{
	
	//审批类型
    public function flow_type()
    {
        $type = get_config('approve.type');
        if (request()->isAjax()) {

            $param = get_params();
            $where = array();
            if (isset($param['type']) && !empty($param['type']) ){
                $where['type'] = $param['type'];
            }

            $cate = Db::name('FlowType')->where($where)->order('type asc')->select()->toArray();

			foreach ($cate as $key => &$value){
				foreach ($type as $k => $val){
					if($value['type'] == $val['id']){
						$value['type_name'] = $val['title'];						
					}
				}
				$value['department']='全公司';
				if($value['department_ids']!=''){
					$department = Db::name('Department')->whereIn('id',$value['department_ids'])->column('title');
					$value['department'] = implode(',',$department);
				}
			}
            return to_assign(0, '', $cate);
        } else {
            View::assign("type", $type);
            return view();
        }
    }
	
    //审批类型添加
    public function flow_type_add()
    {
        $param = get_params();
        if (request()->isAjax()) {
            if (!empty($param['id']) && $param['id'] > 0) {
                try {
                    validate(FlowTypeCheck::class)->scene('edit')->check($param);
                } catch (ValidateException $e) {
                    // 验证失败 输出错误信息
                    return to_assign(1, $e->getError());
                }
                $param['update_time'] = time();
                $res = Db::name('FlowType')->strict(false)->field(true)->update($param);
                if ($res) {
                    add_log('edit', $param['id'], $param);
                }
                return to_assign();
            } else {
                try {
                    validate(FlowTypeCheck::class)->scene('add')->check($param);
                } catch (ValidateException $e) {
                    // 验证失败 输出错误信息
                    return to_assign(1, $e->getError());
                }
                $param['create_time'] = time();
                $insertId = Db::name('FlowType')->strict(false)->field(true)->insertGetId($param);
                if ($insertId) {
                    add_log('add', $insertId, $param);
                }
                return to_assign();
            }
        }
		else {
			$id = isset($param['id']) ? $param['id'] : 0;
            if ($id > 0) {
                $detail = Db::name('FlowType')->where(['id' => $id])->find();
                View::assign('detail', $detail);
            }
            View::assign('id', $id);
			View::assign('type', get_config('approve.type'));
            return view();
        }
    }
	
	//审批类型设置
    public function flow_type_check()
    {
		$param = get_params();
        $res = Db::name('FlowType')->strict(false)->field('id,status')->update($param);
		if ($res) {
			if($param['status'] == 0){
				add_log('disable', $param['id'], $param);
			}
			else if($param['status'] == 1){
				add_log('recovery', $param['id'], $param);
			}
			return to_assign();
		}
		else{
			return to_assign(0, '操作失败');
		}
    }	
	
	//费用类别
    public function cost_cate()
    {
        if (request()->isAjax()) {
            $cate = Db::name('CostCate')->order('create_time asc')->select();
            return to_assign(0, '', $cate);
        } else {
            return view();
        }
    }
    //费用类别添加
    public function cost_cate_add()
    {
        if (request()->isAjax()) {
            $param = get_params();
            if (!empty($param['id']) && $param['id'] > 0) {
                try {
                    validate(CostCateCheck::class)->scene('edit')->check($param);
                } catch (ValidateException $e) {
                    // 验证失败 输出错误信息
                    return to_assign(1, $e->getError());
                }
                $param['update_time'] = time();
                $res = Db::name('CostCate')->strict(false)->field(true)->update($param);
                if ($res) {
                    add_log('edit', $param['id'], $param);
                }
                return to_assign();
            } else {
                try {
                    validate(CostCateCheck::class)->scene('add')->check($param);
                } catch (ValidateException $e) {
                    // 验证失败 输出错误信息
                    return to_assign(1, $e->getError());
                }
                $param['create_time'] = time();
                $insertId = Db::name('CostCate')->strict(false)->field(true)->insertGetId($param);
                if ($insertId) {
                    add_log('add', $insertId, $param);
                }
                return to_assign();
            }
        }
    }
	
    //费用类别设置
    public function cost_cate_check()
    {
		$param = get_params();
        $res = Db::name('CostCate')->strict(false)->field('id,status')->update($param);
		if ($res) {
			if($param['status'] == 0){
				add_log('disable', $param['id'], $param);
			}
			else if($param['status'] == 1){
				add_log('recovery', $param['id'], $param);
			}
			return to_assign();
		}
		else{
			return to_assign(0, '操作失败');
		}
    }
	
	//报销类别
//    public function expense_cate()
//    {
//        if (request()->isAjax()) {
////            $cate = Db::name('ExpenseCate')->order('create_time asc')->select();
//
//            $cate = Db::name('Type')
//                ->field('tt.id,tt.name as name,tt.name as title')
//                ->alias('t')
//                ->join('Type tt', 'tt.pid = t.id')
//                ->where(['tt.status' => 1 , 't.name' => '支出' , 't.controller' => 'store_bill' ])
//                ->select();
//
//            return to_assign(0, '', $cate);
//        } else {
//            return view();
//        }
//    }

    public function expense_cate()
    {
        if (request()->isAjax()) {

            $cate = Db::name('type_pay')
                ->field('
                        tp.id,tp.name as title,tp.remark,tp.status,tp.type,
                        t.name as t_name,t.title as t_title
                    ')
                ->alias('tp')
                ->leftjoin('Type t', 'tp.type_id = t.id')
                ->order("type asc")
                ->select()
                ->each(function ($item, $key) {
                    $type_name = '';
                    switch ($item['type']){
                        case 1:  $type_name = "总部"; break;
                        case 2:  $type_name = "培训部"; break;
                        case 3:  $type_name = "门店"; break;
                    }
                    $item['type_name'] = $type_name;
                    return $item;
                });

            return to_assign(0, '', $cate);
        } else {
            return view();
        }
    }


    //报销类别添加
//    public function expense_cate_add()
//    {
//        if (request()->isAjax()) {
//            $param = get_params();
//
//            var_dump($param);
//            exit();
//
//            if (!empty($param['id']) && $param['id'] > 0) {
//                try {
//                    validate(ExpenseCateCheck::class)->scene('edit')->check($param);
//                } catch (ValidateException $e) {
//                    // 验证失败 输出错误信息
//                    return to_assign(1, $e->getError());
//                }
//                $param['update_time'] = time();
//                $res = Db::name('ExpenseCate')->strict(false)->field(true)->update($param);
//                if ($res) {
//                    add_log('edit', $param['id'], $param);
//                }
//                return to_assign();
//            } else {
//                try {
//                    validate(ExpenseCateCheck::class)->scene('add')->check($param);
//                } catch (ValidateException $e) {
//                    // 验证失败 输出错误信息
//                    return to_assign(1, $e->getError());
//                }
//                $param['create_time'] = time();
//                $insertId = Db::name('ExpenseCate')->strict(false)->field(true)->insertGetId($param);
//                if ($insertId) {
//                    add_log('add', $insertId, $param);
//                }
//                return to_assign();
//            }
//        }else{
//            $param = get_params();
//
//            if (isset($param['id']) && $param['id'] > 0){
//                $details = Db::name('type_pay')
//                    ->order("type asc")
//                    ->where(['id' => $param['id']])
//                    ->find();
//                View::assign("details" , $details);
//            }
//
//
//            return view();
//        }
//    }


    public function expense_cate_add()
    {
        if (request()->isAjax()) {
            $param = get_params();

            $remark = '';
            switch ($param['type']){
                case 1:  $remark = "总部"; break;
                case 2:  $remark = "培训部"; break;
                case 3:  $remark = "门店"; break;
            }
            $param['remark'] = $remark;

            if (!empty($param['id']) && $param['id'] > 0) {

                $res = Db::name('TypePay')->strict(false)->field(true)->update($param);

                if ($res) {
                    add_log('edit', $param['id'], $param);
                }
                return to_assign();
            } else {

                $insertId = Db::name('TypePay')->strict(false)->field(true)->insertGetId($param);
                if ($insertId) {
                    add_log('add', $insertId, $param);
                }
                return to_assign();
            }
        }else{
            $param = get_params();

            if (isset($param['id']) && $param['id'] > 0){
                $details = Db::name('type_pay')
                    ->order("type asc")
                    ->where(['id' => $param['id']])
                    ->find();
                View::assign("details" , $details);
            }


            return view();
        }
    }

    //报销类别设置
    public function expense_cate_check()
    {
		$param = get_params();
        $res = Db::name('ExpenseCate')->strict(false)->field('id,status')->update($param);
		if ($res) {
			if($param['status'] == 0){
				add_log('disable', $param['id'], $param);
			}
			else if($param['status'] == 1){
				add_log('recovery', $param['id'], $param);
			}
			return to_assign();
		}
		else{
			return to_assign(0, '操作失败');
		}
    }
	
    //发票主体
	public function subject()
    {
        if (request()->isAjax()) {
            $subject = Db::name('InvoiceSubject')->order('create_time asc')->select();
            return to_assign(0, '', $subject);
        } else {
            return view();
        }
    }
    //发票主体新建编辑
    public function subject_add()
    {
        if (request()->isAjax()) {
            $param = get_params();
            if (!empty($param['id']) && $param['id'] > 0) {
                try {
                    validate(InvoiceSubjectCheck::class)->scene('edit')->check($param);
                } catch (ValidateException $e) {
                    // 验证失败 输出错误信息
                    return to_assign(1, $e->getError());
                }
                $param['update_time'] = time();
                $res = Db::name('InvoiceSubject')->strict(false)->field('title,id,update_time')->update($param);
                if ($res) {
                    add_log('edit', $param['id'], $param);
                }
                return to_assign();
            } else {
                try {
                    validate(InvoiceSubjectCheck::class)->scene('add')->check($param);
                } catch (ValidateException $e) {
                    // 验证失败 输出错误信息
                    return to_assign(1, $e->getError());
                }
                $param['create_time'] = time();
                $insertId = Db::name('InvoiceSubject')->strict(false)->field('title,id,create_time')->insertGetId($param);
                if ($insertId) {
                    add_log('add', $insertId, $param);
                }
                return to_assign();
            }
        }
    }
	
    //发票主体设置
    public function subject_check()
    {
		$param = get_params();
        $res = Db::name('InvoiceSubject')->strict(false)->field('id,status')->update($param);
		if ($res) {
			if($param['status'] == 0){
				add_log('disable', $param['id'], $param);
			}
			else if($param['status'] == 1){
				add_log('recovery', $param['id'], $param);
			}
			return to_assign();
		}
		else{
			return to_assign(0, '操作失败');
		}
    }

	//行业类型
    public function industry_cate()
    {
        if (request()->isAjax()) {
            $cate = Db::name('Industry')->order('create_time asc')->select();
            return to_assign(0, '', $cate);
        } else {
            return view();
        }
    }
    //行业类型添加
    public function industry_cate_add()
    {
        if (request()->isAjax()) {
            $param = get_params();
            if (!empty($param['id']) && $param['id'] > 0) {
                try {
                    validate(IndustryCheck::class)->scene('edit')->check($param);
                } catch (ValidateException $e) {
                    // 验证失败 输出错误信息
                    return to_assign(1, $e->getError());
                }
                $param['update_time'] = time();
                $res = Db::name('Industry')->strict(false)->field(true)->update($param);
                if ($res) {
                    add_log('edit', $param['id'], $param);
                }
                return to_assign();
            } else {
                try {
                    validate(IndustryCheck::class)->scene('add')->check($param);
                } catch (ValidateException $e) {
                    // 验证失败 输出错误信息
                    return to_assign(1, $e->getError());
                }
                $param['create_time'] = time();
                $insertId = Db::name('Industry')->strict(false)->field(true)->insertGetId($param);
                if ($insertId) {
                    add_log('add', $insertId, $param);
                }
                return to_assign();
            }
        }
    }
	
    //行业类型设置
    public function industry_cate_check()
    {
		$param = get_params();
        $res = Db::name('Industry')->strict(false)->field('id,status')->update($param);
		if ($res) {
			if($param['status'] == 0){
				add_log('disable', $param['id'], $param);
			}
			else if($param['status'] == 1){
				add_log('recovery', $param['id'], $param);
			}
			return to_assign();
		}
		else{
			return to_assign(0, '操作失败');
		}
    }  
	
	//服务类型
    public function services_cate()
    {
        if (request()->isAjax()) {
            $cate = Db::name('Services')->order('create_time asc')->select();
            return to_assign(0, '', $cate);
        } else {
            return view();
        }
    }
    //服务类型添加
    public function services_cate_add()
    {
        if (request()->isAjax()) {
            $param = get_params();
            if (!empty($param['id']) && $param['id'] > 0) {
                try {
                    validate(ServicesCheck::class)->scene('edit')->check($param);
                } catch (ValidateException $e) {
                    // 验证失败 输出错误信息
                    return to_assign(1, $e->getError());
                }
                $param['update_time'] = time();
                $res = Db::name('Services')->strict(false)->field(true)->update($param);
                if ($res) {
                    add_log('edit', $param['id'], $param);
                }
                return to_assign();
            } else {
                try {
                    validate(ServicesCheck::class)->scene('add')->check($param);
                } catch (ValidateException $e) {
                    // 验证失败 输出错误信息
                    return to_assign(1, $e->getError());
                }
                $param['create_time'] = time();
                $insertId = Db::name('Services')->strict(false)->field(true)->insertGetId($param);
                if ($insertId) {
                    add_log('add', $insertId, $param);
                }
                return to_assign();
            }
        }
    }
	
    //服务类型设置
    public function services_cate_check()
    {
		$param = get_params();
        $res = Db::name('Services')->strict(false)->field('id,status')->update($param);
		if ($res) {
			if($param['status'] == 0){
				add_log('disable', $param['id'], $param);
			}
			else if($param['status'] == 1){
				add_log('recovery', $param['id'], $param);
			}
			return to_assign();
		}
		else{
			return to_assign(0, '操作失败');
		}
    }  
	
	//工作类别
    public function work_cate()
    {
        if (request()->isAjax()) {
            $cate = Db::name('WorkCate')->order('create_time asc')->select();
            return to_assign(0, '', $cate);
        } else {
            return view();
        }
    }
    //工作类别添加
    public function work_cate_add()
    {
        if (request()->isAjax()) {
            $param = get_params();
            if (!empty($param['id']) && $param['id'] > 0) {
                try {
                    validate(WorkCateCheck::class)->scene('edit')->check($param);
                } catch (ValidateException $e) {
                    // 验证失败 输出错误信息
                    return to_assign(1, $e->getError());
                }
                $param['update_time'] = time();
                $res = Db::name('WorkCate')->strict(false)->field(true)->update($param);
                if ($res) {
                    add_log('edit', $param['id'], $param);
                }
                return to_assign();
            } else {
                try {
                    validate(WorkCateCheck::class)->scene('add')->check($param);
                } catch (ValidateException $e) {
                    // 验证失败 输出错误信息
                    return to_assign(1, $e->getError());
                }
                $param['create_time'] = time();
                $insertId = Db::name('WorkCate')->strict(false)->field(true)->insertGetId($param);
                if ($insertId) {
                    add_log('add', $insertId, $param);
                }
                return to_assign();
            }
        }
    }
	
    //工作类别设置
    public function work_cate_check()
    {
		$param = get_params();
        $res = Db::name('WorkCate')->strict(false)->field('id,status')->update($param);
		if ($res) {
			if($param['status'] == 0){
				add_log('disable', $param['id'], $param);
			}
			else if($param['status'] == 1){
				add_log('recovery', $param['id'], $param);
			}
			return to_assign();
		}
		else{
			return to_assign(0, '操作失败');
		}
    }  
   
}
