{extend name="../../base/view/common/base" /}
{block name="style"}
<style>
.checkbox16 .layui-form-checkbox span{font-size:15px;font-weight:800;}
.layui-checkbox-disabled span,.layui-checkbox-disabled[lay-skin=primary] span{color: #666666!important;}
</style>
{/block}
<!-- 主体 -->
{block name="body"}
<form class="layui-form p-4">
	<h3 class="pb-3">仲正堂 ({$detail.dname}) 商铺租金付款表</h3>
	<table class="layui-table layui-table-form">
		<tr>
			<td colspan="4">
				起始日期：{$detail.con_start_date} 至 {$detail.con_end_date}
			</td>
			<td colspan="4">
				户名：
			</td>
		</tr>
		<tr>
			<td colspan="4">
				地址：
			</td >
			<td colspan="4">
				卡号：
			</td>
		</tr>
		<tr>
			<td colspan="4">
				押金：63200
			</td>
			<td colspan="4">
				开户行：
			</td>
		</tr>
		<tr>
			<td>

			</td>
			<td>
				递增
			</td>
			<td>
				付款日期
			</td>
			<td>
				月租金
			</td>
			<td>
				每期金额
			</td>
			<td>
				周期 起：
			</td>
			<td>
				止：
			</td>
			<td>
				备注
			</td>
		</tr>
		{volist name="list" key="k" id="vo"}
			<tr>
				<td>{$k}</td>
				<td>{$k}</td>
				<td>{$vo.pay_date}</td>
				<td>{$vo.monthly_rent}</td>
				<td>{$vo.amount_per}</td>
				<td>{$vo.cycle_start_date}</td>
				<td>{$vo.cycle_end_date}</td>
				<td></td>
			</tr>
		{/volist}
	</table>
	

</form>
{/block}
<!-- /主体 -->

<!-- 脚本 -->
{block name="script"}
<script>
	const moduleInit = ['tool'];
	function gouguInit() {
		var tool=layui.tool;	
		//重设密码
		$('.reset-psw').on('click',function(){
			let id=$(this).data('id');
			layer.confirm('确定要重设该用户的密码？', {
				icon: 3,
				title: '提示'
			}, function (index) {
				let callback = function (e) {
					layer.msg(e.msg);
					if (e.code == 0) {
						layer.msg(e.msg);
						if(e.code==0){
							setTimeout(function(){
								location.reload();
							},2000);
						}	
					}
				}
				tool.post("/user/admin/reset_psw",{id: id}, callback);
				layer.close(index);
			})	
		});
	}
	
	//拷贝密码
	function copyToClip(content) {
		if (navigator.clipboard) {
			navigator.clipboard.writeText(content);
		} else {
			var copy_textarea = document.createElement('textarea');
			copy_textarea.style.position = 'fixed';
			copy_textarea.style.clip = 'rect(0 0 0 0)';
			copy_textarea.style.top = '10px';
			copy_textarea.value = content;
			document.body.appendChild(copy_textarea);
			copy_textarea.select();
			document.execCommand('copy', true);
			document.body.removeChild(copy_textarea);
		}
		if (content != '') {
			layer.msg('复制成功');
		}
	}
</script>
{/block}
<!-- /脚本 -->