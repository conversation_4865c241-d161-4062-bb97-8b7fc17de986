<?php
/**
 * @copyright Copyright (c) 2021 仲正堂工作室
 * @license https://opensource.org/licenses/GPL-3.0
 * @link https://www.gougucms.com
 */

declare (strict_types=1);

namespace app\cross\controller;

use app\base\BaseController;
use app\cross\model\CrossStoreSettleCardBalance;
use app\cross\validate\CrossStoreSettleCardBalanceCheck;
use think\exception\ValidateException;
use think\facade\Db;
use think\facade\View;
use think\facade\Log;
use think\App;
use PhpOffice\PhpSpreadsheet\IOFactory;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;

class Balance extends BaseController
{
    public function __construct(App $app)
    {
        parent::__construct($app);
    }

    /**
     * 门店卡余额列表
     */
    public function index()
    {
        if (request()->isAjax()) {
            $param = get_params();

            // 构建查询条件
            $where = [];

            // 按月份筛选
            if (!empty($param['period'])) {
                $where[] = ['cb.period', '=', $param['period']];
            }

            // 按门店筛选
            if (!empty($param['store_ids']) && is_array($param['store_ids'])) {
                $where[] = ['cb.store_id', 'in', $param['store_ids']];
            }

            // 按逻辑门店类型筛选
            if (!empty($param['logical_store_type'])) {
                $where[] = ['cb.logical_store_type', '=', $param['logical_store_type']];
            }

            // 按结算门店类型筛选
            if (!empty($param['settlement_store_type'])) {
                $where[] = ['cb.settlement_store_type', '=', $param['settlement_store_type']];
            }

            // 查询所有数据，不分页
            $query = Db::name('CrossStoreSettleCardBalance')
                ->alias('cb')
                ->join('oa_department d', 'd.id = cb.store_id', 'LEFT');

            // 移除排序参数处理，因为现在排序在前端进行
            $list = $query->field('cb.*, d.title as store_name')
                ->where($where)
                ->order('cb.id', 'desc') // 只保留默认排序，保证结果稳定
                ->select()
                ->each(function ($item) {
                    // 格式化余额显示
                    $item['card_balance_formatted'] = number_format(floatval($item['card_balance']), 2);
                    
                    // 格式化时间
                    $item['create_time_formatted'] = date('Y-m-d H:i:s', $item['create_time']);
                    $item['update_time_formatted'] = date('Y-m-d H:i:s', $item['update_time']);
                    
                    return $item;
                });

            // 计算合计数据
            $totalRowData = [];
            if (count($list) > 0) {
                $totalQuery = Db::name('CrossStoreSettleCardBalance')
                    ->alias('cb')
                    ->where($where);

                $totalData = $totalQuery->field([
                    'SUM(card_balance) as total_balance',
                    'COUNT(*) as total_count',
                    'COUNT(CASE WHEN logical_store_type = "老店" THEN 1 END) as logical_old_store_count',
                    'COUNT(CASE WHEN logical_store_type = "新店" THEN 1 END) as logical_new_store_count',
                    'COUNT(CASE WHEN previous_month_logical_type = "老店" THEN 1 END) as prev_logical_old_store_count',
                    'COUNT(CASE WHEN previous_month_logical_type = "新店" THEN 1 END) as prev_logical_new_store_count',
                    'COUNT(CASE WHEN settlement_store_type = "老店" THEN 1 END) as settlement_old_store_count',
                    'COUNT(CASE WHEN settlement_store_type = "新店" THEN 1 END) as settlement_new_store_count'
                ])->find();

                $totalRowData = [
                    'id' => '',
                    'store_name' => '',
                    // 去掉 period 字段
                    'card_balance' => floatval($totalData['total_balance'] ?? 0), // 添加原始数值字段
                    'card_balance_formatted' => number_format(floatval($totalData['total_balance'] ?? 0), 2),
                    'logical_store_type' => "老店:{$totalData['logical_old_store_count']} 新店:{$totalData['logical_new_store_count']}",
                    'previous_month_logical_type' => "老店:{$totalData['prev_logical_old_store_count']} 新店:{$totalData['prev_logical_new_store_count']}",
                    'settlement_store_type' => "老店:{$totalData['settlement_old_store_count']} 新店:{$totalData['settlement_new_store_count']}"
                ];
            }

            return [
                'code' => 0,
                'msg' => '',
                'count' => count($list),
                'data' => $list,
                'totalRow' => $totalRowData
            ];
        } else {
            // 记录查看门店卡余额列表页面日志
            add_log('view', 0, [], '[跨店结算]-[门店卡余额表]：查看门店卡余额列表页面');
            Log::info('[跨店结算]-[门店卡余额表]：用户查看门店卡余额列表页面，用户ID：' . $this->uid);
            return view();
        }
    }

    /**
     * 查看门店卡余额详情（只读模式）
     */
    public function view()
    {
        $param = get_params();
        $id = isset($param['id']) ? $param['id'] : 0;

        if (empty($id)) {
            return to_assign(1, '参数错误');
        }

        // 获取详情信息
        $detail = Db::name('CrossStoreSettleCardBalance')
            ->alias('cb')
            ->join('oa_department d', 'd.id = cb.store_id', 'LEFT')
            ->field('cb.*, d.title as store_name')
            ->where('cb.id', $id)
            ->find();

        if (empty($detail)) {
            return to_assign(1, '数据不存在');
        }

        View::assign('detail', $detail);

        // 记录查看门店卡余额详情日志
        add_log('view', $id, ['period' => $detail['period'], 'store_name' => $detail['store_name']], '[跨店结算]-[门店卡余额表]：查看门店卡余额详情');
        Log::info('[跨店结算]-[门店卡余额表]：用户查看门店卡余额详情，ID：' . $id . '，用户ID：' . $this->uid);

        return view();
    }

    /**
     * 编辑门店卡余额信息
     */
    public function edit()
    {
        $param = get_params();

        if (request()->isPost()) {
            try {
                validate(CrossStoreSettleCardBalanceCheck::class)->scene('edit')->check($param);
            } catch (ValidateException $e) {
                return to_assign(1, $e->getError());
            }

            $id = intval($param['id']);
            if (empty($id)) {
                return to_assign(1, '参数错误');
            }

            // 检查记录是否存在
            $existRecord = Db::name('CrossStoreSettleCardBalance')->where('id', $id)->find();
            if (empty($existRecord)) {
                return to_assign(1, '记录不存在');
            }

            // 检查门店和月份是否重复（排除当前记录）
            if (CrossStoreSettleCardBalance::checkExists($param['period'], $param['store_id'], $id)) {
                return to_assign(1, '该门店在此月份已存在记录');
            }

            try {
                // 准备更新数据 - 使用用户输入的值而不是自动计算
                $updateData = [
                    'period' => $param['period'],
                    'store_id' => $param['store_id'],
                    'card_balance' => $param['card_balance'],
                    'logical_store_type' => $param['logical_store_type'],
                    'previous_month_logical_type' => $param['previous_month_logical_type'],
                    'settlement_store_type' => $param['settlement_store_type'],
                    'update_time' => time()
                ];

                $res = Db::name('CrossStoreSettleCardBalance')->where('id', $id)->update($updateData);

                if ($res) {
                    // 记录操作日志
                    add_log('edit', $id, $param, '[跨店结算]-[门店卡余额表]：编辑门店卡余额信息');
                    Log::info('[跨店结算]-[门店卡余额表]：编辑门店卡余额信息成功，ID：' . $id . '，用户ID：' . $this->uid);
                    return to_assign(0, '保存成功');
                } else {
                    return to_assign(1, '保存失败');
                }
            } catch (\think\exception\HttpResponseException $e) {
                // 重新抛出HttpResponseException，这是to_assign函数正常的响应机制
                throw $e;
            } catch (\Exception $e) {
                Log::error('[跨店结算]-[门店卡余额表]：编辑门店卡余额信息失败，错误信息：' . $e->getMessage() . '，用户ID：' . $this->uid);
                return to_assign(1, '保存失败：' . $e->getMessage());
            }
        } else {
            // GET请求，显示编辑表单
            $id = isset($param['id']) ? $param['id'] : 0;
            if (empty($id)) {
                return to_assign(1, '参数错误');
            }

            $detail = CrossStoreSettleCardBalance::getDetail($id);
            if (empty($detail)) {
                return to_assign(1, '数据不存在');
            }

            // 获取门店列表（只包含门店）
            $storeList = Db::name('Department')
                ->where('status', 1)
                ->where('remark', '门店')
                ->select()
                ->toArray();
            
            View::assign('detail', $detail);
            View::assign('store_list', $storeList);

            return view();
        }
    }



    /**
     * 处理Excel文件上传
     */
    public function uploadExcel()
    {
        try {
            // 检查是否有上传的文件
            $file = request()->file('file');
            if (empty($file)) {
                return to_assign(1, '请选择要上传的Excel文件');
            }

            // 验证文件类型和大小
            $fileExt = $file->getOriginalExtension();
            if (!in_array($fileExt, ['xlsx', 'xls'])) {
                return to_assign(1, '文件格式不正确，请上传Excel文件');
            }

            // 保存上传的文件
            $dataPath = date('Ym');
            $md5 = $file->hash('md5');
            $savename = \think\facade\Filesystem::disk('public')->putFile($dataPath, $file, function () use ($md5) {
                return $md5;
            });

            if (!$savename) {
                return to_assign(1, '文件保存失败');
            }

            // 记录操作日志
            add_log('upload', 0, ['file_name' => $file->getOriginalName()], '[跨店结算]-[门店卡余额表]：Excel文件上传成功');
            Log::info('[跨店结算]-[门店卡余额表]：Excel文件上传成功，文件名：' . $file->getOriginalName() . '，用户ID：' . $this->uid);

            return to_assign(0, '文件上传成功', [
                'file_path' => $savename,
                'original_name' => $file->getOriginalName()
            ]);

        } catch (\think\exception\HttpResponseException $e) {
            // 重新抛出HttpResponseException，这是to_assign函数正常的响应机制
            throw $e;
        } catch (\Exception $e) {
            Log::error('[跨店结算]-[门店卡余额表]：Excel文件上传失败，错误信息：' . $e->getMessage() . '，用户ID：' . $this->uid);
            return to_assign(1, '文件上传失败：' . $e->getMessage());
        }
    }

    /**
     * 处理Excel数据导入
     */
    public function importExcel()
    {
        $param = get_params();
        $period = $param['period'] ?? '';
        $filePath = $param['file_path'] ?? '';

        if (empty($period)) {
            return to_assign(1, '请选择导入月份');
        }

        if (empty($filePath)) {
            return to_assign(1, '请上传Excel文件');
        }

        try {
            // 获取文件完整路径
            $publicPath = get_config('filesystem.disks.public.url');
            $fullPath = '.' . $publicPath . '/' . $filePath;

            if (!file_exists($fullPath)) {
                return to_assign(1, '文件不存在，请重新上传');
            }

            // 读取Excel文件
            $excelData = $this->readExcelFile($fullPath);

            if (empty($excelData)) {
                return to_assign(1, 'Excel文件为空或格式不正确');
            }

            // 批量导入数据
            $result = CrossStoreSettleCardBalance::batchImport($period, $excelData);

            if ($result['success']) {
                // 记录操作日志
                add_log('import', 0, ['period' => $period, 'count' => $result['success_count']], '[跨店结算]-[门店卡余额表]：Excel数据导入成功');
                Log::info('[跨店结算]-[门店卡余额表]：Excel数据导入成功，月份：' . $period . '，成功数量：' . $result['success_count'] . '，用户ID：' . $this->uid);
                
                return to_assign(0, '导入成功！成功导入' . $result['success_count'] . '条记录');
            } else {
                $errorMsg = '导入失败！成功' . $result['success_count'] . '条，失败' . $result['fail_count'] . '条';
                if (!empty($result['errors'])) {
                    $errorMsg .= '，错误详情：' . implode('；', array_slice($result['errors'], 0, 5));
                    if (count($result['errors']) > 5) {
                        $errorMsg .= '...等' . count($result['errors']) . '个错误';
                    }
                }
                
                Log::error('[跨店结算]-[门店卡余额表]：Excel数据导入失败，错误信息：' . $errorMsg . '，用户ID：' . $this->uid);
                return to_assign(1, $errorMsg);
            }

        } catch (\think\exception\HttpResponseException $e) {
            // 重新抛出HttpResponseException，这是to_assign函数正常的响应机制
            throw $e;
        } catch (\Exception $e) {
            Log::error('[跨店结算]-[门店卡余额表]：Excel数据导入异常，错误信息：' . $e->getMessage() . '，用户ID：' . $this->uid);
            return to_assign(1, '导入失败：' . $e->getMessage());
        }
    }

    /**
     * 读取Excel文件
     * @param string $filePath 文件路径
     * @return array
     */
    private function readExcelFile($filePath)
    {
        try {
            // 判断文件扩展名并创建对应的读取器
            $fileExtendName = strtolower(pathinfo($filePath, PATHINFO_EXTENSION));
            if ($fileExtendName == 'xlsx') {
                $objReader = \PhpOffice\PhpSpreadsheet\IOFactory::createReader('Xlsx');
            } else {
                $objReader = \PhpOffice\PhpSpreadsheet\IOFactory::createReader('Xls');
            }

            // 设置只读模式
            $objReader->setReadDataOnly(true);

            // 读取Excel文件
            $objPHPExcel = $objReader->load($filePath);

            // 获取第一个工作表
            $sheet = $objPHPExcel->getSheet(0);

            // 获取总行数
            $highestRow = $sheet->getHighestRow();

            $data = [];

            // 从第二行开始读取数据（第一行为表头）
            for ($row = 2; $row <= $highestRow; $row++) {
                $storeNameValue = $sheet->getCell('A' . $row)->getCalculatedValue();
                $storeName = trim($storeNameValue ?? '');
                $cardBalance = $sheet->getCell('B' . $row)->getCalculatedValue();

                // 跳过空行
                if (empty($storeName) && (empty($cardBalance) || $cardBalance == 0)) {
                    continue;
                }

                // 数据验证
                if (empty($storeName)) {
                    throw new \Exception("第{$row}行门店名称不能为空");
                }

                if (!is_numeric($cardBalance) || $cardBalance < 0) {
                    throw new \Exception("第{$row}行卡余额必须为非负数字");
                }

                $data[] = [
                    'store_name' => $storeName,
                    'card_balance' => floatval($cardBalance)
                ];
            }

            return $data;
        } catch (\Exception $e) {
            Log::error('[跨店结算]-[门店卡余额表]：读取Excel文件失败，错误信息：' . $e->getMessage());
            throw new \Exception('读取Excel文件失败：' . $e->getMessage());
        }
    }

    /**
     * 下载Excel导入模板
     */
    public function downloadTemplate()
    {
        $param = request()->param();
        $period = $param['period'] ?? ''; // 可选参数，用于生成特定月份的模板
        try {
            // 创建新的Spreadsheet对象
            $spreadsheet = new Spreadsheet();
            $sheet = $spreadsheet->getActiveSheet();

            // 设置表头
            $sheet->setCellValue('A1', '门店名称');
            $sheet->setCellValue('B1', '会员卡总余额');

            // 设置表头样式
            $headerStyle = [
                'font' => [
                    'bold' => true,
                    'size' => 12,
                    'name' => '微软雅黑'
                ],
                'fill' => [
                    'fillType' => \PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID,
                    'startColor' => [
                        'argb' => 'FFE6E6E6',
                    ],
                ],
                'borders' => [
                    'allBorders' => [
                        'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                    ],
                ],
                'alignment' => [
                    'horizontal' => \PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER,
                    'vertical' => \PhpOffice\PhpSpreadsheet\Style\Alignment::VERTICAL_CENTER,
                ],
            ];

            $sheet->getStyle('A1:B1')->applyFromArray($headerStyle);

            // 获取系统中的门店数据作为示例，如果没有则使用固定示例
            $storeList = Db::name('Department')
                ->where('status', 1)
                ->where('remark', '门店')
                ->limit(3)
                ->select()
                ->toArray();

            if (!empty($storeList)) {
                $exampleData = [];
                foreach ($storeList as $store) {
                    $exampleData[] = [$store['title'], 0.00];
                }
            } else {
                // 使用固定的示例数据
                $exampleData = [
                    ['滨江天街店', 500000.00],
                    ['西湖银泰店', 800000.00],
                    ['萧山万象汇店', 350000.00]
                ];
            }

            $row = 2;
            foreach ($exampleData as $data) {
                $sheet->setCellValue('A' . $row, $data[0]);
                $sheet->setCellValue('B' . $row, $data[1]);
                $row++;
            }

            // 设置数据样式
            $dataStyle = [
                'font' => [
                    'size' => 11,
                    'name' => '微软雅黑'
                ],
                'borders' => [
                    'allBorders' => [
                        'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                    ],
                ],
                'alignment' => [
                    'horizontal' => \PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER,
                    'vertical' => \PhpOffice\PhpSpreadsheet\Style\Alignment::VERTICAL_CENTER,
                ],
            ];

            $sheet->getStyle('A2:B' . ($row - 1))->applyFromArray($dataStyle);

            // 设置列宽
            $sheet->getColumnDimension('A')->setWidth(20);
            $sheet->getColumnDimension('B')->setWidth(15);

            // 添加说明信息
            $sheet->setCellValue('D1', '导入说明：');
            if (!empty($period)) {
                $sheet->setCellValue('D2', "目标月份：{$period}");
                $startRow = 3;
            } else {
                $startRow = 2;
            }

            $sheet->setCellValue('D' . $startRow, '1. 门店名称支持OA门店名称或有赞门店名称');
            $sheet->setCellValue('D' . ($startRow + 1), '2. 会员卡总余额必须为数字格式，不能为负数');
            $sheet->setCellValue('D' . ($startRow + 2), '3. 第一行为表头，请勿修改');
            $sheet->setCellValue('D' . ($startRow + 3), '4. 从第二行开始填写数据');
            $sheet->setCellValue('D' . ($startRow + 4), '5. 可以删除示例数据，填入实际数据');
            $sheet->setCellValue('D' . ($startRow + 5), '6. 支持.xls和.xlsx格式，文件大小不超过50MB');
            $sheet->setCellValue('D' . ($startRow + 6), '7. 逻辑门店类型：余额≥60万为老店，否则为新店');
            $sheet->setCellValue('D' . ($startRow + 7), '8. 结算门店类型：延迟一个月生效（"一旦变老，永不降级"）');
            $sheet->setCellValue('D' . ($startRow + 8), '');
            $sheet->setCellValue('D' . ($startRow + 9), '注意：左侧为示例数据，请根据实际情况修改');
            $sheet->setCellValue('D' . ($startRow + 10), '门店名称兼容：支持OA门店名称和有赞门店名称');

            $endRow = $startRow + 10;

            // 设置说明样式
            $noteStyle = [
                'font' => [
                    'size' => 10,
                    'name' => '微软雅黑',
                    'color' => ['argb' => 'FF666666']
                ]
            ];

            $sheet->getStyle('D1:D' . $endRow)->applyFromArray($noteStyle);
            $sheet->getStyle('D1')->getFont()->setBold(true)->setSize(11);

            // 如果有月份信息，设置月份样式
            if (!empty($period)) {
                $sheet->getStyle('D2')->getFont()->setBold(true)->setColor(new \PhpOffice\PhpSpreadsheet\Style\Color('FF0066CC'));
            }

            // 设置注意事项样式
            $sheet->getStyle('D' . ($endRow - 1) . ':D' . $endRow)->getFont()->setColor(new \PhpOffice\PhpSpreadsheet\Style\Color('FF0066CC'));

            // 设置说明列宽
            $sheet->getColumnDimension('D')->setWidth(35);

            // 创建Writer对象
            $writer = new Xlsx($spreadsheet);

            // 设置文件名
            $filenameSuffix = !empty($period) ? "_{$period}" : '';
            $filename = '门店卡余额导入模板' . $filenameSuffix . '_' . date('YmdHis') . '.xlsx';

            // 设置响应头
            header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
            header('Content-Disposition: attachment;filename="' . $filename . '"');
            header('Cache-Control: max-age=0');

            // 记录操作日志
            add_log('down', 0, ['period' => $period], '[跨店结算]-[门店卡余额表]：下载Excel模板');
            Log::info('[跨店结算]-[门店卡余额表]：Excel模板下载成功，月份：' . $period . '，用户ID：' . $this->uid);
            
            // 输出文件
            $writer->save('php://output');
            exit;

        } catch (\Exception $e) {
            return to_assign(1, '模板下载失败：' . $e->getMessage());
        }
    }
}