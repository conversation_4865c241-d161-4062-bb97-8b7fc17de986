<?php
/**
 * @copyright Copyright (c) 2021 仲正堂工作室
 * @license https://opensource.org/licenses/GPL-3.0
 * @link https://www.gougucms.com
 */
declare (strict_types=1);

namespace app\api\controller;

use app\api\service\IndexService;
use app\message\service\QiWeiService;
use app\rent\service\RentService;
use app\store\service\StoreBillService;
use app\store\service\StoreBusinessService;
use app\store\service\StoreSalaryService;
use app\store\service\StoreSecondService;
use app\store\service\StoreService;
use app\user\service\DepartmentService;
use DateTime;
use think\facade\Db;
use ZipStream\Exception;

require_once '../extend/qiyuesuo/Util.php';


class Script
{
    protected $now_month;
    protected $index_service;

    public function __construct()
    {
        $this->now_month = date("Y-m");
        $this->index_service = new IndexService();
    }

    public function store()
    {
        $this->store_rank_tongyong("store");

        $re_store = Db::name("department")->where(['remark' => '门店'])->select();

        foreach ($re_store as $k => $v) {
            $re_data = Db::name("store")->where([
                'did' => $v['id'], 'sdate' => $this->now_month
            ])->find();
            if (empty($re_data)) {
                $insert_data = [
                    'create_time' => time(),
                    'did' => $v['id'],
                    'dname' => $v['title'],
                    'aid' => $v['leader_id'],
                    'aname' => !empty($v['leader_id']) ? Db::name("admin")->where(['id' => $v['leader_id']])->value('name') : '',
                    'sdate' => $this->now_month,
                    'sdate_time' => time()
                ];
                Db::name("store")->insertGetId($insert_data);
            }
        }
    }

    //门店指标
    public function store_rank_target()
    {
        $this->store_rank_tongyong("store_rank_target");
    }

    //门店计算逻辑
    public function store_rank_calculate()
    {
        $this->store_rank_tongyong("store_rank_calculate");
    }

    public function store_rank_tongyong($database)
    {
        $re_data = Db::name($database)->where(['sdate' => $this->now_month])->find();
        if (empty($re_data)) {
            $pre_month = date('Y-m', strtotime('-1 month'));
            $pre_re_data = Db::name($database)->where(['sdate' => $pre_month])->select();
            foreach ($pre_re_data as $k => $v) {
                unset($v['id']);
                $v['sdate'] = $this->now_month;
                Db::name($database)->insertGetId($v);
            }
        }
    }

    //审批脚本
    public function approve_script()
    {
        set_time_limit(0);
        $this->employee_transtore_script();
        $this->employee_lizhi_script();
        $this->employee_zhuangang_script();
        $this->employee_jinsheng_script();
        $this->employee_zhuanzheng_script();
        $this->employee_shebao_script();
    }

    //员工 调店脚本
    public function employee_transtore_script()
    {
        $this->employee_script(37);
    }

    //员工 离职脚本
    public function employee_lizhi_script()
    {
        $this->employee_script(19);
    }

    //员工 转岗脚本
    public function employee_zhuangang_script()
    {
        $this->employee_script(20);
    }

    //员工 晋升脚本
    public function employee_jinsheng_script()
    {
        $this->employee_script(38);
    }

    //员工 转正脚本
    public function employee_zhuanzheng_script()
    {
        $this->employee_script(18);
    }

    public function employee_shebao_script()
    {
        $this->employee_script(42);
    }

    public function employee_script($type)
    {
        $approve = Db::name("approve")->where([
            'type' => $type,
            'check_status' => 2,
            'is_script' => 0
        ])->select()->toArray();

        foreach ($approve as $k => $v) {
            $jsoncode = unserialize($v['jsoncode']);
            $re = false;
            switch ($type) {
                case 37:
                    $re = $this->index_service->transtore($jsoncode);
                    break;
                case 19:
                    $re = $this->index_service->lizhi($jsoncode);
                    break;
                case 20:
                    $re = $this->index_service->zhuangang($jsoncode);
                    break;
                case 38:
                    $re = $this->index_service->jinsheng($jsoncode);
                    break;
                case 18:
                    $re = $this->index_service->zhuanzheng($jsoncode);
                    break;
                case 42:
                    $re = $this->index_service->shebao($jsoncode);
                    break;
            }
            if ($re) {
                Db::name("approve")->where(['id' => $v['id']])->update(['is_script' => 1]);
            }
        }
    }

    //门店老师工资 计算脚本 入职满两年的老师 基本工资从1500调整为1700
    public function base_salary()
    {
        $admin = Db::name("admin")->where([
            'status' => 1,
            'is_virtually' => 0
        ])->select()->toArray();

        foreach ($admin as $k => $v) {
            $department = Db::name("department")->where([
                ['id', 'in', explode(",", $v['did'])],
                ['remark', '=', '门店']
            ])->find();
            if (empty($department) || empty($v['entry_time'])) continue;

            $position_salary = 0;
            $re_bol = isFullTwoYears($v['entry_time']);
            if ($re_bol) {
                $position_salary = 1700;
            } else {
                $position_salary = 1500;
            }

            $admin_expand = Db::name("admin_expand")->where(['id' => $v['id']])->find();
            if (!empty($admin_expand) && ($admin_expand['base_salary'] == 0 || $admin_expand['base_salary'] == 1500)) {
                Db::name("admin_expand")->where(['id' => $v['id']])->update([
                    'base_salary' => $position_salary,
                    'position_salary' => $position_salary
                ]);
            }

        }
    }

    //记录传输过来的参数内容
    public function interface_msg()
    {
        $param = $_POST;

        if (empty($param)) {
            $param = file_get_contents("php://input");
            $param = json_decode($param, true);
        }
        //Db::name("interface_msg")->insertGetId(['jsoncode'=>json_encode($param)]);

        $this->interface_msg_insertData($param);

    }

    public function interface_msg_insertData($param)
    {
        $param_sign = explode("/", $param['sign']);

        $sign = $param_sign[0] . $param_sign[1];

        $insert_param = [
            'create_time' => date("Y-m-d H:i:s"),
            'sign' => $sign,
            'jsoncode' => json_encode($param)
        ];

        $interface_msg_sign = Db::name("interface_msg")->where(['sign' => $sign])->find();

        if (empty($interface_msg_sign)) {
            $im_id = Db::name("interface_msg")->insertGetId($insert_param);
        } else {
            if (!empty($interface_msg_sign['mer_ord_id'])) {
                return;
            } else {
                $im_id = $interface_msg_sign['id'];
            }
        }

        $resp_data = json_decode($param['resp_data']);
        $StoreBillService = new StoreBillService();

        if ($resp_data->trans_type != 'TRANS_REFUND') { //退款
            $mer_ord_id = $resp_data->trans_order_info->mer_ord_id;

            $interface_msg = Db::name("interface_msg")->where(['mer_ord_id' => $mer_ord_id])->find();
            if (!empty($interface_msg)) {
                return "已存在";
            }
            $insert_param['mer_ord_id'] = $mer_ord_id;
            $insert_param['devs_id'] = $resp_data->trans_order_info->devs_id;
            $cash_req_date = $mer_ord_id;

            $insert_param['cash_req_date'] =
                substr($cash_req_date, 0, 4) . '-' .
                substr($cash_req_date, 4, 2) . '-' .
                substr($cash_req_date, 6, 2) . ' ' .
                substr($cash_req_date, 8, 2) . ':' .
                substr($cash_req_date, 10, 2) . ':' .
                substr($cash_req_date, 12, 2);

            $insert_param['ref_amt'] = $resp_data->trans_order_info->ref_amt; //付款金额
            $insert_param['ref_fee_amt'] = $resp_data->trans_order_info->ref_fee_amt; //扣费金额
            $insert_param['unconfirm_amt'] = $resp_data->trans_order_info->unconfirm_amt; //实付金额

            if (isset($resp_data->trans_stat) && $resp_data->trans_stat == 'F') {
                $insert_param['status'] = 0; //实付金额
            }

            //$StoreBillService->count_pos_income($insert_param['cash_req_date'], $insert_param['devs_id'], $insert_param['unconfirm_amt'], 0);

        } else {
            $org_term_ord_id = $resp_data->org_term_ord_id;
            $interface_msg = Db::name("interface_msg")->where(['mer_ord_id' => $org_term_ord_id])->find();
            if (!empty($interface_msg)) {
                Db::name("interface_msg")->where(['id' => $interface_msg['id']])->update(['is_refund' => 1]);
                //$StoreBillService->count_pos_income($interface_msg['cash_req_date'], $interface_msg['devs_id'], $interface_msg['unconfirm_amt'], 1);
            }
        }

        $insert_param['trans_type'] = $resp_data->trans_type;

        $re = Db::name("interface_msg")->where(['id' => $im_id])->update($insert_param);

        Db::query("DELETE t1 FROM oa_interface_msg t1
INNER JOIN oa_interface_msg t2 
WHERE t1.id > t2.id and t1.mer_ord_id = t2.mer_ord_id");


        if ($re) {
            var_dump("成功:" . $re);
        } else {
            var_dump("失败");
        }
    }

    public function test()
    {
        $qiwei_serivce = new QiWeiService();
        $qiwei_serivce->sendQiweiUserList();

    }

    //当前所有员工的可用年假是多少 薪资是多少
    public function user_info()
    {
        set_time_limit(0);

        if (date("m-d") == '01-01') {
            Db::name("admin")->update(['annual_leave' => 0, 'is_annual_leave' => 0]);
        }

        $admin_list = Db::name("admin")->where([
            'is_virtually' => 0,
        ])->select()->toArray();

        $department_service = new DepartmentService();

        foreach ($admin_list as $k => $v) {
            $annual_leave = 0;
            //可用年假
            $entry_time = $v['entry_time']; //入职日期

            //入职不满一年没有年假
            $y_entry_time = date("Y-m-01", strtotime("+1 year,+1 month", $entry_time));

            if (time() >= strtotime($y_entry_time)) {
                $d_store = Db::name("department")->where([['id', 'in', $v['did']], ['remark', '=', '门店']])->find();
                if (!empty($d_store)) {
                    //判断是不是去年入职的
                    $last_year = date("Y", strtotime("-1 year", time()));
                    $store = $department_service->is_md($v['did']);

                    $base_annual_leave = 5;
                    if (!empty($store)) {
                        $base_annual_leave = 3;
                    }

                    if ($entry_time <= strtotime("{$last_year}-01-01")) {
                        $date1 = new DateTime(date('Y-m-d', $entry_time));
                        $date2 = new DateTime(date("Y-m-d"));
                        $diff = $date2->diff($date1);
                        $annual_leave = $base_annual_leave + $diff->y;
                    } else {
                        $date1 = new DateTime(date('Y-m-d', $entry_time));
                        $date2 = new DateTime(date("Y-12-31", strtotime("-1 year", time())));
                        $diff = $date2->diff($date1);
                        $entry_day = date("d", $entry_time);
                        $month = $diff->m;
                        if ($entry_day == '01') {
                            $month = $month + 1;
                        }
                        $annual_leave = $base_annual_leave - (12 - $month) / 12 * $base_annual_leave;
                    }

                    if ($annual_leave > 15) {
                        $annual_leave = 15;
                    }
                } else {
                    $ty_entry_time = date("Y-m-01", strtotime("+10 year,+1 month", $entry_time));
                    if (time() >= strtotime($ty_entry_time)) {
                        $annual_leave = 10;
                    } else {
                        $annual_leave = 5;
                    }
                }
            }

            $annual_leave = ceil($annual_leave * 2) / 2;
//            $y_entry = date("Y-m-01", strtotime("+1 year,+1 month", $entry_time));
//            if (date("Y-m-d") == $y_entry || date("m-d") == '01-01') {
//                var_dump("更新");
//                Db::name("admin")->where(['id' => $v['id']])->update(['annual_leave' => $annual_leave]);
//            }
            //司龄
            Db::name("admin")->where(['id' => $v['id']])->update(['annual_leave' => $annual_leave]);

        }


    }

    public function user_info2()
    {

        set_time_limit(0);

        /***
         * 企微token
         */
        $qiwei_serivce = new QiWeiService();
        $qiwei_serivce->sendQiweiUserList();

        //youzan id
        $admin_list = Db::name("admin")->where([
            'is_virtually' => 0,
            ['yz_uid', 'EXP', Db::raw("IS NULL")]
        ])->select()->toArray();

        foreach ($admin_list as $k => $v) {
            $count = Db::name("admin")->where(['name' => $v['name']])->count();

            $where = [
                ['name', 'like', '%' . $v['name'] . '%']
            ];

            if ($count > 1) {
                $kdt_id = Db::name("department")->where([
                    ['id', 'in', $v['did']], ['remark', '=', '门店']
                ])->value('kdt_id');

                if (!empty($kdt_id)) {
                    $where[] = ['kdt_id', '=', $kdt_id];
                }
            }

            $staff_info = Db::connect('mt')->table('staff_info')
                ->where($where)
                ->field("id,name,mobile,yz_open_id")
                ->find();

            if (!empty($staff_info)) {
                $admin = Db::connect('mysql')->table('oa_admin')
                    ->where(['id' => $v['id']])->update(['yz_uid' => $staff_info['yz_open_id']]);
            }

        }

    }

    //每日自动添加数据
    public function add_storebusiness()
    {
        ini_set("max_execution_time", '0');

        $store_dep = Db::name("department")->where(['remark' => '门店'])->select()->toArray();

        $store_business_service = new StoreBusinessService();
        $store_service = new StoreService();
        $store_salary_service = new StoreSalaryService();

        $param = get_params();

        if (isset($param['sdate'])) {
            $sdate = $param['sdate']; //输入日期后一天
        } else {
            $sdate = date('Y-m-d', strtotime('-1 day')); //输入日期后一天
        }

        $sdate_month = date('Y-m', strtotime($sdate)); //输入日期后一天

        foreach ($store_dep as $k => $v) {
            //每日数据
            $store_business_service->addData($v['id'], $sdate, 1, 'root');
            //老师汇总
            $where = array();
            $where[] = ['sdate', 'between', ["{$sdate_month}-01", getLastMonth($sdate_month)]];
            $where['did'] = $v['id'];
            $param['sdate'] = $sdate_month;
            $a_list = Db::name("store_business")->field('aid')->where($where)->group('aid')->select()->toArray();

            if (!empty($a_list)) {
                foreach ($a_list as $key => $value) {
                    $where['aid'] = $value['aid'];
                    $store_service->record_store_business_t($where, $param);
                }
            }
            //工资
            $param['did'] = $v['id'];
            $store = Db::name("store")->where([
                'sdate' => $param['sdate'],
                'did' => $param['did']
            ])->find();
            $store_salary_service->inittable2($param['sdate'], $param['did'], $store);

            $where_salary['date_time'] = strtotime($param['sdate']);
            $where_salary['did'] = $param['did'];
            $store_salary_list = Db::name("store_salary")->where($where_salary)->select()->toArray();
            foreach ($store_salary_list as $ssl_k => $ssl_v) {
                $total = Db::name("store_salary")->field("
                    (attender_amount
                    + labor_amount
                    + perf_amount
                    + kat_amount
                    + manager_amount
                    + overtime_amount
                    + hp_amount
                    + excess_amount
                    + dz_amount
                    + examine_amount
                    + punish_amount
                    + subsidy_amount
                    + differ_amount
                    + onther_amount
                    + tc_amount
                    - ABS(tuika_amount)
                    - ABS(sushe_amount)
                    - ABS(shebao_amount)
                    - ABS(fund_amount) ) as total
                ")->where(['id' => $ssl_v['id']])->find();

                if (empty($total) || empty($total['total'])) $total['total'] = 0;

                Db::name("store_salary")->where(['id' => $ssl_v['id']])->update(['total_amount' => $total['total']]);
            }

        }


        Db::query("DELETE t1 FROM oa_store_business t1
JOIN oa_store_business t2 ON 
t1.aid = t2.aid 
and t1.did = t2.did 
and t1.sdate = t2.sdate 
and t1.sdate = '$sdate'
AND t1.id < t2.id;");

    }

    public function update_salary()
    {
        ini_set("max_execution_time", '0');
        $storeSalaryService = new StoreSalaryService();

        $store_dep = Db::name("department")->where(['remark' => '门店'])->select()->toArray();

        $sdate = '2024-10';

        foreach ($store_dep as $k => $v) {
            $did = $v['id'];
            $store = Db::name("store")->where([
                'sdate' => $sdate,
                'did' => $did
            ])->find();
            $storeSalaryService->inittable($sdate, $did, $store);
        }

    }

    public function test11()
    {
        $order_item = Db::name("interface_msg")->where(['id' => 6588])->find();

        $jsoncode = json_decode($order_item['jsoncode'], true);

//        var_dump($jsoncode);


        $this->interface_msg_insertData($jsoncode);

    }

    public function qiyuesuo()
    {
        $sdkClient = \Util::getSDk();
        $contractDetailRequest = new \ContractDetailRequest();
        $contractDetailRequest->setContractId('3368156988323246642');
        $result = $sdkClient->service($contractDetailRequest);
        dump($result);
//
//        $filePath = "./storage/qiyuesuo/test.zip";
//        $attachmentDownloadRequest = new \AttachmentDownloadRequest();
//        $attachmentDownloadRequest->setBizId('3169488583681766138');
//        $result = $sdkClient->downloadService($attachmentDownloadRequest, $filePath);
//        print_r($result);

//        $contractViewPageRequest = new \ContractViewPageRequest();
//        $contractViewPageRequest->setContractId('3169488583681766138');
//        $result = $sdkClient->service($contractViewPageRequest);
//        print_r($result);
    }

    public function qiyuesuo2()
    {
        $sdkClient = \Util::getSDk();
        $filePath = "./storage/qiyuesuo/test.zip";
        $contractDownloadRequest = new \ContractDownloadRequest();
        $contractDownloadRequest->setContractId('3169488583681766138');
//        $contractDownloadRequest->setBizId('123456789');
        $contractDownloadRequest->setDownloadItems(['ATTACHMENT']);
        $contractDownloadRequest->setNeedCompressForOneFile(false);
        $result = $sdkClient->downloadService($contractDownloadRequest, $filePath);

//        $contractViewPageRequest = new \ContractViewPageRequest();
//        $contractViewPageRequest->setContractId('3169488583681766138');
//        $result = $sdkClient->service($contractViewPageRequest);
//        print_r($result);
    }




    public function secondservice()
    {
        //1号才执行
        $day = intval(date('d'));

        if ($day == 1) {
            $pre_month = date('Y-m', strtotime('-1 month'));
        } else {
            //上个月
            $pre_month = date('Y-m');
        }
        $pre_last_month = getLastMonth($pre_month);

        $re = Db::name("order_second")->where([
            ['finish_date', 'between', ["{$pre_month}-01 00:00:00", "{$pre_last_month} 23:59:59"]]
        ])->delete();

        $sql = "
            select l.*,a.id as aid,d.id as did
from 
(
SELECT 
    o.tid,
    o.finish_time,
    o.finish_date,
    o.sale_kdt_id,
    o.buyer_yz_open_id,
    ois.yz_open_id,
    Date(o.finish_date) as finish_day,
    NOW() AS create_time
FROM `oa_order` as o,  `oa_order_item_staff` as ois
where o.finish_date between '{$pre_month}-01 00:00:00' and '{$pre_last_month} 23:59:59'
and o.tid = ois.tid 
and o.is_reverse = 0
and ois.type = 1
GROUP BY o.tid,ois.yz_open_id
) l
left join oa_admin a on (a.yz_uid like CONCAT('%',l.yz_open_id,'%') or a.yz_uid2 like CONCAT('%',l.yz_open_id,'%'))
left join oa_department d on d.kdt_id = l.sale_kdt_id
        ";

        $list = Db::query($sql);

        $re = Db::name("order_second")->insertAll($list);

        //sleep(2000);
        if ($re){
            Db::name("store_second")->where(['sdate' => $pre_month])->delete();
            $storeSecondService = new StoreSecondService();
            $list = $storeSecondService->initStoreSecondData($pre_month);
            Db::name("store_second")->strict(false)->field(true)->insertAll($list);
        }


    }


    /***
     * @return void
     * 门店费用自动发起申请脚本
     */
    public function rent()
    {
        //提前七天发起付款申请
        $time = date('Y-m-d', strtotime(date('Y-m-d') . '+9 day'));

        $rent = Db::name('rent')
            ->field('r.id,r.cr_id,r.ei_amount,r.wyf_amount,
                cr.dz_aid,cr.dz_name,cr.files,
                cr.did,cr.cate_id,cr.account_name,cr.account_number,cr.account_bank,cr.pay_cycle')
            ->alias('r')
            ->where([
                'r.status' => 1,
                'r.is_pay' => 0,
                'r.check_status' => -1,
                ['r.pay_date', '<=', $time]
            ])
            ->join('contract_rent cr', 'r.cr_id = cr.id and cr.status = 1', 'right')
            ->select()->toArray();

        $rent_service = new RentService();
        foreach ($rent as $rent_k => $rent_v) {
            try {
                Db::startTrans();
                $aid = $rent_service->init_application($rent_v, 2403, '市场部');
                if (empty($aid)) {
                    Db::rollback();
                    continue;
                }
                $flow_step = Db::name("flow_step")->where(['action_id' => $aid])->find();
                if (empty($flow_step)) {
                    Db::rollback();
                    continue;
                }
                Db::name('rent')->where(['id' => $rent_v['id']])->update(['check_status' => 0]);
                Db::commit();
            } catch (Exception $e) {
                continue;
            }
        }
    }

    /**
     * @return void
     * 合同即将到期提醒
     * 临近付款 和 未付款的 发送企微和提醒给填写人
     */
    public function auto_rent_message()
    {

        $now_date = date("Y-m");

        //提前七天发起付款申请
        $time = date('Y-m-d', strtotime(date('Y-m-d') . '+5 day'));
        $rent = Db::name('rent')
            ->field('r.id,r.cr_id,r.ei_amount,r.pay_date,r.check_status,
            cr.did,cr.cate_id,cr.account_name,cr.account_number,cr.account_bank,cr.create_aid,
            d.title as dname, s.holder_id , s.aid')
            ->alias('r')
            ->where([
                'r.status' => 1,
                'r.is_pay' => 0,
                ['r.check_status', '<>', 2],
                ['r.pay_date', '<', $time]
            ])
            ->join('contract_rent cr', 'r.cr_id = cr.id and cr.status = 1', 'right')
            ->join('department d', 'd.id = cr.did ', 'right')
            ->join('store s', "s.did = cr.did and s.sdate = '$now_date'", 'left')
            ->select()->toArray();

        $check_status = [
            '-1' => '未申请',
            '0' => '已申请',
            '1' => '正在审批',
            '2' => '审批完成',
            '3' => '已驳回'
        ];

        $QiWeiService = new QiWeiService();
        foreach ($rent as $rent_k => $rent_v) {
            $diff_days = getDiffDays(date("Y-m-d"), $rent_v['pay_date']);
            if (strtotime($rent_v['pay_date']) < strtotime(date("Y-m-d"))) {
                $diff_days = 0 - $diff_days;
            }
            if ($diff_days < 0) { //超期
                $title = '### 付款`超期`提醒！';
                $days = "<font color=#6f0106 face='黑体' size=5>`{$diff_days}天`</font>";
            } else {//临近
                $title = '### `临近`付款日期提醒！';
                if ($diff_days >= 5) {
                    $days = "<font color=#ff6427 face='黑体' size=5>`{$diff_days}天`</font>";
                } else {
                    $days = "<font color=#ee232c face='黑体' size=5>`{$diff_days}天`</font>";
                }
            }
            $description = "$title
> 付款日期：{$rent_v['pay_date']}
>
> 门店：{$rent_v['dname']}
>
> **距离付款**：$days
>
> 付款金额：{$rent_v['ei_amount']}
>
> 审批状态：{$check_status[$rent_v['check_status']]}

请尽快联系相关人员进行付款！！！";

            $aids = [$rent_v['holder_id'], $rent_v['aid']];
            if ($rent_v['create_aid'] != 1237) {
                $aids = [
                    $rent_v['create_aid']
                ];
            } elseif (empty($rent_v['holder_id']) && empty($rent_v['aid'])) {
                $aids = [1237];
            }
            $aids = array_unique($aids);
            $QiWeiService->sendMessage_customize($aids,
                $title,
                $description,
                "https://oa.zztcaigou.cn/rent/rent/index?in_cr_id={$rent_v['id']}");
        }

        //合同到期
        $time = date('Y-m-d', strtotime(date('Y-m-d') . '+180 day'));
        $time_2 = date('Y-m-d', strtotime(date('Y-m-d') . '+90 day'));
        $time_3 = date('Y-m-d', strtotime(date('Y-m-d') . '+60 day'));
        $time_4 = date('Y-m-d', strtotime(date('Y-m-d') . '+30 day'));
        $whereor = [
            ['cr.end_date', '=', $time],
            ['cr.end_date', '=', $time_2],
            ['cr.end_date', '=', $time_3],
            ['cr.end_date', '=', $time_4],
        ];


        $contract_rent = Db::name('contract_rent')
            ->where([
                'cr.status' => 1,
            ])
            ->where(function ($query) use ($whereor) {
                $query->whereOr($whereor);
            })
            ->field('cr.*,d.title as dname,s.holder_id , s.aid')
            ->alias('cr')
            ->join('department d', 'd.id = cr.did ', 'right')
            ->join('store s', "s.did = cr.did and s.sdate = '$now_date'", 'left')
            ->select()->toArray();

        foreach ($contract_rent as $rent_k => $rent_v) {
            $diff_days = getDiffDays(date("Y-m-d"), $rent_v['end_date']);
            if (strtotime($rent_v['end_date']) < strtotime(date("Y-m-d"))) {
                $diff_days = 0 - $diff_days;
            }
            $title = '### 合同即将到期提醒！';
            if ($diff_days >= 5) {
                $days = "<font color=#ff6427 face='黑体' size=5>`{$diff_days}天`</font>";
            } else {
                $days = "<font color=#ee232c face='黑体' size=5>`{$diff_days}天`</font>";
            }
            $description = "$title
> 到期日期：{$rent_v['end_date']}
>
> 门店：{$rent_v['dname']}
>
> **距离合同到期**：$days

请尽快联系相关人员进行续签！！！";

            $aids = [$rent_v['holder_id'], $rent_v['aid']];
            if ($rent_v['create_aid'] != 1237) {
                $aids = [
                    $rent_v['create_aid']
                ];
            } elseif (empty($rent_v['holder_id']) && empty($rent_v['aid'])) {
                $aids = [1237];
            }
            $aids = array_unique($aids);

            $QiWeiService->sendMessage_customize($aids,
                $title,
                $description,
                "https://oa.zztcaigou.cn/rent/rent/index?in_cr_id={$rent_v['id']}");
        }
    }


    /***
     * @return void
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * 合同到期 提醒
     */
    public function auto_contract_rent_message()
    {
        //提前七天发起付款申请
        $time = date('Y-m-d', strtotime(date('Y-m-d') . '+90 day'));

        $rent = Db::name('contract_rent')
            ->field('cr.*,
                d.title as dname')
            ->alias('cr')
            ->where([
                'cr.status' => 1,
                ['cr.end_date', '<=', $time]
            ])
            ->join('department d', 'd.id = cr.did ', 'right')
            ->select()->toArray();

        $QiWeiService = new QiWeiService();
        foreach ($rent as $rent_k => $rent_v) {
            $diff_days = getDiffDays(date("Y-m-d"), $rent_v['end_date']);

            if ($diff_days == 90) { //超期
                $title = "### 合同：<font color=#ff5b1b>{$rent_v['code']}</font>90天到期提醒！";
                $days = "<font color=#ff5b1b>`{$diff_days}天`</font>";
            } else if ($diff_days == 60) { //超期
                $title = "### 合同：<font color=#b01e23>{$rent_v['code']}</font>60天到期提醒！";
                $days = "<font color=#b01e23>`{$diff_days}天`</font>";
            } else if ($diff_days <= 30) {//临近
                $title = "### 合同：<font color=#580004>{$rent_v['code']}</font>30天到期提醒！";
                $days = "<font color=#580004>`{$diff_days}天`</font>";
            } else {
                continue;
            }
            $description = "$title
> 合同结束时间：{$rent_v['end_date']}
>
> 门店：{$rent_v['dname']}
>
> **距离结束**：$days

请尽快联系相关人员进行合同续期！！！";
            $QiWeiService->sendMessage_customize([$rent_v['create_aid']], '', $description, "https://oa.zztcaigou.cn/");
        }


    }


    public function test2()
    {
        $list = Db::name("store_business_t")->select()->toArray();

        foreach ($list as $k => $v) {
            Db::name("store_business_t")->where(['id' => $v['id']])->update(['s_sdate' => "{$v['sdate']}-01"]);
        }
    }


    public function auto_bill()
    {
        //抖音
        $douying_c = new douying();
        //对账抖音数据
        $douying_c->douying_composite();

        $douying_c->douying_composite_md();
    }


    //昨天到店消费的客户信息
    public function customer_bymobile()
    {
        set_time_limit(0);

        $end_date = date("Y-m-d 00:00:00");
        $start_date = date("Y-m-d 00:00:00", strtotime("-1 day"));

        $sql = "
            SELECT o.buyer_yz_open_id,o.buyer_belong_kdt_id,o.buyer_mobile,o.buyer_customer_name
FROM `oa_order` o
where o.finish_date BETWEEN '$start_date' and '$end_date'
GROUP BY o.buyer_yz_open_id
        ";

        $res = Db::query($sql);

        $youzan_controller = new Youzan();

        foreach ($res as $k => $v) {
            $youzan_controller->customer_bymobile_url(
                $v['buyer_yz_open_id'],
                $v['buyer_belong_kdt_id'],
                $v['buyer_mobile'],
                $v['buyer_customer_name']
            );
        }

    }

//    public function test3()
//    {
//
//        $ap = Db::name('approve')->where(['id' => 3794])->find();
//        $jsoncode = unserialize($ap['jsoncode']);
//        $jsoncode['crent_id'] = 1226;
//        $jsoncode['rent_id'] = 10415;
//
//        Db::name('approve')->where(['id' => 3794])->update(['jsoncode' => serialize($jsoncode)]);
//        dump($jsoncode);
//
//    }

    public function store_tongbu()
    {

        $param = get_params();

        if (isset($param['sdate']) && !empty($param['sdate'])){
            $sdate = $param['sdate'];
        }else{
            $sdate = '2025-07';
        }

        $store = Db::name("store")->where(['sdate' => $sdate])->select()->toArray();
        dump($store);

        $pre_month = date('Y-m', strtotime('-1 month'));

        foreach ($store as $k => $v) {
            $ss = Db::name("store")->where(['sdate' => $pre_month, 'did' => $v['did']])->find();

            //当前月份的业绩升降档审批
            $approve = Db::name("approve")->where([
                'department_id' => $v['did'],
                'type' => 44,
                ['create_time' , '>' , strtotime("$pre_month")],
                'check_status' => 2
            ])->find();


            if (!empty($approve)){
                $jsondecode = unserialize($approve['jsoncode']);

                $n_store = Db::name("store")->where(['sdate' => $pre_month, 'grade' => $jsondecode['n_grade']])->find();
                Db::name("store")->where(['id' => $v['id']])->update(
                    [
                        'target_amount' => $n_store['target_amount'],
                        'nstatus' => $ss['nstatus'],
                        'rstatus' => $ss['rstatus'],
                        'grade' => $n_store['grade'],
                        'grade_amount' => $n_store['grade_amount'],
                        'ratio' => $n_store['ratio'],
                    ]
                );
            }else{
                Db::name("store")->where(['id' => $v['id']])->update(
                    [
                        'target_amount' => $ss['target_amount'],
                        'nstatus' => $ss['nstatus'],
                        'rstatus' => $ss['rstatus'],
                        'grade' => $ss['grade'],
                        'grade_amount' => $ss['grade_amount'],
                        'ratio' => $ss['ratio'],
                    ]
                );
            }
        }


    }


    public function tttt()
    {

        //a:21:{s:8:"crent_id";i:1048;s:7:"rent_id";i:1671;s:3:"aid";i:2403;s:5:"aname";s:9:"市场部";s:3:"did";i:55;s:5:"store";i:55;s:8:"pay_type";i:2;s:10:"pay_amount";s:8:"19410.96";s:10:"payee_name";s:10:"陈若清 ";s:11:"bank_number";s:20:"6212261001041228534 ";s:9:"open_bank";s:12:"工商银行";s:3:"con";s:6:"fukuan";s:11:"hidden_type";i:13;s:7:"flow_id";i:83;s:2:"id";i:0;s:4:"type";i:13;s:7:"in_text";s:0:"";s:6:"remark";s:283:"付款日期：2025-05-22 付款金额：19500.00                2025-06-01 至 2025-06-30 金额：6500.00，物业费：0.00
        //                2025-07-01 至 2025-07-31 金额：6500.00，物业费：0.00
        //                2025-08-01 至 2025-08-30 金额：6500.00，物业费：0.00";s:8:"file_ids";s:0:"";s:10:"copy_names";s:23:"郑冠羽,王赏,聂珂";s:9:"copy_uids";s:13:"1000,973,1237";}


        //a:21:{s:8:"crent_id";i:1048;s:7:"rent_id";i:1671;s:3:"aid";i:1;s:5:"aname";s:12:"超级员工";s:3:"did";i:55;s:5:"store";i:55;s:8:"pay_type";i:2;s:10:"pay_amount";s:8:"19500.00";s:10:"payee_name";s:10:"陈若清 ";s:11:"bank_number";s:20:"6212261001041228534 ";s:9:"open_bank";s:12:"工商银行";s:3:"con";s:6:"fukuan";s:11:"hidden_type";i:13;s:7:"flow_id";i:83;s:2:"id";i:0;s:4:"type";i:13;s:7:"in_text";s:0:"";s:6:"remark";s:286:"付款日期：2025-05-22 付款金额：19500.00
        //                2025-06-01 至 2025-06-30 金额：6500.00，物业费：0.00
        //                2025-07-01 至 2025-07-31 金额：6500.00，物业费：0.00
        //                2025-08-01 至 2025-08-30 金额：6500.00，物业费：0.00";s:8:"file_ids";s:0:"";s:10:"copy_names";s:23:"郑冠羽,王赏,聂珂";s:9:"copy_uids";s:13:"1000,973,1237";}

        $approve = Db::name("approve")->where(['id' => 4198])->find();
        dump(unserialize($approve['jsoncode']));

        $jsoncode = unserialize($approve['jsoncode']);

        $jsoncode['aid'] = 2403;
        $jsoncode['aname'] = '市场部';
        $jsoncode['payee_name'] = '郑冠羽';
        $jsoncode['bank_number'] = '6212260905000818070';
        $jsoncode['open_bank'] = '工商银行';


        Db::name("approve")->where(['id' => 4198])->update(['jsoncode' => serialize($jsoncode)]);

    }

    public function updateService()
    {
        $param = get_params();
        if (isset($param['sdate']) && !empty($param['sdate']) ){
            $sdate = $param['sdate'];
        }else{
            $sdate = '2025-05-27';
        }

        $did = 52;

        $sql = "
             select 
     '2025-05' as sdate,
ad.id as aid,ost.did as did,
ad.yz_uid,ad.yz_uid2,ad.name,
IFNULL(jo.num_2,0) as num_2,
IFNULL(jo.num_3,0) as num_3,
IFNULL(jo.num_4,0) as num_4,
IFNULL(jo.num_5,0) as num_5,
IFNULL(jo.num_6,0) as num_6,
IFNULL(jo.num_7,0) as num_7,
IFNULL(jo.num_8,0) as num_8,
IFNULL(jo.num_9,0) as num_9,

IFNULL(jo.num_2,0) + IFNULL(jo.num_3,0) + IFNULL(jo.num_4,0) + IFNULL(jo.num_5,0) 
    + IFNULL(jo.num_6,0) +IFNULL(jo.num_7,0) + IFNULL(jo.num_8,0) + IFNULL(jo.num_9,0) as num_total

from oa_store_business_t ost
LEFT JOIN oa_admin ad on ad.id = ost.aid
LEFT JOIN (
select
   aid,
   did,
	sum(num_2) as num_2,
    sum(num_3) as num_3,
    sum(num_4) as num_4,
    sum(num_5) as num_5,
    sum(num_6) as num_6,
    sum(num_7) as num_7,
    sum(num_8) as num_8,
    sum(num_9) as num_9
   from (
        select
    aid,
    did,
    case when a.num >= 2 then 1 else 0 end num_2,
    case when a.num >= 3 then 1 else 0 end num_3,
    case when a.num >= 4 then 1 else 0 end num_4,
    case when a.num >= 5 then 1 else 0 end num_5,
    case when a.num >= 6 then 1 else 0 end num_6,
    case when a.num >= 7 then 1 else 0 end num_7,
    case when a.num >= 8 then 1 else 0 end num_8,
    case when a.num >= 9 then 1 else 0 end num_9
    from (
        select dd.aid, dd.buyer_yz_open_id, dd.did, count(*) as num
				from
						(
							select distinct finish_day,aid, buyer_yz_open_id, did FROM oa_order_second
				 ) dd 
				where dd.finish_day >= '2025-05-01' and  dd.finish_day <= '$sdate'
				
				GROUP BY dd.aid, dd.buyer_yz_open_id, dd.did
				HAVING COUNT(*) >= 2
   ) a
    
   ) b
   group by aid, did
) jo on jo.aid = ost.aid and jo.did = ost.did
where ost.sdate = '2025-05' and ost.did = $did and ad.position_id not in ('85')
        ";

        $list = Db::query($sql);
        $num_total = 0;
        foreach ($list as $k => $v){
            Db::name("store_business")->where(
                [
                    'sdate' => $sdate,
                    'did' => $did,
                    'aid' => $v['aid']
                ]
            )->update(['second_service' => $v['num_total']]);
            $num_total +=  $v['num_total'];
        }
        dump($num_total);


    }

}
