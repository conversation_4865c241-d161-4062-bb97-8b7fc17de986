<div class="layui-row layui-col-space16">
	<div class="layui-col-md12">
		<div class="layui-card">
			<div class="layui-card-title">项目列表</div>
			<div style="padding: 12px;">
				<table id="Project" lay-filter="Project" class="layui-hide" style="margin-top:0"></table>
			</div>
		</div>
	</div>
</div>
<script>
//项目
function layoutProject(table){
	table.render({
		elem: '#Project'
		, url: "/home/<USER>/get_project_list" //数据接口
		, page: false //开启分页
		, cols: [[ //表头
			{ field: 'id', title: '项目编号', align: 'center','width': 90, templet: function (d) {
					return 'P' + d.id;
				}
			},
			{field: 'status', title: '状态', align: 'center', width: 90, templet: function (d) {
					var html = '<span class="layui-color-' + d.status + '">『' + d.status_name + '』</span>';
					return html;
				}
			},
			{ field: 'title', title: '项目名称',templet: '<div><a data-href="/project/index/view/id/{{d.id}}.html" class="side-a">{{d.name}}</a></div>'},
			{ field: 'director_name', title: '负责人', align: 'center', width: 80},
			{ field: 'plan_time', title: '项目周期', align: 'center', width: 190},
		]]
	});
}
</script>