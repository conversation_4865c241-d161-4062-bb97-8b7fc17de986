<?php
/**
 * @copyright Copyright (c) 2021 仲正堂工作室
 * @license https://opensource.org/licenses/GPL-3.0
 * @link https://www.gougucms.com
 */

declare (strict_types=1);

namespace app\dividend\service;

use app\dividend\model\DividendStoreDetail;
use app\dividend\model\DividendStoreDetailPerson;
use think\facade\Db;

class DividendDetailService
{
    /**
     * 获取门店列表（用于搜索）
     * @return array
     */
    public function getStoreList()
    {
        $storeList = Db::name('Department')
            ->where('status', '>=', 0)
            ->where('remark', '门店')
            ->field('id, title')
            ->order('sort asc, id asc')
            ->select()
            ->toArray();

        return $storeList;
    }

    /**
     * 获取门店分红明细列表
     * @param array $param 查询参数
     * @return array
     */
    public function getDetailList($param)
    {
        // 构建查询条件
        $where = [];

        // 门店筛选
        if (!empty($param['store_ids']) && is_array($param['store_ids'])) {
            $where[] = ['dsd.store_id', 'in', $param['store_ids']];
        }

        // 统计周期筛选
        if (!empty($param['period'])) {
            $where[] = ['dsd.period', '=', $param['period']];
        }

        // 分页参数
        $page = $param['page'] ?? 1;
        $limit = $param['limit'] ?? 20;

        // 获取列表数据
        $list = DividendStoreDetail::getList($where, $page, $limit);

        // 获取合计行数据
        $totalRow = DividendStoreDetail::getTotalRow($where);

        return [
            'code' => 0,
            'msg' => '获取成功',
            'count' => $list['total'],
            'data' => $list['data'],
            'totalRow' => $totalRow
        ];
    }


}
