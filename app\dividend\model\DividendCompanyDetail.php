<?php
/**
 * @copyright Copyright (c) 2021 仲正堂工作室
 * @license https://opensource.org/licenses/GPL-3.0
 * @link https://www.gougucms.com
 */

namespace app\dividend\model;

use think\Model;

class DividendCompanyDetail extends Model
{
    protected $name = 'dividend_company_detail';

    // 设置字段信息
    protected $schema = [
        'id'                          => 'int',
        'store_id'                    => 'int',
        'period'                      => 'string',
        'dividend_profit'             => 'decimal',
        'company_shareholding_ratio'  => 'decimal',
        'remark'                      => 'string',
        'is_delete'                   => 'int',
        'delete_time'                 => 'int',
        'create_time'                 => 'int',
        'update_time'                 => 'int',
    ];

    // 自动时间戳
    protected $autoWriteTimestamp = true;
    protected $createTime = 'create_time';
    protected $updateTime = 'update_time';

    /**
     * 关联门店信息
     */
    public function store()
    {
        return $this->belongsTo('app\user\model\Department', 'store_id', 'id');
    }

    /**
     * 关联分红人信息
     */
    public function persons()
    {
        return $this->hasMany('app\dividend\model\DividendCompanyDetailPerson', 'company_detail_id', 'id');
    }

    /**
     * 获取指定周期的公司分红数据
     * @param string $period 统计周期
     * @param array $storeIds 门店ID数组
     * @return array
     */
    public static function getCompanyDividendData($period, $storeIds = [])
    {
        $where = [
            ['period', '=', $period],
            ['is_delete', '=', 0]
        ];

        if (!empty($storeIds)) {
            $where[] = ['store_id', 'in', $storeIds];
        }

        return self::where($where)
            ->with(['store', 'persons' => function($query) {
                $query->where('is_delete', 0)->order('id asc');
            }])
            ->order('store_id asc')
            ->select()
            ->toArray();
    }

    /**
     * 获取统计数据
     * @param string $period 统计周期
     * @param array $storeIds 门店ID数组
     * @return array
     */
    public static function getStatistics($period, $storeIds = [])
    {
        $where = [
            ['period', '=', $period],
            ['is_delete', '=', 0]
        ];

        if (!empty($storeIds)) {
            $where[] = ['store_id', 'in', $storeIds];
        }

        $statistics = self::where($where)
            ->field([
                'COUNT(*) as store_count',
                'SUM(dividend_profit) as total_dividend_profit'
            ])
            ->find();

        return [
            'store_count' => intval($statistics['store_count'] ?? 0),
            'total_dividend_profit' => floatval($statistics['total_dividend_profit'] ?? 0)
        ];
    }

    /**
     * 创建或更新公司分红数据
     * @param array $data 分红数据
     * @return bool|int
     */
    public static function createOrUpdate($data)
    {
        $existing = self::where([
            'store_id' => $data['store_id'],
            'period' => $data['period'],
            'is_delete' => 0
        ])->find();

        if ($existing) {
            // 更新现有记录
            $data['update_time'] = time();
            return self::where('id', $existing['id'])->update($data);
        } else {
            // 创建新记录
            $data['create_time'] = time();
            $data['update_time'] = time();
            return self::insertGetId($data);
        }
    }

    /**
     * 软删除指定周期的数据
     * @param string $period 统计周期
     * @param array $storeIds 门店ID数组
     * @return bool
     */
    public static function softDeleteByPeriod($period, $storeIds = [])
    {
        $where = [
            ['period', '=', $period],
            ['is_delete', '=', 0]
        ];

        if (!empty($storeIds)) {
            $where[] = ['store_id', 'in', $storeIds];
        }

        return self::where($where)->update([
            'is_delete' => 1,
            'delete_time' => time(),
            'update_time' => time()
        ]);
    }
}
