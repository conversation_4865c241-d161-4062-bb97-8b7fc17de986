<?php
/**
 * @copyright Copyright (c) 2021 仲正堂工作室
 * @license https://opensource.org/licenses/GPL-3.0
 * @link https://www.gougucms.com
 */

namespace app\cross\service;

use think\facade\Db;
use app\cross\model\CrossStoreSettleSummary;

class CrossStoreSettleService
{
    /**
     * 获取门店列表（用于搜索）
     * @return array
     */
    public static function getStoreList()
    {
        $storeList = Db::name('Department')
            ->where('status', '>=', 0)
            ->where('remark', '门店')
            ->field('id, title')
            ->order('sort asc, id asc')
            ->select()
            ->toArray();

        return $storeList;
    }

    /**
     * 获取有效门店列表（状态为1，只包含门店）
     * @return array
     */
    public static function getActiveStoreList()
    {
        $list = Db::name('Department')
            ->where(['status' => 1])
            ->where('remark', '门店')
            ->select()
            ->toArray();
        return $list;
    }

    /**
     * 获取月份选项（最近12个月）
     * @return array
     */
    public static function getPeriodOptions()
    {
        $options = [];
        for ($i = 0; $i < 12; $i++) {
            $period = date('Y-m', strtotime("-{$i} month"));
            $options[] = [
                'value' => $period,
                'label' => $period
            ];
        }
        return $options;
    }

    /**
     * 获取月份选项（键值对格式）
     * @return array
     */
    public static function getPeriodOptionsKeyValue()
    {
        $options = [];
        for ($i = 0; $i < 12; $i++) {
            $period = date('Y-m', strtotime("-{$i} month"));
            $options[$period] = $period;
        }
        return $options;
    }

    /**
     * 获取门店类型选项
     * @return array
     */
    public static function getStoreTypeOptions()
    {
        return [
            ['value' => '新店', 'label' => '新店'],
            ['value' => '老店', 'label' => '老店']
        ];
    }

    /**
     * 获取跨店结算统计数据
     * @param string $period 统计周期
     * @return array
     */
    public static function getSettleStatistics($period = '')
    {
        $where = [];
        if (!empty($period)) {
            $where[] = ['period', '=', $period];
        }

        $statistics = CrossStoreSettleSummary::where($where)
            ->field([
                'COUNT(*) as total_count',
                'SUM(total_reconciliation_amount) as total_reconciliation',
                'SUM(p_total_amount) as total_p_amount',
                'SUM(b_total_amount) as total_b_amount',
                'SUM(cc_total_amount) as total_cc_amount',
                'SUM(ol_total_amount) as total_ol_amount'
            ])
            ->find();

        return [
            'total_count' => intval($statistics['total_count'] ?? 0),
            'total_reconciliation' => floatval($statistics['total_reconciliation'] ?? 0),
            'total_p_amount' => floatval($statistics['total_p_amount'] ?? 0),
            'total_b_amount' => floatval($statistics['total_b_amount'] ?? 0),
            'total_cc_amount' => floatval($statistics['total_cc_amount'] ?? 0),
            'total_ol_amount' => floatval($statistics['total_ol_amount'] ?? 0)
        ];
    }

    /**
     * 获取门店配对统计
     * @param string $period 统计周期
     * @return array
     */
    public static function getStorePairStatistics($period = '')
    {
        $where = [];
        if (!empty($period)) {
            $where[] = ['css.period', '=', $period];
        }

        $pairStats = Db::name('CrossStoreSettleSummary')
            ->alias('css')
            ->join('Department d1', 'd1.id = css.store_id', 'LEFT')
            ->join('Department d2', 'd2.id = css.other_store_id', 'LEFT')
            ->where($where)
            ->field([
                'css.store_id',
                'css.other_store_id',
                'd1.title as store_name',
                'd2.title as other_store_name',
                'COUNT(*) as pair_count',
                'SUM(css.total_reconciliation_amount) as pair_total_amount'
            ])
            ->group('css.store_id, css.other_store_id')
            ->order('pair_total_amount desc')
            ->select()
            ->toArray();

        return $pairStats;
    }

    /**
     * 获取门店结算排行
     * @param string $period 统计周期
     * @param int $limit 返回数量限制
     * @return array
     */
    public static function getStoreRanking($period = '', $limit = 10)
    {
        $where = [];
        if (!empty($period)) {
            $where[] = ['css.period', '=', $period];
        }

        $ranking = Db::name('CrossStoreSettleSummary')
            ->alias('css')
            ->join('Department d', 'd.id = css.store_id', 'LEFT')
            ->where($where)
            ->field([
                'css.store_id',
                'd.title as store_name',
                'SUM(css.total_reconciliation_amount) as store_total_amount',
                'COUNT(*) as settle_count'
            ])
            ->group('css.store_id')
            ->order('store_total_amount desc')
            ->limit($limit)
            ->select()
            ->toArray();

        return $ranking;
    }

    /**
     * 获取月度趋势数据
     * @param int $months 获取最近几个月的数据
     * @return array
     */
    public static function getMonthlyTrend($months = 6)
    {
        $trendData = [];
        
        for ($i = $months - 1; $i >= 0; $i--) {
            $period = date('Y-m', strtotime("-{$i} month"));
            
            $monthData = CrossStoreSettleSummary::where('period', $period)
                ->field([
                    'COUNT(*) as count',
                    'SUM(total_reconciliation_amount) as total_amount',
                    'SUM(p_total_amount) as p_amount',
                    'SUM(b_total_amount) as b_amount',
                    'SUM(cc_total_amount) as cc_amount',
                    'SUM(ol_total_amount) as ol_amount'
                ])
                ->find();

            $trendData[] = [
                'period' => $period,
                'count' => intval($monthData['count'] ?? 0),
                'total_amount' => floatval($monthData['total_amount'] ?? 0),
                'p_amount' => floatval($monthData['p_amount'] ?? 0),
                'b_amount' => floatval($monthData['b_amount'] ?? 0),
                'cc_amount' => floatval($monthData['cc_amount'] ?? 0),
                'ol_amount' => floatval($monthData['ol_amount'] ?? 0)
            ];
        }

        return $trendData;
    }

    /**
     * 验证门店配对数据
     * @param array $data 要验证的数据
     * @return array
     */
    public static function validateStorePairData($data)
    {
        $result = [
            'success' => true,
            'message' => ''
        ];

        // 验证门店不能相同
        if ($data['store_id'] == $data['other_store_id']) {
            $result['success'] = false;
            $result['message'] = '门店和他店不能相同';
            return $result;
        }

        // 验证门店是否存在
        $storeExists = Db::name('Department')->where('id', $data['store_id'])->find();
        if (empty($storeExists)) {
            $result['success'] = false;
            $result['message'] = '门店不存在';
            return $result;
        }

        $otherStoreExists = Db::name('Department')->where('id', $data['other_store_id'])->find();
        if (empty($otherStoreExists)) {
            $result['success'] = false;
            $result['message'] = '他店不存在';
            return $result;
        }

        return $result;
    }

    /**
     * 格式化金额显示
     * @param float $amount 金额
     * @param int $decimals 小数位数
     * @return string
     */
    public static function formatAmount($amount, $decimals = 2)
    {
        return number_format(floatval($amount), $decimals);
    }

    /**
     * 获取门店名称
     * @param int $storeId 门店ID
     * @return string
     */
    public static function getStoreName($storeId)
    {
        $storeName = Db::name('Department')
            ->where('id', $storeId)
            ->value('title');

        return $storeName ?: '未知门店';
    }

    /**
     * 导出跨店结算数据
     * @param array $where 查询条件
     * @return array
     */
    public static function exportSettleData($where = [])
    {
        $list = Db::name('CrossStoreSettleSummary')
            ->alias('css')
            ->join('Department d1', 'd1.id = css.store_id', 'LEFT')
            ->join('Department d2', 'd2.id = css.other_store_id', 'LEFT')
            ->field('css.*, d1.title as store_name, d2.title as other_store_name')
            ->where($where)
            ->order('css.period desc, css.id desc')
            ->select()
            ->toArray();

        // 格式化导出数据
        $exportData = [];
        foreach ($list as $item) {
            $exportData[] = [
                '汇总月份' => $item['period'],
                '门店' => $item['store_name'],
                '门店类型' => $item['store_type'],
                '他店' => $item['other_store_name'],
                '他店类型' => $item['other_store_type'],
                '总对账金额' => self::formatAmount($item['total_reconciliation_amount']),
                '储值本金合计' => self::formatAmount($item['p_total_amount']),
                '储值赠金合计' => self::formatAmount($item['b_total_amount']),
                '次卡金额合计' => self::formatAmount($item['cc_total_amount']),
                '网店核销合计' => self::formatAmount($item['ol_total_amount']),
                '创建时间' => date('Y-m-d H:i:s', $item['create_time']),
                '更新时间' => date('Y-m-d H:i:s', $item['update_time'])
            ];
        }

        return $exportData;
    }

    /**
     * 获取结算类型选项
     * @return array
     */
    public static function getSettlementTypeOptions()
    {
        return [
            '储值卡跨店消费' => '储值卡跨店消费',
            '储值卡跨店退款' => '储值卡跨店退款',
            '次卡跨店消费' => '次卡跨店消费',
            '次卡跨店退款' => '次卡跨店退款',
        ];
    }

    /**
     * 获取储值跨店结算明细统计数据
     * @param array $where 查询条件
     * @return array
     */
    public static function getDetailStatistics($where = [])
    {
        $query = Db::name('CrossStoreSettleDetail')
            ->alias('csd');

        if (!empty($where)) {
            $query->where($where);
        }

        $statistics = $query->field([
            'COUNT(*) as total_count',
            'SUM(settlement_amount) as total_settlement_amount',
            'SUM(settlement_principal_amount) as total_principal_amount',
            'SUM(settlement_bonus_amount) as total_bonus_amount',
            'SUM(product_quantity) as total_quantity',
            'COUNT(DISTINCT consume_store_id) as consume_store_count',
            'COUNT(DISTINCT settlement_store_id) as settlement_store_count',
            'COUNT(DISTINCT customer_mobile) as customer_count'
        ])->find();

        return [
            'total_count' => intval($statistics['total_count'] ?? 0),
            'total_settlement_amount' => self::formatAmount($statistics['total_settlement_amount'] ?? 0),
            'total_principal_amount' => self::formatAmount($statistics['total_principal_amount'] ?? 0),
            'total_bonus_amount' => self::formatAmount($statistics['total_bonus_amount'] ?? 0),
            'total_quantity' => intval($statistics['total_quantity'] ?? 0),
            'consume_store_count' => intval($statistics['consume_store_count'] ?? 0),
            'settlement_store_count' => intval($statistics['settlement_store_count'] ?? 0),
            'customer_count' => intval($statistics['customer_count'] ?? 0)
        ];
    }

    /**
     * 获取储值跨店结算明细按门店统计
     * @param array $where 查询条件
     * @return array
     */
    public static function getDetailStoreStatistics($where = [])
    {
        $query = Db::name('CrossStoreSettleDetail')
            ->alias('csd')
            ->join('oa_department cs', 'cs.id = csd.consume_store_id', 'LEFT')
            ->join('oa_department ss', 'ss.id = csd.settlement_store_id', 'LEFT');

        if (!empty($where)) {
            $query->where($where);
        }

        // 按消费门店统计
        $consumeStats = $query->field([
            'cs.title as store_name',
            'COUNT(*) as consume_count',
            'SUM(csd.settlement_amount) as consume_amount'
        ])
        ->group('csd.consume_store_id')
        ->order('consume_amount desc')
        ->limit(10)
        ->select()
        ->toArray();

        // 按结算门店统计
        $settlementStats = $query->field([
            'ss.title as store_name',
            'COUNT(*) as settlement_count',
            'SUM(csd.settlement_amount) as settlement_amount'
        ])
        ->group('csd.settlement_store_id')
        ->order('settlement_amount desc')
        ->limit(10)
        ->select()
        ->toArray();

        return [
            'consume_stats' => $consumeStats,
            'settlement_stats' => $settlementStats
        ];
    }

    /**
     * 导出储值跨店结算明细数据
     * @param array $where 查询条件
     * @return array
     */
    public static function exportDetailData($where = [])
    {
        $query = Db::name('CrossStoreSettleDetail')
            ->alias('csd')
            ->join('oa_department cs', 'cs.id = csd.consume_store_id', 'LEFT')
            ->join('oa_department ss', 'ss.id = csd.settlement_store_id', 'LEFT')
            ->join('oa_department cos', 'cos.id = csd.card_open_store_id', 'LEFT')
            ->join('oa_department cbs', 'cbs.id = csd.customer_belong_store_id', 'LEFT');

        if (!empty($where)) {
            $query->where($where);
        }

        $list = $query->field('csd.*, cs.title as consume_store_name, ss.title as settlement_store_name,
                               cos.title as card_open_store_name, cbs.title as customer_belong_store_name')
            ->order('csd.settlement_time desc, csd.id desc')
            ->select()
            ->toArray();

        // 格式化导出数据
        $exportData = [];
        foreach ($list as $item) {
            $exportData[] = [
                '结算类型' => $item['settlement_type'],
                '消费门店' => $item['consume_store_name'],
                '商品名称' => $item['product_name'],
                '商品数量' => $item['product_quantity'],
                '充值卡名称' => $item['card_name'],
                '结算金额' => self::formatAmount($item['settlement_amount']),
                '结算门店' => $item['settlement_store_name'],
                '结算本金金额' => self::formatAmount($item['settlement_principal_amount']),
                '结算赠金金额' => self::formatAmount($item['settlement_bonus_amount']),
                '结算时间' => $item['settlement_time'],
                '发生订单号' => $item['order_number'],
                '关联订单号' => $item['related_order_number'],
                '充值卡开卡门店' => $item['card_open_store_name'],
                '客户姓名' => $item['customer_name'],
                '客户手机号' => $item['customer_mobile'],
                '客户归属门店' => $item['customer_belong_store_name'],
                '创建时间' => date('Y-m-d H:i:s', $item['create_time']),
                '更新时间' => date('Y-m-d H:i:s', $item['update_time'])
            ];
        }

        return $exportData;
    }
}
