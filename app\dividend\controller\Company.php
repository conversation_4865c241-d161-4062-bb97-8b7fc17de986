<?php
/**
 * @copyright Copyright (c) 2021 仲正堂工作室
 * @license https://opensource.org/licenses/GPL-3.0
 * @link https://www.gougucms.com
 */

declare (strict_types=1);

namespace app\dividend\controller;

use app\base\BaseController;
use app\dividend\model\DividendCompanyDetail;
use app\dividend\model\DividendCompanyDetailPerson;
use think\facade\Db;
use think\facade\View;
use think\facade\Log;
use think\App;
use systematic\ConfigManager;

// 公司股东分红明细表

class Company extends BaseController
{
    public function __construct(App $app)
    {
        parent::__construct($app);
    }

    /**
     * 检查当前用户是否有全部数据访问权限
     * @return bool
     */
    private function hasFullDataAccess()
    {
        try {
            // 获取当前用户信息
            $currentUser = get_admin($this->uid);
            if (!$currentUser) {
                return false;
            }

            // 从ConfigManager获取分红系统数据访问权限配置
            $permissions = ConfigManager::get('dividend_data_access_permissions', [
                'positions' => [],
                'users' => []
            ]);

            $allowedPositions = $permissions['positions'] ?? [];
            $allowedUsers = $permissions['users'] ?? [];

            // 检查用户ID是否在允许列表中
            if (in_array($this->uid, $allowedUsers)) {
                return true;
            }

            // 检查职位ID是否在允许列表中
            if (!empty($currentUser['position_id']) && in_array($currentUser['position_id'], $allowedPositions)) {
                return true;
            }

            return false;
        } catch (\Exception $e) {
            Log::error('检查分红数据访问权限失败: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * 检查当前用户是否有按钮操作权限
     * @return bool
     */
    private function hasButtonAccess()
    {
        try {
            // 从ConfigManager获取分红系统数据访问权限配置
            $permissions = ConfigManager::get('dividend_data_access_permissions', [
                'positions' => [],
                'users' => [],
                'button_access_users' => []
            ]);

            $buttonAccessUsers = $permissions['button_access_users'] ?? [];

            // 检查用户ID是否在按钮访问允许列表中
            return in_array($this->uid, $buttonAccessUsers);
        } catch (\Exception $e) {
            Log::error('检查分红按钮访问权限失败: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * 公司股东分红明细列表
     */
    public function index()
    {
        // 权限检查：如果用户没有全部数据访问权限，则跳转到错误页面
        if (!$this->hasFullDataAccess()) {
            Log::info('[门店分红]-[公司股东分红表]：用户无权限访问公司股东分红表，用户ID：' . $this->uid);
            if (request()->isAjax()) {
                return [
                    'code' => 1,
                    'msg' => '无权限访问',
                    'count' => 0,
                    'data' => []
                ];
            } else {
                // 返回友好的错误页面
                View::assign('error_msg', '抱歉，您没有权限访问公司股东分红表');
                View::assign('error_title', '访问受限');
                return view('error/permission');
            }
        }

        if (request()->isAjax()) {
            $param = get_params();

            // 构建查询条件
            $where = [];
            $personWhere = [];

            if (!empty($param['store_ids']) && is_array($param['store_ids'])) {
                // 通过门店ID数组搜索
                $where[] = ['dcd.store_id', 'in', $param['store_ids']];
            }
            if (!empty($param['period'])) {
                $where[] = ['dcd.period', '=', $param['period']];
            }
            if (!empty($param['shareholder_name'])) {
                $personWhere[] = ['dcdp.shareholder_name', 'like', '%' . $param['shareholder_name'] . '%'];
            }
            $where[] = ['dcd.is_delete', '=', 0];
            $personWhere[] = ['dcdp.is_delete', '=', 0];

            // 不分页查询 - 以分红人为维度，获取所有数据
            $listData = Db::name('DividendCompanyDetailPerson')
                ->alias('dcdp')
                ->join('oa_dividend_company_detail dcd', 'dcd.id = dcdp.company_detail_id', 'INNER')
                ->join('oa_department d', 'd.id = dcd.store_id', 'LEFT')
                ->field('dcdp.*, dcd.store_id, dcd.period, dcd.dividend_profit, dcd.company_shareholding_ratio, d.title as store_name')
                ->where($where)
                ->where($personWhere)
                ->order('dcd.store_id asc, dcdp.id asc')
                ->select()
                ->toArray();

            Log::info('[门店分红]-[公司股东分红表]：查询公司股东分红列表，结果数量：' . count($listData) . '，用户ID：' . $this->uid);

            // 处理数据格式化
            $tableData = [];

            foreach ($listData as $index => $item) {
                // 安全地处理数值字段
                $dividendProfit = is_numeric($item['dividend_profit']) ? floatval($item['dividend_profit']) : 0;
                $companyShareholdingRatio = is_numeric($item['company_shareholding_ratio']) ? floatval($item['company_shareholding_ratio']) : 0;
                $shareholdingRatio = is_numeric($item['shareholding_ratio']) ? floatval($item['shareholding_ratio']) : 0;
                $actualShareholding = is_numeric($item['actual_shareholding']) ? floatval($item['actual_shareholding']) : 0;
                $amount = is_numeric($item['amount']) ? floatval($item['amount']) : 0;
                $adjustmentAmount = is_numeric($item['adjustment_amount']) ? floatval($item['adjustment_amount']) : 0;
                $payableAmount = is_numeric($item['payable_amount']) ? floatval($item['payable_amount']) : 0;

                $rowData = [
                    'id' => $item['id'],
                    'store_id' => $item['store_id'],
                    'store_name' => $item['store_name'] ?? '未知门店',
                    'period' => $item['period'] ?? '',
                    'dividend_profit' => number_format($dividendProfit, 2),
                    'company_shareholding_ratio' => number_format($companyShareholdingRatio, 2),
                    'shareholder_name' => $item['shareholder_name'] ?? '未知分红人',
                    'shareholding_ratio' => number_format($shareholdingRatio, 3),
                    'actual_shareholding' => number_format($actualShareholding, 3),
                    'amount' => number_format($amount, 2),
                    'adjustment_amount' => number_format($adjustmentAmount, 2),
                    'payable_amount' => number_format($payableAmount, 2)
                ];

                $tableData[] = $rowData;
            }

            // 计算合计行数据
            $totalQuery = Db::name('DividendCompanyDetail')
                ->alias('dcd')
                ->where($where);

            $totalData = $totalQuery->field([
                'SUM(dcd.dividend_profit) as total_dividend_profit'
            ])->find();

            // 计算分红人金额、调整金额和应付金额合计
            $totalAmount = 0;
            $totalAdjustmentAmount = 0;
            $totalPayableAmount = 0;
            if (!empty($where)) {
                $personTotals = Db::name('DividendCompanyDetailPerson')
                    ->alias('dcdp')
                    ->join('oa_dividend_company_detail dcd', 'dcd.id = dcdp.company_detail_id', 'LEFT')
                    ->where($where)
                    ->where($personWhere) // 应用数据权限控制
                    ->field([
                        'SUM(dcdp.amount) as total_amount',
                        'SUM(dcdp.adjustment_amount) as total_adjustment_amount',
                        'SUM(dcdp.payable_amount) as total_payable_amount'
                    ])
                    ->find();

                $totalAmount = floatval($personTotals['total_amount'] ?? 0);
                $totalAdjustmentAmount = floatval($personTotals['total_adjustment_amount'] ?? 0);
                $totalPayableAmount = floatval($personTotals['total_payable_amount'] ?? 0);
            }

            $totalRow = [];
            if (!empty($totalData)) {
                $totalRow = [
                    'store_name' => '合计',
                    'dividend_profit' => number_format(floatval($totalData['total_dividend_profit'] ?? 0), 2),
                    'company_shareholding_ratio' => '-',
                    'shareholder_name' => '-',
                    'shareholding_ratio' => '-',
                    'actual_shareholding' => '-',
                    'amount' => number_format($totalAmount, 2),
                    'adjustment_amount' => number_format($totalAdjustmentAmount, 2),
                    'payable_amount' => number_format($totalPayableAmount, 2)
                ];
            }

            // 返回响应数据
            $response = [
                'code' => 0,
                'msg' => '获取成功',
                'count' => count($tableData),
                'data' => $tableData,
                'totalRow' => $totalRow,
                'hasButtonAccess' => $this->hasButtonAccess() // 添加按钮权限信息
            ];

            return json($response);
        } else {
            // 记录查看公司股东分红列表页面日志
            add_log('view', 0, [], '[门店分红]-[公司股东分红表]：查看公司股东分红列表页面');
            Log::info('[门店分红]-[公司股东分红表]：用户查看公司股东分红列表页面，用户ID：' . $this->uid);
            return View::fetch();
        }
    }

    /**
     * 编辑页面
     */
    public function edit()
    {
        // 权限检查：如果用户没有全部数据访问权限，则跳转到错误页面
        if (!$this->hasFullDataAccess()) {
            Log::info('[门店分红]-[公司股东分红表]：用户无权限访问公司股东分红编辑页面，用户ID：' . $this->uid);
            View::assign('error_msg', '抱歉，您没有权限访问公司股东分红编辑功能');
            View::assign('error_title', '访问受限');
            return view('error/permission');
        }

        // 记录查看公司股东分红编辑页面日志
        add_log('view', 0, [], '[门店分红]-[公司股东分红表]：查看公司股东分红编辑页面');
        Log::info('[门店分红]-[公司股东分红表]：用户查看公司股东分红编辑页面，用户ID：' . $this->uid);
        return View::fetch();
    }

    /**
     * 获取门店详细数据（用于编辑）
     */
    public function getStoreDetail()
    {
        // 权限检查：如果用户没有全部数据访问权限，则拒绝访问
        if (!$this->hasFullDataAccess()) {
            Log::info('[门店分红]-[公司股东分红表]：用户无权限访问公司股东分红详情数据，用户ID：' . $this->uid);
            return to_assign(1, '无权限访问');
        }

        $param = get_params();

        try {
            // 验证参数
            if (empty($param['store_id']) || empty($param['period'])) {
                Log::info('[门店分红]-[公司股东分红表]：获取门店详细数据参数错误，用户ID：' . $this->uid);
                return to_assign(1, '参数错误');
            }

            // 获取门店基础信息
            $storeDetail = Db::name('DividendCompanyDetail')
                ->alias('dcd')
                ->join('oa_department d', 'd.id = dcd.store_id', 'LEFT')
                ->field('dcd.*, d.title as store_name')
                ->where('dcd.store_id', $param['store_id'])
                ->where('dcd.period', $param['period'])
                ->where('dcd.is_delete', 0)
                ->find();

            if (!$storeDetail) {
                Log::info('[门店分红]-[公司股东分红表]：未找到门店数据，门店ID：' . $param['store_id'] . '，周期：' . $param['period'] . '，用户ID：' . $this->uid);
                return to_assign(1, '未找到门店数据');
            }

            // 获取分红人信息
            $persons = Db::name('DividendCompanyDetailPerson')
                ->where('company_detail_id', $storeDetail['id'])
                ->where('is_delete', 0)
                ->order('id asc')
                ->select()
                ->toArray();

            // 格式化数据
            $data = [
                'store_id' => $storeDetail['store_id'],
                'store_name' => $storeDetail['store_name'] ?? '未知门店',
                'period' => $storeDetail['period'],
                'dividend_profit' => number_format(floatval($storeDetail['dividend_profit'] ?? 0), 2),
                'company_shareholding_ratio' => floatval($storeDetail['company_shareholding_ratio'] ?? 0),
                'remark' => $storeDetail['remark'] ?? '',
                'persons' => []
            ];

            // 格式化分红人数据
            foreach ($persons as $person) {
                $data['persons'][] = [
                    'id' => $person['id'],
                    'shareholder_id' => intval($person['shareholder_id'] ?? 0),
                    'shareholder_name' => $person['shareholder_name'],
                    'shareholding_ratio' => floatval($person['shareholding_ratio'] ?? 0),
                    'actual_shareholding' => floatval($person['actual_shareholding'] ?? 0),
                    'amount' => floatval($person['amount'] ?? 0),
                    'adjustment_amount' => floatval($person['adjustment_amount'] ?? 0),
                    'payable_amount' => floatval($person['payable_amount'] ?? 0),
                    'remark' => $person['remark'] ?? ''
                ];
            }

            // 记录获取公司股东分红详细数据日志
            add_log('view', $param['store_id'], ['store_name' => $storeDetail['store_name'], 'period' => $param['period']], '[门店分红]-[公司股东分红表]：获取公司股东分红详细数据');
            Log::info('[门店分红]-[公司股东分红表]：获取公司股东分红详细数据成功，门店：' . $storeDetail['store_name'] . '，周期：' . $param['period'] . '，用户ID：' . $this->uid);

        } catch (\Exception $e) {
            Log::info('[门店分红]-[公司股东分红表]：获取公司股东分红详细数据失败，错误信息：' . $e->getMessage() . '，用户ID：' . $this->uid);
            return to_assign(1, '获取数据失败');
        }
        return to_assign(0, '获取成功', $data);
    }

    /**
     * 更新门店详细数据
     */
    public function updateStoreDetail()
    {
        // 权限检查：如果用户没有全部数据访问权限，则拒绝访问
        if (!$this->hasFullDataAccess()) {
            Log::info('[门店分红]-[公司股东分红表]：用户无权限修改公司股东分红表，用户ID：' . $this->uid);
            return to_assign(1, '无权限操作');
        }

        $param = get_params();

        try {
            // 验证参数
            if (empty($param['store_id']) || empty($param['period'])) {
                Log::info('[门店分红]-[公司股东分红表]：更新门店详细数据参数错误，用户ID：' . $this->uid);
                return to_assign(1, '参数错误');
            }

            // 开启事务
            Db::startTrans();

            // 获取门店详细记录
            $storeDetail = Db::name('DividendCompanyDetail')
                ->where('store_id', $param['store_id'])
                ->where('period', $param['period'])
                ->where('is_delete', 0)
                ->find();

            if (!$storeDetail) {
                Db::rollback();
                Log::info('[门店分红]-[公司股东分红表]：更新时未找到门店数据，门店ID：' . $param['store_id'] . '，周期：' . $param['period'] . '，用户ID：' . $this->uid);
                return to_assign(1, '未找到门店数据');
            }

            // 更新门店基础信息
            $updateData = [
                'remark' => trim($param['remark'] ?? ''),
                'update_time' => time()
            ];

            Db::name('DividendCompanyDetail')
                ->where('id', $storeDetail['id'])
                ->update($updateData);

            // 删除原有分红人数据
            Db::name('DividendCompanyDetailPerson')
                ->where('company_detail_id', $storeDetail['id'])
                ->update(['is_delete' => 1, 'delete_time' => time()]);

            // 添加新的分红人数据
            if (!empty($param['persons']) && is_array($param['persons'])) {
                foreach ($param['persons'] as $person) {
                    if (empty($person['shareholder_name'])) {
                        continue; // 跳过空的分红人姓名
                    }

                    $personData = [
                        'company_detail_id' => $storeDetail['id'],
                        'shareholder_id' => intval($person['shareholder_id'] ?? 0),
                        'shareholder_name' => trim($person['shareholder_name']),
                        'shareholding_ratio' => floatval($person['shareholding_ratio'] ?? 0),
                        'actual_shareholding' => floatval($person['actual_shareholding'] ?? 0),
                        'amount' => floatval($person['amount'] ?? 0),
                        'adjustment_amount' => floatval($person['adjustment_amount'] ?? 0),
                        'payable_amount' => floatval($person['payable_amount'] ?? 0),
                        'remark' => trim($person['remark'] ?? ''),
                        'create_time' => time(),
                        'update_time' => time(),
                        'is_delete' => 0
                    ];

                    Db::name('DividendCompanyDetailPerson')->insert($personData);
                }
            }

            // 提交事务
            Db::commit();

            // 触发股东分红清单的重新计算
            $this->recalculatePaymentList($param['period']);

            // 获取门店名称用于日志记录
            $storeName = Db::name('Department')
                ->where('id', $param['store_id'])
                ->value('title') ?: '未知门店';

            // 记录操作日志
            add_log('edit', $param['store_id'], json_encode($param, JSON_UNESCAPED_UNICODE), '[门店分红]-[公司股东分红表]：更新公司股东分红明细数据');
            Log::info('[门店分红]-[公司股东分红表]：更新公司股东分红明细数据成功，门店：' . $storeName . '，周期：' . $param['period'] . '，用户ID：' . $this->uid);

        } catch (\Exception $e) {
            Db::rollback();
            Log::info('[门店分红]-[公司股东分红表]：更新公司股东分红详细数据失败，错误信息：' . $e->getMessage() . '，用户ID：' . $this->uid);
            return to_assign(1, '保存失败：' . $e->getMessage());
        }

        return to_assign(0, '保存成功');
    }

    /**
     * 重新计算股东分红清单
     * @param string $period 统计周期
     * @return void
     */
    private function recalculatePaymentList($period)
    {
        try {
            // 获取该周期所有分红人的列表
            $existingPayments = Db::name('DividendPayment')
                ->where('period', $period)
                ->where('is_delete', 0)
                ->field('shareholder_name, adjustment_amount, paid_amount, remark')
                ->select()
                ->toArray();

            // 如果没有现有数据，则不需要重新计算
            if (empty($existingPayments)) {
                Log::info('[门店分红]-[公司股东分红表]：股东分红清单重新计算跳过，周期：' . $period . '，原因：无现有数据');
                return;
            }

            // 保存原有的调整金额、实付金额和备注信息
            $originalData = [];
            foreach ($existingPayments as $payment) {
                $originalData[$payment['shareholder_name']] = [
                    'adjustment_amount' => floatval($payment['adjustment_amount'] ?? 0),
                    'paid_amount' => floatval($payment['paid_amount'] ?? 0),
                    'remark' => $payment['remark'] ?? ''
                ];
            }

            // 1. 从门店分红人子表获取个人股东数据
            $personalShareholderData = $this->getPersonalShareholderDataForPayment($period);

            // 2. 从公司股东分红人子表获取公司股东数据
            $companyShareholderData = $this->getCompanyShareholderDataForPayment($period);

            // 3. 合并数据并按分红人姓名分组
            $shareholderGroups = $this->groupShareholderDataForPayment($personalShareholderData, $companyShareholderData);

            // 4. 为每个分红人重新计算并保存数据
            foreach ($shareholderGroups as $shareholderName => $shareholderInfo) {
                // 获取原有的调整金额、实付金额和备注
                $originalInfo = $originalData[$shareholderName] ?? [
                    'adjustment_amount' => 0,
                    'paid_amount' => 0,
                    'remark' => ''
                ];

                $adjustmentAmount = $originalInfo['adjustment_amount'];
                $paidAmount = $originalInfo['paid_amount'];
                $actualPayableAmount = $shareholderInfo['total_amount'] - $adjustmentAmount;
                $unpaidAmount = $actualPayableAmount - $paidAmount;

                $paymentData = [
                    'shareholder_name' => $shareholderName,
                    'shareholder_ids' => implode(',', $shareholderInfo['shareholder_ids']),
                    'period' => $period,
                    'payable_amount' => $shareholderInfo['total_amount'],
                    'adjustment_amount' => $adjustmentAmount, // 保留原有调整金额
                    'actual_payable_amount' => $actualPayableAmount,
                    'paid_amount' => $paidAmount, // 保留原有实付金额
                    'unpaid_amount' => $unpaidAmount,
                    'remark' => $originalInfo['remark'], // 保留原有备注
                    'create_time' => time(),
                    'update_time' => time()
                ];

                // 使用软删除再创建的方式更新数据
                $this->savePaymentDataWithSoftDelete($paymentData);
            }

            Log::info('[门店分红]-[公司股东分红表]：股东分红清单重新计算完成，周期：' . $period . '，处理分红人数：' . count($shareholderGroups));

        } catch (\Exception $e) {
            Log::info('[门店分红]-[公司股东分红表]：股东分红清单重新计算失败，周期：' . $period . '，错误信息：' . $e->getMessage());
        }
    }

    /**
     * 从门店分红人子表获取个人股东数据（用于分红清单计算）
     * @param string $period 统计周期
     * @return array
     */
    private function getPersonalShareholderDataForPayment($period)
    {
        return Db::name('DividendStoreDetailPerson')
            ->alias('dsdp')
            ->join('oa_dividend_store_detail dsd', 'dsd.id = dsdp.dividend_store_detail_id', 'INNER')
            ->where([
                'dsd.period' => $period,
                'dsdp.shareholder_type' => 2, // 个人股东
                'dsd.is_delete' => 0,
                'dsdp.is_delete' => 0
            ])
            ->field([
                'dsdp.shareholder_id',
                'dsdp.shareholder_name',
                'dsdp.actual_payable_amount',
                'dsd.store_id'
            ])
            ->select()
            ->toArray();
    }

    /**
     * 从公司股东分红人子表获取公司股东数据（用于分红清单计算）
     * @param string $period 统计周期
     * @return array
     */
    private function getCompanyShareholderDataForPayment($period)
    {
        return Db::name('DividendCompanyDetailPerson')
            ->alias('dcdp')
            ->join('oa_dividend_company_detail dcd', 'dcd.id = dcdp.company_detail_id', 'INNER')
            ->where([
                'dcd.period' => $period,
                'dcd.is_delete' => 0,
                'dcdp.is_delete' => 0
            ])
            ->field([
                'dcdp.shareholder_id',
                'dcdp.shareholder_name',
                'dcdp.payable_amount',
                'dcd.store_id'
            ])
            ->select()
            ->toArray();
    }

    /**
     * 合并数据并按分红人姓名分组（用于分红清单计算）
     * @param array $personalData 个人股东数据
     * @param array $companyData 公司股东数据
     * @return array
     */
    private function groupShareholderDataForPayment($personalData, $companyData)
    {
        $shareholderGroups = [];

        // 处理个人股东数据
        foreach ($personalData as $item) {
            $name = $item['shareholder_name'];
            if (!isset($shareholderGroups[$name])) {
                $shareholderGroups[$name] = [
                    'total_amount' => 0,
                    'shareholder_ids' => [],
                    'store_ids' => []
                ];
            }

            $shareholderGroups[$name]['total_amount'] += floatval($item['actual_payable_amount']);

            // 记录股东ID（避免重复）
            if (!in_array($item['shareholder_id'], $shareholderGroups[$name]['shareholder_ids'])) {
                $shareholderGroups[$name]['shareholder_ids'][] = $item['shareholder_id'];
            }

            // 记录门店ID（避免重复）
            if (!in_array($item['store_id'], $shareholderGroups[$name]['store_ids'])) {
                $shareholderGroups[$name]['store_ids'][] = $item['store_id'];
            }
        }

        // 处理公司股东数据
        foreach ($companyData as $item) {
            $name = $item['shareholder_name'];
            if (!isset($shareholderGroups[$name])) {
                $shareholderGroups[$name] = [
                    'total_amount' => 0,
                    'shareholder_ids' => [],
                    'store_ids' => []
                ];
            }

            $shareholderGroups[$name]['total_amount'] += floatval($item['payable_amount']);

            // 记录股东ID（避免重复）
            if (!in_array($item['shareholder_id'], $shareholderGroups[$name]['shareholder_ids'])) {
                $shareholderGroups[$name]['shareholder_ids'][] = $item['shareholder_id'];
            }

            // 记录门店ID（避免重复）
            if (!in_array($item['store_id'], $shareholderGroups[$name]['store_ids'])) {
                $shareholderGroups[$name]['store_ids'][] = $item['store_id'];
            }
        }

        return $shareholderGroups;
    }

    /**
     * 使用软删除再创建的方式保存分红清单数据
     * @param array $data 数据
     * @return bool
     */
    private function savePaymentDataWithSoftDelete($data)
    {
        // 检查是否存在记录
        $existing = Db::name('DividendPayment')
            ->where('is_delete', 0)
            ->where('shareholder_name', $data['shareholder_name'])
            ->where('period', $data['period'])
            ->find();

        if ($existing) {
            // 修改时先软删除再创建，实现修改留痕
            $deleteTime = time();

            // 1. 软删除现有记录（为避免唯一索引冲突，修改分红人姓名添加时间戳后缀）
            Db::name('DividendPayment')
                ->where('id', $existing['id'])
                ->update([
                    'shareholder_name' => $existing['shareholder_name'] . '_deleted_' . $deleteTime,
                    'is_delete' => 1,
                    'delete_time' => $deleteTime,
                    'update_time' => time()
                ]);

            // 2. 创建新记录
            return Db::name('DividendPayment')->insert($data);
        } else {
            // 创建新记录
            return Db::name('DividendPayment')->insert($data);
        }
    }
}
