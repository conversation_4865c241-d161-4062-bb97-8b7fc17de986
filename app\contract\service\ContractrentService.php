<?php

namespace app\contract\service;

use think\facade\Db;

class ContractrentService
{

    public function insertRent($param)
    {

        //id
        //cr_id
        //did
        //pay_date
        //m_amount
        //wyf_amount
        //o_amount
        //ei_amount
        //ei_start_date
        //ei_end_date
        //sdate
        //status
        //remark

        $rent_data = array();

        //免租期
        $rent_data[] = [
            'cr_id' => $param['id'],
            'did' => $param['did'],
            'm_amount' => 0,
            'wyf_amount' => 0,
            'o_amount' => 0,
            'ei_amount' => 0,
            'ei_start_date' => $param['free_start_date'],
            'ei_end_date' => $param['free_end_date'],
            'remark' => '免租期',
        ];

        $free_start_date = $param['free_start_date']; //免租期 2022-07-29
        $free_end_date = $param['free_end_date']; //免租期 2022-09-06

        $rent_amount = $param['rent_amount']; //首款租金金额
        $wyf_amount = $param['wyf_amount']; //首款租金金额
        $pay_cycle = $param['pay_cycle']; //付费周期
        $start_date = $param['start_date']; //合同起日期
        $end_date = $param['end_date']; //合同止日期

        if ($free_start_date != $free_end_date){
            $between_date = getRentDatesBetween( date("Y-m-d" , strtotime("+1 days",strtotime($free_end_date)) ) , $end_date);
        }else{
            $between_date = getRentDatesBetween( $free_end_date  , $end_date);
        }

        $pay_date = $param['first_pay_date']; //付费日期
        $did = $param['did']; //付费日期


        $flag_cycle = 0;
        $flag_rent_cycle = 1;
        $flag_wyf_cycle = 1;

        $r_index = null;
        $w_index = null;
        if (isset($param['rent_gc']) && !empty($param['rent_gc'])) {
            $r_index = 0;
        }
        if (isset($param['wyf_gc']) && !empty($param['wyf_gc'])) {
            $w_index = 0;
        }
        $base = $param['rent_amount'];
        $ei_amount = 0;

        $pay_amount_key = 0;

        $in_key = 0;
        foreach ($between_date as $k => $v) {
            $flag_rent_cycle++;

            $insert_date = array();

            //付款周期
            if ($flag_cycle == $pay_cycle) {
                $flag_cycle = 0;
            }

            //增长周期
            $bool = $r_index !== null && isset($param['rent_gc'][$r_index]) && !empty($param['rent_gc'][$r_index]['month']) && strtotime($v['end']) > strtotime("+{$param['rent_gc'][$r_index]['month']} month", strtotime($start_date));
            if ($bool) {
                $days = getDayBetween_days(
                    date("d" , strtotime($free_start_date)) ,
                    date("d" , strtotime($v['start'])) ,
                    date("m" , strtotime($v['start'])) ,
                    date("Y" , strtotime($v['start'])) ,
                );

                $m_amount = $base * 12 / 365 * $days['date_1'];
                $base = $param['rent_gc'][$r_index]['amount'];

                if ($days['date_1'] == 0){
                    $m_amount = $base;
                }else{
                    $m_amount = round( $m_amount + $base * 12 / 365 * $days['date_2'] , 2);
                }

                $r_index++;
            }else{
                $m_amount = $base;
            }

            if (strtotime($v['end']) > strtotime($end_date)){
                $v['end'] = $end_date;
                $day_s = getDayBetween_days(
                    date("d" , strtotime($v['start'])) ,
                    date("d" , strtotime($v['end'])) ,
                    date("m" , strtotime($v['start'])) ,
                    date("Y" , strtotime($v['start'])) ,
                );

                $m_amount = $base * 12 / 365 * ( $day_s['date_1'] + 1 );
            }

            if ($flag_cycle == 0) {
                $in_key = $k;
                $insert_date = [
                    'pay_date' => $pay_date,
                    'm_amount' => $m_amount,
                    'wyf_amount' => $wyf_amount,
                    'ei_amount' => $m_amount + $wyf_amount,
                    'in_key' => $in_key,
                ];
                if ($k != 0){
                    $pay_amount_key = $k;
                    $rent_data[$k - $pay_cycle + 1]['ei_amount'] = $ei_amount;
                    $ei_amount = 0;
                }

            } else if ($flag_cycle < $pay_cycle) {
                $insert_date = [
                    'm_amount' => $m_amount,
                    'wyf_amount' => $wyf_amount,
                    'in_key' => $in_key,
                    'status' => 0,
                ];
            }

            $m_amount += $wyf_amount;

            $ei_amount += $m_amount;

            //不是付款的日期 但是已经超过合同结束日期以后
            if ($flag_cycle != 0 && $k + 1 == count($between_date)){
                $rent_data[$pay_amount_key + 1]['ei_amount'] = $ei_amount;
                $ei_amount = 0;
            }

            $pay_date = date("Y-m-d", strtotime("+1 month", strtotime($pay_date)));
            $insert_date['cr_id'] = $param['id'];
            $insert_date['did'] = $did;
            $insert_date['o_amount'] = 0;
            $insert_date['ei_start_date'] = $v['start'];
            $insert_date['ei_end_date'] = $v['end'];
            $insert_date['wyf_amount'] = $wyf_amount;

            $rent_data[] = $insert_date;
            $flag_cycle++;
        }

        foreach ($rent_data as $k => $v){
            Db::name('rent')->strict(false)->field(true)->insertGetId($v);
        }

    }


    public function cross_month_amount($start_date, $end_date)
    {

    }


}