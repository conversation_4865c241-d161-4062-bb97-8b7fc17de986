{extend name="../../base/view/common/base" /}
<!-- 主体 -->
{block name="body"}
<form class="layui-form p-4">
	<h3 class="pb-1">权限配置</h3>
	<table class="layui-table layui-table-form">
		<tr>
			<td colspan="8" class="red" style="line-height:1.8">
				<p><strong>合同模块使用说明：</strong></p>
				<p>{$detail.desc}</p>
			</td>
		</tr>
		<tr>
			<td class="layui-td-gray">权限名称</td>
			<td>
				<input type="hidden" name="id" value="{$detail.id}" />
				{$detail.title}
			</td>
			<td class="layui-td-gray">权限标识</td>
			<td>{$detail.name}</td>
			<td class="layui-td-gray-2">开启关联客户</td>
			<td>
				<input type="radio" name="expected_1" value="1" title="关联" {eq name="$detail.expected_1" value="1"}checked{/eq}>
				<input type="radio" name="expected_1" value="0" title="不关联" {eq name="$detail.expected_1" value="0"}checked{/eq}>
			</td>
			<td class="layui-td-gray-3">合同编号自动生成</td>
			<td>
				<input type="radio" name="expected_2" value="1" title="开启" {eq name="$detail.expected_2" value="1"}checked{/eq}>
				<input type="radio" name="expected_2" value="0" title="不开启" {eq name="$detail.expected_2" value="0"}checked{/eq}>
			</td>
		</tr>
		<tr>
			<td class="layui-td-gray-2">合同管理员</td>
			<td colspan="7">
				<input type="text" id="unames" name="unames" value="{$detail.unames}" readonly placeholder="请选择权限人员" autocomplete="off" class="layui-input picker-more">
				<input type="hidden" id="uids" name="uids" value="{$detail.uids}">
			</td>
		</tr>
	</table>
	<div class="pt-1">
		<button class="layui-btn layui-btn-normal" lay-submit="" lay-filter="webform">立即提交</button>
		<button type="reset" class="layui-btn layui-btn-primary">重置</button>
	</div>
</form>
{/block}
<!-- /主体 -->

<!-- 脚本 -->
{block name="script"}
<script>
	const moduleInit = ['tool','employeepicker'];
	function gouguInit() {
		var form = layui.form, tool = layui.tool;		
		//监听提交
		form.on('submit(webform)', function (data) {
			let callback = function (e) {
				layer.msg(e.msg);
				if(e.code==0){
					tool.sideClose(1000);
				}
			}
			tool.post("/home/<USER>/edit", data.field, callback);
			return false;
		});
	}
</script>
{/block}
<!-- /脚本 -->