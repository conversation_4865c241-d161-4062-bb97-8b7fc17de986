{extend name="../../base/view/common/base" /}

{block name="style"}
<style>
    /* 多选下拉样式 */
    .layui-form-select dl {
        min-width: 100%; /* 保证下拉框至少与select一样宽 */
    }
    .layui-form-select dl dd.layui-this {
        background-color: #5FB878; /* 高亮选中项 */
        color: #fff;
    }
    /* 多选下拉过高时调整高度 */
    select[multiple] + .layui-form-select .layui-anim {
        max-height: 200px; /* 或任意期望高度 */
        overflow-y: auto;
    }

    .label-content {
        max-height: 34px !important;
        width: 125px !important; /* 设置固定宽度为125px */
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }

    .xm-select {
        height: 34px !important;
        line-height: 34px !important;
    }

    /* 按钮组样式 */
    .filter-button-group .layui-btn {
        display: inline-block !important;
        vertical-align: top;
        margin-left: 5px;
    }
    .filter-button-group .layui-btn:first-child {
        margin-left: 0;
    }

    /* 合计行样式 - 无缝效果 */
    .layui-table-total {
        background-color: #f8f9fa !important;
        font-weight: bold !important;
    }

    .layui-table-total td {
        background-color: #f8f9fa !important;
        border: none !important;
        padding: 8px !important;
    }
</style>
{/block}

{block name="body"}
<!-- 权限配置 -->
<input type="hidden" id="hasButtonAccess" value="{$has_button_access|default=0}">

<div class="p-3" style="height:100%; box-sizing: border-box;">
    <div class="body-table" style="overflow:hidden;">
        <!-- 搜索表单 -->
        <form class="layui-form gg-form-bar border-t border-x" lay-filter="webform" style="padding-bottom:10px;">
            <div id="store-select-container" class="layui-input-inline" style="width:200px; height: 38px;"></div>
            <div class="layui-input-inline" style="width:200px;">
                <input type="text" name="shareholder_name" id="shareholder_name"
                       placeholder="请输入股东姓名"
                       autocomplete="off" class="layui-input">
            </div>
            <!-- 按钮组 -->
            <div class="layui-input-inline filter-button-group" style="width: auto; white-space: nowrap;">
                <button class="layui-btn layui-btn-normal" lay-submit="" lay-filter="webform"><i
                        class="layui-icon layui-icon-search mr-1"></i>搜索
                </button>
                <button type="button" class="layui-btn layui-btn-primary" id="resetBtn"><i
                        class="layui-icon layui-icon-refresh mr-1"></i>重置
                </button>
            </div>
        </form>
        <!-- 增加外边距，方便页面布局更美观 -->
        <div style="margin: 10px 0;">
            <span style="font-weight:600">门店分红配置表</span>
        </div>
        <!-- 数据表格 -->
        <table class="layui-hide" id="dataTable" lay-filter="dataTable"></table>
    </div>
</div>

<!-- 工具栏模板 -->
<script type="text/html" id="toolbarDemo">
    {if condition="$has_button_access"}
    <button class="layui-btn layui-btn-sm" lay-event="add">
        <i class="layui-icon layui-icon-add-1"></i>添加
    </button>
    {/if}
</script>


{/block}

<!-- 脚本 -->
{block name="script"}
<script>
    const moduleInit = ['tool', 'tablePlus'];
    function gouguInit() {
        var table = layui.tablePlus, form = layui.form, tool = layui.tool;

        // 全局错误处理
        window.addEventListener('error', function(e) {
            if (e.message && e.message.includes('LAY_NUM')) {
                console.error('LayUI LAY_NUM 错误已被捕获:', e);
                // 不显示给用户，只记录日志
                return true; // 阻止默认错误处理
            }
        });

        // 确保DOM元素存在后再渲染表格
        if (!$('#dataTable').length) {
            console.error('表格容器元素不存在');
            return;
        }

        // 初始化门店选择组件
        var storeMultiSelect = xmSelect.render({
            el: '#store-select-container',
            name: 'store_ids',
            language: 'zn',
            filterable: true,
            data: [],
            model: { label: { type: 'xm-select-count', max: 0 } },
            prop: {
                name: 'title',
                value: 'id'
            },
            placeholder: '请选择门店',
            on: function(data){
                // 门店选择变化时的回调
                if(data.change){
                    // 可以在这里添加联动逻辑
                }
            }
        });



        // 加载门店列表
        function loadStoreList() {
            $.ajax({
                url: '/api/dividend/getStoreList',
                type: 'post',
                dataType: 'json',
                success: function(res) {
                    if (res.code == 0) {
                        var storesForXmSelect = [];
                        if (res.data && res.data.length > 0) {
                            storesForXmSelect = res.data.map(function(item) {
                                return { id: item.id, title: item.title };
                            });
                        }
                        storeMultiSelect.update({
                            data: storesForXmSelect,
                            autoRow: true
                        });
                    } else {
                        layer.msg(res.msg || '获取门店列表失败', {icon: 2});
                    }
                },
                error: function(){
                    layer.msg('获取门店列表失败', {icon:2});
                }
            });
        }

        // 权限配置
        var hasButtonAccess = $('#hasButtonAccess').val() == '1';

        // 将权限信息设置为全局变量，供模板使用
        window.hasButtonAccess = hasButtonAccess;

        // 渲染表
        try {
            // 根据按钮权限动态构建表格列配置
            var tableCols = [
                {field: 'store_name', title: '门店名称', align: 'center', totalRowText: '已计提风险金合计'},
                {
                    field: 'company_shareholding_total',
                    title: '公司股东持股(%)',
                    align: 'center',
                    templet: function(d) {
                        var value = d.company_shareholding_total || '0.000';
                        return '<span style="color: #1E9FFF;">' + value + '</span>';
                    }
                },
                {
                    field: 'personal_shareholding_total',
                    title: '个人股东持股(%)',
                    align: 'center',
                    templet: function(d) {
                        var value = d.personal_shareholding_total || '0.000';
                        return '<span style="color: #009688;">' + value + '</span>';
                    }
                },
                {
                    field: 'risk_reserve',
                    title: '已计提风险金(元)',
                    align: 'center',
                    totalRow: true,
                    templet: function(d) {
                        var value = parseFloat(d.risk_reserve || 0);
                        var formatted = value.toLocaleString('zh-CN', {
                            minimumFractionDigits: 2,
                            maximumFractionDigits: 2
                        });
                        return '<span style="color: #FF5722;">' + formatted + '</span>';
                    }
                }
            ];

            // 所有用户都显示操作列，但根据权限显示不同按钮
            tableCols.push({
                title: '操作',
                width: 160,
                fixed: 'right',
                align: 'center',
                templet: function(d) {
                    if (hasButtonAccess) {
                        return '<a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>' +
                               '<a class="layui-btn layui-btn-xs layui-btn-normal" lay-event="riskReserve">风险金</a>';
                    } else {
                        return '<a class="layui-btn layui-btn-xs layui-btn-primary" lay-event="view">查看</a>';
                    }
                }
            });

            layui.pageTable = table.render({
                elem: '#dataTable',
                toolbar: '#toolbarDemo',
                title: '门店分红信息列表',
                page: false,
                limit: 10000,
                height: 'full-130',
                url: "/dividend/index/index",
                loading: true,
                even: true,
                totalRow: true,
                parseData: function(res) {
                    return {
                        "code": res.code,
                        "msg": res.msg,
                        "count": res.count,
                        "data": res.data,
                        "totalRow": res.totalRow
                    };
                },
                cols: [tableCols],
                done: function(res, curr, count) {
                    // 表格渲染完成后的回调
                    if (res.code !== 0) {
                        console.error('表格数据加载失败:', res.msg);
                    }
                }
            });
        } catch (e) {
            console.error('表格渲染失败:', e);
            layer.msg('表格初始化失败，请刷新页面重试', {icon: 2});
        }

        // 搜索表单提交
        form.on('submit(webform)', function (data) {
            try {
                if (layui.pageTable && layui.pageTable.reload) {
                    var selectedStoreIds = storeMultiSelect.getValue('value'); // 获取选中的门店ID数组
                    layui.pageTable.reload({
                        where: {
                            store_ids: selectedStoreIds,
                            shareholder_name: $('#shareholder_name').val()
                        },
                        page: { curr: 1 }
                    });
                } else {
                    console.error('表格对象不存在，无法重新加载');
                    layer.msg('表格重新加载失败，请刷新页面', {icon: 2});
                }
            } catch (e) {
                console.error('表格重新加载失败:', e);
                layer.msg('搜索失败，请重试', {icon: 2});
            }
            return false;
        });

        // 重置按钮事件
        $('#resetBtn').on('click', function(){
            // 清空门店选择
            storeMultiSelect.setValue([]);
            // 清空股东姓名
            $('#shareholder_name').val('');
            // 不自动刷新表格，等用户点击搜索按钮后再刷新
        });

        // 工具栏事件
        table.on('toolbar(dataTable)', function(obj) {
            try {
                switch(obj.event) {
                    case 'add':
                        tool.side('/dividend/index/add', '新增门店分红信息');
                        break;
                }
            } catch (e) {
                console.error('工具栏事件处理失败:', e);
                layer.msg('操作失败，请重试', {icon: 2});
            }
        });

        // 行操作事件
        table.on('tool(dataTable)', function(obj) {
            try {
                var data = obj.data;
                if (!data || !data.id) {
                    layer.msg('数据异常，无法执行操作', {icon: 2});
                    return;
                }

                switch(obj.event) {
                    case 'edit':
                        tool.side('/dividend/index/add?id=' + data.id, '编辑门店分红信息');
                        break;
                    case 'view':
                        tool.side('/dividend/index/view?id=' + data.id, '查看门店分红信息');
                        break;
                    case 'riskReserve':
                        tool.side('/dividend/index/storeRiskReserveLog?store_id=' + data.store_id + '&store_name=' + encodeURIComponent(data.store_name), data.store_name + ' - 风险金变化记录');
                        break;
                    case 'del':
                        layer.confirm('确定删除该门店分红信息吗？<br><span style="color:red;">删除后将同时删除所有关联的股东信息！</span>', {
                            icon: 3,
                            title: '删除确认',
                            btn: ['确定删除', '取消']
                        }, function(index) {
                            let callback = function (e) {
                                layer.msg(e.msg, {icon: e.code == 0 ? 1 : 2});
                                if(e.code == 0) {
                                    obj.del();
                                }
                            }
                            tool.delete("/dividend/index/delete", {id: data.id}, callback);
                            layer.close(index);
                        });
                        break;
                }
            } catch (e) {
                console.error('行操作事件处理失败:', e);
                layer.msg('操作失败，请重试', {icon: 2});
            }
        });

        // 调整合计行列宽度函数
        function adjustSummaryRowWidth() {
            var summaryRow = $('.table-summary-fixed');
            if (summaryRow.length === 0) return;

            var tableContainer = $('#dataTable').next('.layui-table-view');
            var headerCells = tableContainer.find('.layui-table-header th');

            summaryRow.find('td').each(function(index) {
                var headerCell = headerCells.eq(index);
                if (headerCell.length > 0) {
                    $(this).css('width', headerCell.outerWidth() + 'px');
                }
            });
        }

        // 监听窗口大小变化，调整合计行
        $(window).on('resize', function() {
            setTimeout(adjustSummaryRowWidth, 100);
        });

        // 页面初始化时加载门店列表
        loadStoreList();
    }
</script>
{/block}
