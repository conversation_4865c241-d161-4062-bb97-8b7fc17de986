{extend name="../../base/view/common/base" /}
{block name="style"}
<link rel="stylesheet" href="{__GOUGU__}/gougu/css/layout.css" media="all">
{/block}
<!-- 主体 -->
{block name="body"}
<style>

    body {
        background-color: #f5f6f7;
    }

    .uni-page {
        display: block;
        width: 100%;
        height: 100%;
    }

    .uni-page-head-main {
        display: block;
        box-sizing: border-box;
    }

    .uni-page-head .uni-page-head-hd {
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-align: center;
        -webkit-align-items: center;
        align-items: center;
        font-size: 16px;
    }

    .uni-page-head-main .uni-page-head {
        position: fixed;
        left: var(--window-left);
        right: var(--window-right);
        height: 44px;
        height: calc(44px + constant(safe-area-inset-top));
        height: calc(44px + env(safe-area-inset-top));
        padding: 7px 3px;
        padding-top: calc(7px + constant(safe-area-inset-top));
        padding-top: calc(7px + env(safe-area-inset-top));
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        overflow: hidden;
        -webkit-box-pack: justify;
        -webkit-justify-content: space-between;
        justify-content: space-between;
        box-sizing: border-box;
        z-index: 998;
        color: #fff;
        background-color: #000;
        -webkit-transition-property: all;
        transition-property: all;
        width: 100%;
    }

    .uni-page-head .uni-page-head-bd {
        position: absolute;
        left: 70px;
        right: 70px;
        min-width: 0;
    }

    .uni-page-head .uni-page-head__title {
        font-weight: 700;
        font-size: 16px;
        line-height: 30px;
        text-align: center;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
    }

    .mine-container .header-section[data-v-1716ef58] {
        padding: 15px 15px 45px 15px;
        background-color: #3c96f3;
        color: #fff;
    }

    .uni-page-head-main .uni-page-head ~ .uni-placeholder {
        width: 100%;
        height: 44px;
        height: calc(44px + constant(safe-area-inset-top));
        height: calc(44px + env(safe-area-inset-top));
    }

    .mine-container .header-section .cu-avatar[data-v-1716ef58] {
        border: 2px solid #eaeaea;
    }

    .layui-card:last-child {
        margin-bottom: 0;
    }
    .square .layui-col-xs4 {
        border: 1px solid #f5f5f5;
        border-left-width: 1px;
        border-left-style: solid;
        border-left-color: rgb(245, 245, 245);
        cursor: pointer;
    }

    .square .layui-col-xs4 div {
        padding: 15px 0;
        text-align: center;
        background-color: #fff;
        color: #666;
    }

    .square .layui-col-xs4 i {
        font-size: 24px;
        font-weight: 800;
        display: block;
        padding-bottom: 5px;
        color: #4285f4;
    }

    /*消息主体*/
    .voice {
        padding: 10px;
        background-color: #fafafa !important;
        color: #333;
    }

    .voice_title {
        display: flex;
        border-bottom-style: dashed;
        border-width: 1px;
        border-color: #f5f6f7;
        padding: 0 0 10px 0;
    }

    .read_time_on {
        background-color: #5FB878;
    }

    .read_time_off {
        background-color: #FF5722;
    }

    .name {
        font-size: 16px;
        font-weight: bolder;
    }

    .create_time {
        display: flex;
        flex-direction: row;
        align-items: flex-end;
        margin-left: 20px;
    }

    .b_color {
        color: #333;
        font-weight: bolder;
    }

    .remark {
        padding: 10px 5px;
        background-color: #fff;
        color: #333;
    }

    .read_time {
        position: absolute;
        z-index: 100;
        top: 0;
        right: 0;
        padding: 8px 14px;
        color: #fff;
    }

    .status_1 {
        font-weight: bolder;
        background-color: #666;
    }

    .status_2 {
        font-weight: bolder;
        background-color: #4285f4;
    }

    .status_3 {
        font-weight: bolder;
        background-color: #34a853;
    }

    .layui-tab-title .layui-this:after {
        height: 51px;
    }

</style>
<div class="layui-layout-body">
    <div id="GouguApp">
        <div class="layui-layout gg-layout layout-menu-{$web.menu_mode|default='classical'}">

            <div class="uni-page" data-page="pages/mine/index">
                <div class="layui-row" id="toolbarDemo">
                    <div class="layui-col-md12" style="margin-bottom: 50px">
                        <div class="layui-card">
                            <div class="square">
                                <div class="layui-row">
                                    <div class="layui-col-xs4 tool-btn" data-type="22" onclick="onclickTool('user/adminview/search')">
                                        <div><i class="layui-icon layui-icon-user"></i>人才中心</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
            </div>

            {include file="../../mobile/view/index/tabbar" /}


        </div>
    </div>
</div>


<!-- /主体 -->
{/block}
<!-- 脚本 -->
{block name="script"}
<script src="{__GOUGU__}/layui/layui.js"></script>
<script src="{__GOUGU__}/gougu/gouguInit.js"></script>
<script>
    const moduleInit = ['tool', 'employeepicker', 'laydatePlus'];

    function gouguInit() {
        var form = layui.form, table = layui.table, tool = layui.tool, element = layui.element,
            laydatePlus = layui.laydatePlus;


    }



    function onclickTool(url) {
        console.log(url)
        window.location.pathname = url;
    }

</script>
{/block}
<!-- /脚本 -->