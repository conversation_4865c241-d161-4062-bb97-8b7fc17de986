{extend name="../../base/view/common/base" /}
<!-- 主体 -->
{block name="body"}
{if condition="$id eq 0"}
<form class="layui-form p-4">
	<h2 class="pb-3">
        <div class="layui-input-group">
            <div class="layui-input-prefix">新增合同</div>
            <input type="text" name="code" value="{$codeno}" autocomplete="off" {notempty name="$codeno"}readonly{/notempty} lay-verify="required" lay-reqText="请输入合同编号" placeholder="请输入合同编号" class="layui-input">
        </div>
    </h2>

    <!--    甲方   乙方   门店-->
    <div class="party">
        <div class="party party_a">
            <h3 class="pb-3 title">甲方</h3>
            <table class="layui-table layui-table-form">
                <tr>
                    <td class="layui-td-gray">公司名称<font>*</font></td>
                    <td>
                        <input type="text" name="party_a_name" {if condition="(isset($contract_rent))"}value="{$contract_rent.party_a_name}"{/if} autocomplete="off" lay-verify="required" lay-reqText="公司名称" placeholder="公司名称" class="layui-input">
                    </td>
                </tr>
                <tr>
                    <td class="layui-td-gray">联系人<font>*</font></td>
                    <td >
                        <input type="text" name="party_a_person" {if condition="(isset($contract_rent))"}value="{$contract_rent.party_a_person}"{/if} autocomplete="off" placeholder="联系人" lay-verify="required" lay-reqText="联系人" class="layui-input">
                    </td>
                </tr>
                <tr>
                    <td class="layui-td-gray">联系电话<font>*</font></td>
                    <td >
                        <input type="text" name="party_a_mobile" {if condition="(isset($contract_rent))"}value="{$contract_rent.party_a_mobile}"{/if} autocomplete="off" placeholder="联系电话" lay-verify="required" lay-reqText="联系电话" class="layui-input">
                    </td>
                </tr>
                <tr>
                    <td class="layui-td-gray">地址</td>
                    <td >
                        <input type="text" name="party_a_add" {if condition="(isset($contract_rent))"}value="{$contract_rent.party_a_add}"{/if} autocomplete="off" placeholder="地址" class="layui-input">
                    </td>
                </tr>
            </table>
        </div>
        <div class="party party_b">
            <h3 class="pb-3 title">乙方</h3>
            <table class="layui-table layui-table-form">
                <tr>
                    <td class="layui-td-gray">公司名称<font>*</font></td>
                    <td>
                        <input type="text" name="party_b_name" {if condition="(isset($contract_rent))"}value="{$contract_rent.party_b_name}"{/if}  autocomplete="off"  lay-verify="required" lay-reqText="请输入客户名称" placeholder="请输入客户名称" class="layui-input" >
                    </td>
                </tr>
                <tr>
                    <td class="layui-td-gray">联系人<font>*</font></td>
                    <td >
                        <input type="text" name="party_b_person" {if condition="(isset($contract_rent))"}value="{$contract_rent.party_b_person}"{/if} autocomplete="off" lay-verify="required" lay-reqText="请输入客户名称" placeholder="请输入客户联系地址" class="layui-input">
                    </td>
                </tr>
                <tr>
                    <td class="layui-td-gray">联系电话<font>*</font></td>
                    <td >
                        <input type="text" name="party_b_mobile" {if condition="(isset($contract_rent))"}value="{$contract_rent.party_b_mobile}"{/if} autocomplete="off" lay-verify="required" lay-reqText="请输入客户名称" placeholder="请输入客户联系地址" class="layui-input">
                    </td>
                </tr>
                <tr>
                    <td class="layui-td-gray">地址<font>*</font></td>
                    <td >
                        <input type="text" name="party_b_add" {if condition="(isset($contract_rent))"}value="{$contract_rent.party_b_add}"{/if} autocomplete="off" lay-verify="required" lay-reqText="请输入客户名称" placeholder="请输入客户联系地址" class="layui-input">
                    </td>
                </tr>
            </table>
        </div>
        <div class="party party_a" style="margin-left: 20px">
            <h3 class="pb-3 title">门店</h3>
            <table class="layui-table layui-table-form">
                <tr>
                    <td class="layui-td-gray">门店名称<font>*</font></td>
                    <td>
                        <select id="select-store" lay-search="" name="did" lay-verify="required" lay-reqText="请选择" lay-filter="select-store">
                            <option value="" >--请选择--</option>
                            <option value="1" {if condition="(isset($contract_rent) && ($contract_rent.did == 1) )"}selected{/if} data-dzname="孙艳东" data-dzid="2" data-dzmobile="13916184277" >总部</option>
                            {volist name="$stores" id="vo"}
                            <option value="{$vo.id}" {if condition="(isset($contract_rent) && ($contract_rent.did == $vo.id) )"}selected{/if} data-dzname="{$vo.dzname}" data-dzid="{$vo.dzid}" data-dzmobile="{$vo.dzmobile}" >{$vo.title}</option>
                            {/volist}
                        </select>
                    </td>
                </tr>
                <tr>
                    <td class="layui-td-gray">店长姓名<font>*</font></td>
                    <td >
                        <div class="xm-select-demo" name="aid" id="demo5"></div>
                        <input type="hidden" name="dz_aid" {if condition="(isset($contract_rent))"}value="{$contract_rent.dz_aid}"{/if} autocomplete="off" lay-verify="required" lay-reqText="请输入客户名称" placeholder="请输入客户联系地址" class="layui-input">
                        <input type="hidden" name="dz_name" {if condition="(isset($contract_rent))"}value="{$contract_rent.dz_name}"{/if} autocomplete="off" lay-verify="required" lay-reqText="请输入客户名称" placeholder="请输入客户联系地址" class="layui-input">
                    </td>
                </tr>
                <tr>
                    <td class="layui-td-gray">店长电话<font>*</font></td>
                    <td >
                        <input type="text" name="dz_mobile" {if condition="(isset($contract_rent))"}value="{$contract_rent.dz_mobile}"{/if} autocomplete="off" lay-verify="required" lay-reqText="请输入客户名称" placeholder="请输入客户联系地址" class="layui-input">
                    </td>
                </tr>
            </table>
        </div>
    </div>

<!--    金额   租金收款信息   物业费收款信息-->
    <div class="party" style="margin-top: 10px">
        <div class="party party_a" >
            <h3 class="pb-3 title">金额</h3>
            <table class="layui-table layui-table-form">
                <tr>
                    <td class="layui-td-gray">首款房屋租金/月<font>*</font></td>
                    <td>
                        <input type="number" min="0" step="0.01" lay-affix="number" name="rent_amount" {if condition="(isset($contract_rent))"}value="{$contract_rent.rent_amount}"{/if} autocomplete="off"  lay-verify="required" lay-reqText="房屋租金/月" placeholder="房屋租金/月" class="layui-input" >
                    </td>
                </tr>
                <tr>
                    <td class="layui-td-gray">首款物业费/月<font>*</font></td>
                    <td >
                        <input type="number" min="0" step="0.01" lay-affix="number"  name="wyf_amount"
                               {if condition="(isset($contract_rent))"}value="{$contract_rent.wyf_amount}"{/if}
                               autocomplete="off" lay-verify="required" lay-reqText="物业费/月" placeholder="物业费/月" class="layui-input">
                    </td>
                </tr>
                <tr>
                    <td class="layui-td-gray">押金<font>*</font></td>
                    <td >
                        <input type="number" min="0" step="0.01" lay-affix="number"  name="deposit_amount"
                               {if condition="(isset($contract_rent))"}value="{$contract_rent.deposit_amount}"{/if}
                               autocomplete="off" lay-verify="required" lay-reqText="押金" placeholder="押金" class="layui-input">
                    </td>
                </tr>
                <tr>
                    <td class="layui-td-gray">首款付款日期<font>*</font></td>
                    <td >
                        <input type="text" id="first_pay_date" name="first_pay_date"
                               {if condition="(isset($contract_rent))"}value="{$contract_rent.first_pay_date}"{/if}
                               autocomplete="off"  lay-verify="required" lay-reqText="首款付款日期" placeholder="首款付款日期" class="layui-input" >
                    </td>
                </tr>
            </table>
        </div>
        <div class="party party_b">
            <h3 class="pb-3 title">收款信息</h3>
            <table class="layui-table layui-table-form">
                <tr>
                    <td class="layui-td-gray">收款方<font>*</font></td>
                    <td>
                        <input type="text" name="account_name"
                               {if condition="(isset($contract_rent))"}value="{$contract_rent.account_name}"{/if}
                               autocomplete="off"  lay-verify="required" lay-reqText="收款方" placeholder="收款方" class="layui-input" >
                    </td>
                </tr>
                <tr>
                    <td class="layui-td-gray">收款账号<font>*</font></td>
                    <td >
                        <input type="text" name="account_number"
                               {if condition="(isset($contract_rent))"}value="{$contract_rent.account_number}"{/if}
                               autocomplete="off" lay-verify="required" lay-reqText="收款账号" placeholder="收款账号" class="layui-input">
                    </td>
                </tr>
                <tr>
                    <td class="layui-td-gray">开户行<font>*</font></td>
                    <td >
                        <input type="text" name="account_bank"
                               {if condition="(isset($contract_rent))"}value="{$contract_rent.account_bank}"{/if}
                               autocomplete="off" lay-verify="required" lay-reqText="开户行" placeholder="开户行" class="layui-input">
                    </td>
                </tr>
            </table>
        </div>

    </div>

<!--    增长周期-->
    <div class="party party_a" style="margin-top: 10px">
        <h3 class="pb-3 title">增长周期 <span style="color: red">无增长填写0</span></h3>
        <table class="layui-table layui-table-form">
            <tr>
                <td class="layui-td-gray">
                    租金增长<font>*</font>
                </td>
                <td class="rent_growth_cycle growth_cycle" >
                    <div id="rent_growth_cycle" class="layui-form-item layui-form-pane">
                        <div class="layui-inline">
                            <label class="layui-form-label" style="width: 200px">从合同开始日期</label>
                            <div class="layui-input-inline">
                                <input type="number" lay-affix="number"  name="rent_gc[0][month]" autocomplete="off"  lay-verify="required" lay-reqText="租金增长月周期" placeholder="租金增长月周期" class="layui-input" >
                            </div>
                            <label class="layui-form-label" style="width: 200px">后租金为：</label>
                            <div class="layui-input-inline" style="width:20%;">
                                <input type="number" lay-affix="number" step="0.01" min="0" name="rent_gc[0][amount]" autocomplete="off"   placeholder="租金月金额" class="layui-input" >
                            </div>
                            <label class="layui-form-label" style="width: 200px">/月</label>
                        </div>
                    </div>
                    <span id="addFlow_rent" class="layui-btn layui-btn-xs layui-btn-normal">+ 添加租金增长</span>

                </td>
            </tr>
<!--            <tr>-->
<!--                <td class="layui-td-gray">-->
<!--                    物业费增长-->
<!--                </td>-->
<!--                <td class="wyf_growth_cycle growth_cycle" >-->
<!--                    <div id="wyf_growth_cycle" class="layui-form-item layui-form-pane">-->

<!--                    </div>-->
<!--                    <span id="addFlow_wyf" class="layui-btn layui-btn-xs layui-btn-normal">+ 添加物业费增长</span>-->

<!--                </td>-->
<!--            </tr>-->
        </table>
    </div>

<!--    房东-->
    <div class="party" style="margin-top: 10px">
        <div class="party party_a">
            <h3 class="pb-3 title">房东</h3>
            <table class="layui-table layui-table-form">
                <tr>
                    <td class="layui-td-gray">
                        <div class="layui-input-inline">房产证</div>
<!--                        <div class="layui-input-inline">-->
<!--                            <button type="button" class="layui-btn layui-btn-xs" id="upFczFile"><i class="layui-icon"></i></button>-->
<!--                        </div>-->
                    </td>
                    <td colspan="5" style="line-height:inherit">
                        <div class="layui-row" id="fczfileList">
                            <input type="hidden" data-type="file" name="fcz_files" value="">
                        </div>
                        <button type="button" class="layui-btn" id="upFczFile">
                            <i class="layui-icon layui-icon-upload"></i> 附件（房东身份证）
                        </button>
                    </td>
                </tr>
                <tr>
                    <td class="layui-td-gray">
                        <div class="layui-input-inline">房东身份证</div>
<!--                        <div class="layui-input-inline">-->
<!--                            <button type="button" class="layui-btn layui-btn-xs" id="upFdFile"><i class="layui-icon"></i></button>-->
<!--                        </div>-->
                    </td>
                    <td colspan="5" style="line-height:inherit">
                        <div class="layui-row" id="fdfileList">
                            <input type="hidden" data-type="file" name="fd_files" value="">
                        </div>
                        <button type="button" class="layui-btn" id="upFdFile">
                            <i class="layui-icon layui-icon-upload"></i> 附件（房东身份证）
                        </button>
                    </td>
                </tr>
                <tr>
                    <td class="layui-td-gray">
                        <div class="layui-input-inline">营业执照</div>
<!--                        <div class="layui-input-inline">-->
<!--                            <button type="button" class="layui-btn layui-btn-xs" id="upYyzzFile"><i class="layui-icon"></i></button>-->
<!--                        </div>-->
                    </td>
                    <td colspan="5" style="line-height:inherit">
                        <div class="layui-row" id="yyzzfileList">
                            <input type="hidden" data-type="file" name="yyzz_files" value="">
                        </div>
                        <button type="button" class="layui-btn" id="upYyzzFile">
                            <i class="layui-icon layui-icon-upload"></i> 附件（营业执照）
                        </button>
                    </td>
                </tr>
            </table>
        </div>
    </div>

<!--    //免租期-->
    <div class="party party_a" style="margin-top: 10px">
        <h3 class="pb-3 title">免租期 <span style="color: red">无免租期填写合同开始日期</span></h3>
        <table class="layui-table layui-table-form">
            <tr>
                <td class="layui-td-gray">起始日期<font>*</font></td>
                <td>
                    <input id="free_start_date" name="free_start_date"
                           {if condition="(isset($contract_rent))"}value="{$contract_rent.free_start_date}"{/if}
                           autocomplete="off" class="layui-input" value=""  lay-verify="required" placeholder="请选择日期" lay-reqText="请选择日期">
                </td>
            </tr>
            <tr>
                <td class="layui-td-gray">终止日期<font>*</font></td>
                <td >
                    <input id="free_end_date" name="free_end_date"
                           {if condition="(isset($contract_rent))"}value="{$contract_rent.free_end_date}"{/if}
                           autocomplete="off" class="layui-input" value=""  lay-verify="required" placeholder="请选择日期" lay-reqText="请选择日期">
                </td>
            </tr>
        </table>
    </div>

<!--    基础信息-->
    <div class="party" style="margin-top: 10px">
        <div class="party party_a">
            <h3 class="pb-3 title">基础信息</h3>
            <table class="layui-table layui-table-form">
                <tr>
                    <td class="layui-td-gray">起始日期（合同）<font>*</font></td>
                    <td>
                        <input type="text" id="start_date" name="start_date"
                               {if condition="(isset($contract_rent))"}value="{$contract_rent.start_date}"{/if}
                               autocomplete="off"  lay-verify="required" lay-reqText="起始日期（合同）" placeholder="起始日期（合同）" class="layui-input" >
                    </td>
                </tr>
                <tr>
                    <td class="layui-td-gray">终止日期（合同）<font>*</font></td>
                    <td >
                        <input type="text" id="end_date" name="end_date"
                               {if condition="(isset($contract_rent))"}value="{$contract_rent.end_date}"{/if}
                               autocomplete="off" lay-verify="required" lay-reqText="终止日期（合同）" placeholder="终止日期（合同）" class="layui-input">
                    </td>
                </tr>
                <tr>
                    <td class="layui-td-gray">付费周期/月<font>*</font></td>
                    <td >
                        <input type="number" min="0" lay-affix="number" name="pay_cycle"
                               {if condition="(isset($contract_rent))"}value="{$contract_rent.pay_cycle}"{/if}
                               autocomplete="off"  lay-verify="required" lay-reqText="付费周期" placeholder="X月/次" class="layui-input" >
                    </td>
                </tr>
                <tr>
                    <td class="layui-td-gray">付费日期<font>*</font></td>
                    <td >
                        <input type="text" id="pay_date" name="pay_date"
                               {if condition="(isset($contract_rent))"}value="{$contract_rent.pay_date}"{/if}
                               autocomplete="off" lay-verify="required" lay-reqText="付费日期" placeholder="付费日期" class="layui-input">
                    </td>
                </tr>

                <tr>



                    <td class="layui-td-gray">
                        <div class="layui-input-inline">附件（合同电子版）<font>*</font></div>
<!--                        <div class="layui-input-inline">-->
<!--                            <button type="button" class="layui-btn layui-btn-xs" id="upFiles"><i class="layui-icon"></i></button>-->
<!--                        </div>-->
                    </td>
                    <td colspan="5" style="line-height:inherit">

                        {if condition="(isset($contract_rent))"}
                        <div class="layui-row" id="filesList">
                            <input type="hidden" data-type="file" name="files" value="{$contract_rent.files}">
                            {notempty name="$contract_rent.file_array"}
                            {volist name="$contract_rent.file_array" id="vo"}
                            <div class="layui-col-md4" id="uploadImg{$vo.id}">{:file_card($vo)}</div>
                            {/volist}
                            {/notempty}
                        </div>
                        {else/}
                        <div class="layui-row" id="filesList">
                            <input type="hidden" data-type="file" name="files" value="">
                        </div>
                        {/if}

                        <button type="button" class="layui-btn" id="upFiles">
                            <i class="layui-icon layui-icon-upload"></i> 附件（合同电子版）
                        </button>

                    </td>
                </tr>
                <tr>
                    <td class="layui-td-gray">租赁类型<font>*</font></td>
                    <td >
                        <select name="cate_id" lay-verify="required" lay-reqText="租赁类型">
                            <option value="">请选择租赁类型</option>
                            {volist name=":get_type_pay('fukuan','contract_rent')" id="v"}
                            <option value="{$v.id}" {if condition="(isset($contract_rent) && ($contract_rent.cate_id == $v.id) )"}selected{/if} >{$v.name}</option>
                            {/volist}
                        </select>
                    </td>
                </tr>
                <tr>
                    <td class="layui-td-gray">房屋地址<font>*</font></td>
                    <td >
                        <input type="text" name="address"
                               {if condition="(isset($contract_rent))"}value="{$contract_rent.address}"{/if}
                               autocomplete="off" lay-verify="required" lay-reqText="房屋地址" placeholder="房屋地址" class="layui-input">
                    </td>
                </tr>
                <tr>
                    <td class="layui-td-gray">是否开发票<font>*</font></td>
                    <td >
                        <input type="radio" checked name="is_invoice" value="-1" title="否">
                        <div lay-radio>
                            <span style="color: red;">否</span>
                        </div>
                        <input type="radio" {if condition="(isset($contract_rent) && $contract_rent['is_invoice'] == 1)"}checked{/if} name="is_invoice" value="1" title="是">
                        <div lay-radio>
                            <span style="color: green;">是</span>
                        </div>
                    </td>
                </tr>
            </table>
        </div>
    </div>

    <div class="layui-form">
        <input type="radio" checked name="i_count" value="0" title="自动计算">
        <div lay-radio>
            <span style="color: blue;">自动计算</span>
        </div>
        <input type="radio" name="i_count" value="1" title="手动导入">
        <div lay-radio>
            <span style="color: pink;">手动导入</span>
        </div>
    </div>
    <div class="py-3">
	  <input type="hidden" name="scene" value="add">
      <button id="btn-submit" class="layui-btn layui-btn-normal" lay-submit="" lay-filter="webform">立即提交</button>
      <button type="reset" class="layui-btn layui-btn-primary">重置</button>
    </div>
</form>
{else/}
<form class="layui-form p-4">
<h2 class="pb-3">
    <div class="layui-input-group">
        <div class="layui-input-prefix">编辑合同 </div>
        <div>合同编号：{$contract_rent.code}</div>
    </div>
    <div class="party">
        <div class="party party_a">
            <h3 class="pb-3 title">甲方</h3>
            <table class="layui-table layui-table-form">
                <tr>
                    <td class="layui-td-gray">公司名称<font>*</font></td>
                    <td>
                        {$contract_rent.party_a_name}
                    </td>
                </tr>
                <tr>
                    <td class="layui-td-gray">联系人<font>*</font></td>
                    <td>
                        {$contract_rent.party_a_person}
                    </td>
                </tr>
                <tr>
                    <td class="layui-td-gray">联系电话<font>*</font></td>
                    <td >
                        {$contract_rent.party_a_mobile}
                    </td>
                </tr>
                <tr>
                    <td class="layui-td-gray">地址</td>
                    <td >
                        {$contract_rent.party_a_add}
                    </td>
                </tr>
            </table>
        </div>
        <div class="party party_b">
            <h3 class="pb-3 title">乙方</h3>
            <table class="layui-table layui-table-form">
                <tr>
                    <td class="layui-td-gray">公司名称<font>*</font></td>
                    <td>
                        {$contract_rent.party_b_name}
                    </td>
                </tr>
                <tr>
                    <td class="layui-td-gray">联系人<font>*</font></td>
                    <td >
                        {$contract_rent.party_b_person}
                    </td>
                </tr>
                <tr>
                    <td class="layui-td-gray">联系电话<font>*</font></td>
                    <td >
                        {$contract_rent.party_b_mobile}
                    </td>
                </tr>
                <tr>
                    <td class="layui-td-gray">地址<font>*</font></td>
                    <td >
                        {$contract_rent.party_b_add}
                    </td>
                </tr>
            </table>
        </div>


        <div class="party party_a" style="margin-left: 20px">
            <h3 class="pb-3 title">门店</h3>
            <table class="layui-table layui-table-form">
                <input type="hidden" name="id" value="{$contract_rent.id}" autocomplete="off" placeholder="请选择门店" class="layui-input">
                <tr>
                    <td class="layui-td-gray">门店名称<font>*</font></td>
                    <td>
                        <select id="select-store" lay-search="" name="did" lay-verify="required" lay-reqText="请选择" lay-filter="select-store">
                            <option value="" >--请选择--</option>
                            <option value="1" {if condition="(isset($contract_rent) && ($contract_rent.did == 1) )"}selected{/if} data-dzname="孙艳东" data-dzid="2" data-dzmobile="13916184277" >总部</option>
                            {volist name="$stores" id="vo"}
                            <option value="{$vo.id}" {if condition="($vo.id == $contract_rent.did)"}selected{/if} data-dzname="{$vo.dzname}" data-dzid="{$vo.dzid}" data-dzmobile="{$vo.dzmobile}" >{$vo.title}</option>
                            {/volist}
                        </select>
                    </td>
                </tr>
                <tr>
                    <td class="layui-td-gray">店长姓名<font>*</font></td>
                    <td >
                        <div class="xm-select-demo" name="aid" id="demo5"></div>
                        <input type="hidden" name="dz_aid" value="{$contract_rent.dz_aid}" autocomplete="off" lay-verify="required" lay-reqText="请输入客户名称" placeholder="请输入客户联系地址" class="layui-input">
                        <input type="hidden" name="dz_name" value="{$contract_rent.dz_name}" autocomplete="off" lay-verify="required" lay-reqText="请输入客户名称" placeholder="请输入客户联系地址" class="layui-input">
                    </td>
                </tr>
                <tr>
                    <td class="layui-td-gray">店长电话<font>*</font></td>
                    <td >
                        <input type="text" name="dz_mobile" value="{$contract_rent.dz_mobile}" autocomplete="off" lay-verify="required" lay-reqText="请输入客户名称" placeholder="请输入客户联系地址" class="layui-input">
                    </td>
                </tr>
            </table>
        </div>
    </div>

    <!--    金额   租金收款信息   物业费收款信息-->
    <div class="party" style="margin-top: 10px">
        <div class="party party_a" >
            <h3 class="pb-3 title">金额</h3>
            <table class="layui-table layui-table-form">
                <tr>
                    <td class="layui-td-gray">首款房屋租金/月<font>*</font></td>
                    <td>{$contract_rent.rent_amount}</td>
                </tr>
                <tr>
                    <td class="layui-td-gray">首款物业费/月<font>*</font></td>
                    <td>{$contract_rent.wyf_amount}</td>
                </tr>
                <tr>
                    <td class="layui-td-gray">押金<font>*</font></td>
                    <td>{$contract_rent.deposit_amount}</td>
                </tr>
                <tr>
                    <td class="layui-td-gray">首款付款日期<font>*</font></td>
                    <td>{$contract_rent.first_pay_date}</td>
                </tr>
            </table>
        </div>
        <div class="party party_b">
            <h3 class="pb-3 title">租金收款信息</h3>
            <table class="layui-table layui-table-form">
                <tr>
                    <td class="layui-td-gray">收款方<font>*</font></td>
                    <td>
                        <input type="text" name="account_name"
                               {if condition="(isset($contract_rent))"}value="{$contract_rent.account_name}"{/if}
                        autocomplete="off"  lay-verify="required" lay-reqText="收款方" placeholder="收款方" class="layui-input" >
                    </td>

                </tr>
                <tr>
                    <td class="layui-td-gray">收款账号<font>*</font></td>
                    <td>
                        <input type="text" name="account_number"
                               {if condition="(isset($contract_rent))"}value="{$contract_rent.account_number}"{/if}
                        autocomplete="off"  lay-verify="required" lay-reqText="收款账号" placeholder="收款账号" class="layui-input" >
                    </td>
                </tr>
                <tr>
                    <td class="layui-td-gray">开户行<font>*</font></td>
                    <td>
                        <input type="text" name="account_bank"
                               {if condition="(isset($contract_rent))"}value="{$contract_rent.account_bank}"{/if}
                        autocomplete="off"  lay-verify="required" lay-reqText="开户行" placeholder="开户行" class="layui-input" >
                    </td>
                </tr>
            </table>
        </div>

    </div>

    <!--    增长周期-->
    <div class="party party_a" style="margin-top: 10px">
        <h3 class="pb-3 title">增长周期</h3>
        <table class="layui-table layui-table-form">
            <tr>
                <td class="layui-td-gray">
                    租金增长<font>*</font>
                </td>
                <td>
                    {volist name="$contract_rent['rent_gc']" id="vo"}
                    <div>从合同开始日期<span style="color: red;font-weight: bold">{$vo.gc_month}</span>月后租金为：
                        <span style="color: red;font-weight: bold">{$vo.gc_amount}</span>/月</div>
                    {/volist}
                </td>
            </tr>
<!--            <tr>-->
<!--                <td class="layui-td-gray">-->
<!--                    物业费增长-->
<!--                </td>-->
<!--                <td class="wyf_growth_cycle growth_cycle" >-->
<!--                    {volist name="$contract_rent['wyf_gc']" id="vo"}-->
<!--                    <div>从合同开始日期<span style="color: red;font-weight: bold">{$vo.gc_month}</span>月后物业费为：-->
<!--                        <span style="color: red;font-weight: bold">{$vo.gc_amount}</span>/月</div>-->
<!--                    {/volist}-->
<!--                </td>-->
<!--            </tr>-->
        </table>
    </div>

    <!--    房东-->
    <div class="party" style="margin-top: 10px">
        <div class="party party_a">
            <h3 class="pb-3 title">房东</h3>
            <table class="layui-table layui-table-form">
                <tr>
                    <td class="layui-td-gray">
                        <div class="layui-input-inline">房产证</div>
                    </td>
                    <td colspan="5" style="line-height:inherit">
                        {notempty name="$contract_rent.file_fcz_array"}
                        {volist name="$contract_rent.file_fcz_array" id="vo"}
                        <div class="layui-col-md4" >{:file_card($vo,'view')}</div>
                        {/volist}
                        {/notempty}
                    </td>
                </tr>

                <tr>
                    <td class="layui-td-gray">
                        <div class="layui-input-inline">房东身份证</div>
                    </td>
                    <td colspan="5" style="line-height:inherit">
                        {notempty name="$contract_rent.file_fd_array"}
                        {volist name="$contract_rent.file_fd_array" id="vo"}
                        <div class="layui-col-md4" >{:file_card($vo,'view')}</div>
                        {/volist}
                        {/notempty}
                    </td>
                </tr>
                <tr>
                    <td class="layui-td-gray">
                        <div class="layui-input-inline">营业执照</div>
                    </td>
                    <td colspan="5" style="line-height:inherit">
                        {notempty name="$contract_rent.file_yyzz_array"}
                        {volist name="$contract_rent.file_yyzz_array" id="vo"}
                        <div class="layui-col-md4" >{:file_card($vo,'view')}</div>
                        {/volist}
                        {/notempty}
                    </td>
                </tr>
            </table>
        </div>
    </div>

    <!--    //免租期-->
    <div class="party party_a" style="margin-top: 10px">
        <h3 class="pb-3 title">免租期</h3>
        <table class="layui-table layui-table-form">
            <tr>
                <td class="layui-td-gray">起始日期<font>*</font></td>
                <td>{$contract_rent.free_start_date}</td>
            </tr>
            <tr>
                <td class="layui-td-gray">终止日期<font>*</font></td>
                <td>{$contract_rent.free_end_date}</td>
            </tr>
        </table>
    </div>

    <!--    基础信息-->
    <div class="party" style="margin-top: 10px">
        <div class="party party_a">
            <h3 class="pb-3 title">基础信息</h3>
            <table class="layui-table layui-table-form">
                <tr>
                    <td class="layui-td-gray">起始日期（合同）<font>*</font></td>
                    <td>{$contract_rent.start_date}</td>
                </tr>
                <tr>
                    <td class="layui-td-gray">终止日期（合同）<font>*</font></td>
                    <td>{$contract_rent.end_date}</td>
                </tr>
                <tr>
                    <td class="layui-td-gray">付费周期/月<font>*</font></td>
                    <td>{$contract_rent.pay_cycle}</td>
                </tr>
                <tr>
                    <td class="layui-td-gray">付费日期<font>*</font></td>
                    <td>{$contract_rent.pay_date}</td>
                </tr>
                <tr>
                    <td class="layui-td-gray">
                        <div class="layui-input-inline">附件（合同电子版）<font>*</font></div>
                    </td>
                    <td colspan="5" style="line-height:inherit">
                        <div class="layui-row" id="filesList">
                            <input type="hidden" data-type="file" name="files" value="{$contract_rent.files}">
                            {notempty name="$contract_rent.file_array"}
                            {volist name="$contract_rent.file_array" id="vo"}
                            <div class="layui-col-md4" id="uploadImg{$vo.id}">{:file_card($vo)}</div>
                            {/volist}
                            {/notempty}
                        </div>
                        <button type="button" class="layui-btn" id="upFiles">
                            <i class="layui-icon layui-icon-upload"></i> 附件（合同电子版）
                        </button>
                    </td>
                </tr>
                <tr>
                    <td class="layui-td-gray">租赁类型<font>*</font></td>
                    <td>{$contract_rent.cate_name}</td>
                </tr>
                <tr>
                    <td class="layui-td-gray">房屋地址<font>*</font></td>
                    <td>
                        <input type="text" name="address"
                               {if condition="(isset($contract_rent))"}value="{$contract_rent.address}"{/if}
                        autocomplete="off"  lay-verify="required" lay-reqText="房屋地址" placeholder="房屋地址" class="layui-input" >
                    </td>
                </tr>
                <tr>
                    <td class="layui-td-gray">是否开发票<font>*</font></td>
                    <td >
                        <input type="radio" checked name="is_invoice" value="-1" title="否">
                        <div lay-radio>
                            <span style="color: red;">否</span>
                        </div>
                        <input type="radio" {if condition="(isset($contract_rent) && $contract_rent['is_invoice'] == 1)"}checked{/if} name="is_invoice" value="1" title="是">
                        <div lay-radio>
                            <span style="color: green;">是</span>
                        </div>
                    </td>
                </tr>
            </table>
        </div>
    </div>

    <div class="additional" style="margin-top: 10px">
        <h3 class="pb-3 title">续签/改签明细</h3>
        <table class="layui-table layui-table-form">
            {volist name = "$contract_rent.additional" id="vo" key="k"}
            <tr>
                <td class="layui-td-gray">第{$k}行</td>
                <td>开始日期：{$vo.sdate_start} -- 结束日期：{$vo.sdate_end}</td>
                <td class="layui-td-gray">金额<font>*</font></td>
                <td>{$vo.amount}</td>
            </tr>
            {/volist}
        </table>
    </div>

    <div class="py-3">
        <input type="hidden" name="scene" value="add">
        <button class="layui-btn layui-btn-normal" lay-submit="" lay-filter="webform" >立即提交</button>
        <button type="reset" class="layui-btn layui-btn-primary">重置</button>
    </div>

</h2>
</form>
{/if}
<style>

    .layui-td-gray{
        width: 100px;
    }

    .party{
        display: flex;
        color: #fff;
    }

    .party .party_a .layui-td-gray,.party_a{
        color:#222;
        background-color: #e8f6f3;
        border: 0;
    }

    .party .party_b .layui-td-gray,.party_b{
        color:#222;
        background-color: #f1f1f1;
        border: 0;
    }

    .party_a{
        padding: 10px;
        flex-direction: column;
        flex: 1;
    }

    .party_b{
        padding: 10px;
        flex-direction: column;
        margin-left: 20px;
        flex: 1;
    }
</style>
{/block}
<!-- /主体 -->

<!-- 脚本 -->
{block name="script"}
<script>
	const moduleInit = ['tool','employeepicker','oaTool'];
	function gouguInit() {
        var form = layui.form, tool = layui.tool, table = layui.table, laydate = layui.laydate, oaTool = layui.oaTool,
            employeepicker = layui.employeepicker;

        //日期时间
        laydate.render({
            elem: '#free_start_date,#free_end_date,#start_date,#end_date,#first_pay_date',
            type: 'date',
            format: 'yyyy-MM-dd',
            theme: 'molv',
            showBottom: false
        });
        laydate.render({
            elem: '#pay_date',
            type: 'date',
            format: 'dd',
            showBottom: false
        });
        //相关附件上传
        oaTool.addFile();
        //选择关联的客户
        $('.customer-picker').on('click', function () {
            let that = $(this);
            let callback = function (data) {
                $('[name="party_b_name"]').val(data.name);
                $('[name="party_b_person"]').val(data.contact_name);
                $('[name="party_b_mobile"]').val(data.contact_mobile);
                $('[name="party_b_add"]').val(data.address);

            }
            oaTool.customerPicker(callback);
        });
        //监听提交
        form.on('submit(webform)', function (data) {

            var btn = this;

            btn.classList.add("layui-btn-disabled");

            let callback = function (e) {
                layer.msg(e.msg);
                // layui-btn-disabled
                if (e.code == 0) {
                    tool.sideClose(1000);
                    btn.classList.remove("layui-btn-disabled");
                }
            }
            tool.post("/contract/contractrent/add", data.field, callback);
            return false;
        });

        oaTool.addFile({
            btn: 'upFczFile',
            box: 'fczfileList'
        });
        oaTool.addFile({
            btn: 'upFdFile',
            box: 'fdfileList'
        });
        oaTool.addFile({
            btn: 'upYyzzFile',
            box: 'yyzzfileList'
        });
        oaTool.addFile({
            btn: 'upFiles',
            box: 'filesList'
        });

        //选择门店 带出店长
        var demo4 = xmSelect.render({
            el: '#demo5',        //绑定页面上的id
            filterable: true,
            clickClose: true,
            name: 'aid',     //页面上的name
            remoteSearch: true,
            radio: true,      //单选
            showCount: 20,    //下拉展开显示的条数
            searchTips: "输入名字",
            remoteMethod: function (val, cb, show) {
                //需要回传一个数组
                $.ajax({
                    type: 'post',
                    url: '/api/index/get_personnel_dep',           //数据接口
                    data: {keywords: val},               //搜素框里的值
                    dataType: 'json',
                    success: function (data) {
                        var res_data = data.data;
                        let arr = [];
                        for (let i = 0; i < res_data.length; i++) {
                            arr.push({name: res_data[i].name, value: res_data[i].id})
                        }
                        cb(arr)
                        form.render('select');
                    },
                    error: function myfunction() {
                        cb([]);
                    }
                });
            },
            on: function (data) {
                o_did(data)
            },
        });
        form.on('select(select-store)', function (data) {
            let select_store = $("#select-store").find('option:selected');
            let dzname = select_store.data("dzname");
            let dzid = select_store.data("dzid");
            let dzmobile = select_store.data("dzmobile");

            $('[name="dz_aid"]').val(dzid);
            $('[name="dz_name"]').val(dzname);
            $('[name="dz_mobile"]').val(dzmobile);
            demo4.setValue([{name: dzname, value: dzid}])
        });

        function o_did(data) {
            //arr:  当前多选已选中的数据
            let arr = data.arr
            if (data.arr.length > 0) {
                // 从选中数据中提取value值
                var value = arr[0].value;

                $.ajax({
                    type: 'post',
                    url: '/api/index/get_admin_to_id',           //数据接口
                    data: {id: value},               //搜素框里的值
                    success: function (data) {
                        let re_data = data.data;

                        $('[name="dz_aid"]').val(re_data.id);
                        $('[name="dz_name"]').val(re_data.name);
                        $('[name="dz_mobile"]').val(re_data.mobile);

                        // if (list && list.aid == re_data.id){
                        //     $('input[name="detail_time"]').val(list.detail_time);
                        //     $("#department_type option[value='"+list.department_type+"']").attr("selected","selected");
                        // }else{
                        //     $('[name="dz_aid"]').val(re_data.id);
                        //     $('[name="dz_name"]').val(re_data.name);
                        //     $('[name="dz_mobile"]').val(re_data.mobile);
                        // }

                        // 重新渲染下拉框
                        form.render('select');
                    }
                });
            }
        }

        let dz_aid = '<?php echo isset($contract_rent) ?  $contract_rent["dz_aid"] : "" ;?>';
        let dz_name = '<?php echo isset($contract_rent) ?  $contract_rent["dz_name"] : "" ;?>';

        if (dz_aid && dz_name) {
            demo4.setValue([{name: dz_name, value: dz_aid}])
        }

        $('#addFlow_rent').on('click', function () {
            var len = $('#rent_growth_cycle').find('.layui-inline').length;
            console.log(len)

            var index = len + 1;
            var timestamp = new Date().getTime();
            var tem = `<div class="layui-inline">
                            <label class="layui-form-label" style="width: 200px">从合同开始日期</label>
                            <div class="layui-input-inline">
                                <input type="number" lay-affix="number"  name="rent_gc[${index}][month]" autocomplete="off"  lay-verify="required" lay-reqText="租金增长月周期" placeholder="租金增长月周期" class="layui-input" >
                            </div>
                            <label class="layui-form-label" style="width: 200px">后租金为：</label>
                            <div class="layui-input-inline" style="width:20%;">
                                <input type="number" lay-affix="number" step="0.01" min="0" name="rent_gc[${index}][amount]" autocomplete="off"   placeholder="租金增长月金额" class="layui-input" >
                            </div>
                            <label class="layui-form-label" style="width: 200px">/月</label>

                        <span class="layui-btn layui-btn-danger layui-btn-sm">删除</span>
                    </div>`;
            $('#rent_growth_cycle').append(tem);
            form.render();
        });


        $('#rent_growth_cycle').on('click', '.layui-btn-danger', function () {
            $(this).parents('.layui-inline').remove();
            var items = $('.layui-inline').length;
            if (items > 0) {
                $('.label-index').each(function (index, item) {
                    $(this).html('第' + (index + 2) + '级');
                })
            }
        });

        $('#addFlow_wyf').on('click', function () {
            var len = $('#wyf_growth_cycle').find('.layui-inline').length;
            console.log(len)

            var index = len + 1;
            var timestamp = new Date().getTime();
            var tem = `<div class="layui-inline">
                        <label class="layui-form-label" style="width: 200px">从合同开始日期</label>
                        <div class="layui-input-inline">
                            <input type="number" lay-affix="number"  name="wyf_gc[${index}][month]" autocomplete="off" lay-verify="required" lay-reqText="物业费增长周期/月"  placeholder="物业费增长周期/月" class="layui-input" >
                        </div>
                         <label class="layui-form-label" style="width: 200px">后物业费为：</label>
                        <div class="layui-input-inline" style="width:20%;">
                            <input type="number" lay-affix="number" step="0.01" min="0" name="wyf_gc[${index}][month]" autocomplete="off"  lay-verify="required" lay-reqText="物业费增长金额" placeholder="物业费增长金额" class="layui-input" >
                       </div>
                       <label class="layui-form-label" style="width: 200px">/月</label>
                        <span class="layui-btn layui-btn-danger layui-btn-sm">删除</span>
                    </div>`;
            $('#wyf_growth_cycle').append(tem);
            form.render();
        });

        $('#wyf_growth_cycle').on('click', '.layui-btn-danger', function () {
            $(this).parents('.layui-inline').remove();
            var items = $('.layui-inline').length;
            if (items > 0) {
                $('.label-index').each(function (index, item) {
                    $(this).html('第' + (index + 2) + '级');
                })
            }
        });

    }
</script>
{/block}
<!-- /脚本 -->