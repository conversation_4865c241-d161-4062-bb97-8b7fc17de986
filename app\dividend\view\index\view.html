{extend name="../../base/view/common/base" /}

{block name="head"}
<style>
/* 优化表格和表单显示 */
.layui-table {
    margin-bottom: 15px;
}

.layui-form-item {
    margin-bottom: 15px;
}

/* 只读模式样式 */
.readonly-field {
    background-color: #f8f9fa !important;
    color: #495057 !important;
    border: 1px solid #e9ecef !important;
    cursor: default !important;
}

.readonly-field:hover {
    background-color: #f8f9fa !important;
}

/* 表头样式 */
.layui-table th {
    background-color: #f8f9fa;
    font-weight: bold;
}

/* 统计行样式 */
.summary-row {
    background-color: #e3f2fd !important;
    font-weight: bold;
    border-top: 2px solid #2196f3 !important;
}

.summary-row td {
    color: #1976d2 !important;
    font-size: 14px;
}

/* 信息提示样式 */
.info-box {
    background-color: #e8f5e8;
    border: 1px solid #4caf50;
    border-left: 4px solid #4caf50;
    padding: 15px;
    margin-bottom: 20px;
    border-radius: 4px;
}

.info-box h4 {
    color: #2e7d32;
    margin-bottom: 10px;
    font-weight: bold;
}

.info-box p {
    color: #388e3c;
    margin: 5px 0;
}
</style>
{/block}

{block name="body"}
<div class="p-4">
    <h3 class="pb-3">查看门店分红信息</h3>

    <!-- 信息提示 -->
    <div class="info-box">
        <h4><i class="layui-icon layui-icon-tips"></i> 查看模式</h4>
        <p>当前为只读查看模式，所有数据仅供查看，无法进行编辑操作。</p>
    </div>

    <!-- 基础信息 -->
    <table class="layui-table">
        <tr>
            <td class="layui-td-gray" style="width: 120px;">关联门店</td>
            <td style="width: 200px;">
                <input type="text" value="{$detail.store_name|default='未知门店'}"
                       class="layui-input readonly-field" readonly>
            </td>
            <td class="layui-td-gray" style="width: 120px;">已计提风险金</td>
            <td style="width: 200px;">
                <input type="text" value="{$detail.risk_reserve|default='0.00'} 元"
                       class="layui-input readonly-field" readonly>
            </td>
            <td class="layui-td-gray" style="width: 120px;">公司持股比例</td>
            <td style="width: 200px;">
                <input type="text" value="{$detail.company_shareholding_ratio|default='0.000'}%"
                       class="layui-input readonly-field" readonly>
            </td>
        </tr>
        <tr>
            <td class="layui-td-gray">备注</td>
            <td colspan="5">
                <textarea class="layui-textarea readonly-field" readonly 
                          style="height: 60px; resize: none;">{$detail.remark|default='无'}</textarea>
            </td>
        </tr>
    </table>

    <!-- 公司股东持股详情 -->
    <h4 class="pt-3 pb-2">公司股东持股详情</h4>
    <table class="layui-table">
        <thead>
            <tr>
                <th style="width: 20%;">股东姓名</th>
                <th style="width: 20%;">关联人员</th>
                <th style="width: 20%;">公司内部持股比例(%)</th>
                <th style="width: 20%;">门店持股比例(%)</th>
                <th style="width: 20%;">备注</th>
            </tr>
        </thead>
        <tbody>
            {volist name="company_shareholders" id="shareholder"}
            <tr>
                <td>{$shareholder.shareholder_name|default='-'}</td>
                <td>{$shareholder.admin_name|default='未关联'}</td>
                <td style="text-align: center;">
                    <span style="color: #1E9FFF; font-weight: bold;">
                        {$shareholder.company_shareholding_ratio|default='0.000'}
                    </span>
                </td>
                <td style="text-align: center;">
                    <span style="color: #009688; font-weight: bold;">
                        {$shareholder.store_shareholding_ratio|default='0.000'}
                    </span>
                </td>
                <td>{$shareholder.remark|default='-'}</td>
            </tr>
            {/volist}
            {empty name="company_shareholders"}
            <tr>
                <td colspan="5" style="text-align: center; color: #999;">暂无公司股东信息</td>
            </tr>
            {/empty}
        </tbody>
        <tfoot>
            <tr class="summary-row">
                <td colspan="2" style="text-align: right; padding-right: 10px;">合计：</td>
                <td style="text-align: center;" id="companyInternalTotal">0.000</td>
                <td style="text-align: center;" id="companyStoreTotal">0.000</td>
                <td></td>
            </tr>
        </tfoot>
    </table>

    <!-- 个人股东持股详情 -->
    <h4 class="pt-3 pb-2">个人股东持股详情</h4>
    <table class="layui-table">
        <thead>
            <tr>
                <th style="width: 20%;">股东姓名</th>
                <th style="width: 20%;">关联人员</th>
                <th style="width: 20%;">门店持股比例(%)</th>
                <th style="width: 40%;">备注</th>
            </tr>
        </thead>
        <tbody>
            {volist name="personal_shareholders" id="shareholder"}
            <tr>
                <td>{$shareholder.shareholder_name|default='-'}</td>
                <td>{$shareholder.admin_name|default='未关联'}</td>
                <td style="text-align: center;">
                    <span style="color: #FF5722; font-weight: bold;">
                        {$shareholder.store_shareholding_ratio|default='0.000'}
                    </span>
                </td>
                <td>{$shareholder.remark|default='-'}</td>
            </tr>
            {/volist}
            {empty name="personal_shareholders"}
            <tr>
                <td colspan="4" style="text-align: center; color: #999;">暂无个人股东信息</td>
            </tr>
            {/empty}
        </tbody>
        <tfoot>
            <tr class="summary-row">
                <td colspan="2" style="text-align: right; padding-right: 10px;">合计：</td>
                <td style="text-align: center;" id="personalStoreTotal">0.000</td>
                <td></td>
            </tr>
        </tfoot>
    </table>

    <!-- 返回按钮 -->
    <div class="py-3">
        <button type="button" class="layui-btn layui-btn-primary" onclick="parent.layer.closeAll();">
            <i class="layui-icon layui-icon-return"></i> 关闭
        </button>
    </div>
</div>
{/block}

<!-- 脚本 -->
{block name="script"}
<script>
    const moduleInit = [];
    function gouguInit() {
        // 计算并显示统计数据
        calculateTotals();
    }

    // 计算统计总计
    function calculateTotals() {
        // 计算公司股东统计
        var companyInternalTotal = 0;
        var companyStoreTotal = 0;

        {volist name="company_shareholders" id="shareholder"}
        companyInternalTotal += parseFloat('{$shareholder.company_shareholding_ratio|default=0}') || 0;
        companyStoreTotal += parseFloat('{$shareholder.store_shareholding_ratio|default=0}') || 0;
        {/volist}

        document.getElementById('companyInternalTotal').textContent = companyInternalTotal.toFixed(3);
        document.getElementById('companyStoreTotal').textContent = companyStoreTotal.toFixed(3);

        // 计算个人股东统计
        var personalStoreTotal = 0;

        {volist name="personal_shareholders" id="shareholder"}
        personalStoreTotal += parseFloat('{$shareholder.store_shareholding_ratio|default=0}') || 0;
        {/volist}

        document.getElementById('personalStoreTotal').textContent = personalStoreTotal.toFixed(3);
    }
</script>
{/block}
