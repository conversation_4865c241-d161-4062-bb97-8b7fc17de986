<?php
/**
 * @copyright Copyright (c) 2021 仲正堂工作室
 * @license https://opensource.org/licenses/GPL-3.0
 * @link https://www.gougucms.com
 */

declare (strict_types=1);

namespace app\api\controller;

use app\base\BaseController;
use think\facade\Db;
use think\facade\Log;

class Cross extends BaseController
{
    /**
     * 获取门店列表（用于搜索下拉框）
     * @return \think\response\Json
     */
    public function getStoreList()
    {
        try {
            // 获取所有门店列表，只返回remark字段为"门店"的记录
            $storeList = Db::name('Department')
                ->where('status', '>=', 0)
                ->where('remark', '门店')
                ->field('id, title')
                ->order('sort asc, id asc')
                ->select()
                ->toArray();

            return json([
                'code' => 0,
                'msg' => '获取成功',
                'data' => $storeList
            ]);
        } catch (\Exception $e) {
            Log::error('获取门店列表失败: ' . $e->getMessage());
            return json([
                'code' => 1,
                'msg' => '获取门店列表失败'
            ]);
        }
    }
}
