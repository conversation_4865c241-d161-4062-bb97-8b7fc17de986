<?php

namespace app\marketing\controller;

use app\base\BaseController;
use think\facade\Db;
use think\facade\View;

class Rent extends BaseController
{
    public function index()
    {
        if (request()->isAjax()) {
            $param = get_params();
            $where = array();

            $rows = empty($param['limit']) ? get_config('app.page_size') : $param['limit'];

            $rent_list = Db::name("rent")->paginate($rows);
            return table_assign(0, '', $rent_list);
        } else {
            return view();
        }
    }

    public function view()
    {
        $id = get_params('id');

        $detail = Db::name("rent")->where(['id' => $id])->find();
        $list = Db::name("rent_detail")->where(['r_id' => $id])->order('sort asc')->select();

        View::assign('detail', $detail);
        View::assign('list', $list);

        return view();
    }

    public function census($rent_detail)
    {
        $rent = Db::name("rent")->where(['id' => $rent_detail['r_id']])->find();
        $rent_detail = Db::name("rent_detail")->where(['id' => $rent_detail['id']])->find();
        $rent_detail_count = Db::name("rent_detail")->where(['r_id' => $rent['id']])->count();

        if (!empty($rent_detail)) {

            $pay_cycle = $rent['pay_cycle'];

            $pay_date = date("Y-m-d", strtotime("+{$pay_cycle} months", strtotime($rent_detail['pay_date']))); //增长的付款日期
            if ($rent_detail_count <= 1){
                $pay_date = $rent['pay_date'];
            }

            $cycle_start_date = date("Y-m-d", strtotime("+1 days", strtotime($rent_detail['cycle_end_date']))); //上一条周期止+1天

            $monthly_rent = 0;
            $amount_per = 0;
            $cycle_end_date = '';

            $f_cycle_start_date = $cycle_start_date;
            for ($i = 1; $i <= $pay_cycle; $i++) {
                $cycle_end_date = date("Y-m-d", strtotime("+{$i} months", strtotime($cycle_start_date)));
                $month = DiffDate($f_cycle_start_date, $rent['con_start_date'])[3];
                if ($month >= $rent['con_cycle']) { //超过合同年限
                    $rent_detail_data = [
                        'r_id' => $rent['id'],
                        'pay_date' => $pay_date, //付款日期
                        'monthly_rent' => $monthly_rent, //月租金
                        'amount_per' => $amount_per, //每期金额
                        'cycle_start_date' => $cycle_start_date, //周期起
                        'cycle_end_date' => $rent['con_end_date'], //周期止
                        'sort' => $rent_detail['sort'] + 1, //排序
                        'create_time' => time(), //注册时间
                    ];
                    Db::name("rent_detail")->insertGetId($rent_detail_data);
                    return;
                }

                if ($month % $rent['increase'] == 0 && $month != 0) { //已满增长年限周期
                    $monthly_rent = $rent_detail['monthly_rent'] * ( $rent['increase_per'] / 100 + 1);
                    $amount_per += $monthly_rent;
                }else{
                    $monthly_rent = $rent_detail['monthly_rent'];
                    $amount_per += $rent_detail['monthly_rent'];
                }
                $f_cycle_start_date = date("Y-m-d", strtotime("+{$i} months", strtotime($f_cycle_start_date)));
            }

            $cycle_end_date = date("Y-m-d", strtotime("-1 days", strtotime($cycle_end_date))); //上一条周期止+1天

            $rent_detail_data = [
                'r_id' => $rent['id'],
                'pay_date' => $pay_date, //付款日期
                'monthly_rent' => $monthly_rent, //月租金
                'amount_per' => $amount_per, //每期金额
                'cycle_start_date' => $cycle_start_date, //周期起
                'cycle_end_date' => $cycle_end_date, //周期止
                'sort' => $rent_detail['sort'] + 1, //排序
                'create_time' => time(), //注册时间
            ];

            $rent_detail_id = Db::name("rent_detail")->insertGetId($rent_detail_data);
            if ($rent_detail_id){
                $rent_detail_data['id'] = $rent_detail_id;
                $this->census($rent_detail_data);
            }

        }


    }

    public function add()
    {
        $param = get_params();

        if (request()->isAjax()) {
            // 启动事务
            Db::startTrans();
            try {

                $depar =  Db::name('department')->where(['id'=>$param['did']])->find();
                $param['dname'] = $depar['title'];

                //转换租金增长周期 年->月
                $param['increase'] = $param['increase'] * 12;
                //获取合同年限 月
                $diffDate = DiffDate($param['con_start_date'],$param['con_end_date']);
                $param['con_cycle'] = $diffDate[3];
                //转换付款周期 年->月
                $param['pay_cycle'] = $param['pay_cycle'] * 12;

                $uid = Db::name('rent')->strict(false)->field(true)->insertGetId($param);
                add_log('add', $uid, $param);

                $rent_start_date = date("Y-m-d", strtotime("-1 days", strtotime($param['rent_start_date']))); //上一条周期止+1天



                $rent_detail_data = [
                    'r_id' => $uid,
                    'pay_date' => $param['pay_date'], //付款日期
                    'monthly_rent' => $param['con_amount'] / 12, //月租金
                    'amount_per' => $param['con_amount'] / 12, //每期金额
                    'cycle_start_date' => $param['con_start_date'], //周期起
                    'cycle_end_date' => $rent_start_date, //周期止
                    'sort' => 1, //排序
                    'create_time' => time(), //注册时间
                ];

                $rent_detail_id = Db::name("rent_detail")->insertGetId($rent_detail_data);
                $rent_detail_data['id'] = $rent_detail_id;
                //$rent_detail = Db::name("rent_detail")->where(['r_id' => $rent['id']])->order(['sort asc'])->find();

                $this->census($rent_detail_data);

                // 提交事务
                Db::commit();
            } catch (\Exception $e) {
                // 回滚事务
                Db::rollback();
                return to_assign(1, '提交失败:' . $e->getMessage());
            }
            return to_assign();
        }else{
            $id = isset($param['id']) ? $param['id'] : 0;
            $department = Db::name('department')->where(['remark'=>'门店'])->select();

            View::assign('department', $department);
            View::assign('id', $id);
            return view();
        }
    }


}