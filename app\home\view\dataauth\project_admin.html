{extend name="../../base/view/common/base" /}
<!-- 主体 -->
{block name="body"}
<form class="layui-form p-4">
	<h3 class="pb-1">权限配置</h3>
	<table class="layui-table layui-table-form">
		<tr>
			<td colspan="4" class="red" style="line-height:1.8">
				<p><strong>项目模块使用说明：</strong></p>
				<p>{$detail.desc}</p>
			</td>
		</tr>
		<tr>
			<td class="layui-td-gray">权限名称</td>
			<td>
				<input type="hidden" name="id" value="{$detail.id}" />
				{$detail.title}
			</td>
			<td class="layui-td-gray">权限标识</td>
			<td>{$detail.name}</td>
		</tr>
		<tr>
			<td class="layui-td-gray-2">默认阶段配置<font>*</font>
			</td>
			<td colspan="3">
				<input type="text" name="conf_1" value="{$detail.conf_1}" placeholder="请输入默认阶段，每个阶段以|分割" lay-verify="required" lay-reqText="请输入默认阶段" autocomplete="off" class="layui-input">
			</td>
		</tr>
		<tr>
			<td class="layui-td-gray-2">项目管理员</td>
			<td colspan="3">
				<input type="text" id="unames" name="unames" value="{$detail.unames}" readonly placeholder="请选择权限人员" autocomplete="off" class="layui-input picker-more">
				<input type="hidden" id="uids" name="uids" value="{$detail.uids}">
			</td>
		</tr>
	</table>
	<div class="pt-1">
		<button class="layui-btn layui-btn-normal" lay-submit="" lay-filter="webform">立即提交</button>
		<button type="reset" class="layui-btn layui-btn-primary">重置</button>
	</div>
</form>
{/block}
<!-- /主体 -->

<!-- 脚本 -->
{block name="script"}
<script>
	const moduleInit = ['tool','employeepicker'];
	function gouguInit() {
		var form = layui.form, tool = layui.tool;		
		//监听提交
		form.on('submit(webform)', function (data) {
			let callback = function (e) {
				layer.msg(e.msg);
				if(e.code==0){
					tool.sideClose(1000);
				}
			}
			tool.post("/home/<USER>/edit", data.field, callback);
			return false;
		});
	}
</script>
{/block}
<!-- /脚本 -->