{extend name="../../base/view/common/base" /}
<!-- 主体 -->
{block name="body"}
<div class="p-3">
    <table class="layui-hide" id="test" lay-filter="test"></table>
</div>
<script type="text/html" id="toolbarDemo">
  <div class="layui-btn-container">
  	<button class="layui-btn layui-btn-sm addNew" type="button">+ 添加费用类型</button>
  </div>
</script>
{/block}
<!-- /主体 -->

<!-- 脚本 -->
{block name="script"}
	<script>
	const moduleInit = ['tool'];
	function gouguInit() {
		var table = layui.table, tool = layui.tool, form = layui.form;
		layui.pageTable = table.render({
			elem: '#test'
			,toolbar: '#toolbarDemo'
			,defaultToolbar: false
			,title:'报销类别列表'
			,url: "/home/<USER>/expense_cate"
			,page: false
			,cellMinWidth: 80
			,cols: [[
					{field:'id',width:80, title: 'ID号', align:'center'}
					,{field:'title',title: '费用类型名称'}
                    ,{field:'type_name',title: '费用类型归属'}
					,{field:'t_name',title: '对账类型名称'}
					,{field:'t_title',title: '对账类型别名'}
					,{field:'status', title: '状态',width:80,align:'center',templet: function(d){
						var html1='<span class="green">正常</span>';
						var html2='<span class="yellow">禁用</span>';
						if(d.status==1){
							return html1;
						}
						else{
							return html2;
						}
					}}
					,{width:100,title: '操作', align:'center',templet: function(d){
						var html='';
						var btn='<a class="layui-btn  layui-btn-normal layui-btn-xs" lay-event="edit">编辑</a>';
						var btn1='<a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="disable">禁用</a>';
						var btn2='<a class="layui-btn layui-btn-xs" lay-event="open">启用</a>';
						if(d.status==1){
							html = '<div class="layui-btn-group">'+btn+btn1+'</div>';
						}
						else{
							html = '<div class="layui-btn-group">'+btn+btn2+'</div>';
						}
						return html;
					}}
				]]
			});
			
			table.on('tool(test)',function (obj) {
				if(obj.event === 'edit'){
                    tool.side(`expense_cate_add?id=${obj.data.id}`);
                    return;
					//addExpense(obj.data.id,obj.data.title);
				}
				if(obj.event === 'disable'){
					layer.confirm('确定要禁用该类别吗?', {icon: 3, title:'提示'}, function(index){
						let callback = function (e) {
							layer.msg(e.msg);
							if (e.code == 0) {
								layui.pageTable.reload();
							}
						}
						tool.post("/home/<USER>/expense_cate_check", { id: obj.data.id,status: 0,title: obj.data.title}, callback);
						layer.close(index);
					});
				}
				if(obj.event === 'open'){
					layer.confirm('确定要启用该类别吗?', {icon: 3, title:'提示'}, function(index){
						let callback = function (e) {
							layer.msg(e.msg);
							if (e.code == 0) {
								layui.pageTable.reload();
							}
						}
						tool.post("/home/<USER>/expense_cate_check", { id: obj.data.id,status: 1,title: obj.data.title}, callback);
						layer.close(index);
					});
				}
			});
			
			$('body').on('click','.addNew',function(){
                tool.side(`expense_cate_add`);
			});
			
			function addExpense(id,val){
				var title = '新增类别';
				if(id>0){
					title = '编辑类别';
				}
				layer.prompt({
					title: title,
					value: val,
					yes: function(index, layero) {
						// 获取文本框输入的值
						var value = layero.find(".layui-layer-input").val();
						if (value) {
							let callback = function (e) {
								layer.msg(e.msg);
								if (e.code == 0) {
									layui.pageTable.reload();				
								}
							}
							tool.post("/home/<USER>/expense_cate_add", {id: id,title: value}, callback);
							layer.close(index);
						} else {
							layer.msg('请填写类别标题');
						}
					}
				})
			}
		}
	</script>
{/block}
<!-- /脚本 -->