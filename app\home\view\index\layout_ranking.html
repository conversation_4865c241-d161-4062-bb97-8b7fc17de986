<div class="layui-card">
	<div class="layui-card-header"><h3>最活跃员工<span style="color:#999; font-size:14px; font-weight:400; margin-left:5px">最近30天前十的活跃度</span></h3></div>
	<div class="layui-card-body">
		<div id="logChart" style="width: 100%; height:268px;"></div>
	</div>
</div>
<script>
//动态
var logChart;
function layoutRaking() {
	logChart = echarts.init(document.getElementById('logChart'));
	$.ajax({
		url: "/home/<USER>/get_view_log",
		type: 'get',
		data: {},
		success: function (e) {
			if (e.code == 0) {
				var data_logs = e.data.data_logs;
				var logItem = [];
				$.each(data_logs, function (key, value) {
					let item = {
						value: value.count,
						name: value.name
					};
					logItem.push(item);
				});
				let optC = {
					tooltip: {
						trigger: 'item',
						formatter: '{a} <br/>{b} :{d}%'
					},
					legend: {
						top: '1%',
						left: 'center'
					},
					series: [
						{
							name: '活跃度',
							type: 'pie',
							radius: '50%',
							center: ['50%', '60%'],
							data: logItem,
							emphasis: {
								itemStyle: {
									shadowBlur: 10,
									shadowOffsetX: 0,
									shadowColor: 'rgba(0, 0, 0, 0.5)'
								}
							}
						}
					]
				};
				logChart.setOption(optC);
			}
		}
	})
}
</script>