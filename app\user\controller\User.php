<?php
/**
 * @copyright Copyright (c) 2021 仲正堂工作室
 * @license https://opensource.org/licenses/GPL-3.0
 * @link https://www.gougucms.com
 */

declare (strict_types=1);

namespace app\user\controller;

use app\api\controller\Export;
use app\base\BaseController;
use app\store\service\LizhiService;
use app\store\service\StoreService;
use app\user\model\Admin as AdminList;
use app\user\service\AdminService;
use app\user\validate\AdminCheck;
use avatars\MDAvatars;
use Overtrue\Pinyin\Pinyin;
use PhpOffice\PhpSpreadsheet\IOFactory;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use think\exception\ValidateException;
use think\facade\Db;
use think\facade\View;

class User extends BaseController
{
    public function index()
    {
        if (request()->isAjax()) {
            $param = get_params();
            $where = array();

            if (!empty($param['keywords'])) {
                $where[] = ['id|username|name|nickname|mobile|desc', 'like', '%' . $param['keywords'] . '%'];
            }

            if (isset($param['status']) && $param['status'] != '') {
                $where[] = ['status', '=', $param['status']];
            }

            if (!empty($param['type'])) {
                $where[] = ['type', '=', $param['type']];
            }
            if (isset($param['position_id']) && !empty($param['position_id'])) {
//                $where[] = ['position_id', 'find_in_set', $param['position_id']];
                $where[] = ['', 'exp', Db::raw("FIND_IN_SET('{$param['position_id']}',position_id)")];
            }

            if (isset($param['is_black']) && $param['is_black'] != '') {
                $where[] = ['is_black', '=', $param['is_black']];
            }

            if (isset($param['pay_type']) && !empty($param['pay_type'])) {
                $where[] = ['pay_type', '=', $param['pay_type']];
            }

            if (isset($param['rank_id']) && !empty($param['rank_id'])) {
                $where[] = ['rank_id', '=', $param['rank_id']];
            }

            if (isset($param['label_id']) && !empty($param['label_id'])) {
                $where[] = ['label_id', '=', $param['label_id']];
            }

            $whereor = [];
            if (!empty($param['did'])) {
                $dep_son = get_department_son($param['did']);
                foreach ($dep_son as $k => $v) {
                    $whereor[] = ['did', 'find in set', $v];
                }

                if ($param['did'] != 94) {
                    $where[] = ['did', 'not like', "%94%"];
                }
            } else {
                $where[] = ['is_virtually', '=', 0];
            }

            $rows = empty($param['limit']) ? get_config('app.page_size') : $param['limit'];

            $admin = AdminList::where($where)
                ->where(function ($query) use ($whereor) {
                    $query->whereOr($whereor);
                })
                ->order('id asc')
                ->paginate($rows, false, ['query' => $param])
                ->each(function ($item, $key) {



                    $department = Db::name('Department')
                        ->where([['id', 'in', $item->main_did]])
                        ->field("GROUP_CONCAT(title ORDER BY title SEPARATOR ',') as title")
                        ->find();
                    $item->department = $department['title'];

                    $position = Db::name('Position')
                        ->where([['id', 'in', $item->main_position_id]])
                        ->field("GROUP_CONCAT(title ORDER BY title SEPARATOR ',') as title")
                        ->find();
                    $item->position = $position['title'];

                    $admin_dep = Db::name("admin_dep")
                        ->alias("a")
                        ->join('department d', 'd.id = a.did', 'LEFT')
                        ->join('position p', 'p.id = a.position_id', 'LEFT')
                        ->where(['a.aid' => $item->id,'a.status' => 1])
                        ->field("d.title as dname , p.title as position_name")
                        ->select()
                        ->toArray();

                    $item->o_admin_dep = "";

                    foreach ($admin_dep as $k => $v) {
                        if (!empty($item->o_admin_dep)) {
                            $item->o_admin_dep .= " / {$v['dname']}-{$v['position_name']}";
                        } else {
                            $item->o_admin_dep = "{$v['dname']}-{$v['position_name']}";
                        }
                    }

                    $admin_service = new AdminService();
                    $lo_dep = $admin_service->getPdepartment($item->main_did);
                    $lo_dep = array_reverse($lo_dep);

                    $item->d0 = isset($lo_dep[0]) ? $lo_dep[0] : '';
                    $item->d1 = isset($lo_dep[1]) ? $lo_dep[1] : '';
                    $item->d2 = isset($lo_dep[2]) ? $lo_dep[2] : '';
                    $item->d3 = isset($lo_dep[3]) ? $lo_dep[3] : '';


                    $label_name = Db::name('StoreLabel')->where([['id', '=', $item->label_id]])->field('name')->find();
                    if (!empty($label_name)){
                        $item->label_name = $label_name['name'];
                    }

                    $item->entry_time = empty($item->entry_time) ? '-' : date('Y-m-d', $item->entry_time);
                    $item->res_date = empty($item->res_date) ? '-' : $item->res_date;
                    $item->last_login_time = empty($item->last_login_time) ? '-' : date('Y-m-d H:i', $item->last_login_time);
                    $item->last_login_ip = empty($item->last_login_ip) ? '-' : $item->last_login_ip;
                });

            return table_assign(0, '', $admin);
        } else {
            $position = Db::name("position")->where(['status' => 1])->select();
            $rank = Db::name('StoreRank')->select()->toArray();
            $label = Db::name('StoreLabel')->select()->toArray();
            View::assign("position", $position);
            View::assign("rank", $rank);
            View::assign("label", $label);
            return view();
        }
    }

    //添加
    public function add()
    {
        $param = get_params();

        if (request()->isAjax()) {
            $param['entry_time'] = strtotime($param['entry_time']);
            $param['nickname'] = $param['name'];

            //SCX
            if (empty($addparam['birthday'])) {
                unset($param['birthday']);
            }
            $pinyin = new Pinyin();
            $username = $pinyin->name($param['name'], PINYIN_UMLAUT_V);
            $param['username'] = implode('', $username);

            $admin_dep = array();
            if (isset($param['admin_dep'])){
                $admin_dep = $param['admin_dep'];
            }

            $admin_position = array();
            if (isset($param['admin_position'])){
                $admin_position = $param['admin_position'];
            }

            if (empty($param['res_date'])){
                $param['res_date'] = null;
            }

            if (!empty($param['id']) && $param['id'] > 0) {
                if ($param['id'] == 1) {
                    return to_assign(1, '超级员工资料不支持修改');
                }

                if (!empty($param['mobile'])) {
                    $param['username'] = $param['mobile'];
                }

                $count = Db::name('Admin')->where([['username', '=', $param['username']], ['id', '<>', $param['id']], ['status', '>=', 0]])->count();
                if ($count > 0) {
                    $count_e = Db::name('Admin')->where([['username', 'like', $param['username'] . '%']])->count();
                    $param['username'] = implode('', $username) . $count_e;
                }
                try {
                    validate(AdminCheck::class)->scene('edit')->check($param);
                } catch (ValidateException $e) {
                    // 验证失败 输出错误信息
                    return to_assign(1, $e->getError());
                }

                if (isset($param['is_valid'])  && $param['is_valid'] == 1){
                    if (empty($param['valid_start_date'])){
                        $param['valid_start_date'] = null;
                    }
                    if (empty($param['valid_end_date'])){
                        $param['valid_end_date'] = null;
                    }
                }else{
                    $param['is_valid']  = 0;

                    if ( empty($param['valid_start_date']) || empty($param['valid_end_date']) ){
                        return to_assign(1, "身份证不是永久，请填写开始结束日期");
                    }
                }

                // 启动事务
                Db::startTrans();
                try {

                    $main_did = $param['did'];
                    $main_position = $param['position_id'];
                    $did = $param['did'];
                    $position_id = $param['position_id'];


                    if (isset($param['admin_dep']) && !empty($param['admin_dep'])){
                        $admin_service = new AdminService();
                        $admin_service->insertAdminDep($param['id'] , $param['admin_dep'] , $param['admin_position']);
                        foreach ($param['admin_dep'] as $k => $v){
                            $did .= ",$v";
                            $position_id .= ",{$param['admin_position'][$k]}";
                        }
                    }

                    unset($param['admin_dep']);
                    unset($param['admin_position']);

                    $param['did'] = $did;
                    $param['position_id'] = $position_id;
                    $param['main_did'] = $main_did;
                    $param['main_position_id'] = $main_position;

                    Db::name('Admin')->where(['id' => $param['id']])->strict(false)->field(true)->update($param);
                    if (!isset($param['thumb']) || $param['thumb'] == '') {
                        $char = mb_substr($param['name'], 0, 1, 'utf-8');
                        Db::name('Admin')->where('id', $param['id'])->update(['thumb' => $this->to_avatars($char)]);
                    }

                    $admin_expand = Db::name('admin_expand')->where(['id' => $param['id']])->find();
                    if (!empty($admin_expand)) {
                        Db::name('admin_expand')->where(['id' => $param['id']])->strict(false)->field(true)->update($param);
                    } else {
                        $addparam = $param;
                        $addparam['id'] = $param['id'];
                        Db::name('admin_expand')->strict(false)->field(true)->insertGetId($addparam);
                    }


                    add_log('edit', $param['id'], $param);
                    //清除菜单\权限缓存
                    clear_cache('adminMenu');
                    // 提交事务
                    Db::commit();
                } catch (\Exception $e) {
                    // 回滚事务
                    Db::rollback();
                    return to_assign(1, '提交失败:' . $e->getMessage());
                }
            } else {
                $count = Db::name('Admin')->where([['username', 'like', $param['username'] . '%']])->count();
                if ($count > 0) {
                    $param['username'] = implode('', $username) . $count;
                }
                try {
                    validate(AdminCheck::class)->scene('add')->check($param);
                } catch (ValidateException $e) {
                    // 验证失败 输出错误信息
                    return to_assign(1, $e->getError());
                }

                $param['pwd'] = "52dab9db1f7d480b20bca9559bf64c01";
                $param['salt'] = "svm5wu27kgdpet031z6x";

                // 启动事务
                Db::startTrans();
                try {
                    $param['username'] = $param['mobile'];
                    $uid = Db::name('Admin')->strict(false)->field(true)->insertGetId($param);

                    Db::name('Admin')->where('id', $uid)->update(['workno' => numberStrPad($uid)]);


                    if (!isset($param['thumb']) || $param['thumb'] == '') {
                        //$char = mb_substr($param['name'], 0, 1, 'utf-8');
                        //Db::name('Admin')->where('id', $uid)->update(['thumb' => $this->to_avatars($char)]);
                        Db::name('Admin')->where('id', $uid)->update(['thumb' => "/static/home/<USER>/icon.png"]);
                    }
                    $addparam = $param;
                    $addparam['id'] = $uid;

                    $re_id = Db::name('admin_expand')->strict(false)->field(true)->insertGetId($addparam);

                    $admin_service = new AdminService();
                    $admin_service->syn_admin_openid($re_id);

                    add_log('add', $uid, $param);
                    // 提交事务
                    Db::commit();
                } catch (\Exception $e) {
                    // 回滚事务
                    Db::rollback();
                    return to_assign(1, '提交失败:' . $e->getMessage());
                }
            }
            return to_assign();
        } else {
            $id = isset($param['id']) ? $param['id'] : 0;
            $department = set_recursion(get_department());
            $position = Db::name('Position')->where('status', '>=', 0)->order('create_time asc')->select();
            $did = '';
            $position_ids = '';

            if ($id > 0) {
                $admin = Db::name('admin')->where(['id' => $id])->find();
                if (!empty($admin)) {
                    $did = $admin['main_did'];
                    $position_ids = $admin['main_position_id'];
                }
            }
            if ($id > 0) {
                $detail = get_admin($id);
                $admin_dep = Db::name("admin_dep")->where(['aid' => $id , 'status' => 1])->select()->toArray();
                View::assign('detail', $detail);
                View::assign('admin_dep', $admin_dep);
            } else {
                //初始化密码
                $reg_pwd = set_salt(6);
                View::assign('reg_pwd', $reg_pwd);
            }


            $rank = Db::name('StoreRank')->select()->toArray();
            $label = Db::name('StoreLabel')->select()->toArray();
            View::assign("rank", $rank);
            View::assign("label", $label);


            View::assign('department', $department);
            View::assign('position', $position);
            View::assign('id', $id);
            View::assign('did', $did);
            View::assign('position_ids', $position_ids);
            return view();
        }
    }

    //生成头像
    public function to_avatars($char)
    {
        $defaultData = array('A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N',
            'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'S', 'Y', 'Z',
            '0', '1', '2', '3', '4', '5', '6', '7', '8', '9',
            '零', '壹', '贰', '叁', '肆', '伍', '陆', '柒', '捌', '玖', '拾',
            '一', '二', '三', '四', '五', '六', '七', '八', '九', '十');
        if (isset($char)) {
            $Char = $char;
        } else {
            $Char = $defaultData[mt_rand(0, count($defaultData) - 1)];
        }
        $OutputSize = min(512, empty($_GET['size']) ? 36 : intval($_GET['size']));

        $Avatar = new MDAvatars($Char, 256, 1);
        $avatar_name = '/avatars/avatar_256_' . set_salt(10) . time() . '.png';
        $path = get_config('filesystem.disks.public.url') . $avatar_name;
        $res = $Avatar->Save('.' . $path, 256);
        $Avatar->Free();
        return $path;
    }

    //查看
    public function view()
    {
        $id = get_params('id');
        $detail = get_admin($id);

        $department = Db::name("Department")->where([
            ['id', 'in', "{$detail['did']}"]
        ])->column('title');
        $detail['department'] = implode(" / ", $department);
//        $position = Db::name("position")->where([
//            ['id', 'in', "{$detail['position_id']}"]
//        ])->column('title');
//        $detail['position'] = implode(" / ", $position);

        $detail['pay_type_dname'] = Db::name("Department")->where([
            ['id', '=', "{$detail['pay_type_did']}"]
        ])->value('title');



        //查询所有菜单和权限节点
        $menu = Db::name('AdminRule')->where(['menu' => 1])->order('sort asc,id asc')->select()->toArray();
        $rule = Db::name('AdminRule')->order('sort asc,id asc')->select()->toArray();

        //查询用户拥有的查单和节点
        $user_groups = Db::name('PositionGroup')
            ->alias('a')
            ->join("AdminGroup g", "a.group_id=g.id", 'LEFT')

            ->where([['a.pid', '=', $detail["position_id"]], ['g.status', '=', 1]])
            ->select()
            ->toArray();
        $groups = $user_groups ?: [];

        //获取人员面试id
        $admin_view = Db::name("admin_view")->where(['aid' => $id])->field("id")->find();

        $detail['a_view_id'] = empty($admin_view) ? 0 : $admin_view['id'];

        $admin_service = new AdminService();
        $lo_dep = $admin_service->getPdepartment($detail['main_did']);
        $lo_dep = array_reverse($lo_dep);

        $detail['d0'] = isset($lo_dep[0]) ? $lo_dep[0] : '';
        $detail['d1'] = isset($lo_dep[1]) ? $lo_dep[1] : '';
        $detail['d2'] = isset($lo_dep[2]) ? $lo_dep[2] : '';
        $detail['d3'] = isset($lo_dep[3]) ? $lo_dep[3] : '';

        $detail['rank_name'] = "";
        if (stripos($detail['d1'], "分管") !== false ){
            $groupId = Db::name('PositionGroup')->where(['pid' => $detail['main_position_id']])->column('group_id');
            $groupName = Db::name('AdminGroup')->where('id', 'in', $groupId)->column('title');
            $detail['rank_name'] = implode(",",$groupName);
        }


        $position = Db::name('Position')
            ->where([['id', 'in', $detail['main_position_id']]])
            ->field("GROUP_CONCAT(title ORDER BY title SEPARATOR ',') as title")
            ->find();
        $detail['position'] = $position['title'];

        $admin_dep = Db::name("admin_dep")
            ->alias("a")
            ->join('department d', 'd.id = a.did', 'LEFT')
            ->join('position p', 'p.id = a.position_id', 'LEFT')
            ->where(['a.aid' => $detail['id'],'a.status' => 1])
            ->field("d.title as dname , p.title as position_name")
            ->select()
            ->toArray();

        $detail['o_admin_dep'] = "";

        foreach ($admin_dep as $k => $v) {
            if (!empty($item->o_admin_dep)) {
                $detail['o_admin_dep'] .= " / {$v['dname']}-{$v['position_name']}";
            } else {
                $detail['o_admin_dep'] = "{$v['dname']}-{$v['position_name']}";
            }
        }

        $rules = [];
        foreach ($groups as $g) {
            $rules = array_merge($rules, explode(',', trim($g['rules'], ',')));
        }
        $rules = array_unique($rules);

        //数据嵌套
        $role_rule = create_tree_list(0, $rule, $rules);

        View::assign('role_rule', $role_rule);
        View::assign('detail', $detail);
        add_log('view', get_params('id'));
        return view();
    }

    //禁用,恢复
    public function set()
    {
        $type = get_params("type");
        $ids = get_params("ids");
        $idArray = explode(',', $ids);
        $list = [];
        foreach ($idArray as $key => $val) {
            if ($val == 1) {
                continue;
            }
            $list[] = [
                'status' => 2,
                'id' => $val,
                'update_time' => time(),
                'res_date' => date("Y-m-d")
            ];
        }
        foreach ($list as $key => $v) {
            Db::startTrans();

            $re = Db::name('Admin')->update($v);

            if ($re !== false) {
                $admin = Db::name("admin")->where(['id' => $v['id']])->find();
                $lizhiservice = new LizhiService();
                $data = [
                    'id' => 0,
                    'create_time' => time(),
                    'create_date' => date("Y-m-d"),
                    'transfer_date' => date("Y-m-d"),
                    'aid' => $v['id'],
                    'aname' => $admin['name'],
                    'n_did' => 0
                ];

                $department = Db::name("department")->where([
                    ['id', 'in', $admin['did']],
                    ['remark', '=', '门店']
                ])->find();

                if (!empty($department)) {
                    $data['o_did'] = $department['id'];
                    $data['o_dname'] = $department['title'];
                    $data['status'] = 1;
                    $data['dstatus'] = 2;
                } else {
                    $data['o_did'] = explode(',', $admin['did'])[0];
                    $data['o_dname'] = Db::name("department")->where(['id' => $data['o_did']])->column("title");
                    $data['status'] = 1;
                    $data['dstatus'] = 1;
                }
                try {
                    if ($type == 0) {

                        //添加调转记录
                        $lizhiservice->add_store_transfer($data);
                        //人才中心对应人信息修改
                        $lizhiservice->add_admin_view($data, $this->uid, $this->name);
                        $lizhiservice->deleteStoreBusiness($v['id'], $admin['did'], -1);
                        $lizhiservice->deleteStoreBusiness_t($v['id'], $admin['did'], -1);

                        $lizhiservice->deleteQiwei($admin['wx_account']);

                        add_log('disable', $v['id']);
                    } else if ($type == 1) {
                        Db::name('Admin')->where(['id' => $v['id']])->update(['status' => 1, 'res_date' => null]);
                        $lizhiservice->restore_admin_view($v['id']);
                        $lizhiservice->restore_store_transfer($v['id']);
                        $lizhiservice->deleteStoreBusiness($v['id'], $admin['did'], 1);
                        $lizhiservice->deleteStoreBusiness_t($v['id'], $admin['did'], 1);
                        add_log('recovery', $v['id']);
                    }
                    Db::commit();
                } catch (\Exception $e) {
                    var_dump($e);
                    //回滚事务
                    Db::rollback();
                }
            }
        }
        return to_assign(0, '操作成功');
    }

    //重置密码
    public function reset_psw()
    {
        $id = get_params("id");
        if ($id == 1) {
            return to_assign(1, '该账号是超级管理员，不允许重置');
        }
        $new_pwd = set_salt(6);
        $salt = set_salt(20);
        $data = [
            'reg_pwd' => $new_pwd,
            'salt' => $salt,
            'pwd' => set_password($new_pwd, $salt),
            'id' => $id,
            'update_time' => time(),
        ];
        if (Db::name('Admin')->update($data) !== false) {
            add_log('reset', $id);
            return to_assign(0, '操作成功');
        } else {
            return to_assign(1, '操作失败');
        }
    }

    //管理员操作日志
    public function log()
    {
        if (request()->isAjax()) {
            $param = get_params();
            $where = array();
            if (!empty($param['keywords'])) {
                $where[] = ['name|rule_menu|param_id', 'like', '%' . $param['keywords'] . '%'];
            }
            if (!empty($param['title_cate'])) {
                $where['title'] = $param['title_cate'];
            }
            if (!empty($param['rule_menu'])) {
                $where['rule_menu'] = $param['rule_menu'];
            }
            $rows = empty($param['limit']) ? get_config('app.page_size') : $param['limit'];
            $content = DB::name('AdminLog')
                ->field("id,uid,name,action,title,content,rule_menu,ip,param_id,param,FROM_UNIXTIME(create_time,'%Y-%m-%d %H:%i:%s') create_time")
                ->order('create_time desc')
                ->where($where)
                ->paginate($rows, false, ['query' => $param]);
            $content->toArray();
            foreach ($content as $k => $v) {
                $data = $v;
                $param_array = json_decode($v['param'], true);
                $param_value = '';
                foreach ($param_array as $key => $value) {
                    if (is_array($value)) {
                        $value = implode(',', $value);
                    }
                    $param_value .= $key . ':' . $value . '&nbsp;&nbsp;|&nbsp;&nbsp;';
                }
                $data['param'] = $param_value;
                $content->offsetSet($k, $data);
            }
            return table_assign(0, '', $content);
        } else {
            return view();
        }
    }


}





