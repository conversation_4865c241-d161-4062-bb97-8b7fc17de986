<?php
/**
 * @copyright Copyright (c) 2021 仲正堂工作室
 * @license https://opensource.org/licenses/GPL-3.0
 * @link https://www.gougucms.com
 */

declare (strict_types=1);

namespace app\dividend\controller;

use app\base\BaseController;
use app\dividend\validate\DividendDetailCheck;
use app\dividend\service\DividendDetailService;
use app\dividend\service\DividendCalculateService;
use app\dividend\service\DividendCompanyCalculateService;
use app\dividend\service\DividendPaymentCalculateService;
use think\exception\ValidateException;
use think\facade\Db;
use think\facade\View;
use think\facade\Log;
use think\App;
use systematic\ConfigManager;

// 门店股东分红明细表
class Detail extends BaseController
{
    protected $dividendDetailService;

    public function __construct(App $app)
    {
        parent::__construct($app);
        $this->dividendDetailService = new DividendDetailService();
    }

    /**
     * 检查当前用户是否有全部数据访问权限
     * @return bool
     */
    private function hasFullDataAccess()
    {
        try {
            // 获取当前用户信息
            $currentUser = get_admin($this->uid);
            if (!$currentUser) {
                return false;
            }

            // 从ConfigManager获取分红系统数据访问权限配置
            $permissions = ConfigManager::get('dividend_data_access_permissions', [
                'positions' => [],
                'users' => []
            ]);

            $allowedPositions = $permissions['positions'] ?? [];
            $allowedUsers = $permissions['users'] ?? [];

            // 检查用户ID是否在允许列表中
            if (in_array($this->uid, $allowedUsers)) {
                return true;
            }

            // 检查职位ID是否在允许列表中
            if (!empty($currentUser['position_id']) && in_array($currentUser['position_id'], $allowedPositions)) {
                return true;
            }

            return false;
        } catch (\Exception $e) {
            Log::error('检查分红数据访问权限失败: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * 检查当前用户是否有按钮操作权限
     * @return bool
     */
    private function hasButtonAccess()
    {
        try {
            // 从ConfigManager获取分红系统数据访问权限配置
            $permissions = ConfigManager::get('dividend_data_access_permissions', [
                'positions' => [],
                'users' => [],
                'button_access_users' => []
            ]);

            $buttonAccessUsers = $permissions['button_access_users'] ?? [];

            // 检查用户ID是否在按钮访问允许列表中
            return in_array($this->uid, $buttonAccessUsers);
        } catch (\Exception $e) {
            Log::error('检查分红按钮访问权限失败: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * 门店分红明细列表
     */
    public function index()
    {
        if (request()->isAjax()) {
            $param = get_params();

            // 构建查询条件
            $where = [];
            $personWhere = [];

            if (!empty($param['store_ids']) && is_array($param['store_ids'])) {
                // 通过门店ID数组搜索
                $where[] = ['dsd.store_id', 'in', $param['store_ids']];
            }
            if (!empty($param['period'])) {
                $where[] = ['dsd.period', '=', $param['period']];
            }
            if (!empty($param['shareholder_name'])) {
                // 通过分红人姓名搜索
                $personWhere[] = ['dsdp.shareholder_name', 'like', '%' . $param['shareholder_name'] . '%'];
            }
            $where[] = ['dsd.is_delete', '=', 0];
            $personWhere[] = ['dsdp.is_delete', '=', 0];

            // 数据权限检查：如果用户没有全部数据访问权限，则只能查看自己姓名匹配的数据
            if (!$this->hasFullDataAccess()) {
                $currentUser = get_admin($this->uid);
                if ($currentUser && !empty($currentUser['name'])) {
                    $personWhere[] = ['dsdp.shareholder_name', '=', $currentUser['name']];
                    Log::info('[门店分红]-[门店分红明细表]：应用数据权限限制，用户只能查看自己的数据，用户：' . $currentUser['name'] . '，用户ID：' . $this->uid);
                } else {
                    // 如果无法获取用户姓名，则不返回任何数据
                    $personWhere[] = ['dsdp.shareholder_name', '=', '__NO_DATA__'];
                    Log::info('[门店分红]-[门店分红明细表]：无法获取当前用户姓名，限制查看所有数据，用户ID：' . $this->uid);
                }
            }

            // 不分页查询 - 以分红人为维度，获取所有数据
            $listData = Db::name('DividendStoreDetailPerson')
                ->alias('dsdp')
                ->join('oa_dividend_store_detail dsd', 'dsd.id = dsdp.dividend_store_detail_id', 'INNER')
                ->join('oa_department d', 'd.id = dsd.store_id', 'LEFT')
                ->field('dsdp.*, dsd.store_id, dsd.period, dsd.income, dsd.expense, dsd.risk_reserve_current, dsd.other_adjustment as store_other_adjustment, dsd.dividend_profit, dsd.risk_reserve_total, d.title as store_name')
                ->where($where)
                ->where($personWhere)
                ->order('dsd.store_id asc, dsdp.shareholder_type asc, dsdp.id asc')
                ->select()
                ->toArray();

            Log::info('[门店分红]-[门店分红明细表]：查询门店分红明细列表，结果数量：' . count($listData) . '，用户ID：' . $this->uid);

            // 处理数据格式化
            $tableData = [];

            foreach ($listData as $index => $item) {
                // 安全地处理数值字段
                $income = is_numeric($item['income']) ? floatval($item['income']) : 0;
                $expense = is_numeric($item['expense']) ? floatval($item['expense']) : 0;
                $riskReserveCurrent = is_numeric($item['risk_reserve_current']) ? floatval($item['risk_reserve_current']) : 0;
                $storeOtherAdjustment = is_numeric($item['store_other_adjustment']) ? floatval($item['store_other_adjustment']) : 0;
                $dividendProfit = is_numeric($item['dividend_profit']) ? floatval($item['dividend_profit']) : 0;
                $riskReserveTotal = is_numeric($item['risk_reserve_total']) ? floatval($item['risk_reserve_total']) : 0;
                $shareholdingRatio = is_numeric($item['store_shareholding_ratio']) ? floatval($item['store_shareholding_ratio']) : 0;
                $payableAmount = is_numeric($item['payable_amount']) ? floatval($item['payable_amount']) : 0;
                $personOtherAdjustment = is_numeric($item['other_adjustment']) ? floatval($item['other_adjustment']) : 0;
                $actualPayableAmount = is_numeric($item['actual_payable_amount']) ? floatval($item['actual_payable_amount']) : 0;

                $rowData = [
                    'id' => $item['id'],
                    'store_id' => $item['store_id'],
                    'store_name' => $item['store_name'] ?? '未知门店',
                    'period' => $item['period'] ?? '',
                    'income' => number_format($income, 2),
                    'expense' => number_format($expense, 2),
                    'risk_reserve_current' => number_format($riskReserveCurrent, 2),
                    'other_adjustment' => number_format($storeOtherAdjustment, 2),
                    'dividend_profit' => number_format($dividendProfit, 2),
                    'risk_reserve_total' => number_format($riskReserveTotal, 2),
                    'shareholder_name' => $item['shareholder_name'] ?? '未知分红人',
                    'store_shareholding_ratio' => number_format($shareholdingRatio, 3),
                    'payable_amount' => number_format($payableAmount, 2),
                    'person_other_adjustment' => number_format($personOtherAdjustment, 2),
                    'actual_payable_amount' => number_format($actualPayableAmount, 2)
                ];

                $tableData[] = $rowData;
            }

            // 计算合计行数据 - 根据当前显示的数据进行统计
            $totalIncome = 0;
            $totalExpense = 0;
            $totalRiskReserveCurrent = 0;
            $totalOtherAdjustment = 0;
            $totalDividendProfit = 0;
            $totalRiskReserveTotal = 0;
            $totalPayableAmount = 0;
            $totalPersonOtherAdjustment = 0;
            $totalActualPayableAmount = 0;

            // 用于去重的门店ID集合
            $processedStores = [];

            foreach ($listData as $item) {
                $storeKey = $item['store_id'] . '_' . $item['period'];

                // 门店维度的数据只统计一次（避免因多个分红人导致重复统计）
                if (!isset($processedStores[$storeKey])) {
                    $totalIncome += floatval($item['income'] ?? 0);
                    $totalExpense += floatval($item['expense'] ?? 0);
                    $totalRiskReserveCurrent += floatval($item['risk_reserve_current'] ?? 0);
                    $totalOtherAdjustment += floatval($item['store_other_adjustment'] ?? 0);
                    $totalDividendProfit += floatval($item['dividend_profit'] ?? 0);
                    $totalRiskReserveTotal += floatval($item['risk_reserve_total'] ?? 0);

                    $processedStores[$storeKey] = true;
                }

                // 分红人维度的数据每条记录都要统计
                $totalPayableAmount += floatval($item['payable_amount'] ?? 0);
                $totalPersonOtherAdjustment += floatval($item['other_adjustment'] ?? 0);
                $totalActualPayableAmount += floatval($item['actual_payable_amount'] ?? 0);
            }

            $totalRow = [];
            if (count($listData) > 0) {
                $totalRow = [
                    'store_name' => '合计',
                    'income' => number_format($totalIncome, 2),
                    'expense' => number_format($totalExpense, 2),
                    'risk_reserve_current' => number_format($totalRiskReserveCurrent, 2),
                    'other_adjustment' => number_format($totalOtherAdjustment, 2),
                    'dividend_profit' => number_format($totalDividendProfit, 2),
                    'risk_reserve_total' => number_format($totalRiskReserveTotal, 2),
                    'shareholder_name' => '-',
                    'store_shareholding_ratio' => '-',
                    'payable_amount' => number_format($totalPayableAmount, 2),
                    'person_other_adjustment' => number_format($totalPersonOtherAdjustment, 2),
                    'actual_payable_amount' => number_format($totalActualPayableAmount, 2)
                ];
            }

            // 返回响应数据
            $response = [
                'code' => 0,
                'msg' => '获取成功',
                'count' => count($tableData),
                'data' => $tableData,
                'totalRow' => $totalRow,
                'hasButtonAccess' => $this->hasButtonAccess() // 添加按钮权限信息
            ];

            return json($response);
        } else {
            // 记录查看门店分红明细列表页面日志
            add_log('view', 0, [], '[门店分红]-[门店分红明细表]：查看门店分红明细列表页面');
            Log::info('[门店分红]-[门店分红明细表]：用户查看门店分红明细列表页面，用户ID：' . $this->uid);
            return View::fetch();
        }
    }

    /**
     * 编辑页面
     */
    public function edit()
    {
        // 记录查看门店分红明细编辑页面日志
        add_log('view', 0, [], '[门店分红]-[门店分红明细表]：查看门店分红明细编辑页面');
        Log::info('[门店分红]-[门店分红明细表]：用户查看门店分红明细编辑页面，用户ID：' . $this->uid);
        return View::fetch();
    }

    /**
     * 获取门店详细数据（用于编辑）
     */
    public function getStoreDetail()
    {
        $param = get_params();

        try {
            // 验证参数
            if (empty($param['store_id']) || empty($param['period'])) {
                Log::info('[门店分红]-[门店分红明细表]：获取门店详细数据参数错误，用户ID：' . $this->uid);
                return to_assign(1, '参数错误');
            }

            // 获取门店基础信息
            $storeDetail = Db::name('DividendStoreDetail')
                ->alias('dsd')
                ->join('oa_department d', 'd.id = dsd.store_id', 'LEFT')
                ->field('dsd.*, d.title as store_name')
                ->where('dsd.store_id', $param['store_id'])
                ->where('dsd.period', $param['period'])
                ->where('dsd.is_delete', 0)
                ->find();

            if (!$storeDetail) {
                Log::info('[门店分红]-[门店分红明细表]：未找到门店数据，门店ID：' . $param['store_id'] . '，周期：' . $param['period'] . '，用户ID：' . $this->uid);
                return to_assign(1, '未找到门店数据');
            }

            // 获取股东信息
            $shareholders = Db::name('DividendStoreDetailPerson')
                ->where('dividend_store_detail_id', $storeDetail['id'])
                ->where('is_delete', 0)
                ->order('id asc')
                ->select()
                ->toArray();

            // 格式化数据
            $data = [
                'store_id' => $storeDetail['store_id'],
                'store_name' => $storeDetail['store_name'] ?? '未知门店',
                'period' => $storeDetail['period'],
                'income' => number_format(floatval($storeDetail['income'] ?? 0), 2),
                'expense' => number_format(floatval($storeDetail['expense'] ?? 0), 2),
                'risk_reserve_current' => number_format(floatval($storeDetail['risk_reserve_current'] ?? 0), 2),
                'other_adjustment' => floatval($storeDetail['other_adjustment'] ?? 0),
                'dividend_profit' => number_format(floatval($storeDetail['dividend_profit'] ?? 0), 2),
                'risk_reserve_total' => number_format(floatval($storeDetail['risk_reserve_total'] ?? 0), 2),
                'remark' => $storeDetail['remark'] ?? '', // 添加备注字段
                'shareholders' => []
            ];

            // 格式化股东数据
            foreach ($shareholders as $shareholder) {
                $data['shareholders'][] = [
                    'id' => $shareholder['id'],
                    'shareholder_name' => $shareholder['shareholder_name'],
                    'store_shareholding_ratio' => floatval($shareholder['store_shareholding_ratio'] ?? 0),
                    'payable_amount' => floatval($shareholder['payable_amount'] ?? 0),
                    'other_adjustment' => floatval($shareholder['other_adjustment'] ?? 0),
                    'actual_payable_amount' => floatval($shareholder['actual_payable_amount'] ?? 0),
                    'remark' => $shareholder['remark'] ?? ''
                ];
            }

            // 记录获取门店详细数据日志
            add_log('view', $param['store_id'], ['store_name' => $storeDetail['store_name'], 'period' => $param['period']], '[门店分红]-[门店分红明细表]：获取门店详细数据');
            Log::info('[门店分红]-[门店分红明细表]：获取门店详细数据成功，门店：' . $storeDetail['store_name'] . '，周期：' . $param['period'] . '，用户ID：' . $this->uid);

        } catch (\Exception $e) {
            Log::info('[门店分红]-[门店分红明细表]：获取门店详细数据失败，错误信息：' . $e->getMessage() . '，用户ID：' . $this->uid);
            return to_assign(1, '获取数据失败');
        }
        return to_assign(0, '获取成功', $data);
    }

    /**
     * 更新门店详细数据
     */
    public function updateStoreDetail()
    {
        $param = get_params();

        // 验证参数
        if (empty($param['store_id']) || empty($param['period'])) {
            Log::info('[门店分红]-[门店分红明细表]：更新门店详细数据参数错误，用户ID：' . $this->uid);
            return to_assign(1, '参数错误');
        }

        try {

            // 开启事务
            Db::startTrans();

            // 获取门店详细记录
            $storeDetail = Db::name('DividendStoreDetail')
                ->where('store_id', $param['store_id'])
                ->where('period', $param['period'])
                ->where('is_delete', 0)
                ->find();

            if (!$storeDetail) {
                Db::rollback();
                Log::info('[门店分红]-[门店分红明细表]：更新时未找到门店数据，门店ID：' . $param['store_id'] . '，周期：' . $param['period'] . '，用户ID：' . $this->uid);
                return to_assign(1, '未找到门店数据');
            }

            // 更新门店基础信息
            $updateData = [
                'other_adjustment' => floatval($param['other_adjustment'] ?? 0),
                'remark' => trim($param['remark'] ?? ''), // 添加备注字段更新
                'update_time' => time()
            ];

            // 重新计算分红利润
            $income = floatval($storeDetail['income'] ?? 0);
            $expense = floatval($storeDetail['expense'] ?? 0);
            $riskReserveCurrent = floatval($storeDetail['risk_reserve_current'] ?? 0);
            $otherAdjustment = $updateData['other_adjustment'];
            $updateData['dividend_profit'] = $income - $expense - $riskReserveCurrent - $otherAdjustment;

            Db::name('DividendStoreDetail')
                ->where('id', $storeDetail['id'])
                ->update($updateData);

            // 删除原有股东数据
            Db::name('DividendStoreDetailPerson')
                ->where('dividend_store_detail_id', $storeDetail['id'])
                ->update(['is_delete' => 1, 'delete_time' => time()]);

            // 添加新的股东数据
            if (!empty($param['shareholders']) && is_array($param['shareholders'])) {
                foreach ($param['shareholders'] as $shareholder) {
                    if (empty($shareholder['shareholder_name'])) {
                        continue; // 跳过空的股东姓名
                    }

                    $shareholderData = [
                        'dividend_store_detail_id' => $storeDetail['id'],
                        'shareholder_name' => trim($shareholder['shareholder_name']),
                        'store_shareholding_ratio' => floatval($shareholder['store_shareholding_ratio'] ?? 0),
                        'payable_amount' => floatval($shareholder['payable_amount'] ?? 0),
                        'other_adjustment' => floatval($shareholder['other_adjustment'] ?? 0),
                        'actual_payable_amount' => floatval($shareholder['actual_payable_amount'] ?? 0),
                        'remark' => trim($shareholder['remark'] ?? ''),
                        'shareholder_type' => 1, // 默认为个人股东
                        'create_time' => time(),
                        'update_time' => time(),
                        'is_delete' => 0
                    ];

                    Db::name('DividendStoreDetailPerson')->insert($shareholderData);
                }
            }

            // 提交事务
            Db::commit();

            // 触发公司股东分红明细表的重新计算
            $this->recalculateCompanyDividend($param['store_id'], $param['period']);

            // 触发股东分红清单的重新计算
            $this->recalculatePaymentList($param['period']);

            // 获取门店名称用于日志记录
            $storeName = Db::name('Department')
                ->where('id', $param['store_id'])
                ->value('title') ?: '未知门店';

            // 记录操作日志
            add_log('edit', $param['store_id'], json_encode($param, JSON_UNESCAPED_UNICODE), '[门店分红]-[门店分红明细表]：更新门店分红明细数据');
            Log::info('[门店分红]-[门店分红明细表]：更新门店分红明细数据成功，门店：' . $storeName . '，周期：' . $param['period'] . '，用户ID：' . $this->uid);

        } catch (\Exception $e) {
            Db::rollback();
            Log::info('[门店分红]-[门店分红明细表]：更新门店详细数据失败，错误信息：' . $e->getMessage() . '，用户ID：' . $this->uid);
            return to_assign(1, '保存失败：' . $e->getMessage());
        }

        // 重要的事情：try 代码块不能包括 to_assign ，因为 to_assign 内部会主动抛出异常
        return to_assign(0, '保存成功');
    }

    /**
     * 重新计算公司股东分红明细表
     * @param int $storeId 门店ID
     * @param string $period 统计周期
     * @return void
     */
    private function recalculateCompanyDividend($storeId, $period)
    {
        try {
            // 1. 检查是否存在该门店该周期的公司股东分红表
            $companyDetail = Db::name('DividendCompanyDetail')
                ->where('store_id', $storeId)
                ->where('period', $period)
                ->where('is_delete', 0)
                ->find();

            if (!$companyDetail) {
                // 如果不存在公司股东分红表，则不需要重新计算
                return;
            }

            // 2. 获取原有各个股东的调整金额
            $originalAdjustments = [];
            $originalPersons = Db::name('DividendCompanyDetailPerson')
                ->where('company_detail_id', $companyDetail['id'])
                ->where('is_delete', 0)
                ->field('shareholder_id, adjustment_amount')
                ->select()
                ->toArray();

            foreach ($originalPersons as $person) {
                $originalAdjustments[$person['shareholder_id']] = floatval($person['adjustment_amount'] ?? 0);
            }

            // 3. 从门店分红明细表获取最新的公司分红数据
            $companyDividendData = Db::name('DividendStoreDetailPerson')
                ->alias('dsdp')
                ->join('oa_dividend_store_detail dsd', 'dsd.id = dsdp.dividend_store_detail_id', 'INNER')
                ->where([
                    'dsd.store_id' => $storeId,
                    'dsd.period' => $period,
                    'dsdp.shareholder_name' => '公司',
                    'dsd.is_delete' => 0,
                    'dsdp.is_delete' => 0
                ])
                ->field('dsdp.actual_payable_amount as dividend_profit, dsdp.store_shareholding_ratio as company_shareholding_ratio')
                ->find();

            if (!$companyDividendData) {
                // 如果没有找到公司分红数据，则不需要重新计算
                return;
            }

            $dividendProfit = floatval($companyDividendData['dividend_profit']);
            $companyShareholdingRatio = floatval($companyDividendData['company_shareholding_ratio']);

            // 4. 更新公司分红明细主表
            Db::name('DividendCompanyDetail')
                ->where('id', $companyDetail['id'])
                ->update([
                    'dividend_profit' => $dividendProfit,
                    'company_shareholding_ratio' => $companyShareholdingRatio,
                    'update_time' => time()
                ]);

            // 5. 获取门店分红信息配置
            $storeInfo = Db::name('DividendStoreInfo')
                ->where('store_id', $storeId)
                ->where('is_delete', 0)
                ->find();

            if (!$storeInfo) {
                return;
            }

            // 6. 软删除原有的分红人记录
            Db::name('DividendCompanyDetailPerson')
                ->where('company_detail_id', $companyDetail['id'])
                ->where('is_delete', 0)
                ->update([
                    'is_delete' => 1,
                    'delete_time' => time(),
                    'update_time' => time()
                ]);

            // 7. 获取公司股东信息并重新计算
            $companyShareholders = Db::name('DividendShareholder')
                ->where([
                    'dividend_store_info_id' => $storeInfo['id'],
                    'shareholder_type' => 1, // 公司股东类型
                    'is_delete' => 0
                ])
                ->order('sort_order asc, id asc')
                ->select()
                ->toArray();

            foreach ($companyShareholders as $shareholder) {
                // 计算金额：分红利润 * 持股比例
                $amount = $dividendProfit * ($shareholder['company_shareholding_ratio'] / 100);

                // 获取原有的调整金额
                $adjustmentAmount = $originalAdjustments[$shareholder['id']] ?? 0;

                // 计算应付金额：金额 - 调整金额
                $payableAmount = $amount - $adjustmentAmount;

                $personRecord = [
                    'company_detail_id' => $companyDetail['id'],
                    'shareholder_id' => $shareholder['id'],
                    'shareholder_name' => $shareholder['shareholder_name'],
                    'shareholding_ratio' => $shareholder['company_shareholding_ratio'],
                    'actual_shareholding' => $shareholder['store_shareholding_ratio'],
                    'amount' => $amount,
                    'adjustment_amount' => $adjustmentAmount,
                    'payable_amount' => $payableAmount,
                    'remark' => '',
                    'create_time' => time(),
                    'update_time' => time(),
                    'is_delete' => 0
                ];

                Db::name('DividendCompanyDetailPerson')->insert($personRecord);
            }

            Log::info('[门店分红]-[门店分红明细表]：公司股东分红明细表重新计算完成，门店ID：' . $storeId . '，周期：' . $period);

        } catch (\Exception $e) {
            Log::info('[门店分红]-[门店分红明细表]：公司股东分红明细表重新计算失败，门店ID：' . $storeId . '，周期：' . $period . '，错误信息：' . $e->getMessage());
        }
    }

    /**
     * 检查指定周期的计算状态
     */
    public function checkCalculateStatus()
    {
        $param = get_params();
        if (empty($param['period'])) {
            Log::info('[门店分红]-[门店分红明细表]：检查计算状态参数错误，缺少统计周期，用户ID：' . $this->uid);
            return to_assign(1, '参数错误：缺少统计周期');
        }
        try {

            $period = $param['period'];

            // 简单判断：检查oa_dividend_store_detail表中是否存在该周期的数据
            $existingCount = Db::name('DividendStoreDetail')
                ->where('period', $period)
                ->where('is_delete', 0)
                ->count();

            $calculated = $existingCount > 0;

            // 记录检查计算状态日志
            // add_log('view', 0, ['period' => $period], '[门店分红]-[门店分红明细表]：检查计算状态');
            Log::info('[门店分红]-[门店分红明细表]：检查计算状态，周期：' . $period . '，数据条数：' . $existingCount . '，已计算：' . ($calculated ? 'true' : 'false') . '，用户ID：' . $this->uid);

        } catch (\Exception $e) {
            Log::info('[门店分红]-[门店分红明细表]：检查计算状态失败，错误信息：' . $e->getMessage() . '，用户ID：' . $this->uid);
            return to_assign(1, '检查失败');
        }

        return to_assign(0, '检查完成', [
            'period' => $period,
            'calculated' => $calculated,
            'data_count' => $existingCount
        ]);
    }

    /**
     * 执行完整分红数据计算
     */
    public function calculateDividend()
    {
        $param = get_params();

        // 权限检查：只有有按钮权限的用户可以执行计算
        if (!$this->hasButtonAccess()) {
            Log::info('[门店分红]-[门店分红明细表]：用户无权限计算分红数据，用户ID：' . $this->uid);
            return to_assign(1, '无权限操作');
        }

        if (!$this->uid) {
            Log::info('[门店分红]-[门店分红明细表]：执行分红计算时用户未登录');
            return to_assign(1, '请先登录');
        }

        if (empty($param['period'])) {
            Log::info('[门店分红]-[门店分红明细表]：执行分红计算参数错误，缺少统计周期，用户ID：' . $this->uid);
            return to_assign(1, '参数错误：缺少统计周期');
        }

        $period = $param['period'];
        $adminId = $this->uid; // 获取当前用户ID

        // 验证周期格式（YYYY-MM）
        if (!preg_match('/^\d{4}-\d{2}$/', $period)) {
            Log::info('[门店分红]-[门店分红明细表]：执行分红计算周期格式错误，周期：' . $period . '，用户ID：' . $this->uid);
            return to_assign(1, '统计周期格式错误，应为YYYY-MM格式');
        }

        // 验证只能计算上个月的数据
        $currentDate = new \DateTime();
        $lastMonth = new \DateTime();
        $lastMonth->modify('first day of last month');
        $expectedPeriod = $lastMonth->format('Y-m');

        if ($period !== $expectedPeriod) {
            Log::info('[门店分红]-[门店分红明细表]：执行分红计算周期不符合要求，期望周期：' . $expectedPeriod . '，实际周期：' . $period . '，用户ID：' . $this->uid);
            return to_assign(1, "只能计算上个月（{$expectedPeriod}）的分红数据");
        }

        // 检查是否已存在该周期的数据（简单判断）
        $existingCount = Db::name('DividendStoreDetail')
            ->where('period', $period)
            ->where('is_delete', 0)
            ->count();

        if ($existingCount > 0) {
            Log::info('[门店分红]-[门店分红明细表]：分红数据已存在，无需重复计算，周期：' . $period . '，用户ID：' . $this->uid);
            return to_assign(1, "周期 {$period} 的分红数据已存在，无需重复计算");
        }

        try {

            Log::info('[门店分红]-[门店分红明细表]：开始执行完整分红数据计算，周期：' . $period . '，操作人：' . $adminId);
            $startTime = time();

            // 第一步：门店分红计算
            Log::info('[门店分红]-[门店分红明细表]：第一步，执行门店分红计算');
            $stepStartTime = time();

            $storeResult = DividendCalculateService::calculateDividend($period, $adminId);

            if (!$storeResult['success']) {
                Log::info('[门店分红]-[门店分红明细表]：门店分红计算失败，错误信息：' . $storeResult['message']);
                throw new \Exception("门店分红计算失败：" . $storeResult['message']);
            }

            $stepDuration = time() - $stepStartTime;
            Log::info('[门店分红]-[门店分红明细表]：门店分红计算成功，消息：' . $storeResult['message'] . '，耗时：' . $stepDuration . '秒');

            // 第二步：公司股东分红计算
            Log::info('[门店分红]-[门店分红明细表]：第二步，执行公司股东分红计算');
            $stepStartTime = time();

            $companyResult = DividendCompanyCalculateService::calculateCompanyDividend($period, $adminId);

            if (!$companyResult['success']) {
                Log::info('[门店分红]-[门店分红明细表]：公司股东分红计算失败，错误信息：' . $companyResult['message']);
                throw new \Exception("公司股东分红计算失败：" . $companyResult['message']);
            }

            $stepDuration = time() - $stepStartTime;
            Log::info('[门店分红]-[门店分红明细表]：公司股东分红计算成功，消息：' . $companyResult['message'] . '，耗时：' . $stepDuration . '秒');

            // 第三步：分红人清单计算
            Log::info('[门店分红]-[门店分红明细表]：第三步，执行分红人清单计算');
            $stepStartTime = time();
            $paymentResult = DividendPaymentCalculateService::calculatePaymentData($period, $adminId);

            if (!$paymentResult['success']) {
                Log::info('[门店分红]-[门店分红明细表]：分红人清单计算失败，错误信息：' . $paymentResult['message']);
                throw new \Exception("分红人清单计算失败：" . $paymentResult['message']);
            }

            $stepDuration = time() - $stepStartTime;
            Log::info('[门店分红]-[门店分红明细表]：分红人清单计算成功，消息：' . $paymentResult['message'] . '，耗时：' . $stepDuration . '秒');

            $totalDuration = time() - $startTime;

            Log::info('[门店分红]-[门店分红明细表]：完整分红数据计算完成，周期：' . $period . '，总耗时：' . $totalDuration . '秒，用户ID：' . $this->uid);

        } catch (\Exception $e) {
            Log::info('[门店分红]-[门店分红明细表]：执行分红数据计算失败，错误信息：' . $e->getMessage() . '，用户ID：' . $this->uid);
            return to_assign(1, '计算失败：' . $e->getMessage());
        }

        // 记录操作日志
        add_log('edit', 0, json_encode([
            'period' => $period,
            'store_count' => count($storeResult['data'] ?? []),
            'company_count' => count($companyResult['data'] ?? []),
            'payment_count' => count($paymentResult['data'] ?? []),
            'total_duration' => $totalDuration
        ], JSON_UNESCAPED_UNICODE), '[门店分红]-[门店分红明细表]：完整分红数据计算');

        return to_assign(0, "分红数据计算完成！共处理门店：" . count($storeResult['data'] ?? []) . " 个", [
            'period' => $period,
            'store_result' => $storeResult,
            'company_result' => $companyResult,
            'payment_result' => $paymentResult,
            'total_duration' => $totalDuration
        ]);
    }

    /**
     * 重新计算股东分红清单
     * @param string $period 统计周期
     * @return void
     */
    private function recalculatePaymentList($period)
    {
        try {
            // 获取该周期所有分红人的列表
            $existingPayments = Db::name('DividendPayment')
                ->where('period', $period)
                ->where('is_delete', 0)
                ->field('shareholder_name, adjustment_amount, paid_amount, remark')
                ->select()
                ->toArray();

            // 如果没有现有数据，则不需要重新计算
            if (empty($existingPayments)) {
                Log::info('[门店分红]-[门店分红明细表]：股东分红清单重新计算跳过，周期：' . $period . '，原因：无现有数据');
                return;
            }

            // 保存原有的调整金额、实付金额和备注信息
            $originalData = [];
            foreach ($existingPayments as $payment) {
                $originalData[$payment['shareholder_name']] = [
                    'adjustment_amount' => floatval($payment['adjustment_amount'] ?? 0),
                    'paid_amount' => floatval($payment['paid_amount'] ?? 0),
                    'remark' => $payment['remark'] ?? ''
                ];
            }

            // 1. 从门店分红人子表获取个人股东数据
            $personalShareholderData = $this->getPersonalShareholderDataForPayment($period);

            // 2. 从公司股东分红人子表获取公司股东数据
            $companyShareholderData = $this->getCompanyShareholderDataForPayment($period);

            // 3. 合并数据并按分红人姓名分组
            $shareholderGroups = $this->groupShareholderDataForPayment($personalShareholderData, $companyShareholderData);

            // 4. 为每个分红人重新计算并保存数据
            foreach ($shareholderGroups as $shareholderName => $shareholderInfo) {
                // 获取原有的调整金额、实付金额和备注
                $originalInfo = $originalData[$shareholderName] ?? [
                    'adjustment_amount' => 0,
                    'paid_amount' => 0,
                    'remark' => ''
                ];

                $adjustmentAmount = $originalInfo['adjustment_amount'];
                $paidAmount = $originalInfo['paid_amount'];
                $actualPayableAmount = $shareholderInfo['total_amount'] - $adjustmentAmount;
                $unpaidAmount = $actualPayableAmount - $paidAmount;

                $paymentData = [
                    'shareholder_name' => $shareholderName,
                    'shareholder_ids' => implode(',', $shareholderInfo['shareholder_ids']),
                    'period' => $period,
                    'payable_amount' => $shareholderInfo['total_amount'],
                    'adjustment_amount' => $adjustmentAmount, // 保留原有调整金额
                    'actual_payable_amount' => $actualPayableAmount,
                    'paid_amount' => $paidAmount, // 保留原有实付金额
                    'unpaid_amount' => $unpaidAmount,
                    'remark' => $originalInfo['remark'], // 保留原有备注
                    'create_time' => time(),
                    'update_time' => time()
                ];

                // 使用软删除再创建的方式更新数据
                $this->savePaymentDataWithSoftDelete($paymentData);
            }

            Log::info('[门店分红]-[门店分红明细表]：股东分红清单重新计算完成，周期：' . $period . '，处理分红人数：' . count($shareholderGroups));

        } catch (\Exception $e) {
            Log::info('[门店分红]-[门店分红明细表]：股东分红清单重新计算失败，周期：' . $period . '，错误信息：' . $e->getMessage());
        }
    }

    /**
     * 从门店分红人子表获取个人股东数据（用于分红清单计算）
     * @param string $period 统计周期
     * @return array
     */
    private function getPersonalShareholderDataForPayment($period)
    {
        return Db::name('DividendStoreDetailPerson')
            ->alias('dsdp')
            ->join('oa_dividend_store_detail dsd', 'dsd.id = dsdp.dividend_store_detail_id', 'INNER')
            ->where([
                'dsd.period' => $period,
                'dsdp.shareholder_type' => 2, // 个人股东
                'dsd.is_delete' => 0,
                'dsdp.is_delete' => 0
            ])
            ->field([
                'dsdp.shareholder_id',
                'dsdp.shareholder_name',
                'dsdp.actual_payable_amount',
                'dsd.store_id'
            ])
            ->select()
            ->toArray();
    }

    /**
     * 从公司股东分红人子表获取公司股东数据（用于分红清单计算）
     * @param string $period 统计周期
     * @return array
     */
    private function getCompanyShareholderDataForPayment($period)
    {
        return Db::name('DividendCompanyDetailPerson')
            ->alias('dcdp')
            ->join('oa_dividend_company_detail dcd', 'dcd.id = dcdp.company_detail_id', 'INNER')
            ->where([
                'dcd.period' => $period,
                'dcd.is_delete' => 0,
                'dcdp.is_delete' => 0
            ])
            ->field([
                'dcdp.shareholder_id',
                'dcdp.shareholder_name',
                'dcdp.payable_amount',
                'dcd.store_id'
            ])
            ->select()
            ->toArray();
    }

    /**
     * 合并数据并按分红人姓名分组（用于分红清单计算）
     * @param array $personalData 个人股东数据
     * @param array $companyData 公司股东数据
     * @return array
     */
    private function groupShareholderDataForPayment($personalData, $companyData)
    {
        $shareholderGroups = [];

        // 处理个人股东数据
        foreach ($personalData as $item) {
            $name = $item['shareholder_name'];
            if (!isset($shareholderGroups[$name])) {
                $shareholderGroups[$name] = [
                    'total_amount' => 0,
                    'shareholder_ids' => [],
                    'store_ids' => []
                ];
            }

            $shareholderGroups[$name]['total_amount'] += floatval($item['actual_payable_amount']);

            // 记录股东ID（避免重复）
            if (!in_array($item['shareholder_id'], $shareholderGroups[$name]['shareholder_ids'])) {
                $shareholderGroups[$name]['shareholder_ids'][] = $item['shareholder_id'];
            }

            // 记录门店ID（避免重复）
            if (!in_array($item['store_id'], $shareholderGroups[$name]['store_ids'])) {
                $shareholderGroups[$name]['store_ids'][] = $item['store_id'];
            }
        }

        // 处理公司股东数据
        foreach ($companyData as $item) {
            $name = $item['shareholder_name'];
            if (!isset($shareholderGroups[$name])) {
                $shareholderGroups[$name] = [
                    'total_amount' => 0,
                    'shareholder_ids' => [],
                    'store_ids' => []
                ];
            }

            $shareholderGroups[$name]['total_amount'] += floatval($item['payable_amount']);

            // 记录股东ID（避免重复）
            if (!in_array($item['shareholder_id'], $shareholderGroups[$name]['shareholder_ids'])) {
                $shareholderGroups[$name]['shareholder_ids'][] = $item['shareholder_id'];
            }

            // 记录门店ID（避免重复）
            if (!in_array($item['store_id'], $shareholderGroups[$name]['store_ids'])) {
                $shareholderGroups[$name]['store_ids'][] = $item['store_id'];
            }
        }

        return $shareholderGroups;
    }

    /**
     * 使用软删除再创建的方式保存分红清单数据
     * @param array $data 数据
     * @return bool
     */
    private function savePaymentDataWithSoftDelete($data)
    {
        // 检查是否存在记录
        $existing = Db::name('DividendPayment')
            ->where('is_delete', 0)
            ->where('shareholder_name', $data['shareholder_name'])
            ->where('period', $data['period'])
            ->find();

        if ($existing) {
            // 修改时先软删除再创建，实现修改留痕
            $deleteTime = time();

            // 1. 软删除现有记录（为避免唯一索引冲突，修改分红人姓名添加时间戳后缀）
            Db::name('DividendPayment')
                ->where('id', $existing['id'])
                ->update([
                    'shareholder_name' => $existing['shareholder_name'] . '_deleted_' . $deleteTime,
                    'is_delete' => 1,
                    'delete_time' => $deleteTime,
                    'update_time' => time()
                ]);

            // 2. 创建新记录
            return Db::name('DividendPayment')->insert($data);
        } else {
            // 创建新记录
            return Db::name('DividendPayment')->insert($data);
        }
    }

    /**
     * 清理指定周期的分红数据
     */
    public function clearDividendData()
    {
        $param = get_params();

        
        // 权限检查：只有有按钮权限的用户可以执行清理
        if (!$this->hasButtonAccess()) {
            Log::info('[门店分红]-[门店分红明细表]：用户无权限清理分红数据，用户ID：' . $this->uid);
            return to_assign(1, '无权限操作');
        }

        if (empty($param['period'])) {
            Log::info('[门店分红]-[门店分红明细表]：清理分红数据参数错误，缺少统计周期，用户ID：' . $this->uid);
            return to_assign(1, '参数错误：缺少统计周期');
        }

        $period = $param['period'];

        // 验证周期格式（YYYY-MM）
        if (!preg_match('/^\d{4}-\d{2}$/', $period)) {
            Log::info('[门店分红]-[门店分红明细表]：清理分红数据周期格式错误，周期：' . $period . '，用户ID：' . $this->uid);
            return to_assign(1, '统计周期格式错误，应为YYYY-MM格式');
        }
        
        // 验证只能清理上个月的数据
        $currentDate = new \DateTime();
        $lastMonth = new \DateTime();
        $lastMonth->modify('first day of last month');
        $expectedPeriod = $lastMonth->format('Y-m');

        if ($period !== $expectedPeriod) {
            Log::info('[门店分红]-[门店分红明细表]：清理分红数据周期不符合要求，期望周期：' . $expectedPeriod . '，实际周期：' . $period . '，用户ID：' . $this->uid);
            return to_assign(1, "只能清理上个月（{$expectedPeriod}）的分红数据");
        }

        

        // 检查是否存在该周期的数据
        $existingCount = Db::name('DividendStoreDetail')
            ->where('period', $period)
            ->where('is_delete', 0)
            ->count();

        if ($existingCount == 0) {
            Log::info('[门店分红]-[门店分红明细表]：周期数据不存在，无需清理，周期：' . $period . '，用户ID：' . $this->uid);
            return to_assign(1, "周期 {$period} 的分红数据不存在，无需清理");
        }

        Log::info('[门店分红]-[门店分红明细表]：开始执行分红数据清理，周期：' . $period . '，操作人：' . $this->uid);
        $startTime = time();

        // 开启事务
        Db::startTrans();

        try {
            // 执行清理操作
            $this->executeClearDividendData($period);

            // 提交事务
            Db::commit();

            $endTime = time();
            $duration = $endTime - $startTime;

            Log::info('[门店分红]-[门店分红明细表]：分红数据清理完成，周期：' . $period . '，耗时：' . $duration . '秒，操作人：' . $this->uid);

            // 记录操作日志
            add_log('delete', 0, ['period' => $period], '[门店分红]-[门店分红明细表]：清理分红数据');

        } catch (\Exception $e) {
            Db::rollback();
            Log::error('[门店分红]-[门店分红明细表]：分红数据清理失败，周期：' . $period . '，错误信息：' . $e->getMessage() . '，操作人：' . $this->uid);
            return to_assign(1, '清理失败：' . $e->getMessage());
        }

        return to_assign(0, "周期 {$period} 的分红数据清理完成，现在可以重新调整门店股份配置并重新计算");
    }

    /**
     * 执行分红数据清理的具体操作
     * @param string $period 统计周期
     * @throws \Exception
     */
    private function executeClearDividendData($period)
    {
        $rollbackAmount = 10000.00; // 风险金回滚金额
        $deleteTimestamp = time();

        Log::info('[门店分红]-[门店分红明细表]：开始执行清理操作，周期：' . $period);

        // 步骤 1: 撤销【门店分红信息主表】中已计提的风险金
        $affectedStores = Db::name('DividendRiskReserveLog')
            ->where('period', $period)
            ->where('change_type', 1)
            ->column('store_id');

        if (!empty($affectedStores)) {
            $updateCount = Db::name('DividendStoreInfo')
                ->where('store_id', 'in', $affectedStores)
                ->update([
                    'risk_reserve' => Db::raw('risk_reserve - ' . $rollbackAmount),
                    'update_time' => $deleteTimestamp
                ]);
            Log::info('[门店分红]-[门店分红明细表]：撤销风险金计提，影响门店数：' . $updateCount);
        }

        // 步骤 2: 物理删除【门店风险金变化记录表】中目标周期的数据
        $deletedRiskLogs = Db::name('DividendRiskReserveLog')
            ->where('period', $period)
            ->delete();
        Log::info('[门店分红]-[门店分红明细表]：删除风险金变化记录，删除条数：' . $deletedRiskLogs);

        // 步骤 3: 物理删除【门店分红人子表】中目标周期的数据
        // 先获取需要删除的门店分红明细ID
        $storeDetailIds = Db::name('DividendStoreDetail')
            ->where('period', $period)
            ->where('is_delete', 0)
            ->column('id');

        $deletedPersons = 0;
        if (!empty($storeDetailIds)) {
            $deletedPersons = Db::name('DividendStoreDetailPerson')
                ->where('dividend_store_detail_id', 'in', $storeDetailIds)
                ->where('is_delete', 0)
                ->delete();
        }
        Log::info('[门店分红]-[门店分红明细表]：物理删除门店分红人数据，删除条数：' . $deletedPersons);

        // 步骤 4: 物理删除【公司股东分红人子表】中目标周期的数据
        // 先获取需要删除的公司股东分红明细ID
        $companyDetailIds = Db::name('DividendCompanyDetail')
            ->where('period', $period)
            ->where('is_delete', 0)
            ->column('id');

        $deletedCompanyPersons = 0;
        if (!empty($companyDetailIds)) {
            $deletedCompanyPersons = Db::name('DividendCompanyDetailPerson')
                ->where('company_detail_id', 'in', $companyDetailIds)
                ->where('is_delete', 0)
                ->delete();
        }
        Log::info('[门店分红]-[门店分红明细表]：物理删除公司股东分红人数据，删除条数：' . $deletedCompanyPersons);

        // 步骤 5: 物理删除【门店分红明细主表】中目标周期的数据
        $deletedStoreDetails = Db::name('DividendStoreDetail')
            ->where('period', $period)
            ->where('is_delete', 0)
            ->delete();
        Log::info('[门店分红]-[门店分红明细表]：物理删除门店分红明细数据，删除条数：' . $deletedStoreDetails);

        // 步骤 6: 物理删除【公司股东分红明细主表】中目标周期的数据
        $deletedCompanyDetails = Db::name('DividendCompanyDetail')
            ->where('period', $period)
            ->where('is_delete', 0)
            ->delete();
        Log::info('[门店分红]-[门店分红明细表]：物理删除公司股东分红明细数据，删除条数：' . $deletedCompanyDetails);

        // 步骤 7: 物理删除【股东分红清单表】中目标周期的数据
        $deletedPayments = Db::name('DividendPayment')
            ->where('period', $period)
            ->where('is_delete', 0)
            ->delete();
        Log::info('[门店分红]-[门店分红明细表]：物理删除股东分红清单数据，删除条数：' . $deletedPayments);

        Log::info('[门店分红]-[门店分红明细表]：清理操作执行完成，周期：' . $period);
    }

}
