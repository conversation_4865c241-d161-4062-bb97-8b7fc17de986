{extend name="../../base/view/common/base" /}

{block name="style"}
<style>
    /* 编辑页面样式 */
    .layui-card {
        border: 1px solid #e6e6e6;
        border-radius: 4px;
        margin-bottom: 15px;
    }

    .layui-card-header {
        background-color: #f8f8f8;
        padding: 10px 15px;
        border-bottom: 1px solid #e6e6e6;
        font-weight: bold;
    }

    .layui-card-body {
        padding: 15px;
    }

    .layui-td-gray {
        background-color: #f5f5f5;
        font-weight: bold;
        text-align: left;
        vertical-align: middle;
        width: 120px;
    }

    /* 股东表格样式 */
    #shareholderTable .layui-input {
        height: 32px;
        line-height: 32px;
    }

    /* 表格样式优化 */
    .layui-table {
        margin-bottom: 0;
    }

    .layui-table td {
        padding: 8px;
        vertical-align: middle;
    }

    .layui-table th {
        background-color: #f8f8f8;
        font-weight: bold;
    }

    /* 必填项红色星号 */
    font {
        color: #FF6347;
        margin-left: 3px;
    }

    /* 页面内容区域 */
    .edit-content {
        padding: 20px;
        height: 100%;
        box-sizing: border-box;
        overflow-y: auto;
    }
</style>
{/block}

{block name="body"}
<div class="edit-content">
    <!-- 页面标题 -->
    <div style="margin-bottom: 20px;">
        <h3 style="margin: 0 0 15px 0; font-size: 18px; font-weight: bold; color: #333;">编辑门店分红明细表</h3>

        <!-- 重要提示 -->
        <div class="layui-alert layui-alert-warning" style="border: 2px solid #ff0000; border-left-width: 8px; border-radius: 6px; box-shadow: 0 2px 8px rgba(255,0,0,0.08); padding: 18px 20px; margin-bottom: 18px;">
            <h4 style="color: #d32f2f; font-weight: bold; margin-bottom: 10px;">重要提示*</h4>
            <p style="font-size: 14px; color: #ff0000; font-weight: bold; margin-bottom: 10px;">
                1. 此操作将影响门店分红数据、股东分红数据、分红清单数据，请务必谨慎操作！
            </p>
            <p style="font-size: 14px; color: #d32f2f; font-weight: bold; margin-bottom: 0;">
                2. 其他调整金额为正数表示扣减，负数表示增加，实际应付金额 = 应付金额 - 其他调整金额
            </p>
        </div>
    </div>

    <form class="layui-form" lay-filter="editForm" id="editForm">
        <input type="hidden" name="store_id" id="edit_store_id">
        <input type="hidden" name="period" id="edit_period">

        <!-- 门店基础数据 -->
        <div class="layui-card">
            <div class="layui-card-header">门店基础数据</div>
            <div class="layui-card-body">
                <table class="layui-table">
                    <tr>
                        <td class="layui-td-gray">门店名称</td>
                        <td style="width: 200px;">
                            <span id="edit_store_name" style="font-weight: bold;"></span>
                        </td>
                        <td class="layui-td-gray">统计周期</td>
                        <td style="width: 200px;">
                            <span id="edit_period_display" style="font-weight: bold;"></span>
                        </td>
                    </tr>
                    <tr>
                        <td class="layui-td-gray">收入</td>
                        <td>
                            <span id="edit_income" style="color: #4CAF50; font-weight: bold;"></span>
                        </td>
                        <td class="layui-td-gray">支出</td>
                        <td>
                            <span id="edit_expense" style="color: #F44336; font-weight: bold;"></span>
                        </td>
                    </tr>
                    <tr>
                        <td class="layui-td-gray">风险金(计提)</td>
                        <td>
                            <span id="edit_risk_reserve_current" style="color: #F44336; font-weight: bold;"></span>
                        </td>
                        <td class="layui-td-gray">门店其他调整</td>
                        <td>
                            <input type="number" name="other_adjustment" id="edit_other_adjustment"
                                   class="layui-input" step="0.01" placeholder="请输入门店其他调整金额"
                                   oninput="limitDecimalPlaces(this, 2)">
                        </td>
                    </tr>
                    <tr>
                        <td class="layui-td-gray">分红利润</td>
                        <td colspan="3">
                            <span id="edit_dividend_profit" style="font-weight: bold;"></span>
                            <span style="color: #999; margin-left: 10px; font-size: 12px;">
                                (分红利润 = 收入 - 支出 - 风险金(计提) - 门店其他调整)
                            </span>
                        </td>
                    </tr>
                    <tr>
                        <td class="layui-td-gray">备注</td>
                        <td colspan="3">
                            <textarea name="remark" id="edit_remark" class="layui-textarea" placeholder="请输入备注信息"></textarea>
                        </td>
                    </tr>
                </table>
            </div>
        </div>

        <!-- 股东分红详情 -->
        <div class="layui-card">
            <div class="layui-card-header">股东分红详情</div>
            <div class="layui-card-body">
                <table class="layui-table" id="shareholderTable">
                    <thead>
                        <tr>
                            <th style="width: 10%;">分红人<font>*</font></th>
                            <th style="width: 10%;">持股比例(%)<font>*</font></th>
                            <th style="width: 15%;">应付金额</th>
                            <th style="width: 15%;">分红人其他调整</th>
                            <th style="width: 25%;">实际应付金额
                            <span style="color: #999; margin-left: 10px; font-size: 12px;">
                                (应付金额 - 分红人其他调整)
                            </span>
                            </th>
                            <th style="width: 25%;">备注</th>
                        </tr>
                    </thead>
                    <tbody id="shareholderTableBody">
                        <!-- 动态生成股东行 -->
                    </tbody>
                </table>
            </div>
        </div>

        <!-- 提交按钮 -->
        <div style="margin-top: 15px;">
            <button class="layui-btn layui-btn-normal" lay-submit lay-filter="editSubmit">保存修改</button>
            <button type="button" class="layui-btn layui-btn-primary" id="cancelEditBtn">取消</button>
        </div>
    </form>
</div>
{/block}

{block name="script"}
<script>
    // 限制输入框小数位数的函数
    function limitDecimalPlaces(input, maxDecimalPlaces) {
        var value = input.value;
        if (value.indexOf('.') !== -1) {
            var parts = value.split('.');
            if (parts[1] && parts[1].length > maxDecimalPlaces) {
                input.value = parts[0] + '.' + parts[1].substring(0, maxDecimalPlaces);
            }
        }
    }

    // 当其他调整输入框变化时自动计算实际应付金额（全局函数）
    function calculateActualPayableForInput(input) {
        var $row = $(input).closest('tr');
        var payableAmount = parseFloat($row.find('.payable-amount-hidden').val()) || 0;
        var otherAdjustment = parseFloat($row.find('.other-adjustment').val()) || 0;
        var actualPayable = payableAmount - otherAdjustment;

        // 更新实际应付金额输入框的值
        $row.find('.actual-payable-amount').val(actualPayable.toFixed(2));

        // 更新颜色
        updateActualPayableColor($row.find('.actual-payable-amount')[0]);
    }

    // 更新实际应付金额的颜色（全局函数）
    function updateActualPayableColor(input) {
        var value = parseFloat($(input).val()) || 0;
        var color = value > 0 ? '#4CAF50' : (value < 0 ? '#F44336' : '#000000');
        $(input).css('color', color);
    }

    const moduleInit = ['tool'];

    function gouguInit() {
        var form = layui.form, tool = layui.tool;

        // 获取URL参数
        function getUrlParam(name) {
            var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)");
            var r = window.location.search.substr(1).match(reg);
            if (r != null) return unescape(r[2]);
            return null;
        }

        var storeId = getUrlParam('store_id');
        var period = getUrlParam('period');

        if (!storeId || !period) {
            layer.msg('参数错误', {icon: 2});
            return;
        }

        // 加载门店数据
        loadStoreData(storeId, period);

        // 获取门店详细数据
        function loadStoreData(storeId, period) {
            layer.load(1);
            $.ajax({
                url: '/dividend/detail/getStoreDetail',
                type: 'post',
                dataType: 'json',
                data: {
                    store_id: storeId,
                    period: period
                },
                success: function(res) {
                    layer.closeAll('loading');
                    if (res.code == 0) {
                        fillFormData(res.data);
                    } else {
                        layer.msg(res.msg, {icon: 2});
                    }
                },
                error: function() {
                    layer.closeAll('loading');
                    layer.msg('获取门店数据失败', {icon: 2});
                }
            });
        }

        // 填充表单数据
        function fillFormData(data) {
            // 填充基础信息
            $('#edit_store_id').val(data.store_id);
            $('#edit_period').val(data.period);
            $('#edit_store_name').text(data.store_name);
            $('#edit_period_display').text(data.period);
            $('#edit_income').text(data.income + ' 元');
            $('#edit_expense').text(data.expense + ' 元');
            $('#edit_risk_reserve_current').text(data.risk_reserve_current + ' 元');
            $('#edit_other_adjustment').val(data.other_adjustment || '0.00');
            $('#edit_remark').val(data.remark || ''); // 添加备注字段赋值

            // 计算并显示分红利润
            calculateDividendProfit();

            // 填充股东数据
            fillShareholderData(data.shareholders || []);

            // 初始化计算所有实际应付金额
            calculateAllActualPayable();
        }

        // 填充股东数据
        function fillShareholderData(shareholders) {
            var tbody = $('#shareholderTableBody');
            tbody.empty();

            if (shareholders && shareholders.length > 0) {
                $.each(shareholders, function(index, shareholder) {
                    addShareholderRow(shareholder, index);
                });
            } else {
                tbody.append('<tr class="no-data"><td colspan="6" style="text-align: center; color: #999;">暂无股东信息</td></tr>');
            }
        }

        // 添加股东行
        function addShareholderRow(data, index) {
            data = data || {};
            var html = '<tr>' +
                '<td>' +
                    '<span class="layui-input" style="border: none; background: #f5f5f5; color: #666; cursor: not-allowed;">' +
                    (data.shareholder_name || '') + '</span>' +
                    '<input type="hidden" name="shareholders[' + index + '][shareholder_name]" ' +
                    'value="' + (data.shareholder_name || '') + '">' +
                '</td>' +
                '<td>' +
                    '<span class="layui-input" style="border: none; background: #f5f5f5; color: #666; cursor: not-allowed; text-align: center;">' +
                    (data.store_shareholding_ratio || '') + '</span>' +
                    '<input type="hidden" name="shareholders[' + index + '][store_shareholding_ratio]" ' +
                    'value="' + (data.store_shareholding_ratio || '') + '">' +
                '</td>' +
                '<td>' +
                    '<span class="layui-input payable-amount-display" style="border: none; background: #f5f5f5; color: #666; cursor: not-allowed; text-align: right;">' +
                    (data.payable_amount || '0.00') + '</span>' +
                    '<input type="hidden" name="shareholders[' + index + '][payable_amount]" ' +
                    'class="payable-amount-hidden" value="' + (data.payable_amount || '0.00') + '">' +
                '</td>' +
                '<td>' +
                    '<input type="number" name="shareholders[' + index + '][other_adjustment]" ' +
                    'class="layui-input other-adjustment" placeholder="0.00" ' +
                    'value="' + (data.other_adjustment || '0.00') + '" step="0.01" ' +
                    'oninput="limitDecimalPlaces(this, 2); calculateActualPayableForInput(this)">' +
                '</td>' +
                '<td>' +
                    '<input type="number" name="shareholders[' + index + '][actual_payable_amount]" ' +
                    'class="layui-input actual-payable-amount" placeholder="0.00" ' +
                    'value="' + (data.actual_payable_amount || '0.00') + '" step="0.01" ' +
                    'style="background: #f0f8ff; font-weight: bold;" ' +
                    'oninput="limitDecimalPlaces(this, 2); updateActualPayableColor(this)">' +
                '</td>' +
                '<td>' +
                    '<input type="text" name="shareholders[' + index + '][remark]" ' +
                    'class="layui-input" placeholder="备注" ' +
                    'value="' + (data.remark || '') + '">' +
                '</td>' +
                '</tr>';

            $('#shareholderTableBody .no-data').remove();
            $('#shareholderTableBody').append(html);

            // 计算实际应付金额并设置颜色
            calculateActualPayableForRow($('#shareholderTableBody tr').last());

            // 为已有的实际应付金额设置颜色
            $('#shareholderTableBody tr').last().find('.actual-payable-amount').each(function() {
                updateActualPayableColor(this);
            });
        }

        // 计算单行实际应付金额（自动计算模式）
        function calculateActualPayableForRow($row) {
            var payableAmount = parseFloat($row.find('.payable-amount-hidden').val()) || 0;
            var otherAdjustment = parseFloat($row.find('.other-adjustment').val()) || 0;
            var actualPayable = payableAmount - otherAdjustment;

            // 更新实际应付金额输入框的值
            $row.find('.actual-payable-amount').val(actualPayable.toFixed(2));

            // 更新颜色
            updateActualPayableColor($row.find('.actual-payable-amount')[0]);
        }

        // 计算所有行的实际应付金额
        function calculateAllActualPayable() {
            $('#shareholderTableBody tr:not(.no-data)').each(function() {
                calculateActualPayableForRow($(this));
                // 为每行的实际应付金额设置颜色
                $(this).find('.actual-payable-amount').each(function() {
                    updateActualPayableColor(this);
                });
            });
        }

        // 监听其他调整金额变化
        $(document).on('input', '#edit_other_adjustment', function() {
            calculateDividendProfit();
            // 自动计算应付金额和实际应付金额
            calculatePayableAmounts();
            calculateAllActualPayable();
        });

        // 计算分红利润
        function calculateDividendProfit() {
            var income = parseFloat($('#edit_income').text().replace(/[^\d.-]/g, '')) || 0;
            var expense = parseFloat($('#edit_expense').text().replace(/[^\d.-]/g, '')) || 0;
            var riskReserve = parseFloat($('#edit_risk_reserve_current').text().replace(/[^\d.-]/g, '')) || 0;
            var otherAdjustment = parseFloat($('#edit_other_adjustment').val()) || 0;

            var dividendProfit = income - expense - riskReserve - otherAdjustment;

            var color = dividendProfit > 0 ? '#4CAF50' : (dividendProfit < 0 ? '#F44336' : '#000000');
            $('#edit_dividend_profit').html('<span style="color: ' + color + '; font-weight: bold;">' + dividendProfit.toFixed(2) + ' 元</span>');

            return dividendProfit;
        }

        // 计算应付金额
        function calculatePayableAmounts() {
            var dividendProfit = calculateDividendProfit();

            $('#shareholderTableBody tr:not(.no-data)').each(function() {
                var $row = $(this);
                var ratio = parseFloat($row.find('input[type="hidden"][name*="[store_shareholding_ratio]"]').val()) || 0;
                var payableAmount = (dividendProfit * ratio / 100).toFixed(2);

                // 更新隐藏字段和显示元素
                $row.find('.payable-amount-hidden').val(payableAmount);
                $row.find('.payable-amount-display').text(payableAmount);

                // 重新计算实际应付金额
                calculateActualPayableForRow($row);
            });
        }

        // 监听表单提交
        form.on('submit(editSubmit)', function(data) {
            // 验证是否有股东数据
            var shareholderCount = $('#shareholderTableBody tr:not(.no-data)').length;
            if (shareholderCount === 0) {
                layer.msg('请至少有一个股东信息', {icon: 2});
                return false;
            }

            // 验证股东姓名不能为空
            var hasEmptyName = false;
            $('#shareholderTableBody tr:not(.no-data)').each(function() {
                var $row = $(this);
                var shareholderName = $row.find('input[type="hidden"][name*="[shareholder_name]"]').val().trim();
                if (!shareholderName) {
                    hasEmptyName = true;
                    return false;
                }
            });

            if (hasEmptyName) {
                layer.msg('股东姓名不能为空', {icon: 2});
                return false;
            }

            // 验证持股比例总和
            var totalRatio = 0;
            $('#shareholderTableBody tr:not(.no-data)').each(function() {
                var $row = $(this);
                var ratio = parseFloat($row.find('input[type="hidden"][name*="[store_shareholding_ratio]"]').val()) || 0;
                if (ratio <= 0) {
                    layer.msg('持股比例必须大于0', {icon: 2});
                    return false;
                }
                totalRatio += ratio;
            });

            if (totalRatio > 100) {
                layer.msg('持股比例总和不能超过100%，当前为' + totalRatio.toFixed(3) + '%', {icon: 2});
                return false;
            }

            if (totalRatio < 100 && totalRatio > 0) {
                layer.alert('持股比例总和未达到100%，当前为' + totalRatio.toFixed(3) + '%。<br><br>' +
                    '<span style="color: #ff6600;">请检查：</span><br>' +
                    '• 是否遗漏了某些股东<br>' +
                    '• 各股东的持股比例是否正确<br><br>' +
                    '<span style="color: #d32f2f;">分红明细要求持股比例必须达到100%</span>', {
                    icon: 2,
                    title: '持股比例错误',
                    area: ['400px', 'auto']
                });
                return false;
            }

            // 提交数据
            layer.load(1);
            $.ajax({
                url: '/dividend/detail/updateStoreDetail',
                type: 'post',
                dataType: 'json',
                data: data.field,
                success: function(res) {
                    layer.closeAll('loading');
                    if (res.code == 0) {
                        layer.msg('保存成功', {icon: 1});
                        // 关闭抽屉并刷新父页面
                        tool.sideClose(1000);
                    } else {
                        layer.msg(res.msg, {icon: 2});
                    }
                },
                error: function() {
                    layer.closeAll('loading');
                    layer.msg('保存失败', {icon: 2});
                }
            });

            return false;
        });

        // 取消编辑按钮事件
        $(document).on('click', '#cancelEditBtn', function() {
            tool.sideClose();
        });
    }
</script>
{/block}
