{extend name="../../base/view/common/base" /}

{block name="style"}
<style type="text/css">
    .panel-num table {width: 100%;}
    .panel-num td {text-align: center; padding: 16px 0; border-left: 1px solid #f1f1f1; position: relative;}
    .panel-num td:nth-child(1) { border-left: none}
    .panel-num .num-title { padding-bottom: 16px; color: #999;}
    .panel-num .blue {font-size: 20px; font-weight: 300;}
    .panel-num td .badge {position: absolute;top: 0; right: 0;}
    .panel-num td .badge span { padding: 2px 4px; font-size: 12px; border-radius: 0 0 0 4px;}
    .dashboard-total td {border-top: 1px solid #f1f1f1}
    .dashboard-logs .layui-timeline-item {padding-bottom: 1px;}
    .info-td { width: 90px; text-align: right;background-color: #fafafa; color: #999; padding: 5px 3px;}
    .info-td {width: 90px; text-align: right;background-color: #fafafa; color: #999; padding: 5px 3px;}
	.layui-card-body .layui-timeline-title {padding-bottom: 0;font-size: 14px; margin-bottom:4px;}
	.layui-card-body .layui-timeline-item {padding-bottom: 5px;}
	.layui-short-menu li { text-align: center;}
	.layui-short-menu li .iconfont {display: inline-block; font-weight:600; width: 100%; height: 58px; line-height: 58px; margin-bottom:5px; text-align: center;border-radius: 3px; font-size: 28px; background-color: #F8F8F8; color: #969696; transition: all .3s; -webkit-transition: all .3s;}
	.layui-short-menu li cite {color: #646464;}
	.layui-short-menu li:hover .iconfont{color: #1A75FF; background-color:#F2F8FF;}
	.layui-short-menu li:hover cite {color: #1A75FF;}
	.layui-matter-item li a{display: block; padding: 12px; background-color: #f8f8f8; color: #999; border-radius: 3px; transition: all .3s;-webkit-transition: all .3s;}
	.layui-matter-item li a:hover{background-color:#F2F8FF;}
	.layui-matter-item li cite {font-size: 24px;font-weight: 300;color: #1A75FF;}
	
	.layui-right-bar a{display: inline-block; width: 100%; height: 64px; text-align: center;border-radius: 3px; background-color: #F8F8F8; color: #969696; transition: all .3s; -webkit-transition: all .3s;}
	.layui-right-bar a .iconfont{display: inline-block; font-weight:600; width: 100%; height: 37px; line-height: 39px; text-align: center;font-size: 24px;}
	.layui-right-bar a:hover{color: #1A75FF; background-color:#F2F8FF;}
</style>
{/block}
<!-- 主体 -->
{block name="body"}
<div class="p-page">
<div class="layui-row layui-col-space16">
	<div class="layui-col-md8">
		{volist name="$layout_selected" id="layout"}
		
			{if ( $layout.row == 1) AND ( $layout.name == 'count') }
				{include file="/index/layout_count" /}	
			{/if}
			
			{if ( $layout.row == 1) AND ( $layout.name == 'event') }
				{include file="/index/layout_event" /}	
			{/if}
			
			{if ( $layout.row == 1) AND ( $layout.name == 'note') }
				{include file="/index/layout_note" /}	
			{/if}
			
			{if ( $layout.row == 1) AND ( $layout.name == 'article') }
				{include file="/index/layout_article" /}	
			{/if}
			
			{gt name=":isModule('project')" value="0"}
			
				{if ( $layout.row == 1) AND ( $layout.name == 'project') }
					{include file="/index/layout_project" /}	
				{/if}
				
				{if ( $layout.row == 1) AND ( $layout.name == 'task') }
					{include file="/index/layout_task" /}	
				{/if}
			
			{/gt}
			
			{if ( $layout.row == 1) AND ( $layout.name == 'chartview') }
				{include file="/index/layout_chartview" /}	
			{/if}
			
			{if ( $layout.row == 1) AND ( $layout.name == 'chartyear') }
				{include file="/index/layout_chartyear" /}
			{/if}


			
		{/volist}
	</div>
	<div class="layui-col-md4">
		{volist name="$layout_selected" id="layout"}		
			{if ( $layout.row == 2) AND ( $layout.name == 'fastentry') }
				{include file="/index/layout_fastentry" /}	
			{/if}
			
			{if ( $layout.row == 2) AND ( $layout.name == 'system') }
				{include file="/index/layout_system" /}	
			{/if}
			
			{if ( $layout.row == 2) AND ( $layout.name == 'ranking') }
				{include file="/index/layout_ranking" /}	
			{/if}
			
			{if ( $layout.row == 2) AND ( $layout.name == 'action') }
				{include file="/index/layout_action" /}	
			{/if}
		{/volist}
	</div>
<!--	{include file="/index/layout_chartmap" /}-->

<!--	<div class="layui-col-md12">-->
<!--		{include file="/index/layout_flow" /}-->
<!--	</div>-->
<!--	<div class="layui-col-md12">-->
<!--		<iframe src="https://www.maodouzw.com/maodouzw10507/277049_2.html" -->
<!--			width="100%" height="1000px"></iframe>-->
<!--	</div>-->
</div>
</div>
{/block}
<!-- /主体 -->

<!-- 脚本 -->
{block name="script"}
<script src="{__GOUGU__}/third_party/echart/echarts.min.js"></script>
<script src="{__GOUGU__}/third_party/echart/china.js"></script>
<script>
	function getRange() {
		let today = new Date();
		let tYear = today.getFullYear();
		let tMonth = today.getMonth() + 1;
		let tDate = today.getDate();
		let dateFirst = tYear + "-" + tMonth + "-" + tDate;
		let datelast = (tYear - 1) + "-" + tMonth + "-" + tDate;
		let dataRange = [];
		dataRange.push(dateFirst);
		dataRange.push(datelast);
		return dataRange;
	}

	function getDay(archiveCalendar) {
		var today = new Date();
		var dayArray = [];
		for (var i = 0; i < 366; i++) {
			var targetday_milliseconds = today.getTime() - 1000 * 60 * 60 * 24 * i;
			var date = new Date(targetday_milliseconds);
			dayArray.push(retunDay(date,archiveCalendar));
		}
		return dayArray;
	}

	function retunDay(day,archiveCalendar) {
		var tYear = day.getFullYear();
		var tMonth = day.getMonth();
		var tDate = day.getDate();
		tMonth = tMonth + 1;
		if (tMonth.toString().length == 1) {
			tMonth = "0" + tMonth;
		}
		if (tDate.toString().length == 1) {
			tDate = "0" + tDate;
		}
		var dateStr = tYear + "-" + tMonth + "-" + tDate;
		var dateArray = [];
		dateArray.push(dateStr);
		if (archiveCalendar[dateStr]) {
			dateArray.push(archiveCalendar[dateStr]);
		}
		else {
			dateArray.push(0);
		}
		return dateArray;
	}
	
	function setHour(num) {
		var str = num + ':00';
		if (num < 10) {
			str = '0' + num + ':00';
		}
		return str;
	}

	const moduleInit = ['tool','oaSchedule'];
	function gouguInit() {
		var tool = layui.tool,table = layui.table,work = layui.oaSchedule;
		
		if(typeof layoutNote ==='function'){
			layoutNote(table);
		}
		
		if(typeof layoutArticle ==='function'){
			layoutArticle(table);
		}
		
		if(typeof layoutArticle ==='function'){
			layoutArticle(table);
		}
		
		if(typeof layoutProject ==='function'){
			layoutProject(table);
		}
		
		if(typeof layoutTask ==='function'){
			layoutTask(table);
		}
		
		if(typeof layoutChartView ==='function'){
			layoutChartView();
		}
		
		if(typeof layoutChartYear ==='function'){
			layoutChartYear();
			//layoutChartMap();
		}
		
		if(typeof layoutFastentry ==='function'){
			layoutFastentry(tool,work,table)
		}
		
		if(typeof layoutSystem ==='function'){
			layoutSystem()
		}
		
		if(typeof layoutRaking ==='function'){
			layoutRaking()
		}
		
		if(typeof layoutAction ==='function'){
			layoutAction()
		}	

		//layoutFlow()
	}
	
setTimeout(function () {
	window.onresize = function () {
		if(logChart){
			chartView.resize();
		}
		if(myChart){
			myChart.resize();
		}

		// if(chartMapView){
		// 	chartMapView.resize();
		// }
		if(logChart){
			logChart.resize();
		}
	}
})
</script>
{/block}
<!-- /脚本 -->