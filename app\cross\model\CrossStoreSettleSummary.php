<?php
/**
 * @copyright Copyright (c) 2021 仲正堂工作室
 * @license https://opensource.org/licenses/GPL-3.0
 * @link https://www.gougucms.com
 */

namespace app\cross\model;

use think\Model;
use think\facade\Db;

class CrossStoreSettleSummary extends Model
{
    protected $name = 'cross_store_settle_summary';

    // 设置字段信息
    protected $schema = [
        'id'                              => 'int',
        'period'                          => 'string',
        'store_id'                        => 'int',
        'store_type'                      => 'string',
        'other_store_id'                  => 'int',
        'other_store_type'                => 'string',
        'total_reconciliation_amount'     => 'decimal',
        
        // 储值本金部分
        'p_local_consume_foreign'         => 'decimal',
        'p_local_refund_foreign'          => 'decimal',
        'p_foreign_consume_local'         => 'decimal',
        'p_foreign_refund_local'          => 'decimal',
        'p_total_amount'                  => 'decimal',
        'p_reconciliation_amount'         => 'decimal',
        
        // 储值赠金部分
        'b_local_consume_foreign'         => 'decimal',
        'b_local_refund_foreign'          => 'decimal',
        'b_foreign_consume_local'         => 'decimal',
        'b_foreign_refund_local'          => 'decimal',
        'b_total_amount'                  => 'decimal',
        'b_reconciliation_amount'         => 'decimal',
        
        // 次卡部分
        'cc_local_consume_foreign_count'  => 'int',
        'cc_local_refund_foreign_count'   => 'int',
        'cc_foreign_consume_local_count'  => 'int',
        'cc_foreign_refund_local_count'   => 'int',
        'cc_local_upgrade_foreign_amount' => 'decimal',
        'cc_foreign_upgrade_local_amount' => 'decimal',
        'cc_total_amount'                 => 'decimal',
        'cc_reconciliation_amount'        => 'decimal',
        
        // 网店核销部分
        'ol_local_redeem_foreign_count'   => 'int',
        'ol_local_refund_foreign_count'   => 'int',
        'ol_foreign_redeem_local_count'   => 'int',
        'ol_foreign_refund_local_count'   => 'int',
        'ol_total_amount'                 => 'decimal',
        'ol_reconciliation_amount'        => 'decimal',
        
        // 公共字段
        'create_time'                     => 'int',
        'update_time'                     => 'int',
    ];

    // 自动时间戳
    protected $autoWriteTimestamp = true;
    protected $createTime = 'create_time';
    protected $updateTime = 'update_time';

    /**
     * 关联门店信息
     */
    public function store()
    {
        return $this->belongsTo('app\user\model\Department', 'store_id', 'id');
    }

    /**
     * 关联他店信息
     */
    public function otherStore()
    {
        return $this->belongsTo('app\user\model\Department', 'other_store_id', 'id');
    }

    /**
     * 获取跨店结算汇总列表
     * @param array $where 查询条件
     * @param int $page 页码
     * @param int $limit 每页数量
     * @return array
     */
    public static function getList($where = [], $page = 1, $limit = 20)
    {
        $query = self::alias('css')
            ->join('oa_department d1', 'd1.id = css.store_id', 'LEFT')
            ->join('oa_department d2', 'd2.id = css.other_store_id', 'LEFT')
            ->field('css.*, d1.title as store_name, d2.title as other_store_name');

        if (!empty($where)) {
            $query->where($where);
        }

        $list = $query->order('css.id desc')
            ->paginate($limit, false, ['page' => $page])
            ->each(function ($item) {
                // 格式化金额显示
                $item['total_reconciliation_amount_formatted'] = number_format(floatval($item['total_reconciliation_amount']), 2);
                $item['p_total_amount_formatted'] = number_format(floatval($item['p_total_amount']), 2);
                $item['b_total_amount_formatted'] = number_format(floatval($item['b_total_amount']), 2);
                $item['cc_total_amount_formatted'] = number_format(floatval($item['cc_total_amount']), 2);
                $item['ol_total_amount_formatted'] = number_format(floatval($item['ol_total_amount']), 2);

                // 格式化时间
                $item['create_time_formatted'] = date('Y-m-d H:i:s', $item['create_time']);
                $item['update_time_formatted'] = date('Y-m-d H:i:s', $item['update_time']);
            });

        return $list;
    }

    /**
     * 获取跨店结算汇总详情
     * @param int $id 汇总ID
     * @return array
     */
    public static function getDetail($id)
    {
        $detail = self::alias('css')
            ->join('oa_department d1', 'd1.id = css.store_id', 'LEFT')
            ->join('oa_department d2', 'd2.id = css.other_store_id', 'LEFT')
            ->field('css.*, d1.title as store_name, d2.title as other_store_name')
            ->where('css.id', $id)
            ->find();

        if (empty($detail)) {
            return [];
        }

        return $detail;
    }

    /**
     * 检查门店配对和月份是否已存在
     * @param string $period 月份
     * @param int $storeId 门店ID
     * @param int $otherStoreId 他店ID
     * @param int $excludeId 排除的记录ID
     * @return bool
     */
    public static function checkExists($period, $storeId, $otherStoreId, $excludeId = 0)
    {
        $where = [
            ['period', '=', $period],
            ['store_id', '=', $storeId],
            ['other_store_id', '=', $otherStoreId]
        ];

        if ($excludeId > 0) {
            $where[] = ['id', '<>', $excludeId];
        }

        $exists = self::where($where)->find();
        return !empty($exists);
    }

    /**
     * 获取门店类型选项
     * @return array
     */
    public static function getStoreTypeOptions()
    {
        return [
            '新店' => '新店',
            '老店' => '老店'
        ];
    }

    /**
     * 获取统计汇总数据
     * @param array $where 查询条件
     * @return array
     */
    public static function getSummaryData($where = [])
    {
        $query = self::where($where);

        $summaryData = $query->field([
            'SUM(total_reconciliation_amount) as total_reconciliation',
            'SUM(p_total_amount) as total_p_amount',
            'SUM(b_total_amount) as total_b_amount',
            'SUM(cc_total_amount) as total_cc_amount',
            'SUM(ol_total_amount) as total_ol_amount',
            'COUNT(*) as total_count'
        ])->find();

        return [
            'total_reconciliation' => number_format(floatval($summaryData['total_reconciliation'] ?? 0), 2),
            'total_p_amount' => number_format(floatval($summaryData['total_p_amount'] ?? 0), 2),
            'total_b_amount' => number_format(floatval($summaryData['total_b_amount'] ?? 0), 2),
            'total_cc_amount' => number_format(floatval($summaryData['total_cc_amount'] ?? 0), 2),
            'total_ol_amount' => number_format(floatval($summaryData['total_ol_amount'] ?? 0), 2),
            'total_count' => intval($summaryData['total_count'] ?? 0)
        ];
    }
}
