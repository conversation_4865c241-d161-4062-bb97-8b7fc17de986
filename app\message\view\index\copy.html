{extend name="../../base/view/common/base" /}
<!-- 主体 -->
{block name="body"}
<div class="p-3">
	{include file="index/msgmenu" /}
	<div style="margin-left:172px;">
		<form class="layui-form gg-form-bar border-t border-x">
			<div class="layui-input-inline" style="width:240px">
				<input type="text" name="keywords" placeholder="关键字" class="layui-input" autocomplete="off"/>
			</div>
			<div class="layui-input-inline" id="date">
				<div class="layui-input-inline" style="width:112px; margin-bottom:0;">
				  <input type="text" autocomplete="off" id="start_time" name="start_time" class="layui-input" placeholder="接收开始日期">
				</div>
				~
				<div class="layui-input-inline" style="width:112px; margin-bottom:0">
				  <input type="text" autocomplete="off" id="end_time" name="end_time" class="layui-input" placeholder="接收结束日期">
				</div>
			</div>
			<div class="layui-input-inline" style="width:136px">
				<select name="read">
					<option value="">选择消息状态</option>
					<option value="1">未读消息</option>
					<option value="2">已读消息</option>
				</select>
			</div>
			<div class="layui-input-inline" style="width:136px">
				<select name="template">
					<option value="">选择消息类型</option>
					<option value="1">系统消息</option>
					<option value="2">个人消息</option>
				</select>
			</div>
			<div class="layui-input-inline" style="width:150px;">
				<button class="layui-btn layui-btn-normal" lay-submit="" lay-filter="webform"><i class="layui-icon layui-icon-search mr-1"></i>搜索</button>
				<button type="reset" class="layui-btn layui-btn-reset" lay-filter="reset">清空</button>
			</div>
		</form> 
		<table class="layui-hide" id="test" lay-filter="test"></table>
	</div>
</div>

<script type="text/html" id="toolbarDemo">
	<div class="layui-btn-container">
		<button class="layui-btn layui-btn-normal layui-btn-sm" lay-event="add"><i class="layui-icon">&#xe61f;</i>新建消息</button>
		<button class="layui-btn layui-btn-warm layui-btn-sm" lay-event="read"><i class="layui-icon">&#xe605;</i>设为已读</button>
		<button class="layui-btn layui-btn-danger layui-btn-sm" lay-event="del"><i class="layui-icon">&#xe640;</i>批量删除</button>
	</div>
</script>

<script type="text/html" id="barDemo">
<div class="layui-btn-group"><button class="layui-btn layui-btn-xs layui-btn-normal" lay-event="view">查看</button>
  {{#  if(d.template == 0){ }}
  <button class="layui-btn layui-btn-xs" lay-event="reply">回复</button>
  <button class="layui-btn layui-btn-xs layui-btn-warm" lay-event="resend">转发</button>
  {{#  } }}
  <button class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">删除</button></div>
</script>
{/block}
<!-- /主体 -->

<!-- 脚本 -->
{block name="script"}
<script>
	const moduleInit = ['tool'];
	function gouguInit() {
		var table = layui.table, tool = layui.tool ,form = layui.form,laydate = layui.laydate;		
		//日期范围
		laydate.render({
			elem: '#date',
			range: ['#start_time', '#end_time'],
			rangeLinked:true
		});
		layui.pageTable = table.render({
			elem: '#test',
			toolbar: '#toolbarDemo',
			title:'收件箱',
			url: "/message/index/copy", //数据接口
			page: true ,//开启分页
			limit: 20,
			cellMinWidth: 80, //全局定义常规单元格的最小宽度，layui 2.2.1 新增
			cols: [[ //表头
			  	{type:'checkbox',fixed:'left'},
			  	{field: 'msg_type', title: '消息类型',width:90,align:'center'},
			  	{field: 'from_name', title: '发件人', width:100,align:'center'},
			  	{field: 'title', title: '消息主题',width:300},
				{field: 'form_user', title: '发起人'},
				{field: 'aname', title: '申请人'},
			  	{field: 'send_time', title: '发件时间', align:'center',width:160},
			  	{field: 'read_time', title: '是否已读',align:'center', width:90,templet:function(d){
							var html='<span style="color:#5FB878">已读</span>';
							if(d.read_time==0){
								html='<span style="color:#FF5722">未读</span>';
							}
							return html;
						}},
			  {field: 'right', title: '操作',fixed:'right', toolbar: '#barDemo', width:170, align:'center'}
			]],
			where:{type:1}
		});

		//监听行工具事件
		table.on('tool(test)', function(obj){
			var data = obj.data;
			if(obj.event === 'del'){
				layer.confirm('确定该信息要放入垃圾箱吗?', {icon: 3, title:'提示'}, function(index){
					let callback = function (e) {
						layer.msg(e.msg);
						if(e.code==0){
							layui.pageTable.reload();
						}
					}
					tool.delete("/message/index/check", {
						ids: data.id,
						type:2,
						source:3
					}, callback);
					layer.close(index);
				});
			}
			if(obj.event === 'view'){
				tool.side('/message/index/read?id='+data.id);
				return;
			}
			if(obj.event === 'reply'){
				tool.side('/message/index/reply?id='+data.id+'&type=1');
				return;
			}
			if(obj.event === 'resend'){
				tool.side('/message/index/reply?id='+data.id+'&type=2');
				return;
			}
		});
		
		//表头工具栏事件
		table.on('toolbar(test)', function(obj){
			var checkStatus = table.checkStatus(obj.config.id); //获取选中行状态
			var data = checkStatus.data; 
			if (obj.event === 'add') {
				tool.side("/message/index/add");
				return;
			}
			if(data.length==0){
				layer.msg('请选择要操作的消息');
				return false;
			}
			var idArray=[],msg='是否执行该操作？',type=0;
			for(var i=0;i<data.length;i++){
				idArray.push(data[i].id);
			}
			switch(obj.event){
				case 'read':
					msg = '确定把选中的信息设为已读吗?';
					type = 1;
				break;
				case 'del':
					msg = '确定把选中的信息要放入垃圾箱吗?';
					type = 2;
				break;
			};
			
			layer.confirm(msg, {
				icon: 3,
				title: '提示'
			}, function (index) {
				let callback = function (e) {
					layer.msg(e.msg);
					if(e.code==0){
						layui.pageTable.reload();
					}
				}
				tool.delete("/message/index/check", {
					ids: idArray.join(','),
					type:type,
					source:3
				}, callback);
				layer.close(index);
			});
		});
		 
		//监听搜索提交
		form.on('submit(webform)', function(data){
			let f=data.field;
			layui.pageTable.reload({where:{keywords:f.keywords,start_time:f.start_time,end_time:f.end_time,template:f.template,read:f.read},page:{curr:1}});
			return false;
		});
	}		
</script>
{/block}
<!-- /脚本 -->