{extend name="../../base/view/common/base" /}
{block name="style"}
<style>
    .layui-form-item {
        margin-bottom: 0;
        display: inline-block;
        vertical-align: top;
    }

    .layui-form-item .layui-inline {
        margin-bottom: 0;
        margin-right: 5px;
    }

    .layui-input-block {
        min-height: 24px;
    }

    .gg-form-bar .layui-input-inline{
        margin-bottom: 0;
        margin-right: 5px;
        vertical-align: top;
    }

    /* 多选下拉样式 */
    .layui-form-select dl {
        min-width: 100%;
    }
    .layui-form-select dl dd.layui-this {
        background-color: #5FB878;
        color: #fff;
    }

    .label-content {
        max-height: 34px !important;
        width: 125px !important;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }

    .xm-select {
        height: 34px !important;
        line-height: 34px !important;
    }

    /* 按钮组样式 */
    .filter-button-group .layui-btn {
        display: inline-block !important;
        vertical-align: top;
        margin-left: 5px;
    }
    .filter-button-group .layui-btn:first-child {
        margin-left: 0;
    }

    /* 合计行样式 */
    .layui-table-total {
        background-color: #f8f8f8;
        font-weight: bold;
    }

    .layui-table-total td {
        border-top: 2px solid #e6e6e6;
    }


</style>
{/block}

{block name="body"}
<div class="p-3" style="height:100%; box-sizing: border-box;">
    <div class="body-table" style="overflow:hidden;">
        <!-- 搜索表单 -->
        <form class="layui-form gg-form-bar border-t border-x" lay-filter="webform" style="padding-bottom:10px;">
            <div class="layui-form-item" style="margin-bottom: 10px">
                <input type="text" name="period" id="period"
                       placeholder="请选择统计周期"
                       autocomplete="off" class="layui-input">
            </div>
            <div id="store-select-container" class="layui-input-inline" style="width:200px; height: 38px;"></div>
            <div class="layui-form-item" style="margin-bottom: 10px">
                <input type="text" name="shareholder_name" id="shareholder_name"
                       placeholder="请输入分红人姓名"
                       autocomplete="off" class="layui-input">
            </div>
            <!-- 按钮组 -->
            <div class="layui-input-inline filter-button-group" style="width: auto; white-space: nowrap;">
                <button class="layui-btn layui-btn-normal" lay-submit="" lay-filter="webform"><i
                        class="layui-icon layui-icon-search mr-1"></i>搜索
                </button>
                <button type="button" class="layui-btn layui-btn-primary" id="resetBtn"><i
                        class="layui-icon layui-icon-refresh mr-1"></i>重置
                </button>
            </div>
        </form>
        <!-- 增加外边距，方便页面布局更美观 -->
        <div style="margin: 10px 0;">
            <span style="font-weight:600">门店分红明细表</span>
            <div style="font-weight:600; color: #F44336; font-size:12px;">[调整金额]是减数，正数表示扣减，负数表示增加</div>
        </div>
        <!-- 数据表格 -->
        <table class="layui-hide" id="dataTable" lay-filter="dataTable"></table>
    </div>
</div>

<!-- 表格工具栏模板 -->
<script type="text/html" id="toolbarDemo">
    <div class="layui-btn-group" id="calculateBtnContainer" style="display: none;">
        <button class="layui-btn layui-btn-sm layui-btn-normal" lay-event="calculate" id="calculateBtn">
            <i class="layui-icon layui-icon-engine"></i>计算门店分红数据
        </button>
    </div>
    <div class="layui-btn-group" id="clearBtnContainer" style="display: none;">
        <button class="layui-btn layui-btn-sm layui-btn-danger" lay-event="clear" id="clearBtn">
            <i class="layui-icon layui-icon-delete"></i>清理门店分红数据
        </button>
    </div>
    <div id="toolbarStatusText" style="display: none; color: #999; padding-left: 10px; line-height: 32px; font-size:12px;"></div>
    <div id="toolbarNoPermissionText" style="display: none; color: #999; padding-left: 10px; line-height: 32px; font-size:12px;"></div>
</script>




{/block}

{block name="script"}
<style>
/* 合计行样式 */
.layui-table-total .layui-table-cell {
    font-weight: bold;
}

/* 合并单元格样式 */
.layui-table-body td[rowspan] {
    vertical-align: middle !important;
    border-bottom: 1px solid #e6e6e6 !important;
}

/* 计算按钮样式 */
#calculateBtn {
    min-width: 200px;
}
</style>
<script>
    const moduleInit = ['tool', 'tablePlus', 'tableMerge'];

    function gouguInit() {
        var table = layui.tablePlus, tool = layui.tool, laydate = layui.laydate, form = layui.form;
        var tableMerge = layui.tableMerge;

        // 初始化统计周期选择器 - 默认上一个月
        var lastMonth = new Date();
        lastMonth.setMonth(lastMonth.getMonth() - 1);
        var lastMonthStr = lastMonth.getFullYear() + '-' + String(lastMonth.getMonth() + 1).padStart(2, '0');

        laydate.render({
            elem: '#period',
            type: 'month',
            value: lastMonthStr,
            done: function(value, date, endDate) {
                // 不立即刷新
            }
        });

        // 初始化门店多选组件
        var storeMultiSelect = xmSelect.render({
            el: '#store-select-container',
            name: 'store_ids',
            language: 'zn',
            filterable: true,
            data: [],
            model: { label: { type: 'xm-select-count', max: 0 } },
            prop: {
                name: 'title',
                value: 'id'
            },
            placeholder: '全部门店',
            on: function(data){
                // 门店选择变化时不立即刷新
            }
        });

        // 加载门店列表
        function loadStoreList() {
            $.ajax({
                url: '/api/dividend/getStoreList',
                type: 'post',
                dataType: 'json',
                success: function(res) {
                    if (res.code == 0) {
                        var storesForXmSelect = [];
                        if (res.data && res.data.length > 0) {
                            storesForXmSelect = res.data.map(function(item) {
                                return { id: item.id, title: item.title };
                            });
                        }
                        storeMultiSelect.update({
                            data: storesForXmSelect,
                            autoRow: true
                        });
                    } else {
                        layer.msg(res.msg, {icon: 2});
                    }
                },
                error: function(){
                    layer.msg('获取门店列表失败', {icon:2});
                }
            });
        }

        // 初始化加载门店列表
        loadStoreList();

        // 应用合计行颜色策略函数
        function applyTotalRowColors() {
            setTimeout(function() {
                // 直接通过属性选择器找到合计行的单元格
                $('.layui-table-total td').each(function(index) {
                    var $cell = $(this);
                    var cellText = $cell.text().trim();
                    var cellValue = parseFloat(cellText.replace(/[^\d.-]/g, ''));

                    // 根据列索引应用不同的颜色策略
                    switch(index) {
                        case 1: // 收入字段
                            var color = cellValue > 0 ? '#4CAF50' : '#000000';
                            $cell.find('.layui-table-cell').css('color', color);
                            break;
                        case 2: // 支出字段
                            var color = cellValue > 0 ? '#F44336' : '#000000';
                            $cell.find('.layui-table-cell').css('color', color);
                            break;
                        case 3: // 风险金(计提)字段
                            var color = cellValue > 0 ? '#F44336' : '#000000';
                            $cell.find('.layui-table-cell').css('color', color);
                            break;
                        case 4: // 门店其他调整字段
                            var color = cellValue > 0 ? '#F44336' : '#000000';
                            $cell.find('.layui-table-cell').css('color', color);
                            break;
                        case 5: // 分红利润字段
                            var color = cellValue > 0 ? '#4CAF50' : (cellValue < 0 ? '#F44336' : '#000000');
                            $cell.find('.layui-table-cell').css('color', color);
                            break;
                        case 8: // 应付金额字段
                            if (cellText !== '-') {
                                var color = cellValue > 0 ? '#4CAF50' : (cellValue < 0 ? '#F44336' : '#000000');
                                $cell.find('.layui-table-cell').css('color', color);
                            }
                            break;
                        case 9: // 分红人其他调整字段
                            if (cellText !== '-') {
                                var color = cellValue > 0 ? '#F44336' : '#000000';
                                $cell.find('.layui-table-cell').css('color', color);
                            }
                            break;
                        case 10: // 实际应付金额字段
                            if (cellText !== '-') {
                                var color = cellValue > 0 ? '#4CAF50' : (cellValue < 0 ? '#F44336' : '#000000');
                                $cell.find('.layui-table-cell').css('color', color);
                            }
                            break;
                        case 11: // 风险金总金额字段 - 固定绿色
                            $cell.find('.layui-table-cell').css('color', '#4CAF50');
                            break;
                    }
                });
            }, 200);
        }

        // 渲染表格
        try {
            layui.pageTable = table.render({
                elem: '#dataTable',
                title: '门店分红明细列表',
                page: false, // 不分页
                height: 'full-150',
                url: "/dividend/detail/index",
                loading: true,
                even: true,
                totalRow: true,
                toolbar: '#toolbarDemo',
                where: {
                    period: lastMonthStr, // 默认加载上月数据
                    store_ids: [],
                    shareholder_name: ''
                },
                parseData: function(res) {
                    // 把权限信息附加到 table 对象上
                    this.hasButtonAccess = res.hasButtonAccess;
                    return {
                        "code": res.code,
                        "msg": res.msg,
                        "count": res.count,
                        "data": res.data,
                        "totalRow": res.totalRow
                    };
                },
                cols: [[
                    {
                        field: 'store_name',
                        title: '门店',
                        align: 'center',
                        merge: true,
                        totalRowText: '合计'
                    },
                    {
                        field: 'income',
                        title: '收入(元)',
                        align: 'center',
                        merge: ['store_name', 'income'],
                        totalRow: true,
                        templet: function(d) {
                            var color = parseFloat(d.income) > 0 ? '#4CAF50' : '#000000'; // 大于0为绿色，等于0为黑色
                            return '<span style="color: ' + color + ';">' + d.income + '</span>';
                        }
                    },
                    {
                        field: 'expense',
                        title: '支出(元)',
                        align: 'center',
                        merge: ['store_name', 'expense'],
                        totalRow: true,
                        templet: function(d) {
                            var color = parseFloat(d.expense) > 0 ? '#F44336' : '#000000'; // 大于0为红色，等于0为黑色
                            return '<span style="color: ' + color + ';">' + d.expense + '</span>';
                        }
                    },
                    {
                        field: 'risk_reserve_current',
                        title: '风险金(计提)(元)',
                        align: 'center',
                        merge: ['store_name', 'risk_reserve_current'],
                        totalRow: true,
                        templet: function(d) {
                            var color = parseFloat(d.risk_reserve_current) > 0 ? '#F44336' : '#000000'; // 大于0为红色，等于0为黑色
                            return '<span style="color: ' + color + ';">' + d.risk_reserve_current + '</span>';
                        }
                    },
                    {
                        field: 'other_adjustment',
                        title: '门店其他调整(元)',
                        align: 'center',
                        merge: ['store_name', 'other_adjustment'],
                        totalRow: true,
                        templet: function(d) {
                            var color = parseFloat(d.other_adjustment) > 0 ? '#F44336' : '#000000'; // 大于0为红色，等于0为黑色
                            return '<span style="color: ' + color + ';">' + d.other_adjustment + '</span>';
                        }
                    },
                    {
                        field: 'dividend_profit',
                        title: '分红利润(元)',
                        align: 'center',
                        merge: ['store_name', 'dividend_profit'],
                        totalRow: true,
                        templet: function(d) {
                            var value = parseFloat(d.dividend_profit);
                            var color = value > 0 ? '#4CAF50' : (value < 0 ? '#F44336' : '#000000'); // 大于0为绿色，小于0为红色，等于0为黑色
                            return '<span style="color: ' + color + ';">' + d.dividend_profit + '</span>';
                        }
                    },
                    {
                        field: 'shareholder_name',
                        title: '分红人',
                        align: 'center'
                    },
                    {
                        field: 'store_shareholding_ratio',
                        title: '持股比例(%)',
                        align: 'center'
                    },
                    {
                        field: 'payable_amount',
                        title: '应付金额(元)',
                        align: 'center',
                        totalRow: true,
                        templet: function(d) {
                            if (d.payable_amount !== '-') {
                                var value = parseFloat(d.payable_amount);
                                var color = value > 0 ? '#4CAF50' : (value < 0 ? '#F44336' : '#000000'); // 大于0为绿色，小于0为红色，等于0为黑色
                                return '<span style="color: ' + color + ';">' + d.payable_amount + '</span>';
                            }
                            return d.payable_amount;
                        }
                    },
                    {
                        field: 'person_other_adjustment',
                        title: '分红人其他调整(元)',
                        align: 'center',
                        totalRow: true,
                        templet: function(d) {
                            if (d.person_other_adjustment !== '-') {
                                var value = parseFloat(d.person_other_adjustment);
                                var color = value > 0 ? '#F44336' : '#000000'; // 大于0为红色，等于0为黑色
                                return '<span style="color: ' + color + ';">' + d.person_other_adjustment + '</span>';
                            }
                            return d.person_other_adjustment;
                        }
                    },
                    {
                        field: 'actual_payable_amount',
                        title: '实际应付金额(元)',
                        align: 'center',
                        totalRow: true,
                        templet: function(d) {
                            if (d.actual_payable_amount !== '-') {
                                var value = parseFloat(d.actual_payable_amount);
                                var color = value > 0 ? '#4CAF50' : (value < 0 ? '#F44336' : '#000000'); // 大于0为绿色，小于0为红色，等于0为黑色
                                return '<span style="color: ' + color + ';">' + d.actual_payable_amount + '</span>';
                            }
                            return d.actual_payable_amount;
                        }
                    },
                    {
                        field: 'risk_reserve_total',
                        title: '风险金总金额(元)',
                        align: 'center',
                        merge: ['store_name', 'risk_reserve_total'],
                        totalRow: true,
                        templet: function(d) {
                            return '<span style="color: #4CAF50;">' + d.risk_reserve_total + '</span>'; // 固定为绿色
                        }
                    },
                    {
                        field: 'operate',
                        title: '操作',
                        align: 'center',
                        width: 120,
                        merge: ['store_name'],
                        templet: function(d) {
                            if (layui.pageTable.config.hasButtonAccess) {
                                return '<button class="layui-btn layui-btn-xs edit-store-btn" data-store-id="' + d.store_id + '" data-period="' + d.period + '">编辑</button>';
                            }
                            return '<span style="color: #999;"></span>';
                        }
                    }
                ]],
                done: function(res, curr, count) {
                    // 表格渲染完成后的回调
                    if (res.code === 0) {
                        // 使用 tableMerge 插件实现垂直合并单元格
                        tableMerge.render(this);

                        // 应用合计行颜色策略
                        applyTotalRowColors();
                    }
                    // 使用保存在config中的权限状态，更稳定
                    var hasPermission = this.hasButtonAccess;
                    var $toolbar = $('.layui-table-tool');

                    // 根据权限决定是否检查并显示计算按钮
                    if (hasPermission) {
                        // 有权限，先隐藏"无权限"提示
                        $toolbar.find('#toolbarNoPermissionText').hide();
                        // 再检查计算按钮自身的状态
                        checkCalculateButtonStatus();
                    } else {
                        // 无权限，隐藏按钮和状态文本，并显示"无权限"提示
                        $toolbar.find('#calculateBtnContainer').hide();
                        $toolbar.find('#toolbarStatusText').hide();
                        $toolbar.find('#toolbarNoPermissionText').show();
                    }
                }
            });
        } catch (e) {
            console.error('表格初始化失败:', e);
        }





        // 监听搜索按钮
        form.on('submit(webform)', function(data) {
            var selectedStoreIds = storeMultiSelect.getValue('value');
            layui.pageTable.reload({
                where: {
                    period: $('#period').val(),
                    store_ids: selectedStoreIds,
                    shareholder_name: $('#shareholder_name').val()
                },
                done: function(res, curr, count) {
                    // 重新加载后也要合并单元格
                    if (res.code === 0) {
                        // 使用正确的表格实例
                        tableMerge.render(this);

                        // 应用合计行颜色策略
                        applyTotalRowColors();
                    }
                }
            });
            return false;
        });

        // 监听重置按钮
        $('#resetBtn').on('click', function(){
            $('#period').val(lastMonthStr);
            $('#shareholder_name').val('');
            storeMultiSelect.setValue([]);
            form.render();

            // 重置后自动执行搜索，应用上月筛选条件
            layui.pageTable.reload({
                where: {
                    period: lastMonthStr,
                    store_ids: [],
                    shareholder_name: ''
                },
                done: function(res, curr, count) {
                    // 重新加载后也要合并单元格
                    if (res.code === 0) {
                        // 使用正确的表格实例
                        tableMerge.render(this);

                        // 应用合计行颜色策略
                        applyTotalRowColors();
                    }
                }
            });
        });

        // 监听编辑按钮点击
        $(document).on('click', '.edit-store-btn', function() {
            var storeId = $(this).data('store-id');
            var period = $(this).data('period');

            // 使用抽屉打开编辑页面
            tool.side('/dividend/detail/edit?store_id=' + storeId + '&period=' + period, '编辑门店分红数据');
        });

        // 监听表格工具栏事件
        table.on('toolbar(dataTable)', function(obj) {
            if (obj.event === 'calculate') {
                handleCalculateClick();
            } else if (obj.event === 'clear') {
                handleClearClick();
            }
        });

        // 处理计算按钮点击事件
        function handleCalculateClick() {
            // 计算上个月的日期
            var currentDate = new Date();
            var lastMonth = new Date(currentDate.getFullYear(), currentDate.getMonth() - 1, 1);
            var targetPeriod = lastMonth.getFullYear() + '-' + String(lastMonth.getMonth() + 1).padStart(2, '0');

            // 由于按钮只在可计算时显示，所以不需要额外检查状态

            // 显示输入确认对话框
            layer.prompt({
                title: '确认计算 ' + targetPeriod + ' 分红数据',
                formType: 0, // 输入框类型（明文）
                value: '', // 默认值
                area: ['500px', '300px'],
                maxlength: 10,
                btn: ['立即计算', '取消'],
                yes: function(index, layero) {
                    var inputValue = layero.find('.layui-layer-input').val().trim();
                    if (inputValue === '立即计算') {
                        layer.close(index);
                        calculateDividendData(targetPeriod);
                    } else {
                        layer.msg('请输入"立即计算"四个字确认执行', {icon: 2, time: 2000});
                        return false; // 阻止关闭对话框
                    }
                },
                btn2: function(index) {
                    layer.close(index);
                },
                success: function(layero, index) {
                    // 在对话框成功创建后添加说明内容
                    var promptContent = '<div style="padding: 0 0 15px 0; line-height: 1.6; color: #333; font-size: 13px;">' +
                            '<div style="color: #ff5722; font-weight: bold; margin-bottom: 12px;">' +
                            '⚠️ 计算前请务必在【门店对账】模块查看一次目标月份的【所有门店合计】！' +
                            '</div>' +
                            '<div style="margin-bottom: 12px; font-weight: bold;">' +
                            '此操作将执行完整的分红数据计算，包括：' +
                            '</div>' +
                            '<ul style="margin: 8px 0 12px 20px; color: #666;">' +
                            '<li>门店分红明细表</li>' +
                            '<li>公司股东分红表</li>' +
                            '<li>股东分红清单表</li>' +
                            '</ul>' +
                            '<div style="color: #ff5722; font-weight: bold; margin-bottom: 12px;">' +
                            '⚠️ 请确保数据准确无误后再执行！' +
                            '</div>' +
                            '<div style="color: #333; margin-bottom: 8px;">' +
                            '请在下方输入框中输入"<span style="color: #ff5722; font-weight: bold;">立即计算</span>"确认执行：' +
                            '</div>' +
                            '</div>';

                    // 在输入框前面插入说明内容
                    layero.find('.layui-layer-input').before(promptContent);

                    // 设置输入框占位符
                    layero.find('.layui-layer-input').attr('placeholder', '请输入：立即计算');
                }
            });
        }

        // 处理清理按钮点击事件
        function handleClearClick() {
            // 计算上个月的日期
            var currentDate = new Date();
            var lastMonth = new Date(currentDate.getFullYear(), currentDate.getMonth() - 1, 1);
            var targetPeriod = lastMonth.getFullYear() + '-' + String(lastMonth.getMonth() + 1).padStart(2, '0');

            // 显示输入确认对话框
            layer.prompt({
                title: '确认清理 ' + targetPeriod + ' 分红数据',
                formType: 0, // 输入框类型（明文）
                value: '', // 默认值
                area: ['500px', '350px'],
                maxlength: 10,
                btn: ['立即清理', '取消'],
                yes: function(index, layero) {
                    var inputValue = layero.find('.layui-layer-input').val().trim();
                    if (inputValue === '立即清理') {
                        layer.close(index);
                        clearDividendData(targetPeriod);
                    } else {
                        layer.msg('请输入"立即清理"四个字确认执行', {icon: 2, time: 2000});
                        return false; // 阻止关闭对话框
                    }
                },
                btn2: function(index) {
                    layer.close(index);
                },
                success: function(layero, index) {
                    // 在对话框成功创建后添加说明内容
                    var promptContent = '<div style="padding: 0 0 15px 0; line-height: 1.6; color: #333; font-size: 13px;">' +
                            '<div style="color: #ff5722; font-weight: bold; margin-bottom: 12px;">' +
                            '⚠️ 此操作将删除已计算的分红数据，操作不可逆！' +
                            '</div>' +
                            '<div style="margin-bottom: 12px; font-weight: bold;">' +
                            '此操作将删除以下数据：' +
                            '</div>' +
                            '<ul style="margin: 8px 0 12px 20px; color: #666;">' +
                            '<li>门店分红明细表</li>' +
                            '<li>公司股东分红表</li>' +
                            '<li>股东分红清单表</li>' +
                            '<li>门店风险金变化记录</li>' +
                            '</ul>' +
                            '<div style="color: #ff5722; font-weight: bold; margin-bottom: 12px;">' +
                            '⚠️ 数据将被永久删除，无法恢复！清理后可以重新调整门店股份配置，然后重新计算！' +
                            '</div>' +
                            '<div style="color: #333; margin-bottom: 8px;">' +
                            '请在下方输入框中输入"<span style="color: #ff5722; font-weight: bold;">立即清理</span>"确认执行：' +
                            '</div>' +
                            '</div>';

                    // 在输入框前面插入说明内容
                    layero.find('.layui-layer-input').before(promptContent);

                    // 设置输入框占位符
                    layero.find('.layui-layer-input').attr('placeholder', '请输入：立即清理');
                }
            });
        }

        // 执行分红数据计算
        function calculateDividendData(period) {
            var loadingIndex = layer.load(1, {
                shade: [0.3, '#000'],
                content: '正在计算分红数据，请稍候...<br><small>此过程包括门店分红、公司股东分红和分红人清单计算</small>',
                success: function(layero) {
                    layero.find('.layui-layer-content').css({
                        'padding-top': '40px',
                        'width': '300px',
                        'text-align': 'center'
                    });
                }
            });

            $.ajax({
                url: '/dividend/detail/calculateDividend',
                type: 'post',
                dataType: 'json',
                timeout: 120000, // 2分钟超时
                data: {
                    period: period
                },
                success: function(res) {
                    layer.close(loadingIndex);
                    if (res.code == 0) {
                        // 显示详细的计算结果
                        var message = res.msg;
                        if (res.data && res.data.store_result && res.data.store_result.data) {
                            message += '<br>门店数量：' + res.data.store_result.data.length + ' 个';
                        }

                        layer.alert(message, {
                            icon: 1,
                            title: '计算完成',
                            area: ['400px', '200px']
                        }, function(index) {
                            layer.close(index);

                            // 刷新页面数据到计算的月份
                            layui.pageTable.reload({
                                where: {
                                    period: period,
                                    store_ids: []
                                },
                                done: function(res, curr, count) {
                                    if (res.code === 0) {
                                        tableMerge.render(this);
                                        applyTotalRowColors();
                                    }
                                }
                            });

                            // 更新期间选择器为计算的月份
                            $('#period').val(period);
                            form.render();

                            // 计算完成后重新检查按钮状态
                            checkCalculateButtonStatus();
                        });
                    } else {
                        // 根据错误类型显示不同的提示
                        var errorMsg = res.msg || '计算失败';
                        var isDataError = errorMsg.includes('账单数据') || errorMsg.includes('无数据');

                        layer.alert(errorMsg, {
                            icon: isDataError ? 3 : 2, // 数据问题用警告图标，其他用错误图标
                            title: isDataError ? '数据提示' : '计算失败',
                            area: ['450px', '250px'],
                            btn: ['确定', '查看账单管理'],
                            yes: function(index) {
                                layer.close(index);
                            },
                            btn2: function(index) {
                                if (isDataError) {
                                    // 跳转到账单管理页面
                                    window.open('/store/storebill/index', '_blank');
                                }
                                layer.close(index);
                            }
                        });
                    }
                },
                error: function() {
                    layer.close(loadingIndex);
                    layer.msg('网络错误，请重试', {icon: 2});
                }
            });
        }

        // 执行分红数据清理
        function clearDividendData(period) {
            var loadingIndex = layer.load(1, {
                shade: [0.3, '#000'],
                content: '正在清理分红数据，请稍候...<br><small>此过程将删除所有相关的分红数据</small>',
                success: function(layero) {
                    layero.find('.layui-layer-content').css({
                        'padding-top': '40px',
                        'width': '300px',
                        'text-align': 'center'
                    });
                }
            });

            $.ajax({
                url: '/dividend/detail/clearDividendData',
                type: 'post',
                dataType: 'json',
                timeout: 120000, // 2分钟超时
                data: {
                    period: period
                },
                success: function(res) {
                    layer.close(loadingIndex);
                    if (res.code == 0) {
                        layer.alert(res.msg, {
                            icon: 1,
                            title: '清理完成',
                            area: ['400px', '200px']
                        }, function(index) {
                            layer.close(index);

                            // 刷新页面数据
                            layui.pageTable.reload({
                                where: {
                                    period: period,
                                    store_ids: []
                                },
                                done: function(res, curr, count) {
                                    if (res.code === 0) {
                                        tableMerge.render(this);
                                        applyTotalRowColors();
                                    }
                                }
                            });

                            // 更新期间选择器为清理的月份
                            $('#period').val(period);
                            form.render();

                            // 清理完成后重新检查按钮状态
                            checkCalculateButtonStatus();
                        });
                    } else {
                        layer.alert(res.msg || '清理失败', {
                            icon: 2,
                            title: '清理失败',
                            area: ['400px', '200px']
                        });
                    }
                },
                error: function() {
                    layer.close(loadingIndex);
                    layer.msg('网络错误，请重试', {icon: 2});
                }
            });
        }

        // 检查计算按钮状态（只在页面加载时调用一次，永远检查上一月份）
        function checkCalculateButtonStatus() {
            // 永远检查上一月份，不受筛选框影响
            var currentDate = new Date();
            var lastMonth = new Date(currentDate.getFullYear(), currentDate.getMonth() - 1, 1);
            var targetPeriod = lastMonth.getFullYear() + '-' + String(lastMonth.getMonth() + 1).padStart(2, '0');

            // 查找工具栏中的元素
            var $calculateContainer = $('.layui-table-tool').find('#calculateBtnContainer');
            var $clearContainer = $('.layui-table-tool').find('#clearBtnContainer');
            var $statusText = $('.layui-table-tool').find('#toolbarStatusText');
            var $calculateBtn = $calculateContainer.find('#calculateBtn');
            var $clearBtn = $clearContainer.find('#clearBtn');

            // 每次检查前重置状态
            $calculateContainer.hide();
            $clearContainer.hide();
            $statusText.hide();

            $.ajax({
                url: '/dividend/detail/checkCalculateStatus',
                type: 'post',
                dataType: 'json',
                data: {
                    period: targetPeriod
                },
                success: function(res) {
                    if (res.code == 0) {
                        if (res.data.calculated) {
                            // 已计算过，显示清理按钮
                            $clearBtn.find('i').removeClass().addClass('layui-icon layui-icon-delete');
                            $clearBtn.contents().filter(function() {
                                return this.nodeType === 3;
                            }).remove();
                            $clearBtn.append('清理 ' + targetPeriod + ' 门店分红数据');
                            $clearContainer.show();
                        } else {
                            // 未计算，显示计算按钮
                            $calculateBtn.find('i').removeClass().addClass('layui-icon layui-icon-engine');
                            $calculateBtn.contents().filter(function() {
                                return this.nodeType === 3;
                            }).remove();
                            $calculateBtn.append('计算 ' + targetPeriod + ' 门店分红数据');
                            $calculateContainer.show();
                        }
                    } else if (res.code == 405) {
                        $statusText.text(res.msg || '操作不允许').show();
                    } else {
                        // 接口错误，显示错误状态
                        $statusText.text('无法获取计算状态').show();
                    }
                },
                error: function() {
                    // 网络错误，隐藏错误状态
                    $statusText.text('无法获取计算状态(网络错误)').show();
                }
            });
        }

        // 获取并打印当前用户信息
        function getCurrentUserInfo() {
            $.ajax({
                url: '/home/<USER>/get_current_user',
                type: 'post',
                dataType: 'json',
                success: function(res) {
                    if (res.code == 0 && res.data && res.data.user) {
                        var user = res.data.user;
                        console.log('当前用户信息:', user);
                    } else {
                        console.log('获取用户信息失败:', res.msg || '未知错误');
                    }
                },
                error: function() {
                    console.log('获取用户信息失败: 网络错误');
                }
            });
        }

        // 页面加载完成后检查按钮状态
        $(document).ready(function() {
            setTimeout(function() {
                // 获取并打印当前用户信息
                getCurrentUserInfo();

                // 检查URL参数，如果有计算成功的提示则显示
                var urlParams = new URLSearchParams(window.location.search);
                if (urlParams.get('calculated') === '1') {
                    var period = urlParams.get('period');
                    if (period) {
                        layer.msg('分红数据计算完成！当前显示 ' + period + ' 的数据', {
                            icon: 1,
                            time: 3000
                        });

                        // 清除URL参数
                        if (window.history && window.history.replaceState) {
                            window.history.replaceState({}, document.title, window.location.pathname);
                        }
                    }
                }
            }, 500);
        });

        // 监听抽屉关闭事件，刷新表格
        $(document).on('sideClose', function() {
            // 刷新表格数据
            if (layui.pageTable && layui.pageTable.reload) {
                layui.pageTable.reload({
                    done: function(res, curr, count) {
                        // 重新加载后也要合并单元格
                        if (res.code === 0) {
                            // 使用正确的表格实例
                            tableMerge.render(this);

                            // 应用合计行颜色策略
                            applyTotalRowColors();
                        }
                    }
                });
            }
        });


    }
</script>
{/block}
