{extend name="../../base/view/common/base" /}
<!-- 主体 -->
{block name="body"}
<div class="p-3">
	<form class="layui-form gg-form-bar border-x border-t" lay-filter="barsearchform">
		<div class="layui-input-inline" style="width:300px;">
			<input type="text" class="layui-input" id="diff_time" placeholder="选择时间区间" readonly name="diff_time">
		</div>
		<div class="layui-input-inline">
			<select name="check_status">
				<option value="">请选择状态</option>
				<option value="1">审核中</option>
				<option value="2">审核通过,待开具</option>
				<option value="3">审核拒绝</option>
				<option value="4">撤销</option>
				<option value="5">已开具</option>
				<option value="10">已作废</option>
			</select>
		</div>
		<div class="layui-input-inline" style="width:150px;">
			<button class="layui-btn layui-btn-normal" lay-submit="" lay-filter="webform"><i class="layui-icon layui-icon-search mr-1"></i>搜索</button>
			<button type="reset" class="layui-btn layui-btn-reset" lay-filter="reset">清空</button>
		</div>
	</form>
	<table class="layui-hide" id="test" lay-filter="test"></table>
</div>
<script type="text/html" id="toolbarDemo">
<div class="layui-btn-container">
    <span class="layui-btn layui-btn-sm" lay-event="add">+ 申请开票</span>
</div>
</script>
{/block}
<!-- /主体 -->

<!-- 脚本 -->
{block name="script"}
<script>
	const moduleInit = ['tool','laydatePlus'];
	function gouguInit() {
		var form = layui.form,table = layui.table,tool=layui.tool, laydatePlus = layui.laydatePlus;
		//日期范围
		var diff_time = new laydatePlus({'target':'diff_time'});
		
		//监听搜索提交
		form.on('submit(webform)', function(data) {
			layui.pageTable.reload({
				where: data.field,
				page: {curr: 1}
			});
			return false;
		});

		layui.pageTable = table.render({
			elem: '#test',
			title: '发票列表',
			toolbar: '#toolbarDemo',
			defaultToolbar:['filter', {title:'导出EXCEL',layEvent: 'LAYTABLE_EXCEL',icon: 'layui-icon-export'}],
			url: "/finance/invoice/index", //数据接口		
			page: true, //开启分页
			cellMinWidth: 80,
			limit: 20,
			cols: [
				[
					{
						field: 'id',
						title: 'ID号',
						align: 'center',
						width: 80
					}, {
						field: 'invoice_title',
						title: '开票抬头',
						minWidth: 240,	
						templet:function(d){
							var html='';
							if(d.type==1){
								html='<span class="layui-badge layui-bg-blue">企业</span> '+d.invoice_title;
							}
							else if(d.type==2){
								html='<span class="layui-badge layui-bg-green">个人</span> '+d.invoice_title;
							}
							return html;
						}
					},{
						field: 'amount',
						title: '开票金额(元)',
						align: 'right',
						width: 100,
					},{
						field: 'invoice_type',
						title: '开票类型',
						align: 'center',
						width: 120,
						templet:function(d){
							var html='-';
							if(d.invoice_type==1){
								html='<span class="green">增值税专用发票</span>';
							}
							else if(d.invoice_type==2){
								html='<span class="blue">普通发票</span>';
							}
							else if(d.invoice_type==3){
								html='<span class="red">专业发票</span>';
							}
							return html;
						}
					},{
						field: 'check_status',
						title: '发票状态',
						align: 'center',
						width: 120,
						templet:function(d){
							var html='<span class="black">待审核</span>';
							if(d.check_status==1){
								html='<span class="blue">审核中</span>';
							}
							else if(d.check_status==2){
								html='<span class="green">审核通过,待开具</span>';
							}
							else if(d.check_status==3){
								html='<span class="red">审核不通过</span>';
							}
							else if(d.check_status==4){
								html='<span class="red">已撤销</span>';
							}
							else if(d.check_status==5){
								html='<span class="green">已开具</span>';
							}
							else if(d.check_status==10){
								html='<span class="yellow">已作废</span>';
							}
							return html;
						}
					},{
						field: 'name',
						title: '申请人',
						align: 'center',
						width: 90
					},{
						field: 'department_name',
						title: '所属部门',
						align: 'center',
						width: 120
					},{
						field: 'create_time',
						title: '申请时间',
						align: 'center',
						width: 150
					},{
						field: 'check_user',
						title: '当前审核人',
						width: 140
					},{
						field: 'open_name',
						title: '开票人',
						align: 'center',
						width: 90
					},{
						field: 'open_time',
						title: '开票时间',
						align: 'center',
						width: 100
					},{
						field: 'code',
						title: '发票号码',
						align: 'center',
						width: 120
					}, {
						field: 'right',
						fixed: 'right',
						title: '操作',
						width: 164,
						align: 'center',
						templet:function(d){
							//0待审、1审批中、2通过、3失败、4撤销、5已开具、10已作废
							var html='<div class="layui-btn-group">';
							var btn1='<span class="layui-btn layui-btn-normal layui-btn-xs" lay-event="view">详情</span>';
							var btn2='<span class="layui-btn layui-btn-xs" lay-event="edit">编辑</span>';
							var btn3='<span class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">删除</span>';
							html+=btn1;
							if(d.check_status==4 && d.admin_id==login_admin){
								html+=btn2+btn3;
							}
							html+='</div>';
							return html;
						}
					}
				]
			]
		});

		//表头工具栏事件
		table.on('toolbar(test)', function(obj){
			if (obj.event === 'add') {
				tool.side("/finance/invoice/add");
				return;
			}
			if(obj.event === 'LAYTABLE_EXCEL'){
				var formSelect = form.val('barsearchform');
				formSelect.limit=99999;
				$.ajax({
					url: '/finance/invoice/index',
					data: formSelect,
					success:function(res){
						table.exportFile('test', res.data,'xls');
					}
				});
				return;
			}
		});
		//监听行工具事件
		table.on('tool(test)', function(obj) {
			var data = obj.data;
			if (obj.event === 'view') {
				tool.side("/finance/invoice/view?id="+data.id);
				return;
			}
			if (obj.event === 'edit') {
				tool.side("/finance/invoice/add?id="+data.id);
				return;
			}
			if (obj.event === 'del') {
				layer.confirm('确定删除该发票记录吗？', {
					icon: 3,
					title: '提示'
				}, function(index) {
					$.ajax({
						url: "/finance/invoice/delete",
						type:'post',
						data: {id: data.id},
						success: function(res) {
							layer.msg(res.msg);
							if (res.code == 0) {
								obj.del();
							}
						}
					})
					layer.close(index);
				});
			}
		});
	}
</script>
{/block}
<!-- /脚本 -->
